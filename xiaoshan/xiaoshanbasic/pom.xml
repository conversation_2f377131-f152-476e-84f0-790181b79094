<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <artifactId>xiaoshan</artifactId>
        <groupId>com.xiaoshan</groupId>
        <version>1.3.0.T</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>xiaoshanbasic</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>2.3.5.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-jwt</artifactId>
            <version>1.1.1.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.9</version>
        </dependency>

        <dependency>
            <artifactId>xiaoshanref</artifactId>
            <groupId>com.xiaoshan</groupId>
        </dependency>

        <dependency>
            <artifactId>topbase-core-common</artifactId>
            <groupId>com.topnetwork</groupId>
        </dependency>

        <dependency>
            <artifactId>topbase-service-logger</artifactId>
            <groupId>com.topnetwork</groupId>
            <scope>${profiles.scope}</scope>
        </dependency>

        <dependency>
            <artifactId>topbase-component-boot-swagger</artifactId>
            <groupId>com.topnetwork</groupId>
            <scope>${profiles.scope}</scope>
        </dependency>

        <dependency>
            <artifactId>topbase-core-devtools</artifactId>
            <groupId>com.topnetwork</groupId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cwbase</groupId>
            <artifactId>logback-redis-appender</artifactId>
            <version>1.1.6</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

    </dependencies>

</project>
