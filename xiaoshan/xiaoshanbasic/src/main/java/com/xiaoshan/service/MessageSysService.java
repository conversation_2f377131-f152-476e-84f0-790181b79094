package com.xiaoshan.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.common.message.IMessage;
import com.topnetwork.ao.MessageSysAO;
import com.xiaoshan.basic.ao.MessageTextAO;

import start.framework.commons.constant.Config;
import start.framework.commons.rest.ApiRestTemplate;

/**
 * <AUTHOR>
 */
@Service
public class MessageSysService implements IMessage<MessageSysAO> {

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public boolean supports(Class<?> clazz) {
        return clazz.equals(MessageSysAO.class);
    }

    @Override
    public void send(MessageSysAO data) {
    }

}
