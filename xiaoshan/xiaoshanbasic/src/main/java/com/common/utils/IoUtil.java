package com.common.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.Serializable;

import start.magic.core.ApplicationException;

public class IoUtil {

	/**
	 * 文件拷贝方法
	 * @param src
	 * @param dest
	 */
	public static void copyFile(File src, File dest) {
		InputStream in = null;
		OutputStream out = null;
		try {
			in = new FileInputStream(src);
			out = new FileOutputStream(dest);
			int len = 0;
			// 文件拷贝方法
			byte[] car = new byte[1024];
			while ((len = in.read(car)) != -1) {
				out.write(car, 0, len);
				out.flush();
			}
		} catch (FileNotFoundException e) {
			throw new ApplicationException(e);
		} catch (IOException e) {
			throw new ApplicationException(e);
		} finally {
			try {
				// 关闭输入输出流
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException e) {
				throw new ApplicationException(e);
			}
		}
	}

	/**
	 * 拷贝文件夹操作,使用递归复制
	 * @param src
	 * @param dest
	 */
	public static void copyDirAndFire(File src, File dest) {
		// 判断原路径是否为空
		if (src == null) {
			return;
		}
		// 创建目标路径
		File target = new File(dest, src.getName());
		// 如果是文件夹,递归创建文件夹
		if (src.isDirectory()) {
			target.mkdirs();
			File[] files = src.listFiles();
			for (File f : files) {
				copyDirAndFire(f, target);
			}
		} else {
			// 如果是文件,执行文件拷贝
			IoUtil.copyFile(src, target);
		}
	}

	@SuppressWarnings("unchecked")
	public static <T extends Serializable> T clone(T obj) {
		T clonedObj = null;
		try {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(baos);
			oos.writeObject(obj);
			oos.close();
			ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
			ObjectInputStream ois = new ObjectInputStream(bais);
			clonedObj = (T) ois.readObject();
			ois.close();
		} catch (Exception e) {
			throw new ApplicationException(e);
		}
		return clonedObj;
	}

}
