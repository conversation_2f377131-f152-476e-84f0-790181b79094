package com.common.utils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class XxlJobUtils {

    public static Map<String, Object> addJobMap(Date atDate,
                                                Integer jobGroup,
                                                String executorHandler,
                                                String author,
                                                String jobDesc,
                                                String executorParam) {
        HashMap<String, Object> jobMap = new HashMap<>();
        jobMap.put("jobCron", DateUtils.dateToCron(atDate));
        jobMap.put("jobGroup", jobGroup);
        jobMap.put("executor<PERSON>andler", executorHandler);
        jobMap.put("author", author);
        jobMap.put("executorBlockStrategy", "SERIAL_EXECUTION");
        jobMap.put("executorRouteStrategy", "ROUND");
        jobMap.put("glueType", "BEAN");
        jobMap.put("jobDesc", jobDesc);
        jobMap.put("executorParam", executorParam);
        return jobMap;
    }
}
