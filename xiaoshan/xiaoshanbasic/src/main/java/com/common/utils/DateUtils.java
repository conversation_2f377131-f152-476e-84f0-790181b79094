package com.common.utils;

import com.xiaoshan.edu.enums.timetable.WeekEnum;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DateUtils {

    public static boolean betweenByDate(java.sql.Date startDate, java.sql.Date endDate, java.sql.Date currentDate) {
        return currentDate.compareTo(startDate) >= 0 && endDate.compareTo(currentDate) >= 0;
    }

    // 返回某天开始时间
    public static Date getStartOfDay(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    // 返回某天结束时间
    public static Date getEndOfDay(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    // 时间格式化
    public static String formatDateTime(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(date);
    }

    // 时间格式化
    public static String formatDateHour(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return simpleDateFormat.format(date);
    }

    // 时间格式化
    public static String formatDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
        return simpleDateFormat.format(date);
    }

    // 时间格式化
    public static String formatDate(java.sql.Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
        return simpleDateFormat.format(date);
    }

    // 时间格式化
    public static String formatDateYmd(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    // 时间格式化
    public static String formatDateMd(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM-dd");
        return simpleDateFormat.format(date);
    }

    public static String getDayOfWeek(Date date) {
        String[] weeks = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekIndex = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekIndex < 0) {
            weekIndex = 0;
        }
        return weeks[weekIndex];
    }

    public static String getWeekV2(Date date) {
        String[] weeks = {"星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekIndex = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekIndex < 0) {
            weekIndex = 0;
        }
        return weeks[weekIndex];
    }

    public static String getWeekV3(Date date) {
        String[] weeks = {"日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekIndex = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekIndex < 0) {
            weekIndex = 0;
        }
        return weeks[weekIndex];
    }

    public static Date stringToDate(String date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 日期时间加减
     * date: 要计算的时间
     * calendar：时间单位，例如 Calendar.MINUTE
     * amount: 时间，如果是减，则值为负
     */
    public static Date plus(Date date, Integer calendar, Integer amount) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendar, amount);
        return c.getTime();
    }

    /**
     * 获取两个日期之间的日期列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    public static List<java.sql.Date> getBetweenDate(java.sql.Date startDate, java.sql.Date endDate) {
        List<java.sql.Date> listDate = new ArrayList<>();
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (calendar.getTime().before(endDate) || calendar.getTime().equals(endDate)) {
                Date time = calendar.getTime();
                listDate.add(new java.sql.Date(time.getTime()));
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            return listDate;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listDate;
    }

    /**
     * 根据日期获得所在周的日期
     *
     * @param mdate 日期
     * @return 日期所在周日期集合
     */
    public static List<Date> dateToWeek(Date mdate) {
        // 获取传入日期所在周次
        int seven = 7;
        WeekEnum weekByIndex = WeekEnum.getWeekByIndex(mdate);
        Integer b = WeekEnum.getWeekNum(weekByIndex);
        Date fdate;
        List<Date> list = new ArrayList<Date>();
        long fTime = mdate.getTime() - b * 24 * 3600000;
        for (int a = 1; a <= seven; a++) {
            fdate = new Date();
            fdate.setTime(fTime + ((long) a * 24 * 3600000));
            list.add(a - 1, fdate);
        }
        return list;
    }

    // 两个时间差几小时几分钟
    public static String getDatePoorHour(Date endDate, Date nowDate) {

        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少小时
        long hour = diff / nh;
        // 计算差多少分钟
        long min = diff % nh / nm;
        return hour + "小时" + min + "分钟";
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    // 根据java.util.Date生成Cron表达式
    public static String dateToCron(Date date) {
        LocalDateTime d = toLocalDateTime(date);
        int year = d.get(ChronoField.YEAR);
        int month = d.get(ChronoField.MONTH_OF_YEAR);
        int day = d.get(ChronoField.DAY_OF_MONTH);
        int hour = d.get(ChronoField.HOUR_OF_DAY);
        int minute = d.get(ChronoField.MINUTE_OF_HOUR);
        int second = d.get(ChronoField.SECOND_OF_MINUTE);
        return String.format("%s %s %s %s %s ? %s", second, minute, hour, day, month, year);
    }
}
