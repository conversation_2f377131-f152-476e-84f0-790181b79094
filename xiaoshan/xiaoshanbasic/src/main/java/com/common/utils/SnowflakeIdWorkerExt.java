package com.common.utils;


import start.magic.utils.SnowflakeIdWorker;

/**
 * <AUTHOR>
 */
public class SnowflakeIdWorkerExt extends SnowflakeIdWorker {


    public SnowflakeIdWorkerExt(long workerId, long datacenterId) {
        super(workerId, datacenterId);
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException(
                    String.format("datacenter Id can't be greater than %d or less than 0", maxDatacenterId));
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;
    }

    /**
     * 开始时间截 (2015-01-01)
     */
    private final long twepoch = 1420041600000L;

    /**
     * 序列值所占的位数
     */
    private final long sequenceBits = 12L;

    /**
     * 机器id所占的位数
     */
    private final long workerIdBits = 2L;

    /**
     * 机房id所占的位数
     */
    private final long datacenterIdBits = 2L;

    /**
     * 机器id向左移12位
     */
    private final long workerIdLeftShift = sequenceBits;

    /**
     * 机房id向左移17位(5+12)
     */
    private final long datacenterIdLeftShift = workerIdLeftShift + workerIdBits;

    /**
     * 时间截向左移22位(5+5+12)
     */
    @SuppressWarnings("unused")
    private final long timestampLeftShift = datacenterIdLeftShift + datacenterIdBits;

    /**
     * 支持的最大机器id (位数二进制值)
     */
    private final long maxWorkerId = -1L ^ (-1L << workerIdBits);

    /**
     * 支持的最大数据标识id (位数二进制值)
     */
    private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);

    /**
     * 生成序列的掩码
     */
    private final long sequenceMask = -1L ^ (-1L << sequenceBits);

    /**
     * 工作机器ID(0~31)
     */
    private long workerId;

    /**
     * 数据中心ID(0~31)
     */
    @SuppressWarnings("unused")
    private long datacenterId;

    /**
     * 毫秒内序列
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间截
     */
    private long lastTimestamp = -1L;

    // ==============================Methods==========================================

    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * @return SnowflakeId
     */
    @Override
    public synchronized long nextId() {
        long timestamp = timeGen();

        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(String.format(
                    "Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }

        // 如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            // 毫秒内序列溢出
            if (sequence == 0) {
                // 阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        }
        // 时间戳改变，毫秒内序列重置
        else {
            sequence = 0L;
        }

        // 上次生成ID的时间截
        lastTimestamp = timestamp;

        // 移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - twepoch) << datacenterIdLeftShift)
				| (datacenterId << datacenterIdLeftShift)
                | (workerId << workerIdLeftShift)
                | sequence;
    }


}
