package com.common.utils;

import org.springframework.beans.BeanUtils;
import start.magic.core.ApplicationException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 相互转换
 *
 * <AUTHOR>
 */
public class ClassConverter {

    /**
     * Bean 属性复制
     */
    public static <R, T> T aTob(R a, T b) {
        BeanUtils.copyProperties(a, b);
        return b;
    }

    /**
     * Bean 属性复制
     */
    public static <R, T> List<T> aTobList(List<R> sourceList, Class<T> targetClass) {
        return sourceList.stream().map(source -> {
            try {
                T target = targetClass.newInstance();
                BeanUtils.copyProperties(source, target);
                return target;
            } catch (InstantiationException | IllegalAccessException e) {
                e.printStackTrace();
                throw new ApplicationException("Bean属性批量复制出错");
            }
        }).collect(Collectors.toList());
    }
}
