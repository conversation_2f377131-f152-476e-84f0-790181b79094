package com.common.utils;

import start.framework.commons.utils.TimeUtils;

import java.sql.Date;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DateTimeUtils {

    public static boolean isEntryTime(Time endTime, java.util.Date date) {
        Time startTime = Time.valueOf("00:00:00");
        return isEntryTime(startTime, endTime, date);
    }

    public static boolean isEntryTime(Time startTime, Time endTime, java.util.Date date) {
        Time now = converterTime(date);
        return startTime.before(now) && now.before(endTime);
    }

    public static Time converterTime(java.util.Date date) {
        return Time.valueOf(TimeUtils.format(date, TimeUtils.HHMMSS_F));
    }

    public static List<Date> allBetweenDate(Date startDate, Date endDate) {
        List<Date> lists = new ArrayList<>();
        lists.add(startDate);
        while (endDate.after(startDate)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            startDate = new Date(calendar.getTime().getTime());
            lists.add(startDate);
        }
        return lists;
    }

}
