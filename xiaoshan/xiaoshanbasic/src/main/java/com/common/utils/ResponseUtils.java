package com.common.utils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import start.framework.commons.utils.CodecUtils;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class ResponseUtils {

    public static void setExcel(HttpServletResponse response, String name) {
        setResponse(response, "application/vnd.ms-excel", name + ".xlsx");
    }

    public static void setZip(HttpServletResponse response, String name) {
        setResponse(response, "application/octet-stream", name + ".zip");
    }

    public static void setResponse(HttpServletResponse response, String contentType, String fileName) {
        response.setContentType(contentType);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + CodecUtils.encode(fileName, "UTF-8"));
        response.setHeader("access-control-expose-headers", "Content-disposition");
    }

    public static void setCors(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("access-control-expose-headers", "Authorization,Content-disposition");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers",
                "Authorization, Content-Type, Depth, User-Agent, X-File-Size, X-Requested-With, X-Requested-By, If-Modified-Since, X-File-Name, X-File-Type, Cache-Control, Origin");
    }

    public static void setCors(HttpServletRequest request, HttpServletResponse response, Set<String> origins) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("access-control-expose-headers", "Authorization,Content-disposition");
        String origin = request.getHeader("Origin");
        if (origin != null) {
            if (origins.contains(origin)) {
                response.setHeader("Access-Control-Allow-Origin", origin);
            }
        }
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers",
                "Authorization, Content-Type, Depth, User-Agent, X-File-Size, X-Requested-With, X-Requested-By, If-Modified-Since, X-File-Name, X-File-Type, Cache-Control, Origin");
    }

}
