package com.common.core.oauth2;

import java.util.Map;

import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.stereotype.Component;

import com.common.model.JwtUser;
import com.common.model.UserContextHolder;

import start.framework.commons.rest.ConverterEditorUtils;
import start.magic.thirdparty.json.JsonObject;

/**
 * <AUTHOR>
 */
@Component
public class CustomAccessTokenConverter extends DefaultAccessTokenConverter {

    @Override
    public OAuth2Authentication extractAuthentication(Map<String, ?> claims) {
        OAuth2Authentication authentication = super.extractAuthentication(claims);
        Map<String, Object> userInfo = (Map<String, Object>) claims.get("user_info");
        JsonObject json = new JsonObject(userInfo);
        JwtUser userDTO = ConverterEditorUtils.restoreObject(JwtUser.class, json);
        UserContextHolder.setUser(userDTO);
        authentication.setDetails(claims);
        return authentication;
    }

}
