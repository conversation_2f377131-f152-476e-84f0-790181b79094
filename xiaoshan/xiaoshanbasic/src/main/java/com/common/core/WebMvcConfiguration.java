package com.common.core;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import com.common.mvc.AuthorityInterceptor;
import com.common.mvc.DefaultWebMvcConfiguration;
import com.common.mvc.RequserAgainFilter;
import com.common.mvc.httpmessage.JsonExtHttpMessageConverter;

/**
 * Web配置
 * 
 * <AUTHOR>
 *
 */

@Configuration
public class WebMvcConfiguration extends DefaultWebMvcConfiguration {

	@Autowired
	private OriginConfig originConfig;

	
	@Bean
	public AuthorityInterceptor authorityInterceptor() {
		return new AuthorityInterceptor();
	}
	
	@Order(10)
	@Bean
	public JsonExtHttpMessageConverter jsonExtHttpMessageConverter() {
		return new JsonExtHttpMessageConverter();
	}

	@Bean
	public FilterRegistrationBean<RequserAgainFilter> requserAgainFilter() {
	    FilterRegistrationBean<RequserAgainFilter> filter = new FilterRegistrationBean<>();
	    filter.addUrlPatterns("/*");
	    filter.setFilter(new RequserAgainFilter());
	    return filter;
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(authorityInterceptor()).addPathPatterns("/**");
	}
	
	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
		converters.add(jsonExtHttpMessageConverter());
	}
	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/**")
				.allowedOrigins(originConfig.getHosts().toArray(new String[0]))
				.allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE")
				.maxAge(3600)
				.allowCredentials(true);
	}


}
