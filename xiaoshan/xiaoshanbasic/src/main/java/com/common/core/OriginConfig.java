package com.common.core;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @program: sc_scaffold
 * @description: 白名单链接
 * @author: HeTong
 * @create: 2021-01-16 14:46
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "origins")
@RefreshScope
public class OriginConfig {
    private Set<String> hosts;
}
