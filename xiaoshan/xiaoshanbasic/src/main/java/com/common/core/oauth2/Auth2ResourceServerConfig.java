package com.common.core.oauth2;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

import start.framework.commons.constant.Config;
import start.framework.commons.utils.IoUtils;
import start.magic.core.ApplicationException;

/**
 * <AUTHOR>
 */
@Configuration
@EnableResourceServer
public class Auth2ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private AuthExceptionEntryPoint authenticationEntryPoint;
    @Autowired
    private CustomAccessTokenConverter customAccessTokenConverter;

    @Override
    public void configure(HttpSecurity http) throws Exception {

        String[] antPatterns;
        String uatStr = "uat";
        String proStr = "pro";
        String devStr = "dev";
        if (uatStr.equals(Config.getActive()) || proStr.equals(Config.getActive())) {
            antPatterns = new String[]{
                    "/devdoc/**", "/template/**", "/actuator/health", "/actuator/prometheus", "/actuator/metrics"
            };
        } else if (devStr.equals(Config.getActive())) {
            antPatterns = new String[]{
                    "/swagger-ui/**",
                    "/swagger-resources/**",
                    "/v3/api-docs",
                    "index.html",
                    "/devdoc/**",
                    "/static/**",
                    "/template/**", "/actuator/health", "/actuator/prometheus", "/actuator/metrics"
            };
        } else {
            antPatterns = new String[]{
                    "/swagger-ui/**",
                    "/swagger-resources/**",
                    "/v3/api-docs",
                    "index.html",
                    "/devdoc/**",
                    "/v1/**",
                    "/static/**",
                    "/template/**"
                    , "/actuator/health", "/actuator/prometheus", "/actuator/metrics"
            };
        }
        http.authorizeRequests().
                antMatchers(HttpMethod.OPTIONS).permitAll()
                .antMatchers(antPatterns)
                .permitAll().anyRequest().authenticated();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources.resourceId("gathering");
        resources.tokenServices(tokenServices());
        resources.authenticationEntryPoint(authenticationEntryPoint);
    }

    @Bean
    @Primary
    public DefaultTokenServices tokenServices() {
        DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
        defaultTokenServices.setTokenStore(tokenStore());
        return defaultTokenServices;
    }

    @Bean
    public TokenStore tokenStore() {
        return new JwtTokenStore(accessTokenConverter());
    }

    @Bean
    public JwtAccessTokenConverter accessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
        converter.setAccessTokenConverter(customAccessTokenConverter);
        Resource resource = new ClassPathResource("pubkey/public-" + active + ".txt");
        String pubKey;
        try {
            pubKey = new String(IoUtils.inputStreamConvertBytes(resource.getInputStream(), -1));
        } catch (IOException e) {
            throw new ApplicationException(e);
        }
        converter.setVerifierKey(pubKey);
        return converter;
    }

}
