package com.common.core.oauth2;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.common.core.OriginConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import com.common.utils.ResponseUtils;

/**
 * <AUTHOR>
 */
@Component
public class AuthExceptionEntryPoint implements AuthenticationEntryPoint, AccessDeniedHandler {

    @Autowired
    private OriginConfig originConfig;


    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       AccessDeniedException accessDeniedException) throws IOException {
        response.setStatus(401);
        ResponseUtils.setCors(request,response,originConfig.getHosts());
        response.setContentType("text/javascript;charset=utf-8");
        response.getWriter().print("认证过的用户访问无权限资源时的异常-没有访问权限!");
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        response.setStatus(401);
        ResponseUtils.setCors(request,response,originConfig.getHosts());
        response.setContentType("text/javascript;charset=utf-8");
        response.getWriter().print("匿名用户访问无权限资源时的异常-没有访问权限!");
    }

}
