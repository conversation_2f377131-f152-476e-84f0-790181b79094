package com.common.core;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

import com.common.utils.SnowflakeIdWorkerExt;

import start.framework.commons.constant.Config;
import start.magic.utils.SnowflakeIdWorker;

/**
 * <AUTHOR>
 */
@Order(1)
@Configuration
public class InitContext {

	@Bean
	@Primary
	public SnowflakeIdWorker getSnowflakeIdWorkerExt(){
		return new SnowflakeIdWorkerExt(Config.getBalancedWorkerId(),
				Config.getBalancedDataCenterId());
	}
    
}
