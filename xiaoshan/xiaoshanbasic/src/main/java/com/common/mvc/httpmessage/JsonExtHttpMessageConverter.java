package com.common.mvc.httpmessage;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotWritableException;

import com.common.mvc.NotException;

import start.framework.commons.constant.Config;
import start.framework.commons.rest.ConverterEditorUtils;
import start.magic.thirdparty.codec.Base64;
import start.magic.thirdparty.json.JsonObject;

/**
 * 转换器 application/json
 *
 * <AUTHOR>
 */
public class JsonExtHttpMessageConverter extends JsonHttpMessageConverter {

    @Autowired
    private HttpServletResponse response;

    @Override
    protected void writeInternal(Object t, Type type, HttpOutputMessage outputMessage)
            throws IOException, HttpMessageNotWritableException {
        String content;
        JsonObject res = ConverterEditorUtils.converter(t, JsonObject.class);
        int code = res.getInt("code");
        int sc = 299;
        if (code > sc || code == 0) {
            int s = 100000;
            if (code == s) {
                String[] args = res.getString("message").split("-");
                response.setStatus(Integer.parseInt(args[0]));
                content = new String(Base64.decode(args[1]));
            } else {
                response.setStatus(HttpStatus.BAD_REQUEST.value());
                content = res.getString("message");
            }
        } else {
            response.setStatus(HttpStatus.OK.value());
            JsonObject json = new JsonObject();
            String resultKey = "result";
            if (res.has(resultKey)) {
                json.put("result", res.get("result"));
            }
            String pageInfoKey = "pageInfo";
            if (res.has(pageInfoKey)) {
                json.put("pageInfo", res.get("pageInfo"));
            }
            content = json.toString();
        }
        OutputStream out = outputMessage.getBody();
        try {
            out.write(content.getBytes(Config.getEncoding()));
            out.flush();
        } catch (Exception e) {
            throw new NotException(e);
        }
    }

}
