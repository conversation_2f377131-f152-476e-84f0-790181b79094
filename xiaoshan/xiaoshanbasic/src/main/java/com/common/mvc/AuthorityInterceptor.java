package com.common.mvc;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.method.HandlerMethod;

import com.alibaba.druid.util.StringUtils;
import com.base.common.mvc.BaseInterceptor;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;

import start.framework.commons.constant.Config;
import start.framework.commons.context.Http;

/**
 * 拦截器
 *
 * <AUTHOR>
 *
 */
public class AuthorityInterceptor extends BaseInterceptor {

	@Override
	public void authenticationCheck(Http http, HandlerMethod handlerMethod) {
		http.requestCheck(false);
		http.setAccessId("-");
		String auth = http.getHeaderValue("Authorization");
		if (!StringUtils.isEmpty(auth)) {
			UserContextHolder.setToken(auth);
		}else {
			String devStr="dev";
			if(devStr.equals(Config.getActive())) {
				JwtUser user = new JwtUser();
				user.setId(416L);
				UserContextHolder.setUser(user);
			}
		}
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		super.afterCompletion(request, response, handler, ex);
		UserContextHolder.tokenRemove();
		UserContextHolder.userRemove();
	}
	
	

}
