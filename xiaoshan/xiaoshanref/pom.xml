<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<parent>
		<artifactId>xiaoshan</artifactId>
		<groupId>com.xiaoshan</groupId>
		<version>1.3.0.T</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>xiaoshanref</artifactId>
	<packaging>jar</packaging>

	<dependencies>

		<dependency>
			<artifactId>topbase-core-ref</artifactId>
			<groupId>com.topnetwork</groupId>
		</dependency>
		
		<dependency>
			<groupId>start.magic</groupId>
			<artifactId>magic-framework-core</artifactId>
		</dependency>

		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>

    </dependencies>

</project>
