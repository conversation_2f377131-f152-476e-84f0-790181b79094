.DS_Store
classes/*
.settings/
target/
.classpath
.project

*.iml
.idea/
work/
bin/
HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
*.log


### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache


### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr


### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/


### VS Code ###
.vscode/


.DS_Store
node_modules
/dist


# local env files
.env.local
.env.*.local


# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*


# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
