package com.xiaoshan.common;

import com.topnetwork.constants.CodeResVal;

/**
 * <AUTHOR>
 */
public interface CodeRes extends CodeResVal {

    String CODE_100000 = "100000-";

    String CODE_100001 = "100001-学生信息不存在";
    String CODE_100004 = "100004-节假日已过配置时间段,无法再调整";
    String CODE_100005 = "100005-节假日日期数据配置有误";
    String CODE_100009 = "100009-节假日修改信息有误";
    String CODE_100010 = "100010-今日不需要考勤";
    String CODE_100011 = "100011-考勤信息不存在";
    String CODE_100012 = "100012-学生数据未查询到";
    String CODE_100013 = "100013-导入文件不存在";


    String CODE_200000 = "200000-行事历类型有误";
    String CODE_200001 = "200001-学期不存在";
    String CODE_200002 = "200002-开始日期不能大于结束日期";
    String CODE_200003 = "200003-日期超过学期范围";
    String CODE_200004 = "200004-周信息不存在";
    String CODE_200005 = "200005-所选周次已经存在行事历，请检查";
    String CODE_200006 = "200006-该学期行事历名称已存在";


    String CODE_300001 = "300001-excel表中正确数据为空，请重新上传";
    String CODE_300002 = "300002-教师工资通知不存在";


    String CODE_400000 = "400000-会议开始时间不能大于结束时间";
    String CODE_400001 = "400001-会议室不存在";
    String CODE_400002 = "400002-会议室已被占用";
    String CODE_400003 = "400003-会议时间最小粒度为5分钟";
    String CODE_400004 = "400004-图片限制9张";
    String CODE_400005 = "400005-至少需要一个参会人";
    String CODE_400006 = "400006-请先登录";
    String CODE_400007 = "400007-参会人不允许重复添加";
    String CODE_400008 = "400008-当前无会议记录，导出失败";
    String CODE_400009 = "400009-添加参会人不能为空";
    String CODE_400010 = "400010-excel数据导出失败";
    String CODE_400011 = "400011-会议已结束，拒绝添加参会人";
    String CODE_400012 = "400012-会议开始时间已过时，请选择将来的时间";

    String CODE_500001 = "500001-上课日期范围超过当前学期起止时间";
    String CODE_500002 = "500002-上课时间不能是过去的时间";
    String CODE_500003 = "500003-输入教师id不存在";
    String CODE_500005 = "500005-拒绝重复听课";
    String CODE_500006 = "500006-此班级在此时间已经有公开课安排";
    String CODE_500007 = "500007-该时间段地点被占用";
    String CODE_500008 = "500008-该教师在此时间已有公开课安排";

    String CODE_600001 = "600001-查询开始时间不允许为空";
    String CODE_600002 = "600002-查询结束时间不允许为空";
    String CODE_600006 = "600006-获取审批流程信息失败";
    String CODE_600007 = "600007-销假时间要满足当前时间之后，请假结束时间之前";
    String CODE_600008 = "600008-临时假不允许跨天（不允许超过4小时）";
    String CODE_600009 = "600009-获取教师【公出/请假】数据不存在";
    String CODE_600011 = "600011-已提醒对方审批，请耐心等候。";
    String CODE_600012 = "600012-临时假请假时间不得低于5分钟。";
    String CODE_600013 = "600013-临时假请假时间不得大于4小时。";
    String CODE_600014 = "600014-请假时间不得低于半天。";
    String CODE_600015 = "600015-当前已有审批人审批，不可撤销！";
    String CODE_600016 = "600016-结束时间不得早于开始时间！";
    String CODE_600017 = "600017-非临时假timeScope不允许为空！";
    String CODE_600018 = "600018-节点审批人不允许为空！";
    String CODE_600019 = "600019-您在此时间段内已有公出，请重新选择";
    String CODE_600020 = "600020-您在此时间段内已有请假，请重新选择";

    String CODE_700000 = "700001-该课程编号已存在";
    String CODE_700001 = "700001-该课程名称已存在";
    String CODE_700005 = "700001-该课程简称已存在";
    String CODE_700002 = "700002-不是班主任也没有任课任何班级";
    String CODE_700003 = "700003-学生课表信息不存在";
    String CODE_700004 = "700004-班级课表信息不存在";
    String CODE_700006 = "700005-课表信息已存在";
    String CODE_700007 = "700007-课表基础校验未通过";
    String CODE_700008 = "存在同时间不同老师在同一班级/教室上课";
    String CODE_700015 = "%s学生%s第%s节(%s)同时在多个地点上课";

    String CODE_700010 = "700010-导入表格格式错误，请严格参照模版上传";
    String CODE_700011 = "%s,教师名称与“教师数据”中的名称不匹配";
    String CODE_700012 = "课程简称与“课程管理”中的简称不匹配";
    String CODE_700013 = "700013-%s,同一老师单双周教不同课程";
    String CODE_700014 = "700014-总课表没有任何班级信息";

    String CODE_800001 = "800001-结束时间不允许早于开始时间";
    String CODE_800002 = "800002-请先登录";
    String CODE_800003 = "800003-excel数据导出失败";
    String CODE_800004 = "800004-%s老师%s已%s，请重新选择";
    String CODE_800005 = "800005-选中的节次已经存在代课，请重新选择";
    String CODE_800006 = "800006-该节次已存在调课，请重新选择";
    String CODE_800007 = "800007-%s老师在%s已存在课程，请重新选择";
    String CODE_800008 = "800008-%s课程不是原始课表数据，%s必填";
    String CODE_800009 = "800009-%s课程数据有误";
    String CODE_800010 = "800010-非自修情况下，%s必填";
    String CODE_800011 = "800011-跨周调课不允许循环";
    String CODE_800012 = "800012-管理端不纳入统计字段不能为空";
    String CODE_800013 = "800013-当前课表信息有更新，请重新申请";
    String CODE_800014 = "800014-调课时间不在循环时间段内";
    String CODE_800015 = "800015-所选时间段内无课程可循环，请修改时间段或选择不循环";
    String CODE_800016 = "800016-该节次已存在代课，请重新选择";
    String CODE_800017 = "800017-%s老师有审批中的调课";
    String CODE_800018 = "800018-%s老师有审批中的代课";
    String CODE_800019 = "800019-所选周次内无课程可循环，请修改周循环次数或选择不循环";

    String CODE_900001 = "900001-excel数据导出失败";
    String CODE_900002 = "900002-选择的通知类型有误";
    String CODE_900003 = "900003-巡课对象数据不存在，添加失败";
    String CODE_900004 = "900004-巡课班级数据不存在";

    String CODE_1000001 = "消息发送失败，请检查相关推送事项是否开启";
    String CODE_1000002 = "周次信息有误";

}
