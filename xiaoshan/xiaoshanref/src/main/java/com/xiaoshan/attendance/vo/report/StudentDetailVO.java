package com.xiaoshan.attendance.vo.report;

import java.sql.Time;
import java.util.Date;

import com.xiaoshan.attendance.enums.AttendanceStatus;
import com.xiaoshan.attendance.enums.AttendanceType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class StudentDetailVO extends StudentBasicVO {
	
	@ApiModelProperty("考勤日期")
	private java.sql.Date validDate;
	
	@ApiModelProperty("进校时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time entryTime;
	
	@ApiModelProperty("早读时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time monringReadingTime;

	@ApiModelProperty("进校考勤")
	private AttendanceStatus status;
	
	@ApiModelProperty("早读考勤")
	private AttendanceStatus morningReadingStatus;
	
	@ApiModelProperty("考勤类型")
	private AttendanceType type;

	@ApiModelProperty("是否需要考勤")
	private Boolean entryAttendance;
	
	@ApiModelProperty("是否在校(默认:不在校)")
	private Boolean inSchool=false;
	
	@ApiModelProperty("进考勤时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date entrySchoolDate;
	
	@ApiModelProperty("进识别图片")
	private String entrySchoolUploadKey;
	
	@ApiModelProperty("进识别设备")
	private String entrySchoolEquipment;
	
	@ApiModelProperty("离校考勤时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date leavingSchoolSchoolDate;
	
	@ApiModelProperty("离校识别图片")
	private String leavingSchoolUploadKey;
	
	@ApiModelProperty("离校识别设备")
	private String leavingSchoolEquipment;

	@ApiModelProperty("早读考勤时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date monringReadingDate;
	
	@ApiModelProperty("早读识别图片")
	private String monringReadingUploadKey;
	
	@ApiModelProperty("早读识别设备")
	private String monringReadingEquipment;
	
//	@ApiModelProperty("归寝考勤时间")
//	@PropertyConverter(DateTimeFConverterEditor.class)
//	private Date backBedDate;
//	
//	@ApiModelProperty("归寝识别图片")
//	private String backBedUploadKey;
//	
//	@ApiModelProperty("归寝识别设备")
//	private String backBedEquipment;
	
}
