package com.xiaoshan.attendance.dto;

import java.sql.Time;

import com.xiaoshan.basic.enums.GradeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class TodayDTO {

	@ApiModelProperty("年级")
	private GradeEnum grade;

	@ApiModelProperty("日期")
	private java.sql.Date day;
	
	@ApiModelProperty("是否进校考勤")
	private Boolean attendance;
	
	@ApiModelProperty("是否早读考勤")
	private Boolean monringReadingAttendance;
	
	@ApiModelProperty("是否返校日")
	private Boolean returnToSchool;
	
	@ApiModelProperty("周几考勤")
	private Integer week;
	
	@ApiModelProperty("是否大小礼拜")
	private Boolean maxWeek;

	@ApiModelProperty("节假日是否可回家")
	private Boolean holidayGoHome;

	@ApiModelProperty("早上进校(用作学生考勤)-进")
	private Time morningEntryTime;

	@ApiModelProperty("早读考勤")
	private Time morningReadingTime;
	
	@ApiModelProperty("日常傍晚离校(用作学生出校权限限制)-出")
	private Time nightLeaveTime;
	
	@ApiModelProperty("日常晚自修离校(用作学生出校权限限制)-出")
	private Time lateSelfStudyLeaveTime;
	
	@ApiModelProperty("周五下午离校(用作学生出校权限限制)-出")
	private Time fridayLeaveTime;
	
	@ApiModelProperty("周六下午离校/小礼拜周日下午离校(用作学生出校权限限制)-出")
	private Time saturdayLeaveTime;
	
	@ApiModelProperty("周日下午返校(用作学生考勤)-进")
	private Time sundayEntryTime;
	
}
