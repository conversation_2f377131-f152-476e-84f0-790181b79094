package com.xiaoshan.attendance.vo.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class StudentImportVO {
	
	@ApiModelProperty("学生ID")
	private Long id;
	
	@ApiModelProperty("IC卡号")
	private String icNo;
	
	@ApiModelProperty("学生学号")
	private String studentNo;
	
	@ApiModelProperty("班级")
	private String className;
	
	@ApiModelProperty("学生姓名")
	private String studentName;

	@ApiModelProperty("是否住校")
    private String livingSchoolState;
	
	@ApiModelProperty("寝室号")
	private String dormitoryNo;
	
	@ApiModelProperty("床位号")
	private String bedNo;
	
	@ApiModelProperty("通校生是否申请晚自修")
    private String eveningStudyState;
	
	@ApiModelProperty("周六是否在校")
    private String saturdayState;
	
}
