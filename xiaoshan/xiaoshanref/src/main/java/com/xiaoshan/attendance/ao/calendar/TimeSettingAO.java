package com.xiaoshan.attendance.ao.calendar;

import java.sql.Time;

import com.xiaoshan.attendance.enums.RuleType;
import com.xiaoshan.basic.enums.GradeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TimeSettingAO {

    @ApiModelProperty("年级")
    @NotNull
    @Enum
    private GradeEnum grade;

    @ApiModelProperty("规则类型")
    @NotNull
    @Enum
    private RuleType type;

    @ApiModelProperty("规则时间")
    @NotNull
    @TimeFormat(format = TimeUtils.HHMMSS_F)
    private Time ruleTime;

}
