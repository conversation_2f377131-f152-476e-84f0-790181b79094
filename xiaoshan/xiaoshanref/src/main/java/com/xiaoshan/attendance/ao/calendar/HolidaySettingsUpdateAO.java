package com.xiaoshan.attendance.ao.calendar;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class HolidaySettingsUpdateAO extends HolidayIdAO {

    @ApiModelProperty("配置数据")
    @NotNull
    @Format(type = FormatType.JSONArray)
    private List<TodayUpdateBatchAO> items;

}
