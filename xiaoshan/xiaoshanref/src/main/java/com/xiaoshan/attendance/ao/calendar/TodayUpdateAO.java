package com.xiaoshan.attendance.ao.calendar;

import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TodayUpdateAO {

    @ApiModelProperty("日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date day;

    @ApiModelProperty("是否考勤")
    @NotNull
    @BooleanValid
    private Boolean attendance;

    @ApiModelProperty("是否上课")
    @NotNull
    @BooleanValid
    private Boolean attendclass;

}
