package com.xiaoshan.attendance.ao.report;


import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;

@Getter@Setter@ToString
public class GradeDateParam extends GradeAO {
	
	@ApiModelProperty("统计日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private Date validDate;
	
	@ApiModelProperty("1:进校、2:早读考勤")
	@IntegerValid(min=1,max=2)
	private Integer statusType=1;
	
}
