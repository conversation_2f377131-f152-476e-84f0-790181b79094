package com.xiaoshan.attendance.vo.calendar;

import java.sql.Time;

import com.xiaoshan.basic.enums.GradeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class TimeSettingVO {

	@ApiModelProperty("年级")
	private GradeEnum grade;
	
	@ApiModelProperty("早上进校(用作学生考勤)")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time morningEntryTime;
	
	@ApiModelProperty("早读时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time morningReadingTime;
	
	@ApiModelProperty("日常傍晚离校(用作学生出校权限限制)")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time nightLeaveTime;
	
	@ApiModelProperty("日常晚自修离校(用作学生出校权限限制)")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time lateSelfStudyLeaveTime;
	
	@ApiModelProperty("周五下午离校(用作学生出校权限限制)")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time fridayLeaveTime;
	
	@ApiModelProperty("周六下午离校/小礼拜周日下午离校(用作学生出校权限限制)")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time saturdayLeaveTime;
	
	@ApiModelProperty("周日下午返校(用作学生考勤)")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time sundayEntryTime;

}
