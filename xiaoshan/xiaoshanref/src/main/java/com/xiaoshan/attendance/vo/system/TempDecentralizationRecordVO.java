package com.xiaoshan.attendance.vo.system;

import java.util.Date;

import com.xiaoshan.attendance.vo.report.StudentBasicVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TempDecentralizationRecordVO extends StudentBasicVO {

    @ApiModelProperty("临时放权记录ID")
    private Long tempDecentralizationRecordId;

    @ApiModelProperty("当时是否在校")
    private Boolean inSchool = false;

    @ApiModelProperty("开始时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startDate;

    @ApiModelProperty("结束时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endDate;

}
