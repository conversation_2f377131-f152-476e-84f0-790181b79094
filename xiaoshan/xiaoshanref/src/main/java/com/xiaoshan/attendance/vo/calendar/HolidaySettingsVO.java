package com.xiaoshan.attendance.vo.calendar;

import java.sql.Time;
import java.util.List;

import com.xiaoshan.basic.enums.GradeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class HolidaySettingsVO {
	
	@ApiModelProperty("年级")
	private GradeEnum grade;

	@ApiModelProperty("放假前一天离校时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time leaveSchoolTime;

	@ApiModelProperty("节假日数据配置")
	private List<TodayVO> settingsData;
	
	@ApiModelProperty("放假最一天住校生返校时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time entrySchoolTime;
	
}
