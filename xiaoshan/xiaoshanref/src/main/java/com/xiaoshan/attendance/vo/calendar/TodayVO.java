package com.xiaoshan.attendance.vo.calendar;

import java.sql.Date;

import com.xiaoshan.attendance.enums.TodayType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class TodayVO {
	
	@ApiModelProperty("日期")
	private Date day;
	
	@ApiModelProperty("日期类型")
	private TodayType type;
	
	@ApiModelProperty("是否考勤")
	private Boolean attendance;
	
	@ApiModelProperty("是否上课")
	private Boolean attendclass;
	
}
