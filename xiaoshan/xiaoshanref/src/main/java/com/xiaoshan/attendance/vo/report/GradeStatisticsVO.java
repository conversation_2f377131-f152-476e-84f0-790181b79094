package com.xiaoshan.attendance.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class GradeStatisticsVO {

	@ApiModelProperty("年级名称")
	private String gradeName;
	
	@ApiModelProperty("年段")
	private Integer sectionId;
	
	@ApiModelProperty("入学年份")
	private Integer enrollmentYear;
	
	@ApiModelProperty("通校人数")
	private Integer atHomeNum=0;
	
	@ApiModelProperty("通校离校人数")
	private Integer atHomeLeavingNum=0;
	
	@ApiModelProperty("通校不考勤人数")
	private Integer atHomeAttAnoNum=0;
	
	@ApiModelProperty("通校未考勤人数")
	private Integer atHomeAttNoNum=0;
	
	@ApiModelProperty("通校正常考勤人数")
	private Integer atHomeAttNormalNum=0;
	
	@ApiModelProperty("通校迟到考勤人数")
	private Integer atHomeAttLateNum=0;
	
	@ApiModelProperty("通校请假人数")
	private Integer atHomeAttLeaveNum=0;
	
	@ApiModelProperty("住校人数")
	private Integer atSchoolNum=0;
	
	@ApiModelProperty("住校离校人数")
	private Integer atSchoolLeavingNum=0;
	
	@ApiModelProperty("住校不考勤人数")
	private Integer atSchoolAttAnoNum=0;
	
	@ApiModelProperty("住校未考勤人数")
	private Integer atSchoolAttNoNum=0;
	
	@ApiModelProperty("住校正常考勤人数")
	private Integer atSchoolAttNormalNum=0;
	
	@ApiModelProperty("住校迟到考勤人数")
	private Integer atSchoolAttLateNum=0;
	
	@ApiModelProperty("住校请假人数")
	private Integer atSchoolAttLeaveNum=0;
	
}
