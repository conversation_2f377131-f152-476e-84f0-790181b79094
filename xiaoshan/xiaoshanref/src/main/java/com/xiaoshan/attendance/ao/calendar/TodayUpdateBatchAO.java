package com.xiaoshan.attendance.ao.calendar;

import java.sql.Time;
import java.util.List;

import com.xiaoshan.basic.enums.GradeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TodayUpdateBatchAO {

    @ApiModelProperty("年级")
    @NotNull
    @Enum
    private GradeEnum grade;

    @ApiModelProperty("放假前一天离校时间")
    @NotNull
    @TimeFormat(format = TimeUtils.HHMMSS_F)
    private Time leaveSchoolTime;

    @ApiModelProperty("节假日配置数据")
    @NotNull
    @Format(type = FormatType.JSONArray)
    private List<TodayUpdateAO> settingsData;

    @ApiModelProperty("放假最后一天住校生返校时间")
    @NotNull
    @TimeFormat(format = TimeUtils.HHMMSS_F)
    private Time entrySchoolTime;

}
