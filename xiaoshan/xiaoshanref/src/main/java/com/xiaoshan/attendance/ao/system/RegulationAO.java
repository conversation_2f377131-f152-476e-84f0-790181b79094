package com.xiaoshan.attendance.ao.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class RegulationAO {

	@ApiModelProperty("学生id")
	@LongValid
	private Long id;

	@ApiModelProperty("床位号")
	private Integer bedNo;

	@ApiModelProperty("寝室号")
	private Integer dormitoryNo;

	@ApiModelProperty("是否申请晚自修")
	private Boolean eveningStudyState;

	@ApiModelProperty("是否住校")
	private Boolean livingSchoolState;

	@ApiModelProperty("周六是否在校")
	private Boolean saturdayState;

	@ApiModelProperty("小礼拜周日是否在校")
	private Boolean sundayState;

}
