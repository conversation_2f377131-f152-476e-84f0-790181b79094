package com.xiaoshan.attendance.vo.report;

import java.sql.Time;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter
@ToString
public class StudentLeaveVO {

	@ApiModelProperty("请假日期")
	private java.sql.Date day;
	
	@ApiModelProperty("开始时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time startTime;

	@ApiModelProperty("结束时间")
	@TimeFormat(format = TimeUtils.HHMMSS_F)
	private Time endTime;
	
	@ApiModelProperty("备注")
	private String remark;

}
