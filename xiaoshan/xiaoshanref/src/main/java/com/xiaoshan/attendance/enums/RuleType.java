package com.xiaoshan.attendance.enums;

import io.swagger.annotations.ApiModelProperty;

public enum RuleType {

	/**
	 *早上进校(用作学生考勤)
	 */
	@ApiModelProperty("早上进校(用作学生考勤)")
	morningEntryTime,

	/**
	 *早读时间
	 */
	@ApiModelProperty("早读时间")
	morningReadingTime,

	/**
	 *日常傍晚离校(用作学生出校权限限制)
	 */
	@ApiModelProperty("日常傍晚离校(用作学生出校权限限制)")
	nightLeaveTime,

	/**
	 *日常晚自修离校(用作学生出校权限限制)
	 */
	@ApiModelProperty("日常晚自修离校(用作学生出校权限限制)")
	lateSelfStudyLeaveTime,

	/**
	 *周五下午离校(用作学生出校权限限制)
	 */
	@ApiModelProperty("周五下午离校(用作学生出校权限限制)")
	fridayLeaveTime,

	/**
	 *周六下午离校/小礼拜周日下午离校(用作学生出校权限限制)
	 */
	@ApiModelProperty("周六下午离校/小礼拜周日下午离校(用作学生出校权限限制)")
	saturdayLeaveTime,

	/**
	 *周日下午返校(用作学生考勤)
	 */
	@ApiModelProperty("周日下午返校(用作学生考勤)")
	sundayEntryTime;
	
}
