package com.xiaoshan.attendance.ao.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.number.IntegerValid;

@Getter@Setter@ToString
public class ClassesDateAO extends GradeDateParam {
	
	@ApiModelProperty("班级ID")
	@IntegerValid
	private Integer classId;
	
	@ApiModelProperty("班级ID列表，逗号分隔")
	@NotEmpty
	private String classIdStr;
	
}
