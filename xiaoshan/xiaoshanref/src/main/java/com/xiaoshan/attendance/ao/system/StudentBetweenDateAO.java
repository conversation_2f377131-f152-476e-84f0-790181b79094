package com.xiaoshan.attendance.ao.system;

import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class StudentBetweenDateAO {
	
	@ApiModelProperty("学生ID")
	@NotNull
	@LongValid
	private Long stuId;
	
	@ApiModelProperty("开始日期")
	@NotNull
	@TimeFormat(format=TimeUtils.YYYYMMDD_F)
	private Date startDate;

	@ApiModelProperty("结束日期")
	@NotNull
	@TimeFormat(format=TimeUtils.YYYYMMDD_F)
	private Date endDate;
	
}
