package com.xiaoshan.attendance.ao.report;

import java.sql.Date;

import com.topnetwork.ao.PageAO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;

@Getter@Setter@ToString
public class ClassesDatePageAO extends PageAO {
	
	@ApiModelProperty("学段id")
	@IntegerValid
	private Integer sectionId;
	
	@ApiModelProperty("入学年份")
	@IntegerValid
	private Integer enrollmentYear;
	
	@ApiModelProperty("班级ID")
	@IntegerValid
	private Integer classId;
	
	@ApiModelProperty("班级ID列表，逗号分隔")
	@NotEmpty
	private String classIdStr;
	
	@ApiModelProperty("统计日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private Date validDate;
	
	@ApiModelProperty("1:进校、2:早读考勤")
	@IntegerValid(min=1,max=2)
	private Integer statusType=1;
	
}
