package com.xiaoshan.attendance.vo.report;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class WrapperStudentStatisicsVO {

	@ApiModelProperty("未考勤")
	private List<StudentDetailVO> noAtts;

	@ApiModelProperty("正常")
	private List<StudentDetailVO> normalAtts;

	@ApiModelProperty("迟到")
	private List<StudentDetailVO> lateAtts;

	@ApiModelProperty("请假")
	private List<StudentLeaveVO> leavesAtts;

}
