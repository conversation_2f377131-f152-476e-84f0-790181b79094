package com.xiaoshan.attendance.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class StudentBasicVO {

	@ApiModelProperty("年级名称")
	private String gradeName;
	
	@ApiModelProperty("班级名称")
	private String classesName;
	
	@ApiModelProperty("学生ID")
	private Long stuId;
	
	@ApiModelProperty("IC卡号")
	private String icNo;
	
	@ApiModelProperty("学号")
	private String studentNo;
	
	@ApiModelProperty("学生姓名")
	private String studentName;
	
	@ApiModelProperty("是否住校")
    private Boolean livingSchoolState;
	
}
