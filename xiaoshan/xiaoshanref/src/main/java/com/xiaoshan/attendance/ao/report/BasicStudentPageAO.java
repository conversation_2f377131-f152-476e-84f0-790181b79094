package com.xiaoshan.attendance.ao.report;

import java.util.List;

import com.topnetwork.ao.PageAO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class BasicStudentPageAO extends PageAO {

	@ApiModelProperty("学段id")
	@IntegerValid
	private Integer sectionId;
	
	@ApiModelProperty("入学年份")
	@IntegerValid
	private Integer enrollmentYear;
	
	@ApiModelProperty("班级ID")
	private Integer classId;
	
	@ApiModelProperty("班级ID列表，逗号分隔")
	@NotEmpty
	private String classIdStr;
	
	@ApiModelProperty("学生ID")
	@LongValid
	private Long stuId;
	
	@ApiModelProperty("学生名称")
	private String studentName;
	
	@ApiModelProperty("是否住校")
	@BooleanValid
	private Boolean livingInSchool;
	
	@ApiModelProperty("是否申请晚自修")
	@BooleanValid
	private Boolean eveningStudyState;
	
	@ApiModelProperty("周六是否在校")
	@BooleanValid
	private Boolean saturdayState;
	
	@ApiModelProperty("小礼拜周日是否在校")
	@BooleanValid
	private Boolean sundayState;

	@ApiModelProperty("学生ID")
	@Format(type=FormatType.JSONArrayLong)
	private List<Long> stuIds;

	@ApiModelProperty("学生人员ID")
	@Format(type=FormatType.JSONArrayLong)
	private List<Long> personIds;

	@ApiModelProperty("scope")
	private String scope="3";
	
}
