package com.xiaoshan.attendance.vo.system;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class TempDecentralizationVO {
	
	@ApiModelProperty("临时放权ID")
	private Long tempDecentralizationId;

	@ApiModelProperty("事件名称")
	private String name;

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startDate;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endDate;

	@ApiModelProperty("放行学生数")
	private Integer studentNum;
	
}
