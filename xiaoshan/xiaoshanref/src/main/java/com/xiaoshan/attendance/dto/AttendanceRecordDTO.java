package com.xiaoshan.attendance.dto;

import com.xiaoshan.attendance.enums.AttendanceType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AttendanceRecordDTO {

	@ApiModelProperty("考勤类型")
	private AttendanceType type;

	@ApiModelProperty("是否需要进校考勤")
	private Boolean attendance;

	@ApiModelProperty("是否需要早读考勤")
	private Boolean monringReadingAttendance;
	
	@ApiModelProperty("是否返校日")
	private Boolean returnToSchool;

	@ApiModelProperty("考勤开始时间")
	private java.sql.Time entryTime;

	@ApiModelProperty("考勤结束时间")
	private java.sql.Time entryEndTime;

	@ApiModelProperty("早读开始时间")
	private java.sql.Time monringReadingTime;

	@ApiModelProperty("早读结束时间")
	private java.sql.Time monringReadingEndTime;

}
