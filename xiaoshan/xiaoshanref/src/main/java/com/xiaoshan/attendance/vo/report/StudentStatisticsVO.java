package com.xiaoshan.attendance.vo.report;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class StudentStatisticsVO extends StudentBasicVO {

	@ApiModelProperty("不考勤次数")
	private Integer anoNum=0;
	
	@ApiModelProperty("未考勤次数")
	private Integer noNum=0;
	
	@ApiModelProperty("正常考勤次数")
	private Integer normalNum=0;
	
	@ApiModelProperty("迟到考勤次数")
	private Integer lateNum=0;
	
	@ApiModelProperty("请假次数")
	private Integer leaveTotalNum=0;
	
	@ApiModelProperty("现在是否在校")
	private Boolean inSchool=false;
	
	@ApiModelProperty("最后一次进离校时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date lastAttDate;
	
	@ApiModelProperty("最后一次进离校设备")
	private String lastAttEquipment;
	
	@ApiModelProperty("最后一次早读时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date lastMorningReadingDate;
	
	@ApiModelProperty("最后一次早读设备")
	private String lastMorningReadingEquipment;
	
	@ApiModelProperty("最后一次归寝时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date lastBedAttDate;
	
	@ApiModelProperty("最后一次归寝设备")
	private String lastBedAttEquipment;
	
	@ApiModelProperty("应考勤次数(请假+正常+迟到+未考勤)")
	private Integer attendanceTotalCount;
	
	public Integer getAttendanceTotalCount() {
		return noNum+normalNum+lateNum+leaveTotalNum;
	}
	
}
