package com.xiaoshan.attendance.ao.system;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TempDecentralizationAO {


    @ApiModelProperty("事件名称")
    @NotNull
    @Length
    private String name;

    @ApiModelProperty("开始时间")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startDate;

    @ApiModelProperty("结束时间")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endDate;

    @ApiModelProperty("放行学生StuId列表")
    @NotNull
    @Format(type = FormatType.JSONArrayLong)
    private List<Long> stuIds;

}
