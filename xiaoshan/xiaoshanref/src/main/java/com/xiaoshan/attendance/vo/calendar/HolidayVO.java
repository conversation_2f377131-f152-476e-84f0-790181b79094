package com.xiaoshan.attendance.vo.calendar;

import java.sql.Date;

import com.xiaoshan.attendance.enums.TodayType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class HolidayVO {
	
	@ApiModelProperty("节假日ID")
	private String holidayId;
	
	@ApiModelProperty("学年学期")
	private String semesters;
	
	@ApiModelProperty("事件名称")
	private String name;
	
	@ApiModelProperty("开始日期")
	private Date startDate;

	@ApiModelProperty("结束日期")
	private Date endDate;

	@ApiModelProperty("假日日期")
	private String[] dates;

	@ApiModelProperty("调休日期")
	private String[] compensatoryDates;
	
	@ApiModelProperty("日期类型")
	private TodayType type;
	
	@ApiModelProperty("年级应用范围")
	private String[]  geners;
	
	@ApiModelProperty("校历ID")
	private Long calendarId;
	
}
