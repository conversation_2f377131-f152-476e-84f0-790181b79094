package com.xiaoshan.attendance.vo.report;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class StudentChildInOutRecordVO {

	@ApiModelProperty("是否进校")
	private Boolean entrySchool;
	
	@ApiModelProperty("记录时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date createdDate;

	@ApiModelProperty("进出时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date happenTime;
	
	@ApiModelProperty("识别图片")
	private String uploadKey;
	
	@ApiModelProperty("识别设备")
	private String equipment;

	@ApiModelProperty("是否请假")
	private Boolean leave;
	
}
