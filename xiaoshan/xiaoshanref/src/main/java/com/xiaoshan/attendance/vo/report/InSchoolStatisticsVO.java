package com.xiaoshan.attendance.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class InSchoolStatisticsVO {

	@ApiModelProperty("当前在校人数")
	private Integer inSchoolTotal=0;
	
	@ApiModelProperty("当前离校人数")
	private Integer leavingSchoolTotal=0;
	
	@ApiModelProperty("当前通校生在校人数")
	private Integer atHomeInSchoolTotal=0;
	
	@ApiModelProperty("当前通校生离校人数")
	private Integer atHomeLeavingTotal=0;
	
	@ApiModelProperty("当前住校生在校人数")
	private Integer atSchoolInSchoolTotal=0;
	
	@ApiModelProperty("当前住校生离校人数")
	private Integer atSchoolLeavingTotal=0;
	
}
