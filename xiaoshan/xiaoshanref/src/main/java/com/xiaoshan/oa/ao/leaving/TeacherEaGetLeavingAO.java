package com.xiaoshan.oa.ao.leaving;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;

import java.util.Date;

@Getter
@Setter
@ToString
public class TeacherEaGetLeavingAO {

    @ApiModelProperty("教师Ids,用逗号分隔")
    private String teacherIds;

    @ApiModelProperty("教师ID")
    private Long teacherId;

    @ApiModelProperty(value = "查询时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    @NotNull
    private Date queryTime;

    @ApiModelProperty("数据类型：1请假/2公出，0两者")
    @NotNull
    @IntegerValid(min = 0, max = 2)
    private Integer dataType;
}
