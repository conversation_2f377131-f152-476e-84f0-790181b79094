package com.xiaoshan.oa.vo.calendar;

import com.xiaoshan.oa.enums.TaskType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimeConverterEditor;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class TaskVO {

	@ApiModelProperty("事历ID")
	private Long taskId;
	
	@ApiModelProperty("学年学期")
	private Long semesterId;
	
	@ApiModelProperty("学年学期名称")
	private String semesterName;
	
	@ApiModelProperty("事历名称")
	private String name;
	
	@ApiModelProperty("事历类型")
	private TaskType type;

	@ApiModelProperty("开始日期")
	private java.sql.Date startDate;

	@ApiModelProperty("结束日期")
	private java.sql.Date endDate;

	@ApiModelProperty("是否发布")
	private Boolean publish;
	
	@ApiModelProperty("周ID(类型为周)")
	private Long weekId;

	@ApiModelProperty("是否已读")
	private Boolean read;

	@ApiModelProperty("发布日期")
	@PropertyConverter(DateTimeConverterEditor.class)
	private java.util.Date releaseDate;
	
	@ApiModelProperty("总数")
	private Long total;
	
}
