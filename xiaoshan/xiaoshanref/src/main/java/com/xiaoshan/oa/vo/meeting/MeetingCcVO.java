package com.xiaoshan.oa.vo.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.persistence.annotation.Column;

@Getter@Setter@ToString
public class MeetingCcVO {

	private static final long serialVersionUID = 1L;
	
	public MeetingCcVO(){}

	@ApiModelProperty("id")
	private Long id;

	@ApiModelProperty("教师姓名")
	@Column("teacher_name")
	private String teacherName;

	@ApiModelProperty("教师部门")
	private String department;

}
