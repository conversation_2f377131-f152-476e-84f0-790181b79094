package com.xiaoshan.oa.vo.salary;

import com.xiaoshan.oa.enums.NotifyType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@ApiModel("薪水")
@Getter@Setter@ToString
public class NotifyVO {


	public NotifyVO(){}

	@ApiModelProperty(value = "通知id")
	private Long id;

	@ApiModelProperty("通知标题")
	private String title;

	@ApiModelProperty("发布人")
	private String author;

	@ApiModelProperty("是否定时发送")
	private Boolean isTiming;

	@ApiModelProperty("定时发布时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date sendTime;

	@ApiModelProperty(value = "excel数据id")
	private Long excelId;

	@ApiModelProperty(value = "总人数")
	private Integer total;

	@ApiModelProperty(value = "已读人数")
	private Long readCount = 0L;

	@ApiModelProperty(value = "发布状态")
	private NotifyType type;

	@ApiModelProperty(value = "发布时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date publicTime;

	@ApiModelProperty(value = "消息ids", hidden = true)
	private String messageId;

	@ApiModelProperty(value = "发薪月份")
	private Integer payMonth;

	@ApiModelProperty("阅读状态,true-已读，false-未读")
	private Boolean read = true;
}
