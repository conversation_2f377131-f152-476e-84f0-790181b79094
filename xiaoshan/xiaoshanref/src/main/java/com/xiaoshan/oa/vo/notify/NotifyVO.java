package com.xiaoshan.oa.vo.notify;

import com.xiaoshan.oa.enums.NoticeType;
import com.xiaoshan.oa.enums.PersonalType;
import com.xiaoshan.oa.enums.PublishStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@ApiModel("通知")
@Getter@Setter@ToString
public class NotifyVO {

    @ApiModelProperty(value = "通知id")
    private Long notifyId;

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty("通知类型")
    private NoticeType type;

    @ApiModelProperty("通知内容")
    private String content;

    /**
     * 点击个性化类型的通知预览的时候需要做区分
     */
    @ApiModelProperty("个性化通知类型")
    private PersonalType personalType;

    @ApiModelProperty("发布人")
    private String author;

    @ApiModelProperty("是否定时发送")
    private Boolean isTiming;

    @ApiModelProperty("定时发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date sendTime;

    @ApiModelProperty(value = "总人数")
    private Integer total;

    @ApiModelProperty(value = "已读人数")
    private Integer readCount;

    @ApiModelProperty(value = "发布状态")
    private PublishStatus status;

    @ApiModelProperty(value = "发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty(value = "消息id",hidden = true)
    private String messageId;

    @ApiModelProperty(value = "班牌通知id",hidden = true)
    private String classNoticeId;
}
