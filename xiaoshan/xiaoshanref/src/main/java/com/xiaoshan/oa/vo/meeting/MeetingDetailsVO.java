package com.xiaoshan.oa.vo.meeting;

import com.xiaoshan.oa.ao.meeting.MeetingPlaceAO;
import com.xiaoshan.oa.enums.MeetReminder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;
import java.util.List;

@Getter@Setter@ToString
public class MeetingDetailsVO {

	public MeetingDetailsVO(){}

	@ApiModelProperty("会议id")
	private Long meetingId;

	@ApiModelProperty("会议名称")
	private String name;

	@ApiModelProperty("会议内容")
	private String content;

	@ApiModelProperty("发起时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date createdDate;

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startTime;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endTime;

	@ApiModelProperty("开会地点")
	private MeetingPlaceAO place;

	@ApiModelProperty("参会人员id")
	private List<Long> attendIds;

	@ApiModelProperty("参会人员姓名")
	private List<String> attendNames;

	@ApiModelProperty("图片链接数组")
	private List<String> picUrls;

	@ApiModelProperty("发起人")
	private String author;

	@ApiModelProperty("负责部门")
	private String department;

	@ApiModelProperty("会议提醒")
	private MeetReminder reminder;
}
