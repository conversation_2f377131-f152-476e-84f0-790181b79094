package com.xiaoshan.oa.vo.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("教师请假")
@Getter@Setter@ToString
public class OfficialBusinesVO {

	public OfficialBusinesVO(){}

	@ApiModelProperty(value = "教师请假id")
	private Long leavingId;

	@ApiModelProperty("审批编号")
	private String approvalNo;

	@ApiModelProperty("请教教师姓名")
	private String teacherName;

	@ApiModelProperty("请教教师所属部门")
	private String departmentName;

	@ApiModelProperty("申请时间")
	private String requestTime;

	@ApiModelProperty("请假时间")
	private String leavingTime;

	@ApiModelProperty(value = "请假时长")
	private String duration;

	@ApiModelProperty(value = "审批状态")
	private String approveStatus;
}
