package com.xiaoshan.oa.ao.leaving;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;

import java.sql.Date;

@Getter
@Setter
@ToString
public class AttendanceGetLeavingAO {
    @ApiModelProperty("教师Ids，请用逗号分隔")
    @NotNull
    @NotEmpty
    private String teacherIds;

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty("数据类型：0全部，1请假，2公出")
    @NotNull
    @IntegerValid(min = 0,max = 2)
    private Integer dataType;
}
