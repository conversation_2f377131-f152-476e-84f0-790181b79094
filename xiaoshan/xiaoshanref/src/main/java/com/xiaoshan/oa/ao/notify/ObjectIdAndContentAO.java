package com.xiaoshan.oa.ao.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class ObjectIdAndContentAO {

    @ApiModelProperty("接收对象id")
    @NotNull
    private Long objectId;

    @ApiModelProperty("个性化通知内容")
    @NotEmpty
    private String content;

}
