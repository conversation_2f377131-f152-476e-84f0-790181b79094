package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Column;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel("公共通知")
@Getter@Setter@ToString
public class NotifyCommonShowVO extends NotifyCommonVO {
    @ApiModelProperty(value = "通知id")
    private Long notifyId;

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty("发布人")
    private String author;

    @ApiModelProperty(value = "发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("是否定时发送")
    private Boolean isTiming;

    @ApiModelProperty("定时发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date sendTime;

    @ApiModelProperty("通知正文")
    private String content;

    @ApiModelProperty("教师集合")
    @Column("TeacherList")
    private List<ObjectInfoVO> teacherList = new ArrayList<>();

    @ApiModelProperty("学生集合")
    @Column("StudentList")
    private List<ObjectInfoVO> studentList = new ArrayList<>();

    @ApiModelProperty("家长集合")
    @Column("ParentsList")
    private List<NotifyParentVO> parentsList = new ArrayList<>();

    @ApiModelProperty("服务人员集合")
    @Column("ServerList")
    private List<ObjectInfoVO> serverList = new ArrayList<>();

    @ApiModelProperty("班级集合")
    @Column("ClassList")
    private List<ObjectInfoVO> classList = new ArrayList<>();

    @ApiModelProperty("附件集合")
    @Column("notifyAttachmentVOList")
    private List<NotifyAttachmentVO> notifyAttachmentVoList = new ArrayList<>();
}
