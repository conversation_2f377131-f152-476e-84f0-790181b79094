package com.xiaoshan.oa.ao.calendar;

import com.topnetwork.ao.IDListAO;
import com.xiaoshan.oa.enums.ApprovalType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class ApprovalAO extends IDListAO {

	@ApiModelProperty("审批状态")
	@NotNull
	@Enum
	private ApprovalType approval;

	@ApiModelProperty("审批内容")
	@Length
	private String reason;
	
}
