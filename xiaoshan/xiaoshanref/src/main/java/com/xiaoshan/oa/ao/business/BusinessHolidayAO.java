package com.xiaoshan.oa.ao.business;

import java.util.Date;

import com.xiaoshan.oa.enums.TimeScope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;

@ApiModel("教师公出节假日")
@Getter@Setter@ToString
public class BusinessHolidayAO {

	public BusinessHolidayAO(){}

	@ApiModelProperty(value = "开始时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date startTime;

	@ApiModelProperty(value = "结束时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date endTime;

	@ApiModelProperty(value = "开始时间范围")
	@Enum
	@NotNull
	private TimeScope startTimeScope;

	@ApiModelProperty(value = "结束时间范围")
	@Enum
	@NotNull
	private TimeScope endTimeScope;
}
