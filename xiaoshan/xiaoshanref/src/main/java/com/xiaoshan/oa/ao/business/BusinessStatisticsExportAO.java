package com.xiaoshan.oa.ao.business;

import java.sql.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class BusinessStatisticsExportAO {
    @ApiModelProperty(value = "开始时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty("部门ID")
    private Long departmentId;

    @ApiModelProperty("教师名称")
    private String teacherName;

    @ApiModelProperty("教师id集合")
    private List<Long> teacherList;

    @ApiModelProperty("menuId")
    private Long menuId;
}
