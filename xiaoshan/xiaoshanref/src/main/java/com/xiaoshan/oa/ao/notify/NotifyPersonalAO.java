package com.xiaoshan.oa.ao.notify;

import java.util.Date;

import com.xiaoshan.oa.enums.PersonalType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * 个性化通知AO
 */
@Getter@Setter@ToString
public class NotifyPersonalAO {

    @ApiModelProperty(value = "通知id")
    @LongValid
    @NotNull
    private Long id;

    @ApiModelProperty("通知标题")
    @NotEmpty
    private String title;

    @ApiModelProperty("个性化通知类型")
    @NotNull
    private PersonalType personalType;

    @ApiModelProperty(value = "发布时间", hidden = true)
    @TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("发布人")
    @NotEmpty
    private String author;

    @ApiModelProperty("是否定时发送")
    @NotNull
    private Boolean isTiming;

    @ApiModelProperty("定时发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date sendTime;

    @ApiModelProperty(value = "总人数", hidden = true)
    private Integer total;

}
