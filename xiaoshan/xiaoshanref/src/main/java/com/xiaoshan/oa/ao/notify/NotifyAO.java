package com.xiaoshan.oa.ao.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.Date;
import java.util.List;

@Getter@Setter@ToString
public class NotifyAO {

    @ApiModelProperty(value = "通知id")
    @LongValid
    @NotNull
    private Long id;

    @ApiModelProperty("通知标题")
    @NotEmpty
    private String title;

    @ApiModelProperty("通知内容")
    @NotEmpty
    private String content;

    @ApiModelProperty(value = "发布时间", hidden = true)
    @TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("发布人")
    @NotEmpty
    private String author;

    @ApiModelProperty("是否定时发送")
    @NotNull
    private Boolean isTiming;

    @ApiModelProperty("定时发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date sendTime;

    @ApiModelProperty(value = "总人数", hidden = true)
    private Integer total;

    @ApiModelProperty("附件集合")
    private List<NotifyAttachmentAO> notifyAttachmentAoList;

    @ApiModelProperty("发送给全部教师")
    private Boolean isSendAllTeacher;

    @ApiModelProperty("发送给全部家长")
    private Boolean isSendAllStudent;

    @ApiModelProperty("发送给全部班级")
    private Boolean isSendAllClass;

    @ApiModelProperty("发送给全部服务人员")
    private Boolean isSendAllServer;

    @ApiModelProperty("是否小程序发布")
    private Boolean isAppletPublish;
}
