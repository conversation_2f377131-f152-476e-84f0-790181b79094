package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NoticeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class NotifyReceiverRepeatTestAO {
    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long notifyId;

    @ApiModelProperty("用户id")
    @NotNull
    @LongValid
    private Long userId;

    @ApiModelProperty("通知类型")
    @NotNull
    private NoticeType type;
}
