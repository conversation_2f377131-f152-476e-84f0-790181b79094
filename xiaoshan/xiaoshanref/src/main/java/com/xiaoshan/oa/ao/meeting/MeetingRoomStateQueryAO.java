package com.xiaoshan.oa.ao.meeting;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.Date;

@Getter@Setter@ToString
public class MeetingRoomStateQueryAO extends PageAO {

	public MeetingRoomStateQueryAO(){}

	@ApiModelProperty("开始时间")
	@NotNull
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startDate;

	@ApiModelProperty("结束时间")
	@NotNull
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endDate;

	@ApiModelProperty("会议室地点搜索")
	@Length
	private String place;

	@ApiModelProperty("会议室roomId，用于判断会议室地点是否冲突")
	@LongValid
	private Long roomId;

	@ApiModelProperty("会议id")
	@LongValid
	private Long meetingId;



}
