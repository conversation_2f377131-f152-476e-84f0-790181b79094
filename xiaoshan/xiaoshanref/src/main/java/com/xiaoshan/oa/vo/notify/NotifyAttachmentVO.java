package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("附件")
@Getter@Setter@ToString
public class NotifyAttachmentVO {
    @ApiModelProperty("附件地址")
    private String fileUrl;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件大小（KB）")
    private Double fileSize;
}
