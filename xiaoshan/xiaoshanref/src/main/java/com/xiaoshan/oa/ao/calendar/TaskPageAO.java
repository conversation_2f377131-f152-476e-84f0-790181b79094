package com.xiaoshan.oa.ao.calendar;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.oa.enums.TaskType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskPageAO extends PageAO {

	@ApiModelProperty("事历ID")
	@LongValid
	private Long taskId;

	@ApiModelProperty("学年学期")
	@LongValid
	private String semesterId;
	
	@ApiModelProperty("事历名称")
	@Length
	private String name;
	
	@ApiModelProperty("事历类型")
	@Enum
	private TaskType type;

	@ApiModelProperty("是否发布")
	@BooleanValid
	private Boolean publish;

}
