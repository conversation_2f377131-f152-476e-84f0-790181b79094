package com.xiaoshan.oa.ao.calendar;

import com.xiaoshan.oa.enums.PublishType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskEventAO {

	@ApiModelProperty("任务事件ID")
	@LongValid
	private Long taskEventId;
	
	@ApiModelProperty("任务ID")
	@NotNull
	@LongValid
	private Long taskId;

	@ApiModelProperty("地点")
	@NotNull
	@Length
	private String address;

	@ApiModelProperty("事件")
	@NotNull
	@Length
	private String event;

	@ApiModelProperty("相关部门")
	@Length
	private String departments;
	
	@ApiModelProperty("发布类型")
	@NotNull
	@Enum
	private PublishType publishType;
	
}
