package com.xiaoshan.oa.ao.calendar;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskEventSemesterAO extends TaskEventAO {
	
	@ApiModelProperty("周ID(学期行事历),如果为前后无周次ID则用0表示")
	@NotNull
	@LongValid
	private Long weekId;
	
	@ApiModelProperty("事历-开始日期，WeekId有值周开始日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date sdate;

	@ApiModelProperty("事历-结束日期，WeekId有值周结束日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date edate;

	@ApiModelProperty("开始日期")
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date startDate;
	
}
