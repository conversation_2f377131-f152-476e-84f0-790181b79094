package com.xiaoshan.oa.vo.salary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.thirdparty.json.JsonArray;

import java.util.Date;

@Getter@Setter@ToString
public class ExcelOneAnalysisVO {

    @ApiModelProperty("表头数据")
    private JsonArray heads;

    @ApiModelProperty("表内容数据")
    private JsonArray data;

    @ApiModelProperty("消息id")
    private String messageId;

    @ApiModelProperty("发布人姓名")
    private String author;

    @ApiModelProperty("发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publicTime;


}
