package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel("模拟获取节点")
@Getter@Setter@ToString
public class LeavingFlowNodesVO {

	public LeavingFlowNodesVO(){}

	@ApiModelProperty(value = "版本")
	private Integer version;

	@ApiModelProperty("模拟获取节点")
	private List<FlowNodesVO> nodes;
}
