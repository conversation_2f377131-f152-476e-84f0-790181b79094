package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel("接收对象树（小程序）")
@Getter@Setter@ToString
public class ReceiverOfAppletVO {
    @ApiModelProperty("教师集合")
    List<TeacherDepartmentVO> teachers;
    @ApiModelProperty("学生集合")
    List<StudentGradeOrderVO> students;
    @ApiModelProperty("服务人员集合")
    List<TeacherDepartmentVO> servers;
    @ApiModelProperty("班级集合")
    List<ObjectInfoVO> classes;
    @ApiModelProperty("教师数")
    Integer teacherCount;
    @ApiModelProperty("学生数")
    Integer studentCount;
    @ApiModelProperty("服务人员数")
    Integer serverCount;
    @ApiModelProperty("班级数")
    Integer classCount;
    @ApiModelProperty("教师id集合")
    List<Long> teacherIdList;
    @ApiModelProperty("学生id集合")
    List<Long> studentIdList;
    @ApiModelProperty("服务人员id集合")
    List<Long> serverIdList;
    @ApiModelProperty("班级id集合")
    List<Long> classIdList;
}
