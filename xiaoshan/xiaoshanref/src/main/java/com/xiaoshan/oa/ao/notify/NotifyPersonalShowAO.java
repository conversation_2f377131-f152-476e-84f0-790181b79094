package com.xiaoshan.oa.ao.notify;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class NotifyPersonalShowAO extends PageAO {

    @ApiModelProperty("通知id")
    @LongValid
    @NotNull
    private Long notifyId;

    @ApiModelProperty("学段类型")
    private Long sectionType;

    @ApiModelProperty("入学年份")
    private Integer enrollmentYear;

    @ApiModelProperty("班级id")
    private Long classId;

    @ApiModelProperty("姓名")
    private String name;
}
