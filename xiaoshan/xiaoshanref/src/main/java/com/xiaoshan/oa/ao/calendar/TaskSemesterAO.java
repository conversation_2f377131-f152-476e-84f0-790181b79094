package com.xiaoshan.oa.ao.calendar;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskSemesterAO {
	
	@ApiModelProperty("学年学期")
	@NotNull
	@LongValid
	private Long semesterId;
	
	@ApiModelProperty("事历名称")
	@NotNull
	@Length
	private String name;

	@ApiModelProperty("开始日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date startDate;

	@ApiModelProperty("结束日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date endDate;

	@ApiModelProperty("是否发布")
	@NotNull
	@BooleanValid
	private Boolean publish;
	
}
