package com.xiaoshan.oa.ao.salary;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.oa.enums.NotifyType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class SalaryNotifyPageAO extends PageAO {

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date endDate;

    @ApiModelProperty("发布状态")
    @Enum
    private NotifyType type;
}
