package com.xiaoshan.oa.vo.meeting;

import com.xiaoshan.oa.enums.MeetingState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

/**
 * 教师个人端会议记录VO
 */
@Getter@Setter@ToString
public class MyMeetingRecordsVO {

	public MyMeetingRecordsVO(){}

	@ApiModelProperty("会议id")
	private Long meetingId;

	@ApiModelProperty("会议名称")
	private String name;

	@ApiModelProperty("发起时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date createdDate;

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startTime;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endTime;

	@ApiModelProperty("开会地点")
	private String place;

	@ApiModelProperty("发起人")
	private String author;

	@ApiModelProperty("负责部门")
	private String department;

	@ApiModelProperty("会议状态")
	private MeetingState state;

	@ApiModelProperty("确认状态,0 待确认 1 通过 2 拒绝")
	private Integer resultState;

	@ApiModelProperty("流程id")
	private Long flowId;

}
