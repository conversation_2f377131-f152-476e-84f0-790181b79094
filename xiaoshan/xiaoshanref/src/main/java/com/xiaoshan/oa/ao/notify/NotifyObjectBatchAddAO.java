package com.xiaoshan.oa.ao.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;

import java.util.List;

@Getter@Setter@ToString
public class NotifyObjectBatchAddAO {
    @ApiModelProperty("通知id")
    @NotNull
    private Long notifyId;

    @ApiModelProperty("教师id（批量添加教师）")
    private List<Long> teacherIdList;

    @ApiModelProperty("学生id（批量添加学生）")
    private List<Long> studentIdList;

    @ApiModelProperty("学生id（批量添加班级）")
    private List<Long> classIdList;

    @ApiModelProperty("服务人员id（批量添加学生）")
    private List<Long> serverIdList;

    @ApiModelProperty("1：接收对象列表场景下批量添加")
    private Integer type;

    @ApiModelProperty(value = "家长Id（批量添加学生）",hidden = true)
    private List<Long> parentIdList;
}
