package com.xiaoshan.oa.vo.meeting;

import com.xiaoshan.oa.ao.meeting.MeetingPlaceAO;
import com.xiaoshan.oa.enums.MeetReminder;
import com.xiaoshan.oa.enums.MeetingState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;
import java.util.List;

/**
 * 我的会议记录详情VO
 */
@Data
public class MyMeetingDetailVO {

    @ApiModelProperty("会议id")
    private Long meetingId;

    @ApiModelProperty("会议名称")
    private String name;

    @ApiModelProperty("发起人")
    private String author;

    @ApiModelProperty("发起时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("开始时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startTime;

    @ApiModelProperty("结束时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endTime;

    @ApiModelProperty("开会地点")
    private MeetingPlaceAO place;

    @ApiModelProperty("负责部门")
    private String department;

    @ApiModelProperty("会议内容")
    private String content;

    @ApiModelProperty("会议图片url列表")
    private List<String> picUrls;

    @ApiModelProperty("会议状态")
    private MeetingState meetingState;

    @ApiModelProperty("时间差")
    private String datePoor;

    @ApiModelProperty("流程id，用于查看审批状态")
    private Long flowId;

    @ApiModelProperty("会议提醒")
    private MeetReminder reminder;

}
