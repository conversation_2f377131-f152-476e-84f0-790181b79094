package com.xiaoshan.oa.vo.meeting;

import com.xiaoshan.oa.enums.MeetingState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MeetingRoomDetailVO {

    @ApiModelProperty
    private java.sql.Date date;

    @ApiModelProperty("会议详情")
    List<MeetingInfo> meetingList;


    @Data
    public static class MeetingInfo {

        @ApiModelProperty("会议名称")
        private String name;

        @ApiModelProperty("发起人")
        private String author;

        @ApiModelProperty("部门")
        private String department;

        @ApiModelProperty("开始时间")
        @PropertyConverter(DateTimefConverterEditor.class)
        private Date startTime;

        @ApiModelProperty("结束时间")
        @PropertyConverter(DateTimefConverterEditor.class)
        private Date endTime;

        @ApiModelProperty("会议状态")
        private MeetingState meetingState;
    }
}
