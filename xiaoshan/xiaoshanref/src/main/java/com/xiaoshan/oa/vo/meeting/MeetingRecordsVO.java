package com.xiaoshan.oa.vo.meeting;

import com.xiaoshan.oa.enums.MeetingState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@Getter@Setter@ToString
public class MeetingRecordsVO {


	public MeetingRecordsVO(){}

	@ApiModelProperty("会议id")
	private Long meetingId;

	@ApiModelProperty("会议名称")
	private String name;

	@ApiModelProperty("发起时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date createdDate;

	@ApiModelProperty("总人数")
	private Integer total;

	@ApiModelProperty("已确认人数")
	private Long acceptCount;

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startTime;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endTime;

	@ApiModelProperty("开会地点")
	private String place;

	@ApiModelProperty("发起人")
	private String author;

	@ApiModelProperty("负责部门")
	private String department;

	@ApiModelProperty("会议状态")
	private MeetingState state;

	@ApiModelProperty("流程id")
	private Long flowId;

}
