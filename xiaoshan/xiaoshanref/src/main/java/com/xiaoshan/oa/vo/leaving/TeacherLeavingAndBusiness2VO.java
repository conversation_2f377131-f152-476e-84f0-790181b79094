package com.xiaoshan.oa.vo.leaving;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

@ApiModel
@Getter
@Setter
@ToString
public class TeacherLeavingAndBusiness2VO extends TeacherLeavingAndBusinessVO {
    @ApiModelProperty(value = "实际请假时长")
    private BigDecimal realDuration;

    @ApiModelProperty("实际请假开始时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date realStartTime;

    @ApiModelProperty("实际开始时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String realStartTimeScope;

    @ApiModelProperty("实际请假结束时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date realEndTime;

    @ApiModelProperty("实际结束时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String realEndTimeScope;
}
