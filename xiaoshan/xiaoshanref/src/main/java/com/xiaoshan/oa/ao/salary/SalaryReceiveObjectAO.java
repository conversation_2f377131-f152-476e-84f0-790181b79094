package com.xiaoshan.oa.ao.salary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Data
public class SalaryReceiveObjectAO {

    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long notifyId;

    @ApiModelProperty("教师姓名")
    @NotNull
    @Length
    private String name;
}
