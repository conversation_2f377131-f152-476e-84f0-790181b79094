package com.xiaoshan.oa.ao.notify;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.oa.enums.NotifyReceiveObjectType;
import com.xiaoshan.oa.enums.ReadType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class NotifyReceiveObjectPageAO extends PageAO {
    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long notifyId;

    @ApiModelProperty("接收人类型（默认是教师）")
    @Enum
    private NotifyReceiveObjectType objectType;

    @ApiModelProperty("阅读状态")
    @Enum
    private ReadType readType;

    @ApiModelProperty("接受对象名字")
    private String name;

    @ApiModelProperty("是否查询所有")
    @BooleanValid
    private Boolean isAll;

}
