package com.xiaoshan.oa.ao.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

/**
 * 参会人查询AO
 */
@Setter@Getter@ToString
public class AttendsAddAO {

    @ApiModelProperty("会议id")
    @NotNull
    @LongValid
    private Long meetingId;


    @ApiModelProperty("参会人列表")
    @NotNull
    @NotEmpty
    private List<Long> attends;



}
