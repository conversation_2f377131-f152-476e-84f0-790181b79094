package com.xiaoshan.oa.vo.notify;

import com.xiaoshan.oa.enums.NotifyReceiveObjectType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.persistence.annotation.Column;

/**
 * 四种类型接收对象公共类
 */

@ApiModel("接收对象信息")
@Getter@Setter@ToString
public class ObjectInfoVO{

    @ApiModelProperty("userId")
    @Column("user_id")
    private Long userId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("部门")
    @Column("department_name")
    private String departmentName = "";

    @ApiModelProperty("学段")
    @Column("section_name")
    private String sectionName;

    @ApiModelProperty("年级")
    @Column("grade_name")
    private String gradeName;

    @ApiModelProperty("班级")
    @Column("class_name")
    private String className;

    @ApiModelProperty("学号")
    @Column("student_no")
    private String studentNo;

    @ApiModelProperty("IC卡号")
    @Column("ic_card_no")
    private String icCardNo;

    @ApiModelProperty("通知内容")
    private String content;

    @ApiModelProperty("是否为错误数据")
    private Boolean isError;

    @ApiModelProperty("学段类型")
    @Column("section_type")
    private Long sectionType;

    @ApiModelProperty("入学年份")
    @Column("enrollment_year")
    private Integer enrollmentYear;

    @ApiModelProperty("班级id")
    @Column("class_id")
    private Long classId;

    @ApiModelProperty("接收对象类型")
    @Column("object_type")
    private NotifyReceiveObjectType objectType;

    @ApiModelProperty("接收对象id")
    @Column("object_id")
    private Long objectId;

    @ApiModelProperty("部门ids")
    private String departmentIds;

    @ApiModelProperty("年级顺序")
    private Integer gradeOrder;
}
