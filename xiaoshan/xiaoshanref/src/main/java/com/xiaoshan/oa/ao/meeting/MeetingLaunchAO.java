package com.xiaoshan.oa.ao.meeting;

import com.xiaoshan.oa.enums.MeetReminder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.*;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.Date;
import java.util.List;

@Getter@Setter@ToString
public class MeetingLaunchAO {

	public MeetingLaunchAO(){}

	@ApiModelProperty("会议id")
	@LongValid
	private Long meetingId;

	@ApiModelProperty("会议名称")
	@Length
	@NotEmpty
	private String name;

	@ApiModelProperty("会议内容")
	@NotEmpty
	private String content;

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date startTime;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date endTime;

	@ApiModelProperty("开会地点")
	@NotNull
	private MeetingPlaceAO place;

	@ApiModelProperty("参会人员数组")
	@NotNull
	@Format(type = Format.FormatType.JSONArray)
	private List<Long> attendIds;

	@ApiModelProperty("图片链接数组")
	@Format(type = Format.FormatType.JSONArray)
	private List<String> picUrls;

	@ApiModelProperty("负责部门")
	@NotNull
	@Length
	private String department;

	@ApiModelProperty("会议提醒")
	@NotNull
	@Enum
	private MeetReminder reminder;
}
