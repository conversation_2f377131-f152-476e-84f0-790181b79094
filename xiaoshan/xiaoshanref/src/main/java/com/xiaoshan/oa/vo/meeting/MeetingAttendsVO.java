package com.xiaoshan.oa.vo.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MeetingAttendsVO {

    @ApiModelProperty("总人数")
    private Integer totalNum;

    @ApiModelProperty("分页记录总条数")
    private Integer totalCount;

    @ApiModelProperty("确认参会人数")
    private Long confirmNum;

    @ApiModelProperty("拒绝参会人数")
    private Long refuseNum;

    @ApiModelProperty("待确认人数")
    private Long waitConfirmNum;

    @ApiModelProperty("参会人列表")
    private List<AttendsVO> attends;
}
