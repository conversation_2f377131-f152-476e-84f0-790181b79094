package com.xiaoshan.oa.vo.notify;

import com.xiaoshan.oa.enums.NotifyReceiveObjectType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("接收对象")
@Getter@Setter@ToString
public class ObjectReadStatusVO {

    @ApiModelProperty("接收对象userId")
    private Long userId;

    @ApiModelProperty("接收对象Id")
    private Long objectId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("阅读状态（true：已读）")
    private Boolean isRead = false;

    @ApiModelProperty("通知内容")
    private String content;

    @ApiModelProperty("接收对象类型")
    private NotifyReceiveObjectType objectType;

    @ApiModelProperty("学段")
    private String sectionName;

    @ApiModelProperty("年级")
    private String gradeName;

    @ApiModelProperty("班级")
    private String className;

    @ApiModelProperty("学号")
    private String studentNo;

    @ApiModelProperty("IC卡号")
    private String icCardNo;
}
