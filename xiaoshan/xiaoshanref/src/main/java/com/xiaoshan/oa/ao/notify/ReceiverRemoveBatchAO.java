package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NoticeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

@Getter@Setter@ToString
public class ReceiverRemoveBatchAO {
    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long notifyId;

    @ApiModelProperty("通知类型")
    @NotNull
    @Enum
    private NoticeType type;

    @ApiModelProperty("通知对象id集合")
    @NotNull
    private List<Long> userIdList;
}
