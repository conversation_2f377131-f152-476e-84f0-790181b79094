package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NotifyReceiveObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class ObjectInfoAO {
    @ApiModelProperty("userId")
    @LongValid
    private Long userId;

    @ApiModelProperty("接收对象类型")
    @NotNull
    @Enum
    private NotifyReceiveObjectType objectType;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("学段")
    private String sectionName;

    @ApiModelProperty("年级")
    private String gradeName;

    @ApiModelProperty("班级")
    private String className;

    @ApiModelProperty("学号")
    private String studentNo;

    @ApiModelProperty("IC卡号")
    private String icCardNo;

    @ApiModelProperty("通知内容")
    @NotEmpty
    private String content;

    @ApiModelProperty("学段类型")
    private Long sectionType;

    @ApiModelProperty("入学年份")
    private Integer enrollmentYear;

    @ApiModelProperty("班级id")
    private Long classId;
}
