package com.xiaoshan.oa.ao.salary;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.oa.enums.ReadType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class SalaryReceiveObjectPageAO extends PageAO {

    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long id;

    @ApiModelProperty("阅读状态")
    @Enum
    private ReadType type;

    @ApiModelProperty("教师姓名")
    private String name;


}
