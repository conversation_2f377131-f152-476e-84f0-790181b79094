package com.xiaoshan.oa.ao.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel("调代课查询请假公出条件")
@Getter
@Setter
@ToString
public class AdjustmentQueryLeavingAO {

    @ApiModelProperty("教师id集合")
    List<Long> teacherIds;

    @ApiModelProperty("周次集合")
    List<Integer> weekNumbers;

}
