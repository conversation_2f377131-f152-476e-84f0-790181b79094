package com.xiaoshan.oa.ao.meeting;

import java.util.Date;

import com.xiaoshan.oa.enums.MeetingState;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class MeetingRecordQueryAO {

    @ApiModelProperty("会议名称")
    private String name;

    @ApiModelProperty("会议地点-传房间id")
    @LongValid
    private Long roomId;

    @ApiModelProperty("开始时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startTime;

    @ApiModelProperty("结束时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endTime;

    @ApiModelProperty("会议状态")
    @Enum
    private MeetingState state;

    @ApiModelProperty("教师Id")
    @LongValid
    private Long teacherId;

    @ApiModelProperty(value = "当前页数(默认:1)",example = "1")
    @IntegerValid(min=1)
    private int pageIndex = 1;

    @ApiModelProperty(value = "分页大小(默认:8)",example = "8")
    @IntegerValid(min=-1)
    private int pageSize = 8;

}
