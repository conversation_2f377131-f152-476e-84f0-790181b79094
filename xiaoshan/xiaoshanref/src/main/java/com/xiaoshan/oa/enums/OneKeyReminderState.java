package com.xiaoshan.oa.enums;

import io.swagger.annotations.ApiModelProperty;

// 一键提醒状态
public enum OneKeyReminderState {
    /**
     * 30分钟内只允许提醒一次
     */
    @ApiModelProperty("30分钟内只允许提醒一次")
    REFUSE,
    /**
     * 提醒成功
     */
    @ApiModelProperty("提醒成功")
    SUCCESS,
    /**
     * 所有人都确认或拒绝
     */
    @ApiModelProperty("所有人都确认或拒绝")
    OTHER,
    /**
     * 会议已结束
     */
    @ApiModelProperty("会议已结束")
    END
}
