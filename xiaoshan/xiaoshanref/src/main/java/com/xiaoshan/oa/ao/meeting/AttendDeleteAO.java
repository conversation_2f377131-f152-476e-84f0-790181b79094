package com.xiaoshan.oa.ao.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Data
public class AttendDeleteAO {

    @ApiModelProperty("会议id")
    @LongValid
    @NotNull
    private Long meetingId;

    @ApiModelProperty("删除id")
    @LongValid
    @NotNull
    private Long id;

    @ApiModelProperty("确认状态,0 待确认 1 通过 2 拒绝")
    @IntegerValid
    @NotNull
    private Integer state;
}
