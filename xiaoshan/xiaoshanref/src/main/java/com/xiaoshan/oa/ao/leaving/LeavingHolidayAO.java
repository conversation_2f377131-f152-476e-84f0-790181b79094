package com.xiaoshan.oa.ao.leaving;

import com.xiaoshan.oa.enums.TimeScope;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

import java.util.Date;

@ApiModel("教师请假节假日")
@Getter@Setter@ToString
public class LeavingHolidayAO {

	public LeavingHolidayAO(){}

	@ApiModelProperty(value = "开始时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date startTime;

	@ApiModelProperty(value = "结束时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date endTime;

	@ApiModelProperty(value = "开始时间范围【如果是临时假，则去除此属性，非临时假此项不能为空】")
	@Enum
	private TimeScope startTimeScope;

	@ApiModelProperty(value = "结束时间范围【如果是临时假，则去除此属性，非临时假此项不能为空】")
	@Enum
	private TimeScope endTimeScope;

	@ApiModelProperty("请假类型")
	@NotEmpty
	private String leavingType;
}
