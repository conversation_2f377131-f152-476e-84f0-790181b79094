package com.xiaoshan.oa.ao.leaving;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;
import java.util.List;

@Getter@Setter@ToString
public class LeavingStatisticsPageAO extends PageAO {
    @ApiModelProperty(value = "开始时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty(value = "请假类型")
    private String leavingType;

    @ApiModelProperty("部门ID")
    private Long departmentId;

    @ApiModelProperty("教师名称")
    private String teacherName;

    @ApiModelProperty("教师id集合")
    private List<Long> teacherList;
}
