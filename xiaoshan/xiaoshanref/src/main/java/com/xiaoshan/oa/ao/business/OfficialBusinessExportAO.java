package com.xiaoshan.oa.ao.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;

@Getter@Setter@ToString
public class OfficialBusinessExportAO {
    @ApiModelProperty("审批编号")
    private String approveNo;

    @ApiModelProperty("请假教师姓名")
    private String teacherName;

    @ApiModelProperty("审批状态")
    private Integer approveStatus;

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;
}
