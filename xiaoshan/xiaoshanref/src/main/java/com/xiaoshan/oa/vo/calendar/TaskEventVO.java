package com.xiaoshan.oa.vo.calendar;

import com.xiaoshan.oa.enums.ApprovalType;
import com.xiaoshan.oa.enums.PublishType;
import com.xiaoshan.oa.enums.TaskType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class TaskEventVO {
	
	@ApiModelProperty("行事历名称")
	private String tname;
	
	@ApiModelProperty("行事历类型")
	private TaskType ttype;

	@ApiModelProperty("行事历开始日期")
	private java.sql.Date tstartDate;

	@ApiModelProperty("行事历结束日期")
	private java.sql.Date tendDate;
	
	@ApiModelProperty("行事历学年学期")
	private Long tsemesterId;
	
	@ApiModelProperty("行事历学年学期名称")
	private String tsemesterName;
	
	@ApiModelProperty("任务ID")
	private Long taskEventId;

	@ApiModelProperty("名称")
	private String name;
	
	@ApiModelProperty("周ID")
	private Long weekId;

	@ApiModelProperty("开始日期")
	private java.sql.Date startDate;

	@ApiModelProperty("开始日期")
	private java.sql.Date sDate;
	
	@ApiModelProperty("结束日期")
	private java.sql.Date eDate;
//	
//	@ApiModelProperty("结束日期")
//	private java.sql.Date endDate;
	
	@ApiModelProperty("具体时间")
	private String specificTime;

	@ApiModelProperty("地点")
	private String address;

	@ApiModelProperty("事件")
	private String event;

	@ApiModelProperty("相关部门")
	private String departments;

	@ApiModelProperty("填报人Id")
	private Long teacherId;

	@ApiModelProperty("填报人")
	private String teacherName;
	
	@ApiModelProperty("审批状态")
	private ApprovalType approval;

	@ApiModelProperty("审批内容")
	private String reason;
	
	@ApiModelProperty("发布类型")
	private PublishType publishType;

	@ApiModelProperty("日显示的名称")
	private String monthDayName;
	
	@ApiModelProperty("月显示的名称")
	private String yearMonthName;

//	@ApiModelProperty("是否已读")
//	private Boolean read;
	
}
