package com.xiaoshan.oa.ao.calendar;

import java.util.List;

import com.xiaoshan.oa.enums.ApprovalType;
import com.xiaoshan.oa.enums.PublishType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskEventPageAO extends TaskPageAO {
	
	@ApiModelProperty("任务ID")
	@LongValid
	private Long taskId;

	@ApiModelProperty("任务事件ID")
	@LongValid
	private Long taskEventId;

	@ApiModelProperty("任务事件日期")
	private List<java.sql.Date> eventDates;

	@ApiModelProperty("任务事件周ID")
	private List<Long> weekIds;
	
	@ApiModelProperty("发布类型")
	@Enum
	private PublishType publishType;
	
	@ApiModelProperty("开始日期(周期查询使用)")
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date startDate;

	@ApiModelProperty("结束日期(周期查询使用)")
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date endDate;
	
	@ApiModelProperty("当天日期")
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date todayDate;
	
	@ApiModelProperty("审批状态")
	@Enum
	private ApprovalType approval;

	@ApiModelProperty("是否查看所有")
	@BooleanValid
	private Boolean all;

}
