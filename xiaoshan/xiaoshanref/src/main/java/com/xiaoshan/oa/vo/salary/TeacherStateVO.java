package com.xiaoshan.oa.vo.salary;

import com.xiaoshan.oa.enums.ReadType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("接收对象")
@Getter@Setter@ToString
public class TeacherStateVO {

	public TeacherStateVO(){}

	@ApiModelProperty("教师姓名")
	private String name;

	@ApiModelProperty("部门")
	private String departmentName;

	@ApiModelProperty("阅读状态")
	private ReadType readType = ReadType.UNREAD;

}
