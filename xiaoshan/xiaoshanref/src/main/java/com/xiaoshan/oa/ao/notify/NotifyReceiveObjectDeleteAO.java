package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NoticeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class NotifyReceiveObjectDeleteAO {
    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long notifyId;

    @ApiModelProperty("通知类型")
    @NotNull
    @Enum
    private NoticeType noticeType;

    @ApiModelProperty("userId【除班级外必传，删除消息所需】")
    @LongValid
    private Long userId;

    @ApiModelProperty("通知对象id")
    @NotNull
    @LongValid
    private Long objectId;
}
