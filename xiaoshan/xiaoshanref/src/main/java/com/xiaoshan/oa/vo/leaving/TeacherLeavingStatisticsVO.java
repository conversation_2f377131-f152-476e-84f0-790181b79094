package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel
@Getter@Setter@ToString
public class TeacherLeavingStatisticsVO {

	public TeacherLeavingStatisticsVO(){}

	@ApiModelProperty(value = "全部请假人数")
	private Integer allCnt;

	@ApiModelProperty(value = "事假人数")
	private Integer casualCnt;

	@ApiModelProperty(value = "病假人数")
	private Integer sickCnt;

	@ApiModelProperty(value = "婚假人数")
	private Integer marriageCnt;

	@ApiModelProperty(value = "产假人数")
	private Integer maternityCnt;

	@ApiModelProperty(value = "护理假人数")
	private Integer paternityCnt;

	@ApiModelProperty(value = "工伤假人数")
	private Integer injuryCnt;

	@ApiModelProperty(value = "丧假人数")
	private Integer funeralCnt;

	@ApiModelProperty(value = "临时假人数")
	private Integer temporaryCnt;

	@ApiModelProperty(value = "育儿假人数")
	private Integer parentalCnt;

	@ApiModelProperty(value = "独生子女陪护假人数")
	private Integer accompanyCnt;
}
