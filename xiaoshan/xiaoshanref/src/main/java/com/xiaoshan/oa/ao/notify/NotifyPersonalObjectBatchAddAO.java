package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NotifyReceiveObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;
import start.magic.persistence.annotation.Column;

import java.util.List;

@Getter@Setter@ToString
public class NotifyPersonalObjectBatchAddAO {
    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    Long notifyId;

    @ApiModelProperty("接收对象类型")
    @NotNull
    NotifyReceiveObjectType objectType;

    @ApiModelProperty("接收对象集合")
    @NotNull
    @Column("objectIdAndContentAOList")
    List<ObjectIdAndContentAO> objectIdAndContentAoList;
}
