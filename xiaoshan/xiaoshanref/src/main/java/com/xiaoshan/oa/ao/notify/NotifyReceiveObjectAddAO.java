package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NotifyReceiveObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class NotifyReceiveObjectAddAO {

    @ApiModelProperty("通知id")
    @NotNull
    @LongValid
    private Long notifyId;

    @ApiModelProperty("通知对象id")
    @NotNull
    @LongValid
    private Long userId;

    @ApiModelProperty("通知对象类型")
    @NotNull
    private NotifyReceiveObjectType objectType;

    @ApiModelProperty("通知内容（公共通知不需要）")
    private String content;
}
