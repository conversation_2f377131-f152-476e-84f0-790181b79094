package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;
import java.util.List;

/**
 * 预览个性化通知VO
 */
@ApiModel("个性化通知")
@Getter@Setter@ToString
public class NotifyPersonalVO {
    @ApiModelProperty(value = "通知id")
    private Long notifyId;

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty("发布人")
    private String author;

    @ApiModelProperty(value = "发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("是否定时发送")
    private Boolean isTiming;

    @ApiModelProperty("定时发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date sendTime;

    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    @ApiModelProperty("通知记录列表")
    private List<ObjectInfoVO> recordList;
}
