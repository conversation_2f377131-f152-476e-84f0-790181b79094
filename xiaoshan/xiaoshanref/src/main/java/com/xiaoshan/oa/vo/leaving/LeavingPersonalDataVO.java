package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel
@Getter@Setter@ToString
public class LeavingPersonalDataVO {

	public LeavingPersonalDataVO() {
	}

	@ApiModelProperty(value = "请假ID")
	private Long leavingId;

	@ApiModelProperty(value = "审批编号")
	private String approvalNo;

	@ApiModelProperty("请假类型")
	private String leavingType;

	@ApiModelProperty("申请时间")
	private String requestTime;

	@ApiModelProperty("请假时间")
	private String leavingTime;

	@ApiModelProperty("请假开始时间")
	private String startTime;

	@ApiModelProperty("请假结束时间")
	private String endTime;

	@ApiModelProperty("销假结束时间")
	private String absenceTime;

	@ApiModelProperty(value = "请假时长")
	private String duration;

	@ApiModelProperty(value = "审批状态")
	private String approveStatus;
}
