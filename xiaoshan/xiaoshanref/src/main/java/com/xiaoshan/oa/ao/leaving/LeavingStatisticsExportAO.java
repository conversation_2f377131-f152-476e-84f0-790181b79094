package com.xiaoshan.oa.ao.leaving;

import java.sql.Date;
import java.util.List;

import com.xiaoshan.oa.ao.business.BusinessStatisticsExportAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class LeavingStatisticsExportAO extends BusinessStatisticsExportAO {
    @ApiModelProperty(value = "请假类型")
    private String leavingType;
}
