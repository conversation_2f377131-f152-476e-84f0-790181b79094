package com.xiaoshan.oa.vo.notify;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class NotifyHomeVO {

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty(value = "发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("通知正文")
    private String content;

    @ApiModelProperty("跳转地址")
    private String url;

}
