package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel("小程序端-教师请假详情")
@Getter@Setter@ToString
public class TeacherLeavingMiniDetailVO {

	public TeacherLeavingMiniDetailVO(){}

	@ApiModelProperty(value = "教师请假id")
	private Long leavingId;

	@ApiModelProperty(value = "审批状态")
	private String approveStatus;

	@ApiModelProperty("请假类型")
	private String leavingType;

	@ApiModelProperty("申请时间")
	private String requestTime;

	@ApiModelProperty("请假开始时间")
	private String startTime;

	@ApiModelProperty("请假结束时间")
	private String endTime;

	@ApiModelProperty("销假结束时间")
	private String absenceTime;

	@ApiModelProperty(value = "请假时长")
	private String duration;

	@ApiModelProperty("请假事由")
	private String reason;

	@ApiModelProperty("附件列表")
	private List<LeavingAttachmentsVO> attachments;

	@ApiModelProperty("审批编号")
	private String approvalNo;
}
