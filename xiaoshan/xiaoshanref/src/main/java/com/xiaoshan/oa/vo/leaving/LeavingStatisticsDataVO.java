package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@ApiModel
@Getter@Setter@ToString
public class LeavingStatisticsDataVO {


	public LeavingStatisticsDataVO(){}

	@ApiModelProperty(value = "教师ID")
	private Long teacherId;

	@ApiModelProperty(value = "教师名称")
	private String teacherName;

	@ApiModelProperty(value = "部门ID")
	private String departmentId;

	@ApiModelProperty(value = "部门")
	private String department;

	@ApiModelProperty(value = "请假总次数")
	private Integer leavingNum = 0;

	@ApiModelProperty(value = "请假总时长(天)")
	private BigDecimal leavingDays = new BigDecimal(0);

	@ApiModelProperty(value = "请假总时长(小时)")
	private BigDecimal leavingHours = new BigDecimal(0);

	@ApiModelProperty(value = "事假次数")
	private Integer casualNum = 0;

	@ApiModelProperty(value = "事假时长")
	private BigDecimal casualTimes = new BigDecimal(0);

	@ApiModelProperty(value = "病假次数")
	private Integer sickNum = 0;

	@ApiModelProperty(value = "病假时长")
	private BigDecimal sickTimes = new BigDecimal(0);

	@ApiModelProperty(value = "婚假次数")
	private Integer marriageNum = 0;

	@ApiModelProperty(value = "婚假时长")
	private BigDecimal marriageTimes = new BigDecimal(0);

	@ApiModelProperty(value = "产假次数")
	private Integer maternityNum = 0;

	@ApiModelProperty(value = "产假时长")
	private BigDecimal maternityTimes = new BigDecimal(0);

	@ApiModelProperty(value = "护理假次数")
	private Integer paternityNum = 0;

	@ApiModelProperty(value = "护理假时长")
	private BigDecimal paternityTimes = new BigDecimal(0);

	@ApiModelProperty(value = "工伤假次数")
	private Integer injuryNum = 0;

	@ApiModelProperty(value = "工伤假时长")
	private BigDecimal injuryTimes = new BigDecimal(0);

	@ApiModelProperty(value = "丧假次数")
	private Integer funeralNum = 0;

	@ApiModelProperty(value = "丧假时长")
	private BigDecimal funeralTimes = new BigDecimal(0);

	@ApiModelProperty(value = "临时假次数")
	private Integer temporaryNum = 0;

	@ApiModelProperty(value = "临时假时长")
	private BigDecimal temporaryTimes = new BigDecimal(0);

	@ApiModelProperty(value = "育儿假次数")
	private Integer parentalNum = 0;

	@ApiModelProperty(value = "育儿假时长")
	private BigDecimal parentalTimes = new BigDecimal(0);

	@ApiModelProperty(value = "独生子女陪护假次数")
	private Integer accompanyNum = 0;

	@ApiModelProperty(value = "独生子女陪护假时长")
	private BigDecimal accompanyTimes = new BigDecimal(0);
}
