package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class NotifyPersonalObjectVO {
    @ApiModelProperty("userId")
    private Long userId;

    @ApiModelProperty("通知内容")
    private String content;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("学段")
    private String sectionName;

    @ApiModelProperty("年级")
    private String gradeName;

    @ApiModelProperty("班级")
    private String className;

    @ApiModelProperty("学号")
    private String studentNo;

    @ApiModelProperty("IC卡号")
    private String icCardNo;

    @ApiModelProperty("学段类型")
    private Long sectionType;

    @ApiModelProperty("入学年份")
    private Integer enrollmentYear;

    @ApiModelProperty("班级id")
    private Long classId;

    @ApiModelProperty("接收对象id")
    private Long objectId;
}
