package com.xiaoshan.oa.ao.notify;

import com.xiaoshan.oa.enums.NotifyReceiveObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class NotifyPersonalObjectAO {

    @ApiModelProperty("通知id")
    @NotNull
    private Long notifyId;

    @ApiModelProperty("接收对象类型")
    @NotNull
    private NotifyReceiveObjectType objectType;

    @ApiModelProperty("接收对象id")
    @NotNull
    private Long objectId;

    @ApiModelProperty("通知内容")
    @NotEmpty
    private String content;
}
