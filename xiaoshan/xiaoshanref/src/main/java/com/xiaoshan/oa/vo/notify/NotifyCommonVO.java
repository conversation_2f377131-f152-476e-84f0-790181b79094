package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Column;

import java.util.Date;
import java.util.List;

/**
 * 预览公共通知VO
 */
@ApiModel("通知")
@Getter@Setter@ToString
public class NotifyCommonVO {
    @ApiModelProperty(value = "通知id")
    private Long notifyId;

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty("发布人")
    private String author;

    @ApiModelProperty(value = "发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("通知正文")
    private String content;

    @ApiModelProperty("附件集合")
    @Column("notifyAttachmentVOList")
    private List<NotifyAttachmentVO> notifyAttachmentVoList;

    @ApiModelProperty("发布来源（1:pc，2:小程序）")
    private Integer sourceType;
}
