package com.xiaoshan.oa.ao.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("教师请假附件")
@Getter@Setter@ToString
public class LeavingAttachmentsAO {

	public LeavingAttachmentsAO(){}

	@ApiModelProperty(value = "附件展示顺序")
	private Integer orders;

	@ApiModelProperty("附件地址")
	private String attachUrl;
}
