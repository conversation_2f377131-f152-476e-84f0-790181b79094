package com.xiaoshan.oa.ao.calendar;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class TaskEventWeekAO extends TaskEventAO {

	@ApiModelProperty("开始日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date startDate;

	@ApiModelProperty("具体时间")
	@Length
	private String specificTime;
	
}
