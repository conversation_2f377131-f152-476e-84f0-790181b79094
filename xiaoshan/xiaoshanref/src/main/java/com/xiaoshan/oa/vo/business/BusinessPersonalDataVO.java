package com.xiaoshan.oa.vo.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("教师公出")
@Getter@Setter@ToString
public class BusinessPersonalDataVO {

	public BusinessPersonalDataVO() {
	}

	@ApiModelProperty(value = "公出ID")
	private Long leavingId;

	@ApiModelProperty("申请时间")
	private String requestTime;

	@ApiModelProperty("公出开始时间")
	private String startTime;

	@ApiModelProperty("公出结束时间")
	private String endTime;

	@ApiModelProperty(value = "审批状态")
	private String approveStatus;
}
