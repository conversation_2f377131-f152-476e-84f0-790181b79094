package com.xiaoshan.oa.vo.meeting;

import com.xiaoshan.oa.ao.meeting.MeetingPlaceAO;
import com.xiaoshan.oa.enums.MeetingRoomState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter@Setter@ToString
public class MeetingRoomStateVO {

	public MeetingRoomStateVO(){}

	@ApiModelProperty("会议室")
	private MeetingPlaceAO meetingPlace;

	@ApiModelProperty("会议室状态")
	private MeetingRoomState state;

	@ApiModelProperty("占用时间")
	private List<TimeVO> occupyTimes;
}
