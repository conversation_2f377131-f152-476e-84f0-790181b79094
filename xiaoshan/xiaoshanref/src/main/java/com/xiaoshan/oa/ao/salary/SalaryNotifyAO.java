package com.xiaoshan.oa.ao.salary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;
import start.magic.thirdparty.json.JsonObject;

import java.util.Date;

@ApiModel("薪水")
@Getter@Setter@ToString
public class SalaryNotifyAO {

	public SalaryNotifyAO(){}

	@ApiModelProperty(value = "通知id")
	@LongValid
	private Long id;

	@ApiModelProperty("通知标题")
	@NotEmpty
	private String title;

	@ApiModelProperty(value = "发布时间", hidden = true)
	@TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date publicTime;

	@ApiModelProperty("发布人")
	@NotEmpty
	private String author;

	@ApiModelProperty("是否定时发送")
	@NotNull
	private Boolean isTiming;

	@ApiModelProperty("定时发布时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date sendTime;

	@ApiModelProperty(value = "excel数据id")
	@NotNull
	@LongValid
	private Long excelId;

	@ApiModelProperty(value = "总人数", hidden = true)
	private Integer total;

	@ApiModelProperty(value = "发薪月份")
	@IntegerValid(min = 1, max = 12)
	private Integer payMonth;

	@ApiModelProperty(value = "部门json")
	private JsonObject departmentJson;

}
