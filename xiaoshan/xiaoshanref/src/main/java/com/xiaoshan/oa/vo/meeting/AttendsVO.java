package com.xiaoshan.oa.vo.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AttendsVO {

    public AttendsVO() {
    }

    @ApiModelProperty(value = "教师id", hidden = true)
    private Long attendId;

    @ApiModelProperty(value = "教师userId", hidden = true)
    private Long userId;

    @ApiModelProperty("通过这个id来删除参会人")
    private Long id;

    @ApiModelProperty("参会人姓名")
    private String name;

    @ApiModelProperty("参会人部门")
    private String departmentName;

    @ApiModelProperty("确认状态,0 待确认 1 通过 2 拒绝")
    private Integer state = 0;

    @ApiModelProperty("拒绝理由")
    private String reason;

    @ApiModelProperty("阅读状态")
    private Boolean read = true;

}
