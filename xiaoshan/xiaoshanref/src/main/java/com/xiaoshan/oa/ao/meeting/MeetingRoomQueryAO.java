package com.xiaoshan.oa.ao.meeting;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.Date;

@Getter@Setter@ToString
public class MeetingRoomQueryAO extends PageAO {

	public MeetingRoomQueryAO(){}

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startTime;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endTime;

	@ApiModelProperty("日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date date;

	@ApiModelProperty("房间id")
	@LongValid
	private Long roomId;



}
