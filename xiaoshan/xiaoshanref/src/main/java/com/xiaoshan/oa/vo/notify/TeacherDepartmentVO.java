package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel("一级部门")
@Getter@Setter@ToString
public class TeacherDepartmentVO {

    @ApiModelProperty("一级部门")
    private String departmentName;

    @ApiModelProperty("二级部门列表")
    private List<TwoDepartmentVO> twoDepartments;

    @ApiModelProperty("教师列表")
    private List<ObjectInfoVO> teachers;
}
