package com.xiaoshan.oa.vo.salary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
public class ExcelAnalysisVO {

    @ApiModelProperty("表头数据")
    private JsonArray heads;

    @ApiModelProperty("表内容数据")
    private JsonArray data;

    @ApiModelProperty("总记录数")
    private Integer totalCount;

    @ApiModelProperty("错误记录数")
    private Integer errorCount;

    @ApiModelProperty("表内容id，发布通知时带上")
    private Long excelId;

    @ApiModelProperty("自定义项超出30行")
    private Boolean out = false;


}
