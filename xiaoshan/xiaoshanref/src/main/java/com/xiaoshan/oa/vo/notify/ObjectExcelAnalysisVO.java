package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.persistence.annotation.Column;

import java.util.List;

/**
 * 解析excel数据后的VO对象
 */
@ApiModel("个性化通知上传excel解析结果")
@Getter@Setter@ToString
public class ObjectExcelAnalysisVO {
    @ApiModelProperty("总记录数")
    private Integer totalCount;

    @ApiModelProperty("错误记录数")
    private Integer errorCount;

    @ApiModelProperty("正确数据集合")
    private List<NotifyPersonalObjectVO> rightDataList;

    @ApiModelProperty("excel解析对象集合")
    @Column("objectInfoVOList")
    List<ObjectInfoVO> objectInfoVoList;

}
