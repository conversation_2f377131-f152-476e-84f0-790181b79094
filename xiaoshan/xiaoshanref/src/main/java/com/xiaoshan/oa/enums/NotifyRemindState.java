package com.xiaoshan.oa.enums;

import io.swagger.annotations.ApiModelProperty;

public enum NotifyRemindState {
    /**
     * 提醒成功
     */
    @ApiModelProperty("提醒成功")
    REMIND_SUCCESS,
    /**
     * 提醒失败（所有人已读或通知待发布或通知已撤回）
     */
    @ApiModelProperty("提醒失败（所有人已读或通知待发布或通知已撤回）")
    REMIND_MISS_OTHER,
    /**
     * 提醒失败（两次提醒时间间隔低于30分钟）
     */
    @ApiModelProperty("提醒失败（两次提醒时间间隔低于30分钟）")
    REMIND_MISS_TIME

}
