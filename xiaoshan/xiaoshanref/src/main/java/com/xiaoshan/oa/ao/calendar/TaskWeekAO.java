package com.xiaoshan.oa.ao.calendar;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskWeekAO {
	
	@ApiModelProperty("学年学期")
	@NotNull
	@LongValid
	private Long semesterId;
	
	@ApiModelProperty("周ID(类型为周)")
	@NotNull
	@LongValid
	private Long weekId;

	@ApiModelProperty("是否发布")
	@NotNull
	@BooleanValid
	private Boolean publish;
	
}
