package com.xiaoshan.oa.vo.leaving;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel
@Getter
@Setter
@ToString
public class TeacherLeavingAndBusinessVO {
    @ApiModelProperty(value = "公出/请假 类别：1请假、2公出")
    private Integer dataType;

    @ApiModelProperty(value = "教师请假id")
    private Long leavingId;

    @ApiModelProperty("请假/公出教师姓名")
    private String teacherName;

    @ApiModelProperty("请假/公出教师ID")
    private Long teacherId;

    @ApiModelProperty("请假类型：公出 事假、病假、婚假、产假、护理假、工伤假、丧假、临时假、育儿假、独生子女陪护假")
    private String leavingType;


    @ApiModelProperty(value = "请假时长")
    private BigDecimal duration;

    @ApiModelProperty("请假开始时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startTime;

    @ApiModelProperty("开始时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String startTimeScope;

    @ApiModelProperty("请假结束时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endTime;

    @ApiModelProperty("结束时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String endTimeScope;
}
