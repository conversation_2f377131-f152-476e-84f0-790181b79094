package com.xiaoshan.oa.ao.leaving;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;

@Getter
@Setter
@ToString
public class AttendanceGetTemporaryAO {
    @ApiModelProperty("教师Id")
    @NotNull
    @NotEmpty
    private Long teacherId;

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;
}
