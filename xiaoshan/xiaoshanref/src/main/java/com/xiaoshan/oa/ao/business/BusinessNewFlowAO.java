package com.xiaoshan.oa.ao.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter@Setter@ToString
public class BusinessNewFlowAO {
    @ApiModelProperty(value = "请假/公出ID")
    private Long leavingId;

    @ApiModelProperty(value = "流程条件")
    private Map<String,String> conditions;

    @ApiModelProperty("节点信息(json格式)【前端透传过来】")
    private String nodeInfo;

    @ApiModelProperty("流程定义【前端透传过来】")
    private String processDefinitionKey;

    @ApiModelProperty("下级审批人【前端透传过来】")
    private Map<String, String> approver;

    @ApiModelProperty("关联类型 1学生获奖填报 2教师年度考核 3请假【前端透传过来的】 -- 教师公出对应的是8")
    private Integer relatedType;
}
