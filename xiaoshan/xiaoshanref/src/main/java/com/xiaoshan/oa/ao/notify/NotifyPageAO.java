package com.xiaoshan.oa.ao.notify;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.oa.enums.PublishStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;

@Getter@Setter@ToString
public class NotifyPageAO extends PageAO {
    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty("发布状态")
    private PublishStatus status;

    @ApiModelProperty("数据范围（1：全部数据，2：小程序数据）")
    private Integer scope;

    @ApiModelProperty(value = "小程序端发布记录所需，自动获取",hidden = true)
    private String author;
}
