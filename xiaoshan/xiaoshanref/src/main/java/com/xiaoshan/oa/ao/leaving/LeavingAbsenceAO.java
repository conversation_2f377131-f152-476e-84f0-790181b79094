package com.xiaoshan.oa.ao.leaving;

import java.util.Date;

import com.xiaoshan.oa.enums.TimeScope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;

@ApiModel("教师请假提前销假")
@Getter@Setter@ToString
public class LeavingAbsenceAO {

	public LeavingAbsenceAO(){}

	@ApiModelProperty(value = "教师请假/公出 ID")
	private Long leavingId;

	@ApiModelProperty(value = "提前销假时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date absenceTime;

	@ApiModelProperty(value = "销假时间范围【如果是临时假，则去除此属性，公出和非临时假此项不能为空】")
	@Enum
	private TimeScope absenceTimeScope;

}
