package com.xiaoshan.oa.vo.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@Getter@Setter@ToString
public class TimeVO {

	public TimeVO(){}

	@ApiModelProperty("开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startTime;

	@ApiModelProperty("结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endTime;


}
