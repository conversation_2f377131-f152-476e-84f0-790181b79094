package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("模拟获取节点")
@Getter@Setter@ToString
public class FlowNodesVO {

	public FlowNodesVO(){}

	@ApiModelProperty("抄送ids")
	private String assignCcIds;

	@ApiModelProperty("抄送人名称，英文逗号拼接")
	private String assignCcNames;

	@ApiModelProperty("抄送角色名称，英文逗号拼接")
	private String assignCcRoleName;

	@ApiModelProperty("抄送角色")
	private String assignCcRoles;

	@ApiModelProperty("审批角色")
	private String assignRoleIds;

	@ApiModelProperty("审批角色名称，英文逗号拼接")
	private String assignRoleNames;

	@ApiModelProperty("审批用户")
	private String assignUserIds;

	@ApiModelProperty("审批用户名称，英文逗号拼接")
	private String assignUserNames;

	@ApiModelProperty("处理人id")
	private String assignee;

	@ApiModelProperty("节点类型 cc抄送 approver审批")
	private String busNodeType;

	@ApiModelProperty("当前审批节点选择方式 0 自选一个人1 自选多个人")
	private Integer curMode;

	@ApiModelProperty("当前审批节点选择范围 0 不限范围1 指定范围")
	private Integer curRange;

	@ApiModelProperty("当前审批节点范围人员集合 id集合逗号隔开")
	private String curUsers;

	@ApiModelProperty("是否可编辑 0否1是")
	private Integer isEdit;

	@ApiModelProperty("是否允许发起人指定抄送人 0-否 1-是")
	private Integer isPermission;

	@ApiModelProperty("节点状态")
	private Integer resultState;

	@ApiModelProperty("1或签 2会签")
	private Integer signType;

	@ApiModelProperty("节点key")
	private String taskKey;

	@ApiModelProperty("节点名称")
	private String taskName;

	@ApiModelProperty("角色关联教师姓名，逗号拼接")
	private String teacherNames;

	@ApiModelProperty("当前节点模式 0指定成员1指定角色2发起人自选3上一节点审批人指定")
	private Integer type;
}
