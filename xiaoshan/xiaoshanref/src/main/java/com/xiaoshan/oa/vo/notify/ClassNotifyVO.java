package com.xiaoshan.oa.vo.notify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@ApiModel("班级通知")
@Getter@Setter@ToString
public class ClassNotifyVO {
    @ApiModelProperty(value = "通知id")
    private Long notifyId;

    @ApiModelProperty("通知标题")
    private String title;

    @ApiModelProperty(value = "发布时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date publishTime;

    @ApiModelProperty("是否已读")
    private Boolean isRead;
}
