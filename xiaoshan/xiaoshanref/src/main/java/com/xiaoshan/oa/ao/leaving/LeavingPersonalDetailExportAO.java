package com.xiaoshan.oa.ao.leaving;

import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class LeavingPersonalDetailExportAO {
    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty("请假教师ID")
    private Long teacherId;

    @ApiModelProperty("请假教师名称")
    private String teacherName;
}
