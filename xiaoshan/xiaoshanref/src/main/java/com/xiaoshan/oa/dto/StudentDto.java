package com.xiaoshan.oa.dto;

import com.xiaoshan.basic.vo.TeacherVO;
import com.xiaoshan.oa.vo.notify.NotifyParentVO;
import lombok.Data;

import java.util.List;

@Data
public class StudentDto {
    // 学生id
    private Long id;
    // 用户id
    private Long userId;
    // 学段
    private String sectionName;
    // 年级
    private String gradeName;
    // 班级
    private String className;
    // 学号
    private String studentNo;
    // IC卡号
    private String icCardNo;
    //性别
    private Integer gender;
    //身份证
    private String idNo;
    // 学生姓名
    private String name;
    // 学段类型
    private Long sectionType;
    // 入学年份
    private Integer enrollmentYear;
    // 班级Id
    private Long classId;
    // 年级顺序（高一、高二、高三）
    private Integer gradeOrder;
    // 家长
    private List<NotifyParentVO> parents;
    // 班主任
    private TeacherVO headTeacherInfo;
    // 人脸url
    private String faceData;
}
