package com.xiaoshan.oa.vo.business;

import java.math.BigDecimal;
import java.util.List;

import com.xiaoshan.oa.vo.leaving.LeavingAttachmentsVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("小程序端-教师公出详情")
@Getter@Setter@ToString
public class BusinessMiniAgainVO {

	public BusinessMiniAgainVO(){}

	@ApiModelProperty(value = "审批状态")
	private Integer approveStatus;

	@ApiModelProperty("申请时间")
	private Long requestTime;

	@ApiModelProperty("公出开始时间")
	private Long startTime;

	@ApiModelProperty("公出结束时间")
	private Long endTime;

	@ApiModelProperty(value = "公出时长")
	private BigDecimal duration;

	@ApiModelProperty("公出事由")
	private String reason;

	@ApiModelProperty("附件列表")
	private List<LeavingAttachmentsVO> attachments;

	@ApiModelProperty("审批编号")
	private String approvalNo;
}
