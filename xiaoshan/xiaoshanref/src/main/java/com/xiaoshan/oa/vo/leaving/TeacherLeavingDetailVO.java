package com.xiaoshan.oa.vo.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel("教师请假")
@Getter@Setter@ToString
public class TeacherLeavingDetailVO {

	public TeacherLeavingDetailVO(){}

	@ApiModelProperty(value = "教师请假id")
	private Long leavingId;

	@ApiModelProperty(value = "审批状态：1审批中，2已通过，3已驳回，4已撤销")
	private Integer approveStatus;

	@ApiModelProperty("申请时间")
	private String requestTime;

	@ApiModelProperty("请假类型")
	private String leavingType;

	@ApiModelProperty("请假教师姓名")
	private String teacherName;

	@ApiModelProperty("请假时间")
	private String leavingTime;

	@ApiModelProperty("销假结束时间")
	private String absenceTime;

	@ApiModelProperty(value = "请假时长")
	private String duration;

	@ApiModelProperty("请假教师所属部门")
	private String department;

	@ApiModelProperty("请假事由")
	private String reason;

	@ApiModelProperty("附件列表")
	private List<LeavingAttachmentsVO> attachments;

	@ApiModelProperty("审批编号")
	private String approvalNo;
}
