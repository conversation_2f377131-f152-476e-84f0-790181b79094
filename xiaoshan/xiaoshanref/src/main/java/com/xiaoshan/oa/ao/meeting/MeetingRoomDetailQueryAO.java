package com.xiaoshan.oa.ao.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class MeetingRoomDetailQueryAO {

	public MeetingRoomDetailQueryAO(){}

	@ApiModelProperty("会议室id")
	@NotNull
	@LongValid
	private Long roomId;

	@ApiModelProperty("当天日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
	private java.sql.Date date;

}
