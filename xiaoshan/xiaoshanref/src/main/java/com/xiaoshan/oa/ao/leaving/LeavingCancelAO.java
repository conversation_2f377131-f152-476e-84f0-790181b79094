package com.xiaoshan.oa.ao.leaving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ApiModel("教师请假撤销")
@Getter@Setter@ToString
public class LeavingCancelAO {

	public LeavingCancelAO(){}

	@ApiModelProperty(value = "教师请假/公出 ID")
	private Long leavingId;

	@ApiModelProperty(value = "审批流程实例ID")
	private String engineFlowId;

}
