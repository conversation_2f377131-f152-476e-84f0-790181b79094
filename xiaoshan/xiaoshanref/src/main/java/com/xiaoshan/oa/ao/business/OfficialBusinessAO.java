package com.xiaoshan.oa.ao.business;

import com.xiaoshan.oa.ao.leaving.LeavingAttachmentsAO;
import com.xiaoshan.oa.enums.TimeScope;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("教师公出")
@Getter
@Setter
@ToString
public class OfficialBusinessAO extends BusinessNewFlowAO {


    public OfficialBusinessAO() {
    }


    @ApiModelProperty(value = "公出开始时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
    @PropertyConverter(DateTimefConverterEditor.class)
    @NotNull
    private Date startTime;

    @ApiModelProperty(value = "公出结束时间：如果是非临时假，统一传 yyyy-MM-dd 00:00:00")
    @PropertyConverter(DateTimefConverterEditor.class)
    @NotNull
    private Date endTime;

    @ApiModelProperty(value = "开始时间范围")
    @Enum
    @NotNull
    private TimeScope startTimeScope;

    @ApiModelProperty(value = "结束时间范围")
    @Enum
    @NotNull
    private TimeScope endTimeScope;

    @ApiModelProperty(value = "公出时长")
    @NotNull
    private BigDecimal duration;

    @ApiModelProperty("公出事由")
    @NotEmpty
    @Length
    private String reason;

    @ApiModelProperty("附件列表")
    private List<LeavingAttachmentsAO> attachments;

}
