package com.xiaoshan.oa.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 用于在excel解析封装班级信息
 */
@Data
public class ClassDTO {
    /**
     * 学段id
     */
    private Long sectionId;
    /**
     * 学段名称
     */
    private String sectionName;
    /**
     * 年级名称
     */
    private String gradeName;
    /**
     * 年级顺序 高一 高二 高三
     */
    private Integer gradeOrder;
    /**
     * 学段类型
     */
    private Integer sectionType;
    /**
     * 入学年份
     */
    private Integer enrollmentYear;
    /**
     * id
     */
    private Long id;
    /**
     * 班主任id
     */
    private Long headTeacherId;
    /**
     * 班主任姓名
     */
    private String headTeacherName;

    /**
     * 班级名称
     */
    private String name;

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 教师名称
     */
    private String roomName;

    /**
     * 学生id列表
     */
    private List<Long> studentIds;

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof ClassDTO)) {
            // instanceof 已经处理了obj = null的情况
            return false;
        }
        ClassDTO classDto = (ClassDTO) o;
        // 地址相等
        if (this == classDto) {
            return true;
        }
        // 比较两个对象的学段名，年纪名，班级名是否相等
        return classDto.sectionName.equals(this.sectionName) && classDto.gradeName.equals(this.gradeName) && classDto.name.equals(this.name);
    }
}
