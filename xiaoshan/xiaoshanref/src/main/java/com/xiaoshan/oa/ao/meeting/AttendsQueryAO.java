package com.xiaoshan.oa.ao.meeting;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * 参会人查询AO
 */
@Setter@Getter@ToString
public class AttendsQueryAO extends PageAO {

    @ApiModelProperty("参会人姓名")
    public String name;

    @ApiModelProperty("确认状态,0 待确认 1 通过 2 拒绝")
    public Integer state;

    @ApiModelProperty("会议id")
    @LongValid
    @NotNull
    private Long meetingId;


}
