package com.xiaoshan.edu.ao.timetable;

import com.topnetwork.ao.PageAO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CurriculumTablePageAO extends PageAO {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("学段ID")
	@LongValid
	private Long sectionId;

	@ApiModelProperty("入学年份")
	@IntegerValid
	private Integer enrollmentYear;

	@ApiModelProperty("学期ID")
	@LongValid
	private Long semesterId;


	@ApiModelProperty("是否班主任")
	@BooleanValid
	private Boolean classTeacher;

	@ApiModelProperty("仅获取对应权限数据")
	@BooleanValid
	private Boolean only=false;

	@ApiModelProperty("是否获取历史学期数据(不包括当前)")
	@BooleanValid
	private Boolean history=false;
	
	@ApiModelProperty("是否发布")
	@BooleanValid
	private Boolean published;
	
	@ApiModelProperty("是否已首次发布")
	@BooleanValid
	private Boolean firstPublished;

}
