package com.xiaoshan.edu.dto;

import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.converter.MapStringMapLongStringConverterEditor;
import com.xiaoshan.edu.vo.timetable.StudentTableDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherTableDetailVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class StudentTableDTO extends StudentTableDetailVO {


    @ApiModelProperty(value = "Map<课程,Map<老师ID,教室ID>>", hidden = true)
    @PropertyConverter(MapStringMapLongStringConverterEditor.class)
    private Map<String, Map<Long, String>> detail;

    @ApiModelProperty(value = "走班课程")
    private List<String> leaveClass;

    @ApiModelProperty("课程:老师")
    private Map<String, TeacherTableDetailVO> detailv;

}
