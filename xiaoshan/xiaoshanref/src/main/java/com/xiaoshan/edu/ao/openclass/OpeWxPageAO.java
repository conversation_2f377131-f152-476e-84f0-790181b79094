package com.xiaoshan.edu.ao.openclass;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;

import java.util.List;

@Getter
@Setter
@ToString
public class OpeWxPageAO extends PageAO {

    @ApiModelProperty("是否发布")
    @NotNull
    @BooleanValid
    public Boolean isPublish;

    @ApiModelProperty("科目")
    public List<String> subjectMult;

    @ApiModelProperty("类型")
    public List<OpenClassType> typeMult;

    @ApiModelProperty("上课状态")
    public Boolean isFinish;

    @ApiModelProperty("教师或课题")
    public String keywords;

    @ApiModelProperty("上课日期排序(1-正序，2-倒序)")
    private Integer dataSort;
}
