package com.xiaoshan.edu.enums.survey;

import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;

public enum SurveyObj {
    /**
     * 班主任
     */
    @ApiModelProperty("班主任")
    CLASS,

    /**
     * 任课教师
     */
    @ApiModelProperty("任课教师")
    CURRICULUM;

    public String getDescription(){
        for (Field field : getClass().getFields()) {
            if (field.getName().equals(this.name())){
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                return annotation.value();
            }
        }
        return this.name();
    }
}
