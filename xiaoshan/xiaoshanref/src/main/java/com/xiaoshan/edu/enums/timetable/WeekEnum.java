package com.xiaoshan.edu.enums.timetable;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */

public enum WeekEnum {
    /**
     * 周日
     */
    @ApiModelProperty("周日")
    Sunday,
    /**
     * 周一
     */
    @ApiModelProperty("周一")
    Monday,
    /**
     * 周二
     */
    @ApiModelProperty("周二")
    Tuesday,
    /**
     * 周三
     */
    @ApiModelProperty("周三")
    Wednesday,
    /**
     * 周四
     */
    @ApiModelProperty("周四")
    Thursday,
    /**
     * 周五
     */
    @ApiModelProperty("周五")
    Friday,
    /**
     * 周六
     */
    @ApiModelProperty("周六")
    Saturday;

    public static WeekEnum getDayOfWeek(String name) {
        String mStr = "周一";
        String tStr = "周二";
        String wednesdayStr = "周三";
        String thursdayStr = "周四";
        String fridayStr = "周五";
        String saturdayStr = "周六";
        String mStr1 = "一";
        String tStr1 = "二";
        String wednesdayStr1 = "三";
        String thursdayStr1 = "四";
        String fridayStr1 = "五";
        String saturdayStr1 = "六";

        if (mStr.equals(name) || mStr1.equals(name)) {
            return Monday;
        } else if (tStr.equals(name) || tStr1.equals(name)) {
            return Tuesday;
        } else if (wednesdayStr.equals(name) || wednesdayStr1.equals(name)) {
            return Wednesday;
        } else if (thursdayStr.equals(name) || thursdayStr1.equals(name)) {
            return Thursday;
        } else if (fridayStr.equals(name) || fridayStr1.equals(name)) {
            return Friday;
        } else if (saturdayStr.equals(name) || saturdayStr1.equals(name)) {
            return Saturday;
        } else {
            return Sunday;
        }
    }

    public static String getDayOfWeek(WeekEnum week) {
        if (week == WeekEnum.Monday) {
            return "周一";
        } else if (week == WeekEnum.Tuesday) {
            return "周二";
        } else if (week == WeekEnum.Wednesday) {
            return "周三";
        } else if (week == WeekEnum.Thursday) {
            return "周四";
        } else if (week == WeekEnum.Friday) {
            return "周五";
        } else if (week == WeekEnum.Saturday) {
            return "周六";
        } else {
            return "周日";
        }
    }

    public static Integer getWeekNum(WeekEnum week) {
        if (week == WeekEnum.Monday) {
            return 1;
        } else if (week == WeekEnum.Tuesday) {
            return 2;
        } else if (week == WeekEnum.Wednesday) {
            return 3;
        } else if (week == WeekEnum.Thursday) {
            return 4;
        } else if (week == WeekEnum.Friday) {
            return 5;
        } else if (week == WeekEnum.Saturday) {
            return 6;
        } else {
            return 7;
        }
    }

    public static Map<Integer, WeekEnum> getWeekMap() {
        Map<Integer, WeekEnum> weekMap = new HashMap<>();
        weekMap.put(1, WeekEnum.Monday);
        weekMap.put(2, WeekEnum.Tuesday);
        weekMap.put(3, WeekEnum.Wednesday);
        weekMap.put(4, WeekEnum.Thursday);
        weekMap.put(5, WeekEnum.Friday);
        weekMap.put(6, WeekEnum.Saturday);
        weekMap.put(7, WeekEnum.Sunday);
        return weekMap;
    }

    public static Map<WeekEnum, Integer> getWeekNumMap() {
        Map<WeekEnum, Integer> weekNumMap = new HashMap<>();
        weekNumMap.put(WeekEnum.Monday, 1);
        weekNumMap.put(WeekEnum.Tuesday, 2);
        weekNumMap.put(WeekEnum.Wednesday, 3);
        weekNumMap.put(WeekEnum.Thursday, 4);
        weekNumMap.put(WeekEnum.Friday, 5);
        weekNumMap.put(WeekEnum.Saturday, 6);
        weekNumMap.put(WeekEnum.Sunday, 7);
        return weekNumMap;
    }

    /**
     * 当前周
     *
     * @return
     */
    public static WeekEnum currentWeek() {
        Calendar calendar = Calendar.getInstance();
        return WeekEnum.values()[calendar.get(Calendar.DAY_OF_WEEK)];
    }

    public static WeekEnum getWeekByIndex(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int index = calendar.get(Calendar.DAY_OF_WEEK);
        switch (index) {
            case 2:
                return WeekEnum.Monday;
            case 3:
                return WeekEnum.Tuesday;
            case 4:
                return WeekEnum.Wednesday;
            case 5:
                return WeekEnum.Thursday;
            case 6:
                return WeekEnum.Friday;
            case 7:
                return WeekEnum.Saturday;
            case 1:
                return WeekEnum.Sunday;
            default:
                return null;
        }
    }

}
