package com.xiaoshan.edu.enums.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

// 调代课审批状态
@Getter
@AllArgsConstructor
public enum ApprovalState {
    /**
     * 已通过
     */
    PASS("已通过"),
    /**
     * 审批中
     */
    UNDER_APPROVAL("审批中"),
    /**
     * 已驳回
     */
    REJECT("已驳回"),
    /**
     * 已撤销
     */
    REVOKED("已撤销");

    private String name;
}
