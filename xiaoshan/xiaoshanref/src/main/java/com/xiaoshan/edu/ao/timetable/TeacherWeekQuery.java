package com.xiaoshan.edu.ao.timetable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TeacherWeekQuery extends TeacherQuery {

    @ApiModelProperty("第几周")
    @NotNull
    @IntegerValid(min = 1)
    private Integer number;

    @ApiModelProperty("教师列表")
    private List<Long> teacherIds;

    @ApiModelProperty("周列表")
    private List<Integer> numbers;

}
