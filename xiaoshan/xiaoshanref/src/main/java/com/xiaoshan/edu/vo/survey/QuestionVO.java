package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.converter.survey.CurriculumListConverterEditor;
import com.xiaoshan.edu.converter.survey.QuestionOptionListConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.converter.PropertyConverter;

import java.util.List;

@Data
public class QuestionVO {

    @ApiModelProperty("题目id")
    private Long questionId;

    @ApiModelProperty("题目类型")
    private QuestionType questionType;

    @ApiModelProperty(value = "题干内容")
    private String title;

    @ApiModelProperty(value = "题目选项")
    @PropertyConverter(QuestionOptionListConverterEditor.class)
    private List<QuestionOptionDTO> items;

    @ApiModelProperty(value = "是否必填")
    private Boolean isRequired;

    @ApiModelProperty(value = "结果是否教师可见，若是单选、多选则传false")
    private Boolean isVisible;

    @ApiModelProperty(value = "关联课程列表，若是班主任类型可不传")
    @PropertyConverter(CurriculumListConverterEditor.class)
    private List<String> curriculumList;

    @ApiModelProperty(value = "题号")
    private Integer questionSort;

}
