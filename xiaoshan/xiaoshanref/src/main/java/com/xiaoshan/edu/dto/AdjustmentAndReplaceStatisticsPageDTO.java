package com.xiaoshan.edu.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class AdjustmentAndReplaceStatisticsPageDTO {

    @ApiModelProperty("教师ID")
    private Long teacherId;

    @ApiModelProperty("教师姓名")
    private String teacherName;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("调课次数")
    private Integer adjustmentCount;

    @ApiModelProperty("代课次数")
    private Integer toReplaceCount;

    @ApiModelProperty("被代次数")
    private Integer beReplacedCount;

    @ApiModelProperty("代课节数")
    private Double toReplaceSection;

    @ApiModelProperty("被代节数")
    private Double beReplacedSection;

}
