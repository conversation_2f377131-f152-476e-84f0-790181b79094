package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;

@Getter@Setter@ToString
public class MyAdjustmentPageAO extends TimePageAO{

    @ApiModelProperty("被调课程")
    private String adjustedCourseName;

    @ApiModelProperty("被调教师")
    private String adjustedTeacherName;

    @ApiModelProperty("调课时间（如：第一周周一第一节，可单独一级，如：第二周）")
    private String courseTime;

    @ApiModelProperty("审批状态")
    @Enum
    private ApprovalState approvalState;

    @ApiModelProperty("审批编号")
    private String approvalNo;

}
