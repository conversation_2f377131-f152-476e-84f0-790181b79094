package com.xiaoshan.edu.ao.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter@Setter@ToString
public class OpenClassStartFlowAO {

    @ApiModelProperty(value = "流程条件")
    private Map<String,String> conditions;

    @ApiModelProperty("节点信息(json格式)【前端透传过来】")
    private String nodeInfo;

    @ApiModelProperty("流程定义【前端透传过来】")
    private String processDefinitionKey;

    @ApiModelProperty("下级审批人【前端透传过来】")
    private Map<String, String> approver;
}
