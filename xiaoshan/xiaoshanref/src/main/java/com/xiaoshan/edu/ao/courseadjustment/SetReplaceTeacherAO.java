package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

@Getter
@Setter
@ToString
public class SetReplaceTeacherAO {
    @ApiModelProperty("调代课ID")
    @NotNull
    private Long adjustmentId;

    @ApiModelProperty("教师Id")
    @NotNull
    private Long teacherId;

    @ApiModelProperty("教师名字")
    @NotNull
    @NotEmpty
    private String teacherName;
}
