package com.xiaoshan.edu.vo.timetable;

import java.util.List;

import com.xiaoshan.edu.converter.StudentDataTableConverterEditor;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class StudentTableVO {

	@ApiModelProperty("数据")
	@PropertyConverter(StudentDataTableConverterEditor.class)
	private List<StudentTableDTO> data;

	@ApiModelProperty("周类型")
	private WeekTypeEnum type;

}
