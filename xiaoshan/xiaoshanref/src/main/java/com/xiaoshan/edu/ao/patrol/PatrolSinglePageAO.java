package com.xiaoshan.edu.ao.patrol;

import com.xiaoshan.edu.enums.patrol.PatrolType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class PatrolSinglePageAO extends PatrolTimeNotNullPageAO {

    @ApiModelProperty("巡查对象id（班级id、教师id、学生id）")
    @NotNull
    private Long objectId;

    @ApiModelProperty("巡课类型")
    @NotNull
    @Enum
    private PatrolType type;

    @ApiModelProperty("巡课对象")
    @NotNull
    private String objectName;

}
