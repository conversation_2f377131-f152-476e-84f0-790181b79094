package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;

@Getter@Setter@ToString
public class AdjustmentCancelAO {

    @ApiModelProperty("调代课id")
    private Long id;

    @ApiModelProperty(value = "流程实例id",example = "9ff284ad-e854-11eb-8491-408d5ca430aa")
    private String processInstanceId;

    @ApiModelProperty("调代课类型【1 调课，2 代课】")
    @NotNull
    @IntegerValid(min = 1,max = 2)
    private Integer type;
}
