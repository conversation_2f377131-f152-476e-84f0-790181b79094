package com.xiaoshan.edu.vo.survey;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@Getter@Setter@ToString
public class TaskPageVO {

	@ApiModelProperty("调查任务id")
	private Long taskId;

	@ApiModelProperty("开始学年")
	private String startYear;

	@ApiModelProperty("结束学年")
	private String endYear;

	@ApiModelProperty("学期")
	private Integer term;

	@ApiModelProperty("年级")
	private String grade;

	@ApiModelProperty("调查任务")
	private String name;

	@ApiModelProperty("调查开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startDate;

	@ApiModelProperty("调查结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endDate;

	@ApiModelProperty("任务状态，1-待开始，2-进行中，3-已结束")
	private Integer state;

	@ApiModelProperty("学生总数")
	private Integer total;

	@ApiModelProperty("已完成学生数")
	private Integer finishNum = 0;

	@ApiModelProperty("是否公布")
	private Boolean isPublish;
}
