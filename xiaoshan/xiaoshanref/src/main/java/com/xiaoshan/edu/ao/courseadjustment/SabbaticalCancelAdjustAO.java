package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.lang.Nullable;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class SabbaticalCancelAdjustAO {

    @ApiModelProperty("调休日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date date;

    @ApiModelProperty("调休班次")
    private Integer dayOfWeek;
}
