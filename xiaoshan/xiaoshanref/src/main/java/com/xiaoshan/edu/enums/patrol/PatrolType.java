package com.xiaoshan.edu.enums.patrol;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

// 巡课类型
@Getter
@AllArgsConstructor
public enum PatrolType {
    /**
     * 班级
     */
    @ApiModelProperty("班级")
    CLASS("班级"),
    /**
     * 教师
     */
    @ApiModelProperty("教师")
    TEACHER("教师"),
    /**
     * 学生
     */
    @ApiModelProperty("学生")
    STUDENT("学生");

    private String name;
}
