package com.xiaoshan.edu.vo.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.persistence.annotation.Column;

@Data
public class BaseCommentVO {
    @ApiModelProperty("语言表达评分")
    private Float expression;

    @ApiModelProperty("学生互动评分")
    private Float interaction;

    @ApiModelProperty("时间分配评分")
    @Column("time_allocation")
    private Float timeAllocation;

    @ApiModelProperty("板书内容评分")
    @Column("blackboard_content")
    private Float blackboardContent;

    @ApiModelProperty("信息化评分")
    private Float informationize;

    @ApiModelProperty("总评分")
    private Float totalScore;
}
