package com.xiaoshan.edu.vo.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class GeneralCommentVO extends BaseCommentVO{

    @ApiModelProperty("课题")
    private String courseName;

    @ApiModelProperty("科目")
    private String subject;

    @ApiModelProperty("教师姓名")
    private String teacherName;

    @ApiModelProperty("上课时间")
    private String date;

    @ApiModelProperty("节次")
    private String section;

    @ApiModelProperty("公开课类型")
    private OpenClassType type;

    @ApiModelProperty("评分人数")
    private Integer totalPeople;
}
