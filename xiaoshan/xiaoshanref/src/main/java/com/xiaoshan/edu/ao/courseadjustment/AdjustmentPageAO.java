package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class AdjustmentPageAO extends TimePageAO {

    @ApiModelProperty("调课课程")
    private String courseName;

    @ApiModelProperty("调课教师")
    private String teacherName;

    @ApiModelProperty("调课时间（如：第一周周一第一节，可单独一级，如：第二周）")
    private String courseTime;

    @ApiModelProperty("被调课程")
    private String adjustedCourseName;

    @ApiModelProperty("被调教师")
    private String adjustedTeacherName;

    @ApiModelProperty("被调时间（如：第一周周一第一节，可单独一级，如：第二周）")
    private String adjustedCourseTime;

    @ApiModelProperty("审批编号")
    private String approvalNo;

    @ApiModelProperty("审批状态")
    @Enum
    private ApprovalState approvalState;

    @ApiModelProperty("循环方式(1-不循环，2-周循环，3-自定义时间段)")
    private Integer cycleType;

}
