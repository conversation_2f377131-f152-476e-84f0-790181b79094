package com.xiaoshan.edu.ao.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

@Getter
@Setter
@ToString
public class ClassRecordAO {

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date endDate;

    @ApiModelProperty("教师id")
    private Long teacherId;
}
