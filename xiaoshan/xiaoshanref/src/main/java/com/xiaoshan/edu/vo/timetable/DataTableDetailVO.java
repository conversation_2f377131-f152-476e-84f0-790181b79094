package com.xiaoshan.edu.vo.timetable;

import java.util.Map;

import com.xiaoshan.edu.converter.MapWeekMapStringStringConverterEditor;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;

@Getter@Setter@ToString
public class DataTableDetailVO {

	@ApiModelProperty("周类型")
	private WeekTypeEnum type;

	@ApiModelProperty("Map<周:Map<节次:课程>>")
	@PropertyConverter(MapWeekMapStringStringConverterEditor.class)
	private Map<WeekEnum, Map<String,String>> detail;

}
