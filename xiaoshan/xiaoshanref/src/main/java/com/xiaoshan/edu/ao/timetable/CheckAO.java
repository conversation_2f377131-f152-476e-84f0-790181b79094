package com.xiaoshan.edu.ao.timetable;

import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class CheckAO {
	
	@ApiModelProperty("上传ID")
	@NotNull
	@LongValid
	private Long uploadId;
    
    @ApiModelProperty("是否开启单双周")
    @NotNull
	@BooleanValid
    private Boolean singleWeek;
	
	@ApiModelProperty("学段ID")
    @NotNull
	@LongValid
	private Integer sectionId;
	
    @ApiModelProperty("入学年份")
    @NotNull
	@IntegerValid
	private Integer enrollmentYear;

	@ApiModelProperty("周类型")
	@Enum
	private WeekTypeEnum weekType;

	@ApiModelProperty("课表类型") 
	@NotNull @Enum 
	private CurriculumTableTypeEnum curriculumTableType;
	
}
