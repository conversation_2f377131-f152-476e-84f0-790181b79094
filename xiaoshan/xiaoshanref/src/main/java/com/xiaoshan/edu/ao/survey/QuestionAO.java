package com.xiaoshan.edu.ao.survey;

import com.xiaoshan.edu.converter.survey.CurriculumListConverterEditor;
import com.xiaoshan.edu.converter.survey.QuestionOptionListConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.*;
import start.magic.core.valid.annotation.number.IntegerValid;

import java.util.List;

@Data
public class QuestionAO {

    @ApiModelProperty("调查模板id")
    private Long templateId;

    @ApiModelProperty("题目类型")
    @NotNull
    @Enum
    private QuestionType questionType;

    @ApiModelProperty(value = "题干内容")
    @NotNull
    @NotEmpty
    @Length(max = 100)
    private String title;

    @ApiModelProperty(value = "题目选项，简答题传空数组")
    @NotNull
    @PropertyConverter(QuestionOptionListConverterEditor.class)
    private List<QuestionOptionDTO> items;

    @ApiModelProperty(value = "是否必填")
    @NotNull
    @BooleanValid
    private Boolean isRequired;

    @ApiModelProperty(value = "结果是否教师可见，若是单选、多选则传true")
    @NotNull
    @BooleanValid
    private Boolean isVisible;

    @ApiModelProperty(value = "关联课程列表，若是班主任传空数组")
    @PropertyConverter(CurriculumListConverterEditor.class)
    @NotNull
    private List<String> curriculumList;

    @ApiModelProperty(value = "题号")
    @NotNull
    @IntegerValid
    private Integer questionSort;

}
