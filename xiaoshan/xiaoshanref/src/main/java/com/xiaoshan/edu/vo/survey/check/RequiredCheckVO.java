package com.xiaoshan.edu.vo.survey.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RequiredCheckVO extends BaseCheckVO{

    @ApiModelProperty("是否必填")
    private String isRequired = "";

    public RequiredCheckVO(String isRequired, Boolean error){
        if (isRequired == null){
            isRequired = "";
        }
        this.isRequired = isRequired;
        this.error = error;
    }
}
