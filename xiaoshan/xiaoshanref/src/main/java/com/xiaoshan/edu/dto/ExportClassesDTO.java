package com.xiaoshan.edu.dto;

import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ExportClassesDTO {

    @ApiModelProperty("班级教室ID")
    private Long classOrRoomId;

    @ApiModelProperty("班级教室名称")
    private String classOrRoomName;

    @ApiModelProperty("标题")
    private String cellTitle;

    @ApiModelProperty("教师名称")
    private String teacherName;

    @ApiModelProperty("单周课程信息")
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceSingle;

    @ApiModelProperty("双周课程信息")
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceDouble;

}
