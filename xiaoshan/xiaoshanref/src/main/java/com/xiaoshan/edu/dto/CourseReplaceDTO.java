package com.xiaoshan.edu.dto;

import java.util.Date;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
public class CourseReplaceDTO {
    @ApiModelProperty("代课id")
    private Long id;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("代课开始时间【如：yyyy-MM-dd 第一节】")
    private String replaceStartTime;

    @ApiModelProperty("代课结束时间【如：yyyy-MM-dd 第一节】")
    private String replaceEndTime;

    @ApiModelProperty("代课节次")
    private Integer section;

    @ApiModelProperty("课程教师")
    private String teacherName;

    @ApiModelProperty("代课教师")
    private String replaceTeacherName;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("代课事由")
    private String content;

    @ApiModelProperty("图片url列表")
    private JsonArray picUrls;

    @ApiModelProperty("审批编号")
    private String approvalNo;

    @ApiModelProperty("审批状态")
    private ApprovalState approvalState;
}
