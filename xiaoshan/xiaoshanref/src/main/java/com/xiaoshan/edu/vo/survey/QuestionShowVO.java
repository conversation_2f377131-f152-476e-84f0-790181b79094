package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.converter.survey.QuestionOptionListConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.converter.PropertyConverter;

import java.util.List;

@Data
public class QuestionShowVO {
    // 此id是TaskTemplateStudentTeacherQuestionDO的id
    @ApiModelProperty("题目id")
    private Long questionId;

    @ApiModelProperty("题目")
    private String title;

    @ApiModelProperty("选项")
    private List<QuestionOptionDTO> items;

    @ApiModelProperty("题号")
    private Integer questionSort;

    @ApiModelProperty("题目类型")
    private QuestionType questionType;

    @ApiModelProperty("是否必填")
    private Boolean isRequired;

    @ApiModelProperty("是否完成填写")
    private Boolean isFinish;

    @ApiModelProperty("所选答案")
    @PropertyConverter(QuestionOptionListConverterEditor.class)
    private List<QuestionOptionDTO> answer;

}
