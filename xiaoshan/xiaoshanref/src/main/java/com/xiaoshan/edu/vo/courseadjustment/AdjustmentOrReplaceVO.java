package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AdjustmentOrReplaceVO {

    @ApiModelProperty("时间")
    private String courseTime;

    @ApiModelProperty("节次")
    private String sectionName;

    @ApiModelProperty("班级或教室名字")
    private String courseRoom;

    @ApiModelProperty(value = "班级或教室id")
    private String courseRoomId;

    @ApiModelProperty("老师")
    private String teacherName;

    @ApiModelProperty("课程")
    private String courseName;

    @ApiModelProperty("被调时间")
    private String adjustedCourseTime;

    @ApiModelProperty("被调节次")
    private String adjustedSectionName;

    @ApiModelProperty("被调班级或教室")
    private String adjustedCourseRoom;

    @ApiModelProperty(value = "班级或教室id")
    private String adjustedCourseRoomId;

    @ApiModelProperty("被调教师")
    private String adjustedTeacherName;

    @ApiModelProperty("被调课程")
    private String adjustedCourseName;

    @ApiModelProperty("是否自修（自习）")
    private Boolean isSelfStudy;

    @ApiModelProperty("代课老师")
    private String replaceTeacherName;
}
