package com.xiaoshan.edu.ao.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

// 时间范围查询
@Getter@Setter@ToString
public class OpenClassTimeAO {

    @ApiModelProperty("开始时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    @NotNull
    private java.sql.Date startDate;

    @ApiModelProperty("结束时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    @NotNull
    private java.sql.Date endDate;
}
