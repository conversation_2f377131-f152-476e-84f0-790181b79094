package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;

@Data
public class ApproveAO {

    @ApiModelProperty("调代课id")
    private Long id;

    @ApiModelProperty("审批状态")
    @NotNull
    @Enum
    private ApprovalState approvalState;

    @ApiModelProperty("调代课类型【1 调课，2 代课】")
    @NotNull
    @IntegerValid(min = 1,max = 2)
    private Integer type;

}
