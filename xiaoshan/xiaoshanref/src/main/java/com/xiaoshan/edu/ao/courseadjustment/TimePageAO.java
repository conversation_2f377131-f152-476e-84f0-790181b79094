package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;

import java.sql.Date;

@Getter@Setter@ToString
public class TimePageAO {

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty(value = "当前页数(默认:1)",example = "1")
    @IntegerValid(min=1)
    private int pageIndex = 1;

    @ApiModelProperty(value = "分页大小(默认:8)，查询所有传-1",example = "8")
    @IntegerValid(min=-1)
    private int pageSize = 8;

    public int index() {
        return (getPageIndex() - 1) * getPageSize();
    }

}
