package com.xiaoshan.edu.vo.timetable;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class LessonPrepareVO {
	
	@ApiModelProperty("课程ID")
	private Long curriculumId;

	@ApiModelProperty("学段ID")
	private Long sectionId;
	
	@ApiModelProperty("入学年份")
	private Integer enrollmentYear;
	
	@ApiModelProperty("课程") 
	private String abb;

	@ApiModelProperty("字典对应的编码") 
	private String code;
	
	@ApiModelProperty("老师列表")
	private List<TeacherAbbDetailVO> teachers;

}