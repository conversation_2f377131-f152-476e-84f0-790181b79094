package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;

@Getter@Setter@ToString
public class TestReplaceConflictAO {

    @ApiModelProperty("代课开始日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date replaceStartDate;

    @ApiModelProperty("代课结束日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date replaceEndDate;

    @ApiModelProperty("开始日期节次")
    @NotNull
    @NotEmpty
    private String startSection;

    @ApiModelProperty("开始日期节次")
    @NotNull
    @NotEmpty
    private String endSection;
}
