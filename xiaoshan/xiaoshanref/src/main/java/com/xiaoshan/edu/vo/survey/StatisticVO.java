package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.dto.survey.PercentageDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class StatisticVO {

    @ApiModelProperty(value = "题目", example = "本学期班主任早自习情况")
    private String title;

    @ApiModelProperty(value = "题目类型")
    private QuestionType questionType;

    @ApiModelProperty("题号")
    private Integer questionSort;

    @ApiModelProperty(value = "选择题列表，Map<(班级/课程)$姓名, Map<选项名, Map<key:value>>",
            example = "{'2401班$郭老师': {'一般': {'num': 0,'percentage': '0.00%'},'较多': {'num': 2,'percentage': '100.00%'},'较少': {'num': 0,'percentage': '0.00%'}}}")
    private HashMap<String, HashMap<String, PercentageDTO>> selectMap;

    @ApiModelProperty(value = "选择题平均列表",
            example = "{'年级平均': {'一般': {'num': 0,'percentage': '0.00%'},'较多': {'num': 2,'percentage': '100.00%'},'较少': {'num': 0,'percentage': '0.00%'}}}")
    private HashMap<String, PercentageDTO> avgMap;

    @ApiModelProperty(value = "填空题列表",
            example = "{'语文$周敏轩': ['希望老师可以多点关心多点爱']}")
    private Map<String, List<String>> writeMap;

}
