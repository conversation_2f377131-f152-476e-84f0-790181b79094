package com.xiaoshan.edu.ao.survey;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

@Getter@Setter@ToString
public class QuestionSubmitAO {

    @ApiModelProperty("学生id")
    @NotNull
    @LongValid
    private Long studentId;

    @ApiModelProperty("教师id")
    @NotNull
    @LongValid
    private Long teacherId;

    @ApiModelProperty("题目数据")
    @NotNull
    private List<QuestionData> data;


}
