package com.xiaoshan.edu.ao.timetable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TableBasicQuery {
	
	@ApiModelProperty("课程ID")
	@NotNull
	@LongValid
	private Long curriculumTableId;
	
}
