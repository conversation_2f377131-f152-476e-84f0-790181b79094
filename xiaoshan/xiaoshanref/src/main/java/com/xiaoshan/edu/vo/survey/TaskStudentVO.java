package com.xiaoshan.edu.vo.survey;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TaskStudentVO {

    @ApiModelProperty("学生id")
    private Long studentId;

    @ApiModelProperty("ic卡号")
    private String icCardNo;

    @ApiModelProperty("学号")
    private Long studentNo;

    @ApiModelProperty("学生姓名")
    private String name;

    @ApiModelProperty("班级名称")
    private String className;

    @ApiModelProperty("是否完成")
    private Boolean isFinish;
}
