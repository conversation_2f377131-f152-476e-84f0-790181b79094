package com.xiaoshan.edu.vo.courseadjustment;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Column;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
public class CourseReplaceVO {
    @ApiModelProperty("代课id")
    private Long id;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("代课时间")
    private String replaceTime;

    @ApiModelProperty("代课节次")
    private String section;

    @ApiModelProperty("课程教室")
    private String courseRoom;

    @ApiModelProperty("课程教师")
    private String teacherName;

    @ApiModelProperty("课程教师ID")
    private Long teacherId;

    @ApiModelProperty("代课教师")
    private String replaceTeacherName;

    @ApiModelProperty("代课教师ID")
    private Long replaceTeacherId;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("代课事由")
    private String content;

    @ApiModelProperty("图片url列表")
    private JsonArray picUrls;

    @ApiModelProperty("审批编号")
    private String approvalNo;

    @ApiModelProperty("审批状态")
    private String approvalState;

    @ApiModelProperty("代课节次详情列表")
    private List<ReplaceCourseDetailVO> replaceCourseDetailList;

    @ApiModelProperty("课表id【用于标识学期、年级】")
    private Long curriculumTableId;

    @ApiModelProperty("是否异常【因课表调整导致调代课冲突失效】")
    private Boolean isUnusual;
}
