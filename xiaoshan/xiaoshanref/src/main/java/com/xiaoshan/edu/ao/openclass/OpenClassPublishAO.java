package com.xiaoshan.edu.ao.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;
import start.magic.persistence.annotation.Column;

import java.sql.Time;

// 发布公开课
@Data
public class OpenClassPublishAO {

    @ApiModelProperty("公开课id，更新时必填")
    @LongValid
    private Long openClassId;

    @ApiModelProperty("课题名称")
    @NotNull
    @Length(max = 50)
    private String courseName;

    @ApiModelProperty("科目")
    @NotNull
    @Length
    private String subject;

    @ApiModelProperty("科目简称")
    @NotNull
    @Length
    private String abbreviation;

    @ApiModelProperty("教师姓名")
    @NotNull
    @Length
    private String teacherName;

    @ApiModelProperty("教师id")
    @NotNull
    @LongValid
    private Long teacherId;

    @ApiModelProperty("公开课类型")
    @NotNull
    @Enum
    private OpenClassType type;

    @ApiModelProperty("上课日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date date;

    @ApiModelProperty(value = "上课班级", example = "高中 高中2021届 2401")
    @NotNull
    @Length
    private String attendClass;

    @ApiModelProperty("周次")
    @NotNull
    @IntegerValid
    private Integer weekNum;

    @ApiModelProperty(value = "本周星期几",example = "星期三")
    @NotNull
    @Length
    private String weekName;

    @ApiModelProperty(value = "节次", example = "上午 第一节")
    @NotNull
    @Length
    private String section;

    @ApiModelProperty(value = "节次开始时间",example = "09:00:00")
    @NotNull
    private Time startTime;

    @ApiModelProperty(value = "地点，空格分隔", example = "行知楼 1层 404")
    @NotNull
    @Length
    private String place;

    @ApiModelProperty("课题简介")
    private String introduction;

    @ApiModelProperty(value = "是否展示new标签",hidden = true)
    private Boolean isNew;

    @ApiModelProperty("流程，若流程未开启，则传null或不传")
    @Column("startFlowAO")
    private OpenClassStartFlowAO startFlowAo;

}
