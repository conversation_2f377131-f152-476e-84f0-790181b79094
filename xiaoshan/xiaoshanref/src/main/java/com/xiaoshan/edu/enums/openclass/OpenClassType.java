package com.xiaoshan.edu.enums.openclass;

import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;

// 公开课类型
public enum OpenClassType {
    /**
     * 校内公开课
     */
    @ApiModelProperty("校内公开课")
    INNER_OPEN_CLASS,
    /**
     * 校内讲座
     */
    @ApiModelProperty("校内讲座")
    INNER_LECTURE,
    /**
     * 校际公开课
     */
    @ApiModelProperty("校际公开课")
    SCHOOL_OPEN_CLASS,
    /**
     * 云课堂
     */
    @ApiModelProperty("云课堂")
    CLOUD_CLASS;

    public String getDescription(){
        for (Field field : getClass().getFields()) {
            if (field.getName().equals(this.name())){
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                return annotation.value();
            }
        }
        return this.name();
    }

    public static void main(String[] args) {
        System.out.println(OpenClassType.CLOUD_CLASS.getDescription());
    }
}
