package com.xiaoshan.edu.ao.timetable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class PublishedAO extends CurriculumTableAO {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("是否发布")
	@NotNull
	@BooleanValid
	private Boolean published;
	
	@ApiModelProperty("上传ID")
	@NotNull
	@LongValid
	private Long uploadId;
	
}
