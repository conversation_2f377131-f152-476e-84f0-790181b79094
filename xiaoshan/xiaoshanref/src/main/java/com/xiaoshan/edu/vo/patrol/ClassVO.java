package com.xiaoshan.edu.vo.patrol;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ClassVO {

    @ApiModelProperty("学段id")
    private Long sectionId;

    @ApiModelProperty("学段")
    private String sectionName;

    @ApiModelProperty("年级")
    private String gradeName;

    @ApiModelProperty("年级顺序")
    private Integer gradeOrder;

    @ApiModelProperty("学段类型")
    private Integer sectionType;

    @ApiModelProperty("入学年份")
    private Integer enrollmentYear;

    @ApiModelProperty("班级id")
    private Long id;

    @ApiModelProperty("班主任Id")
    private Long headTeacherId;

    @ApiModelProperty("班级名")
    private String name;

    @ApiModelProperty("房间id")
    private Long roomId;

    @ApiModelProperty("教室名")
    private String roomName;

}
