package com.xiaoshan.edu.ao.patrol;

import com.xiaoshan.edu.enums.patrol.NotifyObject;
import com.xiaoshan.edu.enums.patrol.PatrolAppraise;
import com.xiaoshan.edu.enums.patrol.PatrolType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.thirdparty.json.JsonArray;

import java.util.List;

@Getter@Setter@ToString
public class PatrolSaveAO {

    @ApiModelProperty("巡课类型")
    @NotNull
    @Enum
    private PatrolType type;

    @ApiModelProperty("巡课班级或教室")
    @NotNull
    @NotEmpty
    private String className;

    @ApiModelProperty("巡课班级或教室Id")
    @NotNull
    private String classId;

    @ApiModelProperty("巡课时间（课程时间，如：第一周周一第一节）")
    @NotNull
    @NotEmpty
    private String courseTime;

    @ApiModelProperty("巡课科目（根据课表获取，如：语文）")
    @NotNull
    @NotEmpty
    private String courseName;

    @ApiModelProperty("课程教师Id（根据课表获取）")
    @NotNull
    private Long courseTeacherId;

    @ApiModelProperty("巡课老师")
    @NotNull
    @NotEmpty
    private String patrolTeacherName;

    @ApiModelProperty("巡课评价")
    @NotNull
    @Enum
    private PatrolAppraise appraise;

    @ApiModelProperty("巡课事由")
    @NotNull
    @NotEmpty
    private String content;

    @ApiModelProperty(value = "巡课照片urls", example = "[\"http://www.aaa.com\",\"http://www.bbb.com\"]")
    private JsonArray picUrls;

    @ApiModelProperty("巡课对象id（班级填classId、教室填roomId、教师填teacherId、学生填studentIds：使用,分隔）")
    @NotNull
    @NotEmpty
    private String objectId;

    @ApiModelProperty("是否通知")
    @NotNull
    @BooleanValid
    private Boolean isNotify;

    @ApiModelProperty("通知对象")
    @Enum
    private NotifyObject notifyObject;

    @ApiModelProperty("听课学生班级id集合")
    private List<Long> studentIds;

    @ApiModelProperty("选中的巡课学生的班级id集合")
    private List<Long> classIds;

}
