package com.xiaoshan.edu.ao.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
public class OpenClassCommentAO {

    @ApiModelProperty("公开课id")
    private Long openClassId;

    @ApiModelProperty("语言表达评分")
    @NotNull
    private Integer expression;

    @ApiModelProperty("学生互动评分")
    private Integer interaction;

    @ApiModelProperty("时间分配评分")
    private Integer timeAllocation;

    @ApiModelProperty("板书内容评分")
    private Integer blackboardContent;

    @ApiModelProperty("信息化评分")
    private Integer informationize;

    @ApiModelProperty("课程评价")
    private String comment;

    @ApiModelProperty("评价图片url列表")
    private JsonArray picUrls;
}
