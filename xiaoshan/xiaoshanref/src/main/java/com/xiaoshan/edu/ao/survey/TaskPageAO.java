package com.xiaoshan.edu.ao.survey;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@Getter@Setter@ToString
public class TaskPageAO extends PageAO {

	@ApiModelProperty("开始学年")
	private Integer startYear;

	@ApiModelProperty("学期")
	private Integer term;

	@ApiModelProperty("年级")
	private String grade;
	
	@ApiModelProperty("调查任务")
	private String name;

	@PropertyConverter(DateTimefConverterEditor.class)
	@ApiModelProperty("开始时间")
	private Date startDate;

	@PropertyConverter(DateTimefConverterEditor.class)
	@ApiModelProperty("结束时间")
	private Date endDate;
}
