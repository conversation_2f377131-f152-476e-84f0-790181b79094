package com.xiaoshan.edu.dto;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.thirdparty.json.JsonArray;

import java.util.Date;

@Getter@Setter@ToString
public class CourseAdjustmentDTO {
    @ApiModelProperty("调课id")
    private Long id;

    @ApiModelProperty("调课课程")
    private String courseName;

    @ApiModelProperty("调课时间")
    private String courseTime;

    @ApiModelProperty("调课教室")
    private String courseRoom;

    @ApiModelProperty("调课教师")
    private String teacherName;

    @ApiModelProperty("被调课程")
    private String adjustedCourseName;

    @ApiModelProperty("被调时间")
    private String adjustedCourseTime;

    @ApiModelProperty("被调教室")
    private String adjustedCourseRoom;

    @ApiModelProperty("被调教师")
    private String adjustedTeacherName;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("调课事由")
    private String content;

    @ApiModelProperty("图片url列表")
    private JsonArray picUrls;

    @ApiModelProperty("审批编号")
    private String approvalNo;

    @ApiModelProperty("审批状态")
    private ApprovalState approvalState;

    @ApiModelProperty("是否自修")
    private Boolean isSelfStudy;

    @ApiModelProperty("不纳入统计")
    private Boolean isNotStatistics;

    @ApiModelProperty("周循环调课")
    private Integer weekCycleNum;

    @ApiModelProperty("循环方式")
    private Integer cycleType;

    @ApiModelProperty("自定义开始时间")
    private Date customStartTime;

    @ApiModelProperty("自定义结束时间")
    private Date customEndTime;

    @ApiModelProperty("是否衍生数据")
    private String attachment;

    @ApiModelProperty("衍生数据父Id")
    private Long mainId;
}
