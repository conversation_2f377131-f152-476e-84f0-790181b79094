package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class CourseTableAdjustmentAO {

    @ApiModelProperty("调代类型")
    @Enum
    @NotNull
    private AbbTypeEnum type;

    @ApiModelProperty("调代课ID")
    @NotNull
    @LongValid
    private Long adjustmentId;

    @ApiModelProperty("课程时间：如：第一周周一第一节【调代课类型为代课，必传】")
    private String courseTime;
}
