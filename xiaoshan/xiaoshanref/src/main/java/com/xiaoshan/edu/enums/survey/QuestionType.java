package com.xiaoshan.edu.enums.survey;

import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;

public enum QuestionType {

    /**
     * 单选题
     */
    @ApiModelProperty("单选题")
    SINGLE,

    /**
     * 多选题
     */
    @ApiModelProperty("多选题")
    MULTIPLE,

    /**
     * 简答题
     */
    @ApiModelProperty("简答题")
    ANSWER;

    public String getDesc(){
        for (Field field : getClass().getFields()) {
            if (field.getName().equals(this.name())){
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                return annotation.value();
            }
        }
        return this.name();
    }

    public static void main(String[] args) {
        System.out.println(QuestionType.MULTIPLE.name());
    }

}
