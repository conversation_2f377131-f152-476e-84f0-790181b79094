package com.xiaoshan.edu.vo.courseadjustment;

import com.xiaoshan.edu.vo.timetable.GeneralTableVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.persistence.annotation.Column;

import java.util.List;

@Getter
@Setter
@ToString
public class ClassTableVO {

    @ApiModelProperty("班级课表")
    @Column("classTableVOList")
    List<GeneralTableVO> classTableVoList;

//    // 课表ID从详细数据中获取
//    @ApiModelProperty("被调课课表ID")
//    Long adjustedCurriculumTableId;

    @ApiModelProperty(value = "走班课课程时间",hidden = true)
    List<String> courseTimes;
}
