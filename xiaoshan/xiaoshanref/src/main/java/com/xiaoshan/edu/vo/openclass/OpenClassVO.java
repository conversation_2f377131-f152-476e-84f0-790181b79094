package com.xiaoshan.edu.vo.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassApprovalState;
import com.xiaoshan.edu.enums.openclass.OpenClassConfirmState;
import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.sql.Time;
import java.util.Date;

// 公开课记录VO
@Data
public class OpenClassVO {

    @ApiModelProperty("公开课id")
    private Long openClassId;

    @ApiModelProperty("课题")
    private String courseName;

    @ApiModelProperty("科目")
    private String subject;

    @ApiModelProperty("教师姓名")
    private String teacherName;

    @ApiModelProperty("教师id")
    private Long teacherId;

    @ApiModelProperty("公开课类型")
    private OpenClassType type;

    @ApiModelProperty("上课日期")
    private String date;

    @ApiModelProperty("节次")
    private String section;

    @ApiModelProperty("上课班级")
    private String attendClass;

    @ApiModelProperty("上课地点")
    private String place;

    @ApiModelProperty("总评分数")
    private String score;

    @ApiModelProperty("上课状态，true-已结束,false-待开课")
    private Boolean courseState;

    @ApiModelProperty("确认状态")
    private OpenClassConfirmState confirmState;

    @ApiModelProperty("审批状态")
    private OpenClassApprovalState approvalState;

    @ApiModelProperty("听课教师数量")
    private Integer listenTeacherNum = 0;

    @ApiModelProperty("是否已经选择听课")
    private Boolean listen;

    @ApiModelProperty("是否评价完成，true-评价完成，false-未评价")
    private Boolean evaluated = false;

    @ApiModelProperty("当管理员更新公开课信息后为new")
    private Boolean isNew;

    @ApiModelProperty("课程简介")
    private String introduction;

    @ApiModelProperty("发起时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("课程简称")
    private String abbreviation;

    @ApiModelProperty("开始时间")
    private Time startTime;

    @ApiModelProperty("审批编号")
    private String approvalCode = "";
}
