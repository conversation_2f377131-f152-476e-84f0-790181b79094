package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;

import java.sql.Date;

@Getter@Setter@ToString
public class ReplacePageAO extends MyReplacePageAO {
    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课程教师")
    private String teacherName;

    @ApiModelProperty("代课开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date replaceStartDate;

    @ApiModelProperty("代课结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date replaceEndDate;

    @ApiModelProperty("审批编号")
    private String approvalNo;

    @ApiModelProperty(value = "查询所有【课程教师和代课教师】",hidden = true)
    private Boolean queryAll = false;
}
