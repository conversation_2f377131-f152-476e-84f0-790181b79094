package com.xiaoshan.edu.ao.timetable;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.edu.enums.timetable.CurriculumTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;

@Getter
@Setter
@ToString
public class CurriculumPageAO extends PageAO {
	
	@ApiModelProperty("课程编码")
	@Length
	private String code;

	@ApiModelProperty("课程名称")
	@Length
	private String name;

	@ApiModelProperty("课程简称")
	@Length
	private String abbreviation;

	@ApiModelProperty("课程性质")
	@Enum
	private CurriculumTypeEnum type;

	@ApiModelProperty("备注")
	@Length(min = 0)
	private String remark;

}
