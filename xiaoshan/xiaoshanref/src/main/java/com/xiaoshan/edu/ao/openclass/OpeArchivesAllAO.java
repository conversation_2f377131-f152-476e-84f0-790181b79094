package com.xiaoshan.edu.ao.openclass;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class OpeArchivesAllAO {

    @ApiModelProperty("开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date startDate;

    @ApiModelProperty("结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private java.sql.Date endDate;

    @ApiModelProperty("教师id")
    @NotNull
    @LongValid
    private Long teacherId;

}
