package com.xiaoshan.edu.ao.timetable;

import com.xiaoshan.edu.enums.timetable.CurriculumTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class CurriculumAO {
	
	@ApiModelProperty("课程ID")
	@LongValid
	private Long curriculumId;

	@ApiModelProperty("课程编码")
	@NotNull
	@Length
	private String code;

	@ApiModelProperty("课程名称")
	@NotNull
	@Length
	private String name;

	@ApiModelProperty("课程简称")
	@NotNull
	@Length
	private String abbreviation;

	@ApiModelProperty("课程性质")
	@NotNull
	@Enum
	private CurriculumTypeEnum type;

	@ApiModelProperty("备注")
	@NotNull
	@Length(min = 0)
	private String remark;

}
