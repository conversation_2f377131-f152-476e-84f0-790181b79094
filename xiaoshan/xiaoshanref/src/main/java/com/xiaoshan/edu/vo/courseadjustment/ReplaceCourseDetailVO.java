package com.xiaoshan.edu.vo.courseadjustment;

import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ReplaceCourseDetailVO {
    @ApiModelProperty("课程时间：如：第一周周一第一节")
    private String courseTime;

    @ApiModelProperty("课程班级或教室")
    private String courseRoom;

    @ApiModelProperty("课程班级或教室id（教室以ROOM结尾）")
    private String courseRoomId;

    @ApiModelProperty("课程开始时间")
    private String startTime;

    @ApiModelProperty("课程结束时间")
    private String endTime;

    @ApiModelProperty("（二）调代类型")
    private AbbTypeEnum type;

    @ApiModelProperty("（二）调代课ID")
    private Long adjustmentId;

    @ApiModelProperty("代课课表ID【发起时需要】")
    private Long curriculumTableId;
}
