package com.xiaoshan.edu.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.converter.MapWeekMapStringStringConverterEditor;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherTableDetailVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TeacherTableDTO extends TeacherTableDetailVO {

    /**
     *
     */
    private static final long serialVersionUID = 7179680378637421573L;

    @ApiModelProperty(value = "Map<周:Map<节次:班级或教室ID>>", hidden = true)
    @PropertyConverter(MapWeekMapStringStringConverterEditor.class)
    private Map<WeekEnum, Map<String, String>> detail;


    @ApiModelProperty("Map<周:Map<节次:{课程、班级或教室ID名称、教师ID名称}>>")
    @PropertyConverter(MapWeekMapStringStringConverterEditor.class)
    private Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailv;

    @ApiModelProperty("课程")
    private String courseName;

    public void setResult(Map<WeekEnum, Map<String, String>> detail) {
        detailv = new HashMap<>();
        for (WeekEnum week : detail.keySet()) {
            Map<String, String> data = detail.get(week);
            Map<String, List<TeacherAbbDetailVO>> vv = new HashMap<>();
            for (String section : data.keySet()) {
                String[] dataStr = data.get(section).split(Constant.SPLITDATASTRING);
                if (dataStr.length == 6) {
                    List<TeacherAbbDetailVO> abbList = new ArrayList<>();
                    TeacherAbbDetailVO vo = new TeacherAbbDetailVO();
                    vo.setAbbreviation(dataStr[0]);
                    vo.setCourseName(dataStr[1]);
                    vo.setClassOrRoomId(dataStr[2]);
                    vo.setClassOrRoomName(dataStr[3]);
                    vo.setTeacherId(Long.parseLong(dataStr[4]));
                    vo.setTeacherName(dataStr[5]);
                    vo.setType(AbbTypeEnum.NORMAL);
                    abbList.add(vo);
                    vv.put(section, abbList);
                }
            }
            detailv.put(week, vv);
        }
        setDetail(null);
    }

}
