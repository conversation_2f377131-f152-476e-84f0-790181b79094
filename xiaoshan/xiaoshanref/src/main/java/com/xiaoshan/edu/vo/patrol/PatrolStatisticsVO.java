package com.xiaoshan.edu.vo.patrol;

import com.xiaoshan.edu.enums.patrol.PatrolAppraise;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class PatrolStatisticsVO {

    @ApiModelProperty("巡查对象id（教师id、学生id、班级id）")
    private Long objectId;

    @ApiModelProperty("名字（教师、学生、班级）")
    private String objectName;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("学号")
    private String studentNo;

    @ApiModelProperty("年级")
    private String gradeName;

    @ApiModelProperty("班级")
    private String className;

    @ApiModelProperty(value = "记录时间", hidden = true)
    private Date recordTime;

    @ApiModelProperty(value = "巡课评价", hidden = true)
    private PatrolAppraise appraise;

    @ApiModelProperty("被巡课次数")
    private String totalCount = "0次";

    @ApiModelProperty("表现优秀次数")
    private String excellentCount = "0次";

    @ApiModelProperty("表现一般次数")
    private String commonCount = "0次";

    @ApiModelProperty("表现较差次数")
    private String badCount = "0次";

}
