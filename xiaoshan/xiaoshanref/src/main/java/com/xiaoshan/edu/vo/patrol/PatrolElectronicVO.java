package com.xiaoshan.edu.vo.patrol;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PatrolElectronicVO {

    @ApiModelProperty("巡课时间（课程时间）")
    private String courseTime;

    @ApiModelProperty("巡课班级")
    private String className;

    @ApiModelProperty("巡课老师")
    private String patrolTeacherName;

    @ApiModelProperty("巡课评价")
    private String appraise;

    @ApiModelProperty("巡课id")
    private Long id;

    @ApiModelProperty("巡课对象")
    private String objectName;

    @ApiModelProperty("巡课对象id")
    private Long objectId;

    @ApiModelProperty(value = "排序值1",hidden = true)
    private Integer weekNumber;

    @ApiModelProperty(value = "排序值2",hidden = true)
    private Integer dayNumber;

    @ApiModelProperty(value = "排序值3",hidden = true)
    private Integer sectionNumber;
}
