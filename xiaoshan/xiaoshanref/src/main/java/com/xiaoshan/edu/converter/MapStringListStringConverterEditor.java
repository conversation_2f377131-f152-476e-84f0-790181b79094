package com.xiaoshan.edu.converter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import start.magic.core.converter.AbstractConverterEditor;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;

public class MapStringListStringConverterEditor extends AbstractConverterEditor<String> {
	
	public MapStringListStringConverterEditor(Class<?> prototype) {
		super(prototype);
	}
	
	@Override
	public void restore(Object value) {
		if (value != null) {
			Class<?> target=value.getClass();
			JsonObject json;
			if(target.equals(JsonObject.class)) {
				json=(JsonObject)value;
			}else {
				json=new JsonObject(String.valueOf(value));
			}
			Map<String,List<String>> map=new HashMap<>();
			Iterator<String> keys=json.keys();
			while(keys.hasNext()) {
				String key=keys.next();
				List<String> list=map.get(key);
				if(list==null) {
					list=new ArrayList<>();
					map.put(key, list);
				}
				JsonArray array=json.getJsonArray(key);
				for(int i=0;i<array.length();i++) {
					list.add(array.getString(i));
				}
			}
			setValue(map);
		}
	}
	
	@Override
	public String converter() {
		if (getSource() == null) {
			return null;
		}
		return getSource().toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setValue(Object v) {
		super.setValue(v);
		if(getValue()!=null) {
			Map<String,List<String>> val=(Map<String,List<String>>)getValue();
			JsonObject json=new JsonObject();
			for(String k:val.keySet()) {
				List<String> abbs=val.get(k);
				JsonArray array=new JsonArray();
				for(String a:abbs) {
					array.put(a);
				}
				json.put(k, array);
			}
			setSource(json);
		}
	}


}
