package com.xiaoshan.edu.ao.survey;

import com.topnetwork.ao.PageAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class TaskStudentAO extends PageAO {

    @ApiModelProperty("调查任务id")
    @NotNull
    @LongValid
    private Long taskId;

    @ApiModelProperty("班级名称")
    private String className;

    @ApiModelProperty("学生姓名")
    private String name;

    @ApiModelProperty("是否已完成")
    private Boolean isFinish;
}
