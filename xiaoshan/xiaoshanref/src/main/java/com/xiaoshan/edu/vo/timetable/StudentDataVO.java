package com.xiaoshan.edu.vo.timetable;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class StudentDataVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("学生Id")
	private Long studentId;

	@ApiModelProperty("数据")
	private List<StudentDataRelationsItemsVO> releations;

}
