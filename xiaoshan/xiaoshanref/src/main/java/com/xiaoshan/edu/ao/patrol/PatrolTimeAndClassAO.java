package com.xiaoshan.edu.ao.patrol;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class PatrolTimeAndClassAO {

    @ApiModelProperty("班级或教室id")
    @NotNull
    @LongValid
    private Long classOrRoomId;

    @ApiModelProperty("巡课时间：如：第一周周一第一节")
    @NotNull
    @NotEmpty
    private String courseTime;

    @ApiModelProperty("班级类型：1 行政班, 2 其他班级")
    @NotNull
    @IntegerValid(min = 1, max = 2)
    private Integer classType;

}
