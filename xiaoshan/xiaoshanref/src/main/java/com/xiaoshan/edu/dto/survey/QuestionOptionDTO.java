package com.xiaoshan.edu.dto.survey;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("题目选项")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionOptionDTO {

    @ApiModelProperty(value = "选项名", example = "A")
    private String option;

    @ApiModelProperty(value = "选项值", example = "优秀")
    private String txt;
}
