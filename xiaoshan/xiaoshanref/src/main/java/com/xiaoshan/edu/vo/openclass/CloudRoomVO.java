package com.xiaoshan.edu.vo.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class CloudRoomVO {

    public CloudRoomVO(){}

    public CloudRoomVO(String buildingName, String floorName, String roomName) {
        this.buildingName = buildingName;
        this.floorName = floorName;
        this.roomName = roomName;
    }

    @ApiModelProperty("云课堂教室id")
    private Long cloudRoomId;

    @ApiModelProperty("楼宇名称")
    private String buildingName;

    @ApiModelProperty("楼层名称")
    private String floorName;

    @ApiModelProperty("房间名称")
    private String roomName;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CloudRoomVO that = (CloudRoomVO) o;
        return Objects.equals(buildingName, that.buildingName) && Objects.equals(floorName, that.floorName) && Objects.equals(roomName, that.roomName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(buildingName, floorName, roomName);
    }
}
