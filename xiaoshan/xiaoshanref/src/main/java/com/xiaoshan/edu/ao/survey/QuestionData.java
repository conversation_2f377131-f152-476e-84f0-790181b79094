package com.xiaoshan.edu.ao.survey;

import com.xiaoshan.edu.converter.survey.QuestionOptionListConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

@Data
public class QuestionData {

    @ApiModelProperty("题目id")
    @NotNull
    @LongValid
    private Long questionId;

    @ApiModelProperty(value = "答案,如果空必须传空数组，如果是简答题，[{'option':'题号','txt':'答案'}]", example = "[{'option':'A','txt':'良好'}]")
    @NotNull
    @PropertyConverter(QuestionOptionListConverterEditor.class)
    private List<QuestionOptionDTO> answer;
}
