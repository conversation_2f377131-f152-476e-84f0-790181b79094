package com.xiaoshan.edu.enums.openclass;

import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;

// 公开课审批状态
public enum OpenClassApprovalState {
    /**
     * 已通过
     */
    @ApiModelProperty("已通过")
    PASS,
    /**
     * 审批中
     */
    @ApiModelProperty("审批中")
    UNDER_APPROVAL,
    /**
     * 已驳回
     */
    @ApiModelProperty("已驳回")
    REJECT,
    /**
     * 已撤销
     */
    @ApiModelProperty("已撤销")
    REVOKED;

    public String getDescription(){
        for (Field field : getClass().getFields()) {
            if (field.getName().equals(this.name())){
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                return annotation.value();
            }
        }
        return name();
    }
}
