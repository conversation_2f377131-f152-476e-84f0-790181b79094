package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.lang.Nullable;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.persistence.annotation.Column;

import java.sql.Date;
import java.util.List;

@Getter@Setter@ToString
public class HolidaysOrSabbaticalAO {
    @ApiModelProperty("节假日开始日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    @Nullable
    private Date startDate;

    @ApiModelProperty("节假日结束日期")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    @Nullable
    private Date endDate;

    @ApiModelProperty("调休")
    @Nullable
    @Column("sabbaticalCancelAdjustAOS")
    private List<SabbaticalCancelAdjustAO> sabbaticalCancelAdjustAos;
}
