package com.xiaoshan.edu.vo.timetable;

import java.io.Serializable;
import java.util.List;

import com.xiaoshan.edu.converter.DataTableConverterEditor;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class GeneralTableVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据")
    @PropertyConverter(DataTableConverterEditor.class)
    private List<DataTableDTO> data;

    @ApiModelProperty("周类型")
    private WeekTypeEnum type;

}
