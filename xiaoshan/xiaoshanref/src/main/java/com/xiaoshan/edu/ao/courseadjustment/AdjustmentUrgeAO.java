package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class AdjustmentUrgeAO {

    @ApiModelProperty("流程Id")
    @NotNull
    @LongValid
    private Long flowId;

    @ApiModelProperty("调代课id")
    @NotNull
    @LongValid
    private Long id;

    @ApiModelProperty("调代课类型【1 调课，2 代课】")
    @NotNull
    @IntegerValid(min = 1,max = 2)
    private Integer type;

}
