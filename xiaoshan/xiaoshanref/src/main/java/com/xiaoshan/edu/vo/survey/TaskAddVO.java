package com.xiaoshan.edu.vo.survey;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;
import java.util.List;

@Getter@Setter@ToString
public class TaskAddVO {

	@ApiModelProperty("调查任务id")
	private Long taskId;

	@ApiModelProperty(value = "开始学年")
	private Integer startYear;

	@ApiModelProperty(value = "结束学年")
	private Integer endYear;

	@ApiModelProperty("学期，1-第一学期，2-第二学期")
	private Integer term;

	@ApiModelProperty("调查名称")
	private String name;

	@ApiModelProperty("调查年级，初一到高三")
	private String grade;

	@ApiModelProperty("调查开始日期")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startDate;

	@ApiModelProperty("调查结束日期")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endDate;

	@ApiModelProperty("调查提醒")
	private String reminder;

	@ApiModelProperty("调查模板列表")
	private List<TaskTemplateVO> templateList;

	@ApiModelProperty("调查说明")
	private String explanation;

	@ApiModelProperty("创建时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private java.util.Date createdDate;

	@ApiModelProperty("创建时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private java.util.Date modifiedDate;
}
