package com.xiaoshan.edu.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 临时课表数据
 * <AUTHOR>
 *
 */
@Getter@Setter@ToString
public class TempCurriculumTableDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("上传ID")
	private Long uploadId;
	
	@ApiModelProperty("课表类型")
	private CurriculumTableTypeEnum type;
	
	@ApiModelProperty("周类型")
	private WeekTypeEnum weekType;

	@ApiModelProperty("Excel映射数据Map<Sheet,每行数据>")
	private Map<String,List<Map<Integer,String>>> data;
	
}
