package com.xiaoshan.edu.vo.timetable;

import com.xiaoshan.edu.enums.timetable.CurriculumTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class CurriculumVO {

	@ApiModelProperty("课程ID")
	private String curriculumId;
	
	@ApiModelProperty("课程编码")
	private String code;

	@ApiModelProperty("课程名称")
	private String name;

	@ApiModelProperty("课程简称")
	private String abbreviation;

	@ApiModelProperty("课程性质")
	private CurriculumTypeEnum type;

	@ApiModelProperty("系统级别")
	private Boolean sys;

	@ApiModelProperty("备注")
	private String remark;

}
