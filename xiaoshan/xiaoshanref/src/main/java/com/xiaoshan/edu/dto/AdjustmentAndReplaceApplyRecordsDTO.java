package com.xiaoshan.edu.dto;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Column;

import java.util.Date;

@Getter@Setter@ToString
public class AdjustmentAndReplaceApplyRecordsDTO {

    @ApiModelProperty("调代课类型【1：调课，2：代课】")
    private String type;

    @ApiModelProperty("调代课id")
    private Long id;

    @ApiModelProperty("审批状态")
    private ApprovalState approvalState;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("是否异常")
    private Boolean isUnusual;

    @ApiModelProperty("调课时间")
    private String courseTime;

    @ApiModelProperty("被调时间【如果是自修，此项为空】")
    private String adjustedCourseTime;

    @ApiModelProperty("代课开始时间【yyyy-MM-dd 第一节】")
    private String replaceStartTime;

    @ApiModelProperty("代课结束时间【yyyy-MM-dd 第一节】")
    private String replaceEndTime;

    @ApiModelProperty("是否自修")
    private Boolean isSelfStudy;

    @ApiModelProperty("周循环调课次数")
    private Integer weekCycleNum;

    @ApiModelProperty("循环方式")
    private Integer cycleType;

    @ApiModelProperty("自定义开始时间")
    private Date customStartTime;

    @ApiModelProperty("自定义结束时间")
    private Date customEndTime;

}
