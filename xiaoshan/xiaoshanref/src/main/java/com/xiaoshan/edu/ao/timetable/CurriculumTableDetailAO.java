package com.xiaoshan.edu.ao.timetable;

import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class CurriculumTableDetailAO {
	
	@ApiModelProperty("课表ID")
	@LongValid
	private Long curriculumTableId;
	
	@ApiModelProperty("周几")
	@Enum
	private WeekEnum week;

	@ApiModelProperty("第几节课")
	@IntegerValid
	private Integer section;

	@ApiModelProperty("周类型")
	@Enum
	private WeekTypeEnum type;

	@ApiModelProperty("教室、老师、班级名称")
	@Length
	private String name;

}
