package com.xiaoshan.edu.enums.courseadjustment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum AdjustmentCheckTypeEnum {

    /**
     * 调课信息中存在冲突
     */
    REPETITION_COURSE(1,"调课信息中存在冲突"),
    /**
     * 被调课程信息存在冲突
     */
    REPETITION_ADJUSTED_COURSE(2,"被调课程信息存在冲突"),
    /**
     * 调课时间不在循环时间段内
     */
    CYCLE(3,"调课时间不在循环时间段内"),
    /**
     * 正确
     */
    CORRECT(4,"正确"),
    /**
     * 调课信息与被调课程信息存在冲突
     */
    ADJUSTED_COURSE(5,"调课信息与被调课程信息存在冲突"),
    /**
     * 存在审批中的调课信息
     */
    APPROVAL_ERROR(6,"已经有审批中调课信息"),
    /**
     * 存在审批中的代课信息
     */
    REPLACE_ERROR(7,"已经有审批中的代课信息"),
    /**
     * 存在相同的信息
     */
    REPEAT_ERROR(8,"两条相同的信息")
    ;

    private Integer type;
    private String name;

}
