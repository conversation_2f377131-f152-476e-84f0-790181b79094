package com.xiaoshan.edu.enums.timetable;

import java.lang.reflect.Field;

import io.swagger.annotations.ApiModelProperty;

public enum CurriculumTableTypeEnum {

	/**
	 * 总课表
	 */
	@ApiModelProperty("总课表")
	GENERAL,
	/**
	 *班级课表
	 */
	@ApiModelProperty("班级课表")
	CLASSES,
	/**
	 *教师课表
	 */
	@ApiModelProperty("教师课表")
	TEACHER,
	/**
	 *学生课表
	 */
	@ApiModelProperty("学生课表")
	STUDENT;
	
	public String getDescription() {
		for (Field f : CurriculumTableTypeEnum.class.getFields()) {
			if (this.name().equals(f.getName())) {
				ApiModelProperty property = f.getAnnotation(ApiModelProperty.class);
				if (property != null) {
					return property.value();
				}
			}
		}
		return this.name();
	}
	
	public static void main(String[] args) {
		System.out.println(CurriculumTableTypeEnum.GENERAL.getDescription());
	}
	
}
