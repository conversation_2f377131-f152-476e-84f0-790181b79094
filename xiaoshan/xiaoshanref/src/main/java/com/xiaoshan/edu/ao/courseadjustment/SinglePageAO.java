package com.xiaoshan.edu.ao.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class SinglePageAO extends TimeNotNullPageAO{
    @ApiModelProperty("教师姓名")
    @NotNull
    @NotEmpty
    private String teacherName;

    @ApiModelProperty("调代课类型：1调课，2代课，数据导出时必填")
    private Integer type;
}
