package com.xiaoshan.edu.ao.timetable;

import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.enums.timetable.WeekEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class StudentListQuery extends TableBasicQuery {

    @ApiModelProperty("周")
    @NotNull
    @Enum
    private WeekEnum week;

    @ApiModelProperty("节次")
    @NotNull
    @Length
    private String section;

    @ApiModelProperty("课程简称走班课请用" + Constant.SPLITSTRING + "分隔(例:语、数)")
    @NotNull
    @Length
    private String abbreviation;


    @ApiModelProperty("班级或教室ID")
    @NotNull
    @Length
    private String classOrRoomId;

}
