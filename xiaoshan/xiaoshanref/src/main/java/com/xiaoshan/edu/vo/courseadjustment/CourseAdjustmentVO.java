package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.thirdparty.json.JsonArray;

import java.util.Date;
import java.util.List;

@Data
public class CourseAdjustmentVO {
    @ApiModelProperty("调课id")
    private Long id;

    @ApiModelProperty("调课课程")
    private String courseName;

    @ApiModelProperty("调课时间")
    private String courseTime;

    @ApiModelProperty("调课教室")
    private String courseRoom;

    @ApiModelProperty("调课教师")
    private String teacherName;

    @ApiModelProperty("被调课程")
    private String adjustedCourseName;

    @ApiModelProperty("被调时间")
    private String adjustedCourseTime;

    @ApiModelProperty("被调教室")
    private String adjustedCourseRoom;

    @ApiModelProperty("被调教师")
    private String adjustedTeacherName;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("调课事由")
    private String content;

    @ApiModelProperty("图片url列表")
    private JsonArray picUrls;

    @ApiModelProperty("审批编号")
    private String approvalNo;

    @ApiModelProperty("审批状态")
    private String approvalState;

    @ApiModelProperty("是否自修")
    private Boolean isSelfStudy;

    @ApiModelProperty("被调班级或教室id（教室以ROOM结尾）")
    private String adjustedCourseRoomId;

    @ApiModelProperty("被调教师id")
    private Long adjustedTeacherId;

    @ApiModelProperty("调课班级或教室id")
    private String courseRoomId;

    @ApiModelProperty("调课课表id【用于标识学期、年级】")
    private Long curriculumTableId;

    @ApiModelProperty("被调课课表id【用于标识学期、年级】")
    private Long adjustedCurriculumTableId;

    @ApiModelProperty("统计情况")
    private String statistics;

    @ApiModelProperty("周循环调课次数")
    private String weekCycle;

    @ApiModelProperty("周循环调课详情列表")
    private List<String> weekCycleList;

    @ApiModelProperty("如果是周循环数据不为空，审批流relatedId需要替换成此ID")
    private Long mainId;

    @ApiModelProperty("是否异常【因课表调整导致调代课冲突失效】")
    private Boolean isUnusual;

    @ApiModelProperty("循环方式")
    private String cycleType;

    @ApiModelProperty("循环方式类型")
    private Integer cycleTypeInt;

    @ApiModelProperty("自定义开始时间")
    private String customStartTime;

    @ApiModelProperty("自定义结束时间")
    private String customEndTime;
}