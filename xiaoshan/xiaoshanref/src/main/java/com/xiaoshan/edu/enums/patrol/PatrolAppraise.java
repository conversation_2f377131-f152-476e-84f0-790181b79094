package com.xiaoshan.edu.enums.patrol;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

// 巡课评价
@Getter
@AllArgsConstructor
public enum PatrolAppraise {
    /**
     * 表现优秀
     */
    @ApiModelProperty("表现优秀")
    EXCELLENT("表现优秀"),
    /**
     * 表现一般
     */
    @ApiModelProperty("表现一般")
    COMMON("表现一般"),
    /**
     * 表现较差
     */
    @ApiModelProperty("表现较差")
    BAD("表现较差");

    private String name;
}
