package com.xiaoshan.edu.vo.timetable;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.converter.MapDataTableConverterEditor;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TeacherTableVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("周类型")
    private WeekTypeEnum type;

    @ApiModelProperty("课程->List<教师ID:Map<周:Map<节次:班级教室ID>>>")
    @PropertyConverter(MapDataTableConverterEditor.class)
    private Map<String, List<TeacherTableDTO>> data;

}
