package com.xiaoshan.edu.vo.timetable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class CurriculumTableVO {

    @ApiModelProperty("课表ID")
    private Long curriculumTableId;

    @ApiModelProperty("学段ID")
    private Long sectionId;

    @ApiModelProperty("入学年份")
    private Integer enrollmentYear;

    @ApiModelProperty("学期ID")
    private Long semesterId;

    @ApiModelProperty("学期开始")
    private Integer startYear;

    @ApiModelProperty("学期结束")
    private Integer endYear;

    @ApiModelProperty("学期")
    private Integer orderNo;

    @ApiModelProperty("是否单双周")
    private Boolean singleWeek;

    @ApiModelProperty("上传ID")
    private Long uploadId;

    @ApiModelProperty("是否发布")
    private Boolean published;

    @ApiModelProperty("是否首次已发布")
    private Boolean firstPublished;

    @ApiModelProperty("年级")
    private String gradeName;

    public String getGradeName() {
        if (sectionId == 1) {
            return "高中" + (enrollmentYear + 3) + "届";
        } else {
            return "初中" + (enrollmentYear + 3) + "届";
        }
    }

}
