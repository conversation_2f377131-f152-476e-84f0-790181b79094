package com.xiaoshan.edu.ao.survey;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;

import java.util.Date;
import java.util.List;

@Getter@Setter@ToString
public class TaskAddAO {

	@ApiModelProperty("调查任务id，编辑时传入")
	private Long taskId;

	@ApiModelProperty(value = "开始学年")
	@NotNull
	@IntegerValid
	private Integer startYear;

	@ApiModelProperty(value = "结束学年")
	@NotNull
	@IntegerValid
	private Integer endYear;

	@ApiModelProperty("学期，1-第一学期，2-第二学期")
	@NotNull
	@IntegerValid
	private Integer term;

	@ApiModelProperty("调查名称")
	@NotNull
	@Length(max = 30)
	private String name;

	@ApiModelProperty("调查年级，初一到高三")
	@NotNull
	@Length(min = 2, max = 2)
	private String grade;

	@ApiModelProperty("调查开始日期")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date startDate;

	@ApiModelProperty("调查结束日期")
	@PropertyConverter(DateTimefConverterEditor.class)
	@NotNull
	private Date endDate;

	@ApiModelProperty("调查提醒")
	@Length(min = 0, max = 200)
	private String reminder;

	@ApiModelProperty("调查模板列表")
	@NotNull
	private List<TaskTemplateAO> templateList;

	@ApiModelProperty("调查说明")
	private String explanation;
}
