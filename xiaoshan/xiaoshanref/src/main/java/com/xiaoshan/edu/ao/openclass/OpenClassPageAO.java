package com.xiaoshan.edu.ao.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassApprovalState;
import com.xiaoshan.edu.enums.openclass.OpenClassConfirmState;
import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;

import java.util.List;

// 公开课记录条件查询
@Getter@Setter@ToString
public class OpenClassPageAO extends OpenClassTeacherNameAO {

    @ApiModelProperty("确认状态")
    @Enum
    private OpenClassConfirmState confirmState;

    @ApiModelProperty("科目")
    private String subject;

    @ApiModelProperty("公开课类型")
    @Enum
    private OpenClassType type;

    @ApiModelProperty("课题")
    private String courseName;

    @ApiModelProperty("上课状态，true-已结束,false-待开课")
    @BooleanValid
    private Boolean courseState;

    @ApiModelProperty("审批状态")
    @Enum
    private OpenClassApprovalState approvalState;

    @ApiModelProperty("科目多选")
    private List<String> subjectMult;

    @ApiModelProperty("公开课类型多选")
    private List<OpenClassType> typeMult;

    @ApiModelProperty("审批编号")
    private String approvalCode;

    @ApiModelProperty("上课日期排序(1-正序，2-倒序)")
    private Integer dataSort;


}
