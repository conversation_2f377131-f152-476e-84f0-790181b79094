package com.xiaoshan.edu.ao.patrol;

import com.xiaoshan.edu.enums.patrol.PatrolType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class PatrolStatisticsPageAO extends PatrolTimePageAO {

    @ApiModelProperty("班级")
    private String className;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("名字（教师、学生）")
    private String objectName;

    @ApiModelProperty("巡课类型")
    @NotNull
    @Enum
    private PatrolType type;

}
