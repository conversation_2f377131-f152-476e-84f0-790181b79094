package com.xiaoshan.edu.vo.patrol;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.result.QueryResult;
import start.magic.persistence.annotation.Column;

import java.util.List;

@Getter
@Setter
@ToString
public class PatrolElectronicArchivesVO {
    @ApiModelProperty("总记录数")
    private Integer totalCount;

    @ApiModelProperty("表现优秀数量")
    private Integer excellentCount;

    @ApiModelProperty("表现一般数量")
    private Integer commonCount;

    @ApiModelProperty("表现较差数量")
    private Integer badCount;

    @ApiModelProperty("巡课记录列表")
    @Column("patrolElectronicVOList")
    private List<PatrolElectronicVO> patrolElectronicVoList;
}
