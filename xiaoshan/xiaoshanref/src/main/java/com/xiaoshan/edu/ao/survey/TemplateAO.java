package com.xiaoshan.edu.ao.survey;

import com.xiaoshan.edu.enums.survey.SurveyObj;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

@Data
public class TemplateAO {

    @ApiModelProperty("调查模板id，编辑时传入")
    @LongValid
    private Long templateId;

    @ApiModelProperty("调查名称")
    @NotNull
    @NotEmpty
    @Length(max = 30)
    private String name;

    @ApiModelProperty("调查对象")
    @NotNull
    @Enum
    private SurveyObj surveyObj;

    @ApiModelProperty("课程列表，班主任传空数组")
    @NotNull
    private List<String> curriculumList;
}
