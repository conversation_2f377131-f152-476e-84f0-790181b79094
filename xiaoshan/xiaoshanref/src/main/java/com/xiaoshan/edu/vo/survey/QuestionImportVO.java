package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.vo.survey.check.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QuestionImportVO {

    @ApiModelProperty("题目类型")
    private QuestionTypeCheckVO questionTypeCheck;

    @ApiModelProperty("题干内容")
    private TitleCheckVO titleCheck;

    @ApiModelProperty("选项")
    private List<OptionCheckVO> itemsCheck;

    @ApiModelProperty("是否必填")
    private RequiredCheckVO requiredCheck;

    @ApiModelProperty("应用科目")
    private CurriculumListCheckVO curriculumListCheck;

}
