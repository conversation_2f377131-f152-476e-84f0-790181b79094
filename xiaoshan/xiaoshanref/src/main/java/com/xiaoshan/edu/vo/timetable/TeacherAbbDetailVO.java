package com.xiaoshan.edu.vo.timetable;

import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TeacherAbbDetailVO extends TeacherDetailVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程简称")
    private String abbreviation;

    @ApiModelProperty("课程类型")
    private AbbTypeEnum type;

    @ApiModelProperty("调代课ID")
    private Long adjustmentId;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课表ID")
    private Long curriculumTableId;

    @ApiModelProperty("选修Code")
    private String courseCode;

}
