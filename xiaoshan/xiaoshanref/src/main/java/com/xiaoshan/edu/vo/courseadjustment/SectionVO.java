package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class SectionVO {
    @ApiModelProperty("代课节次")
    private String section;
    @ApiModelProperty("（二）代课节次详情列表")
    private List<ReplaceCourseDetailVO> replaceCourseDetailList;
    @ApiModelProperty("课程教室")
    private String roomNames;
}
