package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;

@Getter@Setter@ToString
public class MyReplacePageAO extends TimePageAO {

    @ApiModelProperty("代课教师")
    private String replaceTeacherName;

    @ApiModelProperty("审批状态")
    @Enum
    private ApprovalState approvalState;

    @ApiModelProperty("审批编号")
    private String approvalNo;

}
