package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.dto.survey.PercentageDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class TeacherStatisticVO {

    @ApiModelProperty("题目")
    private String title;

    @ApiModelProperty("总人数")
    private Integer total;

    @ApiModelProperty("题目类型")
    private QuestionType questionType;

    @ApiModelProperty("题号")
    private Integer questionSort;

    @ApiModelProperty(value = "选择题数据")
    private Map<String, Map<String, PercentageDTO>> data;

    @ApiModelProperty("简答题数据")
    private List<String> writeData;

}
