package com.xiaoshan.edu.ao.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassApprovalState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;

@Data
public class OpenClassApproveAO {

    @ApiModelProperty("会议id")
    private Long openClassId;

    @ApiModelProperty("审批状态")
    @NotNull
    @Enum
    private OpenClassApprovalState approvalState;
}
