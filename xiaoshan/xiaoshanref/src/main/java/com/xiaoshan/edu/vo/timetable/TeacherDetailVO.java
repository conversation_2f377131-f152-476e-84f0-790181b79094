package com.xiaoshan.edu.vo.timetable;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TeacherDetailVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("班级教室ID")
    private String classOrRoomId;

    @ApiModelProperty("班级教室名称")
    private String classOrRoomName;

    @ApiModelProperty("教师ID")
    private Long teacherId;

    @ApiModelProperty("教师名称")
    private String teacherName;

    @ApiModelProperty(value = "课程时间")
    private String courseTime;

}
