package com.xiaoshan.edu.enums.timetable;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */

public enum WeekTypeEnum {

    /**
     * 单周
     */
    @ApiModelProperty("单周")
    SINGLE,

    /**
     * 双周
     */
    @ApiModelProperty("双周")
    DOUBLE;

    public static String getWeekType(WeekTypeEnum weekType) {
        if (weekType == SINGLE) {
            return "单";
        } else {
            return "双";
        }
    }

    public static String getWeekTypeName(WeekTypeEnum weekType) {
        if (weekType == SINGLE) {
            return "单周";
        } else {
            return "双周";
        }
    }
}
