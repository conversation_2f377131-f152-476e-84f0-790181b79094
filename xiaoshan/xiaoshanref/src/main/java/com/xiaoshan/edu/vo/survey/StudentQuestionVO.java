package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Getter@Setter@ToString
public class StudentQuestionVO {

    @ApiModelProperty("题目")
    private String title;

    @ApiModelProperty("题目类型")
    private QuestionType questionType;

    @ApiModelProperty(value = "答案，<科目(姓名):选项>", example = "{'(数学)郭老师': [{'txt': '较多','option': 'A'}]}")
    private Map<String, List<QuestionOptionDTO>> data;
}
