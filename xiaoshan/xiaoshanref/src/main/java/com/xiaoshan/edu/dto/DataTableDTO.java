package com.xiaoshan.edu.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.converter.MapWeekMapStringStringConverterEditor;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.converter.PropertyConverter;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DataTableDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("班级教室ID")
    private String classOrRoomId;

    @ApiModelProperty("班级教室名称")
    private String classOrRoomName;

    @ApiModelProperty(value = "Map<周:Map<节次:课程>>", hidden = true)
    @PropertyConverter(MapWeekMapStringStringConverterEditor.class)
    private Map<WeekEnum, Map<String, String>> detail;

    @ApiModelProperty("Map<周:Map<节次:{课程、班级或教室ID名称、教师ID名称}>>")
    @PropertyConverter(MapWeekMapStringStringConverterEditor.class)
    private Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailv;


    /**
     * 周课表转换
     *
     * @param weekTable
     */
    public void setResult(Map<WeekEnum, Map<String, String>> weekTable) {
        detailv = new HashMap<>(5);
        for (WeekEnum dayOfWeek : weekTable.keySet()) {
            Map<String, String> sectionTeacherAbbDetailSourceMap = weekTable.get(dayOfWeek);
            Map<String, List<TeacherAbbDetailVO>> sectionTeacherAbbDetailMap = new HashMap<>(10);
            for (String section : sectionTeacherAbbDetailSourceMap.keySet()) {
                String[] courseInfoArray = sectionTeacherAbbDetailSourceMap.get(section).split("\\|");
                List<TeacherAbbDetailVO> teacherAbbDetailList = new ArrayList<>();
                for (String courseInfo : courseInfoArray) {
                    if (!StringUtils.isBlank(courseInfo)) {
                        String[] courseInfoList = courseInfo.split(Constant.SPLITDATASTRING);
                        TeacherAbbDetailVO teacherAbbDetailVO = new TeacherAbbDetailVO();
                        teacherAbbDetailVO.setAbbreviation(courseInfoList[0]);
                        teacherAbbDetailVO.setCourseName(courseInfoList[1]);
                        teacherAbbDetailVO.setClassOrRoomId(courseInfoList[2]);
                        teacherAbbDetailVO.setClassOrRoomName(courseInfoList[3]);
                        teacherAbbDetailVO.setTeacherId(Long.parseLong(courseInfoList[4]));
                        teacherAbbDetailVO.setTeacherName(courseInfoList[5]);
                        teacherAbbDetailVO.setType(AbbTypeEnum.NORMAL);
                        teacherAbbDetailList.add(teacherAbbDetailVO);
                        sectionTeacherAbbDetailMap.put(section, teacherAbbDetailList);
                    }
                }
            }
            detailv.put(dayOfWeek, sectionTeacherAbbDetailMap);
        }
        setDetail(null);
    }

}
