package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.enums.survey.SurTaskState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@Data
public class StudentTaskVO {

    @ApiModelProperty("调查任务id")
    private Long taskId;

    @ApiModelProperty("调查名称")
    private String name;

    @ApiModelProperty("调查开始时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startDate;

    @ApiModelProperty("调查结束时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endDate;

    @ApiModelProperty("调查说明")
    private String explanation;

    @ApiModelProperty("是否完成任务")
    private Boolean isSubmit;

    @ApiModelProperty("阶段")
    private SurTaskState state;
}
