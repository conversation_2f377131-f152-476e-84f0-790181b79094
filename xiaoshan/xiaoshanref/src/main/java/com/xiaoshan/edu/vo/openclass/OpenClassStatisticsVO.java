package com.xiaoshan.edu.vo.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OpenClassStatisticsVO {

    @ApiModelProperty("校内公开课（节）")
    private Long innerOpenClassNum = 0L;

    @ApiModelProperty("校际公开课（节）")
    private Long schoolOpenClassNum = 0L;

    @ApiModelProperty("校内讲座（节）")
    private Long innerLectureNum = 0L;

    @ApiModelProperty("云课堂（节）")
    private Long cloudClassNum = 0L;
}
