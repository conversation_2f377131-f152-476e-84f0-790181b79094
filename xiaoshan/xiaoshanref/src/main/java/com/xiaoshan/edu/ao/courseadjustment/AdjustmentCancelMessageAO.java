package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import lombok.Data;

import java.util.List;

@Data
public class AdjustmentCancelMessageAO {
    // 调代课ID
    private Long adjustmentId;
    // 调代课类型
    private AdjustmentType adjustmentType;
    // 代课时间列表
    private List<String> replaceCourseTimeList;
    // 调课时间
    private String courseTime;
    // 被调时间
    private String adjustedCourseTime;
    // 课程教师userId
    private Long courseTeacherUserId;
    // 调代课教师userId
    private Long reverseTeacherUserId;
    // 自动撤销类型(1:课表更新删除，2，节假日/调休新增、更新)
    private Integer type;
}
