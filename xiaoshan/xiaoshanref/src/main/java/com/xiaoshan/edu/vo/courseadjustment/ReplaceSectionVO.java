package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.persistence.annotation.Column;

@Getter@Setter@ToString
public class ReplaceSectionVO {

    @ApiModelProperty("代课id")
    @Column("replace_id")
    private Long replaceId;

    @ApiModelProperty("课程教室")
    @Column("course_room")
    private String courseRoom;

}
