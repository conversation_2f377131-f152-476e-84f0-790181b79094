package com.xiaoshan.edu.vo.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.thirdparty.json.JsonArray;

import java.util.Date;

@Getter@Setter@ToString
public class PersonalCommentVO extends BaseCommentVO{

    @ApiModelProperty("课程评价")
    private String comment;

    @ApiModelProperty("评价图片url列表")
    private JsonArray picUrls;

    @ApiModelProperty("教师姓名")
    private String teacherName;

    @ApiModelProperty("教师部门")
    private String teacherDepartment;

    @ApiModelProperty("最后修改时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date modifiedDate;

    @ApiModelProperty("教师人脸url")
    private String faceUrl;
}
