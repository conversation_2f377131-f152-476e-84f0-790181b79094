package com.xiaoshan.edu.dto;

import java.util.Date;

import com.xiaoshan.edu.enums.patrol.PatrolAppraise;
import com.xiaoshan.edu.enums.patrol.PatrolType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
public class PatrolDTO {
    @ApiModelProperty("巡课id")
    private Long id;

    @ApiModelProperty("巡课对象")
    private String objectName;

    @ApiModelProperty("巡课对象id")
    private Long objectId;

    @ApiModelProperty("巡课类型")
    private PatrolType type;

    @ApiModelProperty("巡课班级")
    private String className;

    @ApiModelProperty("巡课时间（课程时间）")
    private String courseTime;

    @ApiModelProperty("巡课科目")
    private String courseName;

    @ApiModelProperty("巡课老师")
    private String patrolTeacherName;

    @ApiModelProperty("巡课评价")
    private PatrolAppraise appraise;

    @ApiModelProperty("记录时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date recordTime;

    @ApiModelProperty("巡课内容")
    private String content;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("学号")
    private String studentNo;

    @ApiModelProperty("年级")
    private String gradeName;

    @ApiModelProperty("巡课照片urls")
    private JsonArray picUrls;
}
