package com.xiaoshan.edu.ao.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;

import java.util.List;

// 公开课广场条件查询
@Getter@Setter@ToString
public class OpenClassSquarePageAO extends OpenClassTeacherNameAO {

    @ApiModelProperty("科目")
    private String subject;

    @ApiModelProperty("公开课类型")
    @Enum
    private OpenClassType type;

    @ApiModelProperty("课题")
    private String courseName;

    @ApiModelProperty("小程序搜索-教师或课题")
    private String search;

    @ApiModelProperty("上课状态，true-已结束,false-待开课")
    @BooleanValid
    private Boolean courseState;

    @ApiModelProperty("是否是听课记录，公开课广场-false，听课记录-true")
    @BooleanValid
    @NotNull
    private Boolean listenRecord;

    @ApiModelProperty("科目多选")
    private List<String> subjectMult;

    @ApiModelProperty("公开课类型多选")
    private List<OpenClassType> typeMult;

    @ApiModelProperty("上课日期排序(1-正序，2-倒序)")
    private Integer dataSort;

}
