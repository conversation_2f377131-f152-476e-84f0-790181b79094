//package com.xiaoshan.edu.ao.timetable;
//
//import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
//import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.ToString;
//import start.magic.core.valid.annotation.Enum;
//
//@Getter
//@Setter
//@ToString
//public class CheckMessageAO extends TableBasicAO {
//
//	@ApiModelProperty("周类型")
//	@Enum
//	private WeekTypeEnum weekType;
//
//	@ApiModelProperty("课表类型") 
//	@Enum 
//	private CurriculumTableTypeEnum curriculumTableType;
//	
//}
