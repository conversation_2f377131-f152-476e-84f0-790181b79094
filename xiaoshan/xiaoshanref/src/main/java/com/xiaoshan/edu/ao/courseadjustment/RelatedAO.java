package com.xiaoshan.edu.ao.courseadjustment;

import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class RelatedAO {

    @ApiModelProperty("关联id（调代课主键）")
    @NotNull
    @LongValid
    private Long relatedId;

    @ApiModelProperty("关联类型")
    @NotNull
    private Integer relatedType;

    @ApiModelProperty("调代课类型")
    @NotNull
    private AdjustmentType adjustmentType;
}
