package com.xiaoshan.edu.ao.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.persistence.annotation.Column;

@Getter@Setter@ToString
public class OpenClassSubjectAO extends OpenClassTimePageAO {

    @ApiModelProperty("是否是本学期时间查询，是-true，不是-false")
    @BooleanValid
    @NotNull
    @Column("is_current_semester")
    private Boolean isCurrentSemester;

    @ApiModelProperty("本学期开始年份")
    @IntegerValid
    @NotNull
    private Integer startYear;

    @ApiModelProperty("本学期结束年份")
    @IntegerValid
    @NotNull
    private Integer endYear;

    @ApiModelProperty("第几个学期")
    @IntegerValid
    @NotNull
    private Integer orderNo;
}
