package com.xiaoshan.edu.vo.timetable;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class TeachClassesDetailVO {
	
	@ApiModelProperty("课程简称") 
	private String abbreviation;
	
	@ApiModelProperty("课程名称") 
	private String courseName;

	@ApiModelProperty("年级名称")
	private String gradeName;
	
	@ApiModelProperty("班级ID")
	private Long classId;

	@ApiModelProperty("班级")
	private String className;

	@ApiModelProperty("教师ID")
	private Long teacherId;
	
	@ApiModelProperty("教师名称")
	private String teacherName;
	
	@ApiModelProperty("班级或教室ID")
	private String classOrRoomId;
	
	@ApiModelProperty("班级或教室")
	private String classOrRoomName;
	
	@ApiModelProperty("开始时间")
	private String startTime;
	
	@ApiModelProperty("结束时间")
	private String endTime;

	@ApiModelProperty("学生列表")
	private List<StudentTableDetailVO> students;
	
}
