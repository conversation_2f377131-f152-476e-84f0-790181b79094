package com.xiaoshan.edu.ao.openclass;

import com.xiaoshan.edu.enums.openclass.OpenClassType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class StaticTeachersDetailAO extends OpenClassTimePageAO{

    @ApiModelProperty("教师统计详细--教师UserId")
    @LongValid
    private Long teacherUserId;

    @ApiModelProperty("科目统计详情--科目名称")
    private String subject;

    @ApiModelProperty("公开课类型")
    @Enum
    private OpenClassType type;
}
