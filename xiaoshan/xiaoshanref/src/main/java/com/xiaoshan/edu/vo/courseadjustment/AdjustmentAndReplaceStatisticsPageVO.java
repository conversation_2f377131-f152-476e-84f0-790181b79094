package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class AdjustmentAndReplaceStatisticsPageVO {

    @ApiModelProperty("教师姓名")
    private String teacherName;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("调课次数")
    private String adjustmentCount;

    @ApiModelProperty("代课次数")
    private String toReplaceCount;

    @ApiModelProperty("被代次数")
    private String beReplacedCount;

    @ApiModelProperty("代课节数")
    private String toReplaceSection;

    @ApiModelProperty("被代节数")
    private String beReplacedSection;
}
