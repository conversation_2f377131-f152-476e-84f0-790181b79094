package com.xiaoshan.edu.vo.survey;

import com.xiaoshan.edu.enums.survey.SurveyObj;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class TemplateAndQuestionVO {

    @ApiModelProperty("调查模板id")
    private Long templateId;

    @ApiModelProperty("调查名称")
    private String name;

    @ApiModelProperty("调查对象")
    private SurveyObj surveyObj;

    @ApiModelProperty("课程列表")
    private List<String> curriculumList;

    @ApiModelProperty("题目列表")
    private List<QuestionVO> questionList;

    @ApiModelProperty("新的课程列表")
    private Set<String> newCurriculumList;
}
