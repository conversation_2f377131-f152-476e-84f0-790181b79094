package com.xiaoshan.edu.ao.openclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter@Setter@ToString
public class OpenClassSimulationAO {
    @ApiModelProperty(value = "流程定义")
    private String processDefinitionKey;

    @ApiModelProperty(value = "流程条件")
    private Map<String,String> conditions;
}
