package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.TimeFormat;

import java.util.Date;

@Getter@Setter@ToString
public class AdjustmentAndReplaceApplyRecordsVO {

    @ApiModelProperty("调代课类型【调课/代课】")
    private String type;

    @ApiModelProperty("调代课id")
    private Long id;

    @ApiModelProperty("审批状态")
    private String approvalState;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;

    @ApiModelProperty("是否异常")
    private Boolean isUnusual;

    @ApiModelProperty("调课时间")
    private String courseTime;

    @ApiModelProperty("被调时间【如果是自修，此项为空】")
    private String adjustedCourseTime;

    @ApiModelProperty("代课开始时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private String replaceStartTime;

    @ApiModelProperty("代课结束时间")
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private String replaceEndTime;

    @ApiModelProperty("是否自修")
    private Boolean isSelfStudy;

    @ApiModelProperty("周循环调课次数")
    private String weekCycle;

    @ApiModelProperty("循环方式")
    private String cycleType;
}
