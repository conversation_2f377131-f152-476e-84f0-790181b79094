package com.xiaoshan.edu.vo.courseadjustment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApplyAdjustmentVO {

    @ApiModelProperty("1-调课信息中存在冲突，2-被调课程信息存在冲突,3-调课时间不在循环时间段内,4-正确")
    private Integer type;

    @ApiModelProperty("错误信息")
    private String content;

    @ApiModelProperty("错误数据角标")
    private List<Integer> index;

}
