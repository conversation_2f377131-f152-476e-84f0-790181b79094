package com.xiaoshan.edu.ao.timetable;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class CurriculumTableAO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("课表ID")
	@LongValid
	private Long curriculumTableId;
    
    @ApiModelProperty("学段ID")
    @NotNull
	@LongValid
	private Long sectionId;
	
    @ApiModelProperty("入学年份")
    @NotNull
	@IntegerValid
	private Integer enrollmentYear;

	@ApiModelProperty("是否单双周")
	@NotNull
	@BooleanValid
	private Boolean singleWeek;

}
