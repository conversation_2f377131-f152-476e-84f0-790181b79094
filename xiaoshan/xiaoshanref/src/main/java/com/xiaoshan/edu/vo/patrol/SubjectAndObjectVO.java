package com.xiaoshan.edu.vo.patrol;

import com.xiaoshan.edu.vo.timetable.StudentTableDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@Getter@Setter@ToString
public class SubjectAndObjectVO {
    @ApiModelProperty("科目")
    private String courseName;

    @ApiModelProperty("任课教师id")
    private Long teacherId;

    @ApiModelProperty("任课教师名字")
    private String teacherName;

    @ApiModelProperty("班级或教室id")
    private String classOrRoomId;

    @ApiModelProperty("班级或教室名称")
    private String classOrRoomName;

    @ApiModelProperty("听课学生列表")
    private List<StudentTableDetailVO> students;

    @ApiModelProperty("听课学生班级Id集合【需要透传给发起接口】")
    private List<Long> studentIds;
}
