package com.xiaoshan.edu.ao.timetable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
@ToString
public class StudentQuery extends TableBasicQuery {

	@ApiModelProperty("学生ID") 
	@NotNull @LongValid
	private Long stuId;

}
