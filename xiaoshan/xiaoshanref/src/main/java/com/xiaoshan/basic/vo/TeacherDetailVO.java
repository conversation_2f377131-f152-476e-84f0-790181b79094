package com.xiaoshan.basic.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter@Setter@ToString
public class TeacherDetailVO {
    @ApiModelProperty("教师id")
    private Long id;

    @ApiModelProperty("教师姓名")
    private String name;

    @ApiModelProperty("电话号码")
    private String phone;

    @ApiModelProperty("id类型")
    private Integer idType;

    @ApiModelProperty("id编号")
    private String idNo;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("部门ID")
    private List<Long> departmentIds;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("对应的用户ID")
    private Long userId;

    @ApiModelProperty("状态")
    private Integer state;
}
