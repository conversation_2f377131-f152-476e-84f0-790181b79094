package com.xiaoshan.basic.dto;

import com.xiaoshan.basic.ao.MessageParentAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MessageDTO {

    private String content;
    /**
     * 345:我的填报
     */
    private Long sourceId;
    /**
     * 推送类型 0 不推送 1通知 2透传
     */
    private Integer pushType;
    /**
     * 来源类型 1 系统 2个人 3应用
     */
    private Integer sourceType;

    private List<Long> targetUserIds;

    private String title;
    /**
     * 消息类型 1系统消息 2通知公告 3业务消息
     */
    private Integer type;

    @ApiModelProperty("短信内容")
    private String smsContent;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 附加消息
     */
    private String extra;

    private String templateData;

    private Long studentId;

    /**
     * 公众号消息数据
     */
    private String officeAccountData;

    /**
     * 短信跳转小程序地址
     */
    private String path;

    /**
     * 短信跳转小程序查询条件
     */
    private String query;

    /**
     * 批量发送消息给家长：[{"studentId":"123","userId":456}]
     * studentId：学生id
     * userId：家长userId
     */
    private List<MessageParentAO> messageParents;
}
