package com.xiaoshan.basic.ao;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.Length;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

@Data
public class MessageTextAO {
	
	@ApiModelProperty("群聊ID")
    @NotNull
    @Length
    private String chatId;

    @ApiModelProperty("消息标题")
    @NotNull
    @Length
	private String title;
    
    @ApiModelProperty("消息内容")
    @NotEmpty
	private String content;
    
}
