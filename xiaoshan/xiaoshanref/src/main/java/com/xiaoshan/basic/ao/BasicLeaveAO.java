package com.xiaoshan.basic.ao;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class BasicLeaveAO {
	
	@ApiModelProperty("请假ID")
	@NotNull
	@LongValid
	private Long id;

	@ApiModelProperty("请假学生ids,逗号分隔")
	@NotNull
	@NotEmpty
	private String studentIds;
	
	@ApiModelProperty("请假类型：0事假；1病假；2课间操")
	@NotNull
	@IntegerValid
	private Integer leaveType;

	@ApiModelProperty("请假时间")
	@NotNull
	@Format(type=FormatType.JSONArray)
	private List<BasicLeaveItemAO> items;
	
	@ApiModelProperty("请假事由")
	@NotNull
	@NotEmpty
	private String leaveReason;
	
	@ApiModelProperty("是否周期请假：0是；1否")
	@IntegerValid
	private Integer isCycle;
	
	@ApiModelProperty("请假时长")
	@IntegerValid
	private Integer leaveHours;
	
	@ApiModelProperty("归寝设置：0最后一天归寝；1请假期间均归寝；2请假期间均不归寝")
	@NotNull
	@IntegerValid
	private Integer goBed;
	
	@ApiModelProperty("重复日期；eg：1;2;3 代表周一、周二、周三重复，数字之间采用分号分隔，周一到周日使用1-7表示")
	@NotEmpty
	private String repeatDate;
	
}
