package com.xiaoshan.basic.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.persistence.annotation.Column;

import java.io.Serializable;

@Getter@Setter@ToString
public class TokenVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Column("access_token")
	@JsonProperty("access_token")
	private String accessToken;
	@Column("token_type")
	@JsonProperty("token_type")
	private String tokenType;
	@Column("refresh_token")
	@JsonProperty("refresh_token")
	private String refreshToken;
	
}
