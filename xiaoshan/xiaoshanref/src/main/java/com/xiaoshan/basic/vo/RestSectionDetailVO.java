package com.xiaoshan.basic.vo;

import java.io.Serializable;

import lombok.Data;

@Data
public class RestSectionDetailVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//节次名称
	private String sectionName;
	//时段：0上午；1下午；2晚上
	private Integer timeRange;
	private String startTime;
	private String endTime;
	//节次类型
	private Integer type;
	//节次顺序
	private Integer sectionOrder;

}