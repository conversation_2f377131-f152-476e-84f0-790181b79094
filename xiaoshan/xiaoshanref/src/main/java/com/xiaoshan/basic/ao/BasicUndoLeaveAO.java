package com.xiaoshan.basic.ao;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class BasicUndoLeaveAO {
	
	@ApiModelProperty("请假ID")
	@NotNull
	@LongValid
	private Long id;

	@ApiModelProperty("请假学生ids,逗号分隔")
	@NotNull
	@NotEmpty
	private String studentIds;
	
	@ApiModelProperty("返校时间")
	@TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date goBackTime;
	
	@ApiModelProperty("归寝设置：0最后一天归寝；1请假期间均归寝；2请假期间均不归寝")
	@NotNull
	@IntegerValid
	private Integer goBed;
	
}
