package com.xiaoshan.basic.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class SemesterVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("学期ID")
    private Long id;

    @ApiModelProperty("起始年份")
    private Integer startYear;

    @ApiModelProperty("终止年份")
    private Integer endYear;

    @ApiModelProperty("起始日期")
    private Date startDate;

    @ApiModelProperty("终止日期")
    private Date endDate;

    @ApiModelProperty("序号")
    private Integer orderNo;

    @ApiModelProperty("状态 1 当前学期 0 历史学期")
    private Integer state;

}
