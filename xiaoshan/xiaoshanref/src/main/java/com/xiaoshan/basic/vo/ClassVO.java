package com.xiaoshan.basic.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter@ToString
public class ClassVO {

    @ApiModelProperty("班级id")
	private Long id;
    @ApiModelProperty("学段")
	private Integer sectionType;
    @ApiModelProperty("年级")
	private String gradeName;
    @ApiModelProperty("姓名")
	private String name;
    @ApiModelProperty("学段")
	private Integer sectionId;
    @ApiModelProperty("年")
	private Integer enrollmentYear;
    @ApiModelProperty("班级总人数")
	private Integer studentCount;
    @ApiModelProperty("班主任")
	private String headTeacherName;
    @ApiModelProperty("班主任ID")
	private Long headTeacherId;
    @ApiModelProperty("房间ID")
    private Long roomId;
    @ApiModelProperty("房间名称")
    private String roomName;

}
