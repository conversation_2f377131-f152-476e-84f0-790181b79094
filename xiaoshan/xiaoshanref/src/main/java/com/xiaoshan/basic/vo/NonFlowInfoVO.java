package com.xiaoshan.basic.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NonFlowInfoVO {

    @ApiModelProperty("审批人id")
    private Long approverId;

    @ApiModelProperty("审批人类型")
    private Integer approverType;

    @ApiModelProperty("审批内容")
    private String content;

    @ApiModelProperty("关联id")
    private Long relatedId;

    @ApiModelProperty("关联类型")
    private Integer relatedType;

    @ApiModelProperty("审批状态：0审批中1通过2拒绝")
    private Integer resultState;
}
