package com.xiaoshan.basic.vo;

import java.io.Serializable;
import java.sql.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AdjustmentVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("itemId")
    private Long itemId;
    @ApiModelProperty("具体时间")
    private Date detailDate;
    @ApiModelProperty("每个星期的哪一天")
    private Integer dayOfWeek;
}
