package com.xiaoshan.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskApprovalStateEnum {

    /**
     * 审批状态
     */
    STARTER_IS_APPROVER(13, "发起人与审批人相同自动通过"),
    /**
     * 角色为空已自动通过
     */
    ROLE_INVALID_HAS_PASS(12, "角色为空已自动通过"),
    /**
     * 审批人异常已自动通过
     */
    APPROVER_INVALID_HAS_PASS(11, "审批人异常已自动通过"),
    /**
     * 角色为空将自动通过
     */
    ROLE_INVALID_PASS(10, "角色为空将自动通过"),
    /**
     * 审批人异常将自动通过
     */
    APPROVER_INVALID_PASS(9, "审批人异常将自动通过"),
    /**
     * 角色为空将转交
     */
    ROLE_INVALID_TRANSFER(8, "角色为空将转交"),
    /**
     * 审批人异常将转交
     */
    APPROVER_INVALID_TRANSFER(7, "审批人异常将转交"),
    /**
     * 已转交
     */
    TRANSFER(6, "已转交"),
    /**
     * 审批人为空已转交
     */
    APPROVER_NONE_TRANSFER(5, "审批人为空已转交"),
    /**
     * 角色为空已转交
     */
    ROLE_TRANSFER(4, "角色为空已转交"),
    /**
     * 审批状态不一致
     */
    STATUS_NOT_SAME(3, "审批状态不一致"),
    /**
     * 已驳回
     */
    REJECTED(2, "已驳回"),
    /**
     * 已通过
     */
    PASSED(1, "已通过"),
    /**
     * 审批中
     */
    APPROVING(0, "审批中"),
    /**
     * 待审批
     */
    WAITING(-1, "待审批"),
    /**
     * 未提交
     */
    UNCOMMITTED(-2, "未提交"),
    /**
     * 已撤销
     */
    CANCEL(-3, "已撤销");

    private final Integer code;

    private final String message;

    public static final Map<Integer, String> APPROVAL_STATE_MAP = new HashMap<>();

}
