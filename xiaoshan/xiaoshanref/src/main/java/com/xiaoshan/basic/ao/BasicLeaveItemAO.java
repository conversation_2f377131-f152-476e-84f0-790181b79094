package com.xiaoshan.basic.ao;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;

@Getter@Setter@ToString
public class BasicLeaveItemAO {
	
	@ApiModelProperty("请假开始日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startTime;
	
	@ApiModelProperty("请假结束日期")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endTime;
	
}
