package com.xiaoshan.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;
import start.magic.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel("教师请假/公出")
@Getter
@Setter
@ToString
public class LeavingAndBusinessVO {

    /**
     * 公出
     */
    public static final String BUSINESS_OUT = "0";

    /**
     * 临时假
     */
    public static final String TEMPORARY_LEAVE = "8";

    @ApiModelProperty(value = "公出/请假 类别：1请假、2公出")
    private Integer dataType;

    @ApiModelProperty(value = "教师请假id")
    private Long leavingId;

    @ApiModelProperty("请假/公出教师姓名")
    private String teacherName;

    @ApiModelProperty("请假/公出教师ID")
    private Long teacherId;

    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date requestTime;

    @ApiModelProperty("审批通过时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date approvalPassDate;

    @ApiModelProperty("请假类型：0公出 1事假、2病假、3婚假、4产假、5护理假、6工伤假、7丧假、8临时假; 默认为0")
    private String leavingType;

    @ApiModelProperty("请假开始时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date startTime;

    @ApiModelProperty("开始时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String startTimeScope;

    @ApiModelProperty("请假结束时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date endTime;

    @ApiModelProperty("结束时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String endTimeScope;

    @ApiModelProperty("销假时间【如果是非临时假或公出，格式为日期加0时0分0秒的Date类型 yyyy-MM-dd 00:00:00，临时假正常日期时间】")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date absenceTime;

    @ApiModelProperty("销假时间范围：上午、下午【如果是临时假，则此属性为空】")
    private String absenceTimeScope;

    @ApiModelProperty(value = "请假时长")
    private BigDecimal duration;

    /**
     * 获取请假类型描述
     *
     * @return
     */
    public String getLeavingTypeDesc() {
        if (BUSINESS_OUT.equals(leavingType)) {
            return "公出";
        } else {
            return "请假";
        }
    }

    /**
     * 是否为临时假
     *
     * @return
     */
    public Boolean temporaryLeft() {
        return TEMPORARY_LEAVE.equals(leavingType);
    }

    /**
     * 获取实际结束时间
     *
     * @return
     */
    public Date getActualEndTime() {
        if (absenceTime != null) {
            return absenceTime;
        }
        return endTime;
    }

    /**
     * 获取实际结束时间范围
     *
     * @return
     */
    public String getActualEndTimeScope() {
        if (!StringUtils.isEmpty(absenceTime)) {
            return absenceTimeScope;
        }
        return endTimeScope;
    }


}

