package com.xiaoshan.basic.dto;

import lombok.Data;
import start.magic.thirdparty.json.JsonArray;

/**
 * 电子班牌通知
 */
@Data
public class ClassNotifyDTO {
    /**
     * 班级id集合
     */
    private JsonArray classIds;
    /**
     * 内容
     */
    private String content;
    /**
     * 是否面向全部
     */
    private Boolean targetAll;
    /**
     * 发布对象类型
     */
    private Integer targetType;
    /**
     * 标题
     */
    private String title;
    /**
     * 保存类型1发布2草稿
     */
    private Integer type;
    /**
     * 操作人
     */
    private String userName;

    /**
     * 通知来源：0 班牌管理端 1 通知管理端
     */
    private Integer sourceType = 1;
}
