/**
  * Copyright 2021 bejson.com 
  */
package com.xiaoshan.basic.vo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;
import java.util.List;

@Data
public class FlowVO {
    @ApiModelProperty("当前人是否活跃")
    private Boolean active;
    @ApiModelProperty("申请人id")
    private Integer applicantId;
    @ApiModelProperty("申请人姓名")
    private String applicantName;
    @ApiModelProperty("申请人类型")
    private Integer applicantType;
    @ApiModelProperty("申请时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date applyTime;
    @ApiModelProperty("抄送对象")
    private List<CarbonCopies> carbonCopies;
    @ApiModelProperty("流程实例id")
    private String engineFlowId;
    @ApiModelProperty("流程模版id")
    private Integer flowTemplateId;
    @ApiModelProperty("流程id")
    private Integer id;
    @ApiModelProperty("是否最后一个审批人")
    private Boolean isLast;
    @ApiModelProperty("节点信息（json格式）")
    private String nodeInfo;
    @ApiModelProperty("任务节点")
    private List<Nodes> nodes;
    @ApiModelProperty("关联id")
    private Long relatedId;
    @ApiModelProperty("关联类型")
    private Integer relatedType;
    @ApiModelProperty("序列化")
    private String serialNumber;
    @ApiModelProperty("流程状态")
    private Integer state;
    @ApiModelProperty("更新时间")
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date updatedTime;

    @Data
    public static class Approvers {
        @ApiModelProperty("审批人ID")
        private Long approverId;
        @ApiModelProperty("审批人姓名")
        private String approverName;
        @ApiModelProperty("审批人类型")
        private Integer approverType;
        @ApiModelProperty("内容")
        private String content;
        @ApiModelProperty("ID")
        private Integer id;
        @ApiModelProperty("旧审批人ID")
        private Integer oldApproverId;
        @ApiModelProperty("旧审批人姓名")
        private String oldApproverName;
        @ApiModelProperty("旧审批人类型")
        private Integer oldApproverType;
        @ApiModelProperty("结果状态")
        private Integer resultState;
        @ApiModelProperty("任务ID")
        private String taskId;
        @PropertyConverter(DateTimefConverterEditor.class)
        @ApiModelProperty("更新时间")
        private Date updatedTime;
    }

    @Data
    public static class CarbonCopies {
        @ApiModelProperty("抄送对象ID")
        private Integer targetId;
        @ApiModelProperty("抄送对象姓名")
        private String targetName;
        @ApiModelProperty("抄送对象类别")
        private Integer targetType;
        @ApiModelProperty("类型")
        private Boolean type;
        @ApiModelProperty("用户名")
        private String userNames;
    }

    @Data
    public static class Nodes {
        @ApiModelProperty("审批人")
        private List<Approvers> approvers;
        @ApiModelProperty("curMode")
        private Integer curMode;
        @ApiModelProperty("curRange")
        private Integer curRange;
        @ApiModelProperty("curUsersList")
        private String curUsersList;
        @ApiModelProperty("engineNodeId")
        private String engineNodeId;
        @ApiModelProperty("isEdit")
        private Integer isEdit;
        @ApiModelProperty("nodeType")
        private Integer nodeType;
        @ApiModelProperty("resultState")
        private Integer resultState;
        @ApiModelProperty("角色")
        private List<Roles> roles;
        @ApiModelProperty("signType")
        private Integer signType;
        @ApiModelProperty("type")
        private Integer type;
        @PropertyConverter(DateTimefConverterEditor.class)
        @ApiModelProperty("更新时间")
        private Date updatedTime;

    }

    @Data
    public static class Roles {
        @ApiModelProperty("approvers")
        private List<Approvers> approvers;
        @ApiModelProperty("角色ID")
        private int id;
        @ApiModelProperty("角色姓名")
        private String name;

    }
}