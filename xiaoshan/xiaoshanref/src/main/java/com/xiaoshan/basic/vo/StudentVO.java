package com.xiaoshan.basic.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter@Setter@ToString
@Data
public class StudentVO {

	@ApiModelProperty("学生id")
	private Long id;

	@ApiModelProperty("用户id")
	private Long userId;

	@ApiModelProperty("年段名称")
	private String sectionName;

	@ApiModelProperty("年段id")
	private Long sectionId;

	@ApiModelProperty("年级名称")
	private String gradeName;

	@ApiModelProperty("班级名称")
	private String className;

	@ApiModelProperty("班级id")
	private Long classId;

	@ApiModelProperty("姓名")
	private String name;

	@ApiModelProperty("学号")
	private String studentNo;

	@ApiModelProperty("ic卡号")
	private String icCardNo;

	@ApiModelProperty("入团状态(0-否，1-是")
	private Integer leagueState;

	@ApiModelProperty("民族")
	private String nation;

	@ApiModelProperty("出生日期")
	private String birthday;

	@ApiModelProperty("家庭住址")
	private String address;

	@ApiModelProperty("邮编")
	private String postcode;

	@ApiModelProperty("ic卡物理编码")
	private String icCardPhysicalNo;

	@ApiModelProperty("性别")
	private Integer gender;

	@ApiModelProperty("学生类型")
	private Integer studentType;

	@ApiModelProperty("学生状态")
	private Integer state;

	@ApiModelProperty("身份证号码")
	private String idNo;

	@ApiModelProperty("身份证类型")
	private Integer idType;

	@ApiModelProperty("入学年份")
	private Integer enrollmentYear;

	@ApiModelProperty("人员id")
	private Long personId;

	@ApiModelProperty("学生人脸url")
	private String faceData;

	@ApiModelProperty("人脸文件id")
	private Long fileId;

	@ApiModelProperty("初中毕业学校")
	private String graduatedSchool;

	/**
	 * 班级序号
	 */
	@ApiModelProperty("班级序号")
	private Integer orderNumber;

	@ApiModelProperty("学段类型")
	private Long sectionType;

	private List<ParentVO> parents;

	@ApiModelProperty("是否申请晚自修")
	private Boolean eveningStudyState;

	@ApiModelProperty("周六是否在校")
	private Boolean saturdayState;

	@ApiModelProperty("小礼拜周日是否在校")
	private Boolean sundayState;

	@ApiModelProperty("楼宇名称")
	private String dormName;

	@ApiModelProperty("宿舍名称")
	private String roomName;

	@ApiModelProperty("寝室id")
	private Long roomId;

	@ApiModelProperty("床位号")
	private Integer bedNo;

	@ApiModelProperty("是否住校")
	private Boolean livingSchoolState;

	@ApiModelProperty("班主任信息")
	private TeacherVO headTeacherInfo;

	@ApiModelProperty("家长手机号码")
	private String parentPhone;

	@ApiModelProperty("维护状态：true已维护；false未维护")
	private Boolean maintainState;

	@ApiModelProperty("年级顺序：1高一；2高二；3高三")
	private Integer gradeOrder;

	@ApiModelProperty("职位")
	private String title;
}
