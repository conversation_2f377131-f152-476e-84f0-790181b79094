package com.xiaoshan.basic.ao;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter@ToString
public class CalendarSyncAO {
	
	@ApiModelProperty("学期ID")
	@NotNull
	@LongValid
	private Long semesterId;
	
	@ApiModelProperty("同步所有")
	@BooleanValid
	private Boolean all=false;
	
	@ApiModelProperty("新增事项id")
	@LongValid
	private Long addItemId;
	
	@ApiModelProperty("删除事项id")
	@LongValid
	private Long deletedItemId;
	
}
