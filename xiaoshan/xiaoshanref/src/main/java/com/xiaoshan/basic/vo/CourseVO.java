package com.xiaoshan.basic.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class CourseVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private Long classId;
    private String className;
    private String courseCode;
    private String courseName;
    private Long roomId;
    private String roomName;
    private Long teacherId;
    private String teacherName;
    private String restDetailString;
    private List<SectionByCourseCodeListVO> sectionByCourseCodeList;
}
