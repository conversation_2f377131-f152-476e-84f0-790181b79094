package com.xiaoshan.basic.ao.iot;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter
@Setter
public class ExtraInfoAO {

	@ApiModelProperty("人员id")
	@NotNull
	@LongValid
	private Long personId;

	@ApiModelProperty("出入 0- 出 1-入")
	@NotNull
	@IntegerValid
	private Integer direction;

	@ApiModelProperty("事件发生时间")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date happenTime;

	@ApiModelProperty("对应卡号，在匹配不到人员时返回")
	@NotNull
	@NotEmpty
	private String extEventCardNo;

	@ApiModelProperty("系统间唯一编号")
	@NotNull
	@NotEmpty
	private String outUniCode;
	
	@ApiModelProperty("图片URL")
	@NotEmpty
	private String pictureUrl;
	
}
