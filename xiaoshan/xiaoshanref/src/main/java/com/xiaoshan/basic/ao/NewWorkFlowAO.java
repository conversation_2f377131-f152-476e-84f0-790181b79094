package com.xiaoshan.basic.ao;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter@Setter@ToString
public class NewWorkFlowAO {
	@ApiModelProperty("申请人id【session中的id】")
	private Long applicantId;

	@ApiModelProperty("申请人姓名【session中的用户名】")
	private String applicantName;
	
	@ApiModelProperty("申请人类型 1教师 2 学生 3 家长 4 访客")
	private Integer applicantType;

	@ApiModelProperty("部门ids【教师所属部门】")
	private String applicationDepts;
	
	@ApiModelProperty("角色ids【session中的角色ids】")
	private String applicationRoles;
	
	@ApiModelProperty("人员ids【session中的id】")
	private String assigneeIds;
	
	@ApiModelProperty("关联业务id【教师公出/请假对应的业务数据的ID】")
	private String relatedId;
	
	@ApiModelProperty("关联类型 1学生获奖填报 2教师年度考核 3请假【前端透传过来的】")
	private Integer relatedType;

	@ApiModelProperty("流程条件【前端透传过来】")
	private Map<String, String> conditions;

	@ApiModelProperty("节点信息(json格式)【前端透传过来】")
	private String nodeInfo;

	@ApiModelProperty("下级审批人【前端透传过来】")
	private Map<String, String> approver;

	@ApiModelProperty("附加json【业务数据转成JSON】")
	private String extra;

	@ApiModelProperty("流程定义【前端透传过来】")
	private String processDefinitionKey;

	@ApiModelProperty("待办内容【后台组装好的（***的教师请假申请、***的教师公出申请）】")
	private String toDoContent;

	@ApiModelProperty("业务id【教师公出/请假对应的业务数据的ID】")
	private String businessKey;

	@ApiModelProperty("微信公众号模板内容")
	private String officeAccountData;

}
