package com.xiaoshan.basic.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class OssFileInfoDTO {
    private String bucketName;
    // 上传文件类型
    private String contentType;
    // 文件大小
    private Integer fileSize;
    private Integer id;
    // 图片高度
    private Integer imageHeight;
    // 图片宽度
    private Integer imageWidth;
    private String innerUrl;
    // 是否图片 1是 0不是
    private Boolean isImg;
    // 文件md5
    private String md5;
    // 原始文件名
    private String name;
    private String objectName;
    // 冗余字段
    private String path;
    // 签名路径
    private String signedUrl;
    // FileType字段
    private String source;
    // 访问路径
    private String url;
}
