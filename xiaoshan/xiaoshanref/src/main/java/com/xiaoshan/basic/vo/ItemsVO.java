package com.xiaoshan.basic.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Date;
import java.util.List;

@Getter
@Setter@ToString
public class ItemsVO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("dayId")
	private Long dayId;
	@ApiModelProperty("id")
	private Long id;
	@ApiModelProperty("开始时间")
	private Date startDate;
	@ApiModelProperty("结束时间")
	private Date endDate;
	@ApiModelProperty("备注")
	private String notes;
	@ApiModelProperty("类别")
	private Integer type;
	@ApiModelProperty("adjustments")
	private List<AdjustmentVO> adjustments;
}
