package com.xiaoshan.basic.vo;

import com.xiaoshan.basic.dto.BatchFlowsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BatchFlowsVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("关联流程id")
    private Long relatedId;

    @ApiModelProperty("审批编号")
    private String serialNumber;

    @ApiModelProperty("审批人列表")
    private List<BatchFlowsDTO> approvalList;

    @ApiModelProperty("抄送人ids")
    private List<Long> carbonIds;

}
