package com.xiaoshan.basic.ao;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.NotNull;

@Getter@Setter@ToString
public class StudentAO {
	
//	@ApiModelProperty("操作日期")
//	@NotNull
//	@TimeFormat(format = TimeUtils.YYYYMMDD_F)
//	private java.sql.Date date;
	
	@ApiModelProperty("学生ID")
	@NotNull
	@Format(type=FormatType.JSONArrayLong)
	private List<Long> stuIds;
	
}