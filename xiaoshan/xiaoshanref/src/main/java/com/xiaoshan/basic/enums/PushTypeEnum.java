package com.xiaoshan.basic.enums;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum  PushTypeEnum {
    /**
     * 不推送
     */
    @ApiModelProperty("不推送")
    NOT_PUSH(0,"不推送"),
    /**
     * 通知
     */
    @ApiModelProperty("通知")
    NOTICE(1,"通知"),
    /**
     * 透传
     */
    @ApiModelProperty("透传")
    TRANSPARENT(2,"透传");

    private Integer type;

    private String message;
}
