package com.xiaoshan.basic.enums;

import io.swagger.annotations.ApiModelProperty;

public enum GradeEnum {

	/**
	 * 初一
	 */
	@ApiModelProperty("初一")
	middle1("初一",1),

	/**
	 *初二
	 */
	@ApiModelProperty("初二")
	middle2("初二",2),

	/**
	 *初三
	 */
	@ApiModelProperty("初三")
	middle3("初三",3),

	/**
	 *高一
	 */
	@ApiModelProperty("高一")
	high1("高一",4),

	/**
	 *高二
	 */
	@ApiModelProperty("高二")
	high2("高二",5),

	/**
	 *高三
	 */
	@ApiModelProperty("高三")
	high3("高三",6);

	private String name;
	private int sort;

	private GradeEnum(String name,int sort) {
		this.name = name;
		this.sort=sort;
	}

	public String getName() {
		return name;
	}

	public int getSort() {
		return sort;
	}

	public void setSort(int sort) {
		this.sort = sort;
	}
	
	public static GradeEnum getGrade(int sectionType,int gradeOrder) {
    	return GradeEnum.valueOf(sectionType==1?("high"+gradeOrder):("middle"+gradeOrder));
    }

}
