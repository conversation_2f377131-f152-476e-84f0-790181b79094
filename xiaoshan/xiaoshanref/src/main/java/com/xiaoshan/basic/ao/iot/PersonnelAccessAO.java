package com.xiaoshan.basic.ao.iot;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.converter.PropertyConverter;
import start.magic.core.valid.annotation.Format;
import start.magic.core.valid.annotation.Format.FormatType;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

@Getter@Setter
public class PersonnelAccessAO {

	@ApiModelProperty("设备类型，为8，对应门禁点,17:电子班牌")
	@NotNull
	@IntegerValid
	private Integer devType;
	
	@ApiModelProperty("事件类型，20008:进校、20010:归寝、70001:电子班牌刷脸")
	@NotNull
	@IntegerValid
	private Integer evtType;
	
	@ApiModelProperty("设备id")
	@NotNull
	@LongValid
	private Long devId;
	
	@ApiModelProperty("设备名称")
	@NotNull
	@NotEmpty
	private String devName;
	
	@ApiModelProperty("事件时间")
	@NotNull
	@TimeFormat(format = TimeUtils.YYYYMMDDHHMMSS_F)
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date evtTime;
	
	@ApiModelProperty("事件特有数据，见各业务场景相关描述")
	@NotNull
	@Format(type = FormatType.JSONObject)
	private ExtraInfoAO extraInfo;

}
