package com.xiaoshan.basic.vo;

import java.io.Serializable;
import java.sql.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ItemConfigVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Date endDate;
    private Date startDate;
    private Integer type;
    private String notes;
    private List<SimpleDayConfigVO> dayList;
    private List<AdjustmentVO> adjustments;
}
