package com.xiaoshan.edu.api.facade;


import com.alibaba.fastjson.JSON;
import com.common.RunXiaoShanEdu;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = RunXiaoShanEdu.class)
@Slf4j
public class SettingFacadeTest {

    @Autowired
    SettingFacade settingFacade;


    @Test
    public void testSemester() {
       log.info(JSON.toJSONString(settingFacade.currentSemester())); ;
    }

}
