package com.xiaoshan.edu.model.ao;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Slf4j
public class ReplaceCourseDetailTest {

    @Test
    public void testWeek() {
        ReplaceCourseDetailAO replaceCourseDetailAO = new ReplaceCourseDetailAO();
        replaceCourseDetailAO.setCourseTime("第一周周一第一节");
        log.info(replaceCourseDetailAO.getFullChineseDayOfWeek());
    }

    @Test
    public void testIndexOf() {
        ReplaceCourseDetailAO replaceCourseDetailAO = new ReplaceCourseDetailAO();
        replaceCourseDetailAO.setCourseTime("第一周周一第一节");
        log.info(replaceCourseDetailAO.getCourseTime().substring(replaceCourseDetailAO.getIndexOfWeek() + 1));
    }


}
