package com.xiaoshan.edu.model.ao;


import com.xiaoshan.edu.enums.timetable.WeekEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Slf4j
public class CourseAdjustmentApplyTest {

    @Test
    public void testCourseTimeIndexOfWeek() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals(2L, courseAdjustmentApplyAO.getIndexOfWeekOfCourseTime().longValue());
    }

    @Test
    public void testAdjustedCourseTimeIndexOfWeek() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第十一周周一第一节");
        Assert.assertEquals(3L, courseAdjustmentApplyAO.getIndexOfWeekAdjustedCourseTime().longValue());
    }

    @Test
    public void testCourseTimeChineseWeekNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals("一", courseAdjustmentApplyAO.getChineseWeekNumberOfCourseTime());
    }

    @Test
    public void testAdjustedCourseTimeChineseWeekNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第十一周周一第一节");
        Assert.assertEquals("十一", courseAdjustmentApplyAO.getChineseWeekNumberOfAdjustedCourseTime());
    }

    @Test
    public void testCourseTimeWeekNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals(1L, courseAdjustmentApplyAO.getWeekNumberOfCourseTime().longValue());
    }

    @Test
    public void testAdjustedCourseTimeWeekNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第十一周周一第一节");
        Assert.assertEquals(11L, courseAdjustmentApplyAO.getWeekNumberOfAdjustedCourseTime().longValue());
    }

    @Test
    public void testCourseTimeSection() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals("一", courseAdjustmentApplyAO.getSectionOfCourseTime());
    }

    @Test
    public void testAdjustedCourseTimeSection() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第一周周一第四节");
        Assert.assertEquals("四", courseAdjustmentApplyAO.getSectionOfAdjustedCourseTime());
    }

    @Test
    public void testCourseTimeDayOfWeek() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals(WeekEnum.Monday, courseAdjustmentApplyAO.getDayOfWeekOfCourseTime());
    }




    @Test
    public void testAdjustedCourseTimeDayOfWeek() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第一周周四第一节");
        Assert.assertEquals(WeekEnum.Thursday, courseAdjustmentApplyAO.getDayOfWeekOfAdjustedCourseTime());
    }

    @Test
    public void testCourseTimeChineseDayOfWeek() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals("一", courseAdjustmentApplyAO.getChineseDayOfWeekOfCourseTime());
    }

    @Test
    public void testAdjustedCourseTimeChineseDayOfWeek() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第四周周四第一节");
        Assert.assertEquals("四", courseAdjustmentApplyAO.getChineseDayOfWeekOfAdjustedCourseTime());
    }

    @Test
    public void testCourseTimeByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals("第二周周一第一节", courseAdjustmentApplyAO.getCourseTimeByNumber(1));
    }

    @Test
    public void testAdjustedCourseTimeByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第一周周一第一节");
        Assert.assertEquals("第五周周一第一节", courseAdjustmentApplyAO.getAdjustedCourseTimeByNumber(4));
    }

    @Test
    public void testCourseTimeIndexByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals(3L, courseAdjustmentApplyAO.getIndexOfWeekOfCourseTimeByNumber(10).longValue());
    }

    @Test
    public void testAdjustedCourseTimeIndexByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第一周周一第一节");
        Assert.assertEquals(2L, courseAdjustmentApplyAO.getIndexOfWeekOfAdjustedCourseTimeByNumber(1).longValue());
    }

    @Test
    public void testChineseWeekNumberOfCourseTimeByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals("十一", courseAdjustmentApplyAO.getChineseWeekNumberOfCourseTimeByNumber(10));
    }

    @Test
    public void testChineseWeekNumberOfAdjustedCourseTimeByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第一周周一第一节");
        Assert.assertEquals("十", courseAdjustmentApplyAO.getChineseWeekNumberOfAdjustedCourseTimeByNumber(9));
    }

    @Test
    public void testWeekNumberOfCourseTimeByNumber() {
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setCourseTime("第一周周一第一节");
        Assert.assertEquals(10, courseAdjustmentApplyAO.getWeekNumberOfCourseTimeByNumber(9).longValue());
    }

    @Test
    public void testWeekNumberOfAdjustedCourseTimeByNumber(){
        CourseAdjustmentDetailAO courseAdjustmentApplyAO = new CourseAdjustmentDetailAO();
        courseAdjustmentApplyAO.setAdjustedCourseTime("第一周周一第一节");
        Assert.assertEquals(10, courseAdjustmentApplyAO.getWeekNumberOfAdjustedCourseTimeByNumber(9).longValue());
    }


}
