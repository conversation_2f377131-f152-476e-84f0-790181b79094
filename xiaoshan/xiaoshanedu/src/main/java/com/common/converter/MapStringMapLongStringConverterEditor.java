package com.common.converter;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import com.topnetwork.converter.AbstractArgsConverterEditor;

import start.framework.commons.rest.converter.map.MapLongStringConverterEditor;
import start.magic.thirdparty.json.JsonObject;

public class MapStringMapLongStringConverterEditor extends AbstractArgsConverterEditor<String> {

	private MapLongStringConverterEditor editor;
	
	public MapStringMapLongStringConverterEditor(Class<?> prototype) {
		super(prototype);
		editor=new MapLongStringConverterEditor(Map.class); 
	}

	@SuppressWarnings("unchecked")
	@Override
	public void restore(Object value) {
		if (value != null) {
			JsonObject json;
			if(value.getClass().equals(String.class)) {
				json=new JsonObject(String.valueOf(value));
			}else {
				json=(JsonObject)value;
			}
			Map<String, Map<Long,String>> dataItems=new LinkedHashMap<>();
			Iterator<String> keys=json.keys();
			while(keys.hasNext()) {
				String name=keys.next();
				editor.restore(json.getJsonObject(name));
				dataItems.put(name, (Map<Long,String>)editor.getValue());
			}
			setValue(dataItems);
		}
	}

	@Override
	public String converter() {
		if (getValue() == null) {
			return null;
		}
		return getSource().toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setValue(Object value) {
		super.setValue(value);
		if(value!=null) {
			Map<String, Map<Long,String>> dataItems=(Map<String, Map<Long,String>>)value;
			JsonObject sourceData=new JsonObject();
			for(String key:dataItems.keySet()) {
				editor.setValue(dataItems.get(key));
				sourceData.put(key, editor.getSource());
			}
			setSource(sourceData);
		}
	}

	@Override
	public void setArgs(String args) {
		
	}
	

}
