package com.common.converter;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import com.topnetwork.converter.AbstractArgsConverterEditor;
import com.xiaoshan.edu.enums.timetable.WeekEnum;

import start.framework.commons.rest.converter.map.MapStringStringConverterEditor;
import start.magic.thirdparty.json.JsonObject;

public class MapWeekMapStringStringConverterEditor extends AbstractArgsConverterEditor<String> {

	private MapStringStringConverterEditor editor;
	
	public MapWeekMapStringStringConverterEditor(Class<?> prototype) {
		super(prototype);
		editor=new MapStringStringConverterEditor(Map.class); 
	}

	@SuppressWarnings("unchecked")
	@Override
	public void restore(Object value) {
		if (value != null) {
			JsonObject json;
			if(value.getClass().equals(String.class)) {
				json=new JsonObject(String.valueOf(value));
			}else {
				json=(JsonObject)value;
			}
			Map<WeekEnum, Map<String,String>> dataItems=new LinkedHashMap<>();
			Iterator<String> keys=json.keys();
			while(keys.hasNext()) {
				String name=keys.next();
				editor.restore(json.getJsonObject(name));
				dataItems.put(WeekEnum.valueOf(name), (Map<String,String>)editor.getValue());
			}
			setValue(dataItems);
		}
	}

	@Override
	public String converter() {
		if (getValue() == null) {
			return null;
		}
		return getSource().toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setValue(Object value) {
		super.setValue(value);
		if(value!=null) {
			Map<WeekEnum, Map<String,String>> dataItems=(Map<WeekEnum, Map<String,String>>)value;
			JsonObject sourceData=new JsonObject();
			for(WeekEnum key:dataItems.keySet()) {
				editor.setValue(dataItems.get(key));
				sourceData.put(key.name(), editor.getSource());
			}
			setSource(sourceData);
		}
	}

	@Override
	public void setArgs(String args) {
		
	}
	

}
