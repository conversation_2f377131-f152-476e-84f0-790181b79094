package com.common.converter;

import java.util.List;

import com.topnetwork.converter.AbstractArgsConverterEditor;
import com.xiaoshan.edu.dto.TeacherTableDTO;

import start.framework.commons.rest.ConverterEditorUtils;
import start.magic.thirdparty.json.JsonArray;

public class TeacherTableConverterEditor extends AbstractArgsConverterEditor<String> {

	public TeacherTableConverterEditor(Class<?> prototype) {
		super(prototype);
	}

	@Override
	public void restore(Object value) {
		if (value != null) {
			JsonArray array;
			if(value.getClass().equals(String.class)) {
				array=new JsonArray(String.valueOf(value));
			}else {
				array=(JsonArray)value;
			}
			setValue(ConverterEditorUtils.restoreList(TeacherTableDTO.class, array));
		}
	}

	@Override
	public String converter() {
		if (getValue() == null) {
			return null;
		}
		return getSource().toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setValue(Object value) {
		super.setValue(value);
		if(value!=null) {
			List<TeacherTableDTO> list=(List<TeacherTableDTO>)value;
			setSource(ConverterEditorUtils.converterObject(list));
		}
	}

	@Override
	public void setArgs(String args) {
		
	}
	

}
