package com.common.converter;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.topnetwork.converter.AbstractArgsConverterEditor;
import com.xiaoshan.edu.dto.DataTableDTO;

import start.magic.thirdparty.json.JsonObject;

public class MapDataTableConverterEditor extends AbstractArgsConverterEditor<String> {

	private TeacherTableConverterEditor editor;
	
	public MapDataTableConverterEditor(Class<?> prototype) {
		super(prototype);
		editor=new TeacherTableConverterEditor(List.class);
	}

	@Override
	public void restore(Object value) {
		if (value != null) {
			JsonObject data;
			if(value.getClass().equals(String.class)) {
				data=new JsonObject(String.valueOf(value));
			}else {
				data=(JsonObject)value;
			}
			Map<String,Object> valMap=new LinkedHashMap<>();
			Iterator<String> keys=data.keys();
			while(keys.hasNext()) {
				String name=keys.next();
				editor.restore(data.get(name));
				valMap.put(name, editor.getValue());
			}
			setValue(valMap);
		}
	}

	@Override
	public String converter() {
		if (getValue() == null) {
			return null;
		}
		return getSource().toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public void setValue(Object value) {
		super.setValue(value);
		if(value!=null) {
			Map<String,List<DataTableDTO>> dataItems=(Map<String,List<DataTableDTO>>)value;
			JsonObject sourceData=new JsonObject();
			for(String key:dataItems.keySet()) {
				editor.setValue(dataItems.get(key));
				sourceData.put(key, editor.getSource());
			}
			setSource(sourceData);
		}
	}

	@Override
	public void setArgs(String args) {
		
	}
	

}
