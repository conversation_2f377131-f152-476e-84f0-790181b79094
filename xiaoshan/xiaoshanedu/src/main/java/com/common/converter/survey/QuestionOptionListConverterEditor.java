package com.common.converter.survey;

import java.util.List;

import com.topnetwork.converter.AbstractArgsConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;

import start.framework.commons.rest.ConverterEditorUtils;
import start.magic.thirdparty.json.JsonArray;

public class QuestionOptionListConverterEditor extends AbstractArgsConverterEditor<String> {

    public QuestionOptionListConverterEditor(Class<?> prototype) {
        super(prototype);
    }

    @Override
    public void setArgs(String args) {

    }


    @Override
    public void restore(Object value) {
        if (value != null){
            JsonArray jsonArray;
            if (value.getClass().equals(String.class)) {
                jsonArray = new JsonArray(String.valueOf(value));
            }else {
                jsonArray = ((JsonArray) value);
            }
            setValue(ConverterEditorUtils.restoreList(QuestionOptionDTO.class, jsonArray));
        }
    }

    @Override
    public String converter() {
        if (getValue() == null) {
            return null;
        }
        return getSource().toString();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void setValue(Object value) {
        super.setValue(value);
        if(value!=null) {
            List<QuestionOptionDTO> list=(List<QuestionOptionDTO>)value;
            setSource(ConverterEditorUtils.converterObject(list));  // 转成 jsonArray 保存一份，方便直接返回前端
        }
    }
}
