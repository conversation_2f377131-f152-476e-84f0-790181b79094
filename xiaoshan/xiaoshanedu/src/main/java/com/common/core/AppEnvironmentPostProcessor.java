package com.common.core;

import com.common.utils.LocalizationMessage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileUrlResource;
import org.springframework.core.io.Resource;
import start.magic.core.ApplicationException;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * 加载自定义配置文件
 * <AUTHOR>
 *
 */
public class AppEnvironmentPostProcessor implements EnvironmentPostProcessor {

	@Override
	public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
		
		
		
		
		//1、类路径配置文件
		String[] profilesCls = {};
		for (String profile : profilesCls) {
			Resource resource = new ClassPathResource(profile);
			environment.getPropertySources().addLast(loadProfiles(resource));
		}
		//2、本地配置文件
		String[] profilesFile = {};
		for (String profile : profilesFile) {
			try {
				Resource resource = new FileUrlResource(profile);
				environment.getPropertySources().addLast(loadProfiles(resource));
			} catch (MalformedURLException e) {
				throw new ApplicationException(e);
			}
		}
		LocalizationMessage.setLanaguageBundle("cn", ResourceBundle.getBundle("language.message_cn"));
	}

	public PropertySource<?> loadProfiles(Resource resource) {
		try {
			Properties properties = new Properties();
			properties.load(resource.getInputStream());
			return new PropertiesPropertySource(resource.getFilename(), properties);
		} catch (IOException e) {
			throw new ApplicationException(e);
		}
	}
	
}
