package com.common;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import start.framework.commons.rest.HttpClient;

/**
 * 开启服务
 * <AUTHOR>
 */

@SpringBootApplication
@ComponentScan(basePackages = {
		"com.common.core",
		"com.xiaoshan"})
@EnableAsync
public class RunXiaoShanEdu {

    public static void main(String[] args) {
    	HttpClient.setDefaultHttps();
		SpringApplication.run(RunXiaoShanEdu.class, args);
    }
}
