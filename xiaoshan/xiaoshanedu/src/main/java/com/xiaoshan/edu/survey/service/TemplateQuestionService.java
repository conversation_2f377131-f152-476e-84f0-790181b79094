package com.xiaoshan.edu.survey.service;

import com.xiaoshan.edu.ao.survey.QuestionAO;
import com.xiaoshan.edu.survey.entity.TemplateQuestionDO;
import com.xiaoshan.edu.vo.survey.QuestionImportVO;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import org.springframework.web.multipart.MultipartFile;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface TemplateQuestionService extends SqlBaseService<TemplateQuestionDO,Long> {

    /**
     * 获取模板题目
     * @param id
     * @return
     */
    List<QuestionVO> getByTemplateId(Long id);

    /**
     * 批量添加（编辑）调查题目
     * @param templateId
     * @param ao
     */
    void addOrUpdate(Long templateId, List<QuestionAO> ao);

    /**
     * 导入模板内容
     * @param templateId
     * @param file
     * @return
     */
    List<QuestionImportVO> importTemplate(Long templateId, MultipartFile file);


    /**
     * excel导出
     * @param id
     * @param response
     */
    void export(HttpServletResponse response, Long id);
}
