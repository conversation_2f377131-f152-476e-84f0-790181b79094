package com.xiaoshan.edu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaoshan.basic.dto.ClassRoomTreeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.persistence.annotation.Column;

import java.util.List;

@Data
public class ClassFloorTreeDTO {

    @ApiModelProperty("楼层id")
    private Long floorId;

    @ApiModelProperty("楼层名称")
    private String floorName;

    @ApiModelProperty("楼层")
    private Long level;

    @ApiModelProperty("房间集合")
    @Column("roomTreeDTOS")
    @JsonProperty("roomTreeDTOS")
    private List<ClassRoomTreeDTO> classRoomTreeDTOList;
}
