package com.xiaoshan.edu.openclass.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.common.model.UserContextHolder;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.common.annotation.AuthenticationCheck;
import com.common.annotation.RepeatSubmit;
import com.common.mvc.BaseController;
import com.topnetwork.ao.IDAO;
import com.topnetwork.ao.PageAO;
import com.xiaoshan.basic.vo.FlowNodesListVO;
import com.xiaoshan.edu.ao.openclass.CancelAO;
import com.xiaoshan.edu.ao.openclass.ClassRecordAO;
import com.xiaoshan.edu.ao.openclass.CloudRoomAO;
import com.xiaoshan.edu.ao.openclass.OpeArchivesAO;
import com.xiaoshan.edu.ao.openclass.OpeWxPageAO;
import com.xiaoshan.edu.ao.openclass.OpenClassApproveAO;
import com.xiaoshan.edu.ao.openclass.OpenClassCommentAO;
import com.xiaoshan.edu.ao.openclass.OpenClassPageAO;
import com.xiaoshan.edu.ao.openclass.OpenClassPublishAO;
import com.xiaoshan.edu.ao.openclass.OpenClassSimulationAO;
import com.xiaoshan.edu.ao.openclass.OpenClassSquarePageAO;
import com.xiaoshan.edu.ao.openclass.OpenClassSubjectAO;
import com.xiaoshan.edu.ao.openclass.OpenClassTeacherNameAO;
import com.xiaoshan.edu.ao.openclass.OpenClassTimeAO;
import com.xiaoshan.edu.ao.openclass.OpenClassTimePageAO;
import com.xiaoshan.edu.ao.openclass.RoomNameAO;
import com.xiaoshan.edu.ao.openclass.StaticTeachersDetailAO;
import com.xiaoshan.edu.ao.openclass.StatisticsTeacherAO;
import com.xiaoshan.edu.ao.openclass.UrgeAO;
import com.xiaoshan.edu.openclass.service.CloudRoomService;
import com.xiaoshan.edu.openclass.service.OpenClassService;
import com.xiaoshan.edu.vo.openclass.CloudRoomVO;
import com.xiaoshan.edu.vo.openclass.ConfirmAndPublishNumVO;
import com.xiaoshan.edu.vo.openclass.GeneralCommentVO;
import com.xiaoshan.edu.vo.openclass.OpenClassStatisticsVO;
import com.xiaoshan.edu.vo.openclass.OpenClassVO;
import com.xiaoshan.edu.vo.openclass.PersonalCommentVO;
import com.xiaoshan.edu.vo.openclass.StatisticsDetailAO;
import com.xiaoshan.edu.vo.openclass.SubjectStatisticsVO;
import com.xiaoshan.edu.vo.openclass.TeacherInfoVO;
import com.xiaoshan.edu.vo.openclass.TeacherStatisticsVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@RestController
@RequestMapping("/v1/openClass")
@Api(tags = "公开课接口")
public class OpenClassController extends BaseController {

	@Autowired
	private OpenClassService openClassService;
	@Autowired
	private CloudRoomService cloudRoomService;

	@RepeatSubmit
	@ApiOperation("公开课发布")
	@AuthenticationCheck
	@PostMapping("/publish")
	public BaseResponse publish(@RequestBody OpenClassPublishAO ao){
		openClassService.publish(ao);
		return response();
	}

	@RepeatSubmit
	@ApiOperation("编辑后保存公开课")
	@AuthenticationCheck
	@PutMapping("/edit")
	public BaseResponse edit(@RequestBody OpenClassPublishAO ao){
		openClassService.edit(ao);
		return response();
	}


	@ApiOperation("管理员端-公开课记录")
	@AuthenticationCheck
	@PostMapping("/page")
	public PageResponse<List<OpenClassVO>> page(@RequestBody OpenClassPageAO ao){
		return response(openClassService.page(ao));
	}

	@ApiOperation("小程序管理端--公开课记录")
	@AuthenticationCheck
	@PostMapping("/wx/page")
	public PageResponse<List<OpenClassVO>> wxPage(@RequestBody OpeWxPageAO ao){
		return response(openClassService.wxPage(ao));
	}


	@ApiOperation("个人端-公开课广场、听课记录")
	@AuthenticationCheck
	@PostMapping("/me/square")
	public PageResponse<List<OpenClassVO>> square(@RequestBody OpenClassSquarePageAO ao){
		return response(openClassService.square(ao));
	}


	@ApiOperation("个人端-上课记录")
	@AuthenticationCheck
	@PostMapping("/me/launch/record")
	public PageResponse<List<OpenClassVO>> launchRecord(@RequestBody OpenClassPageAO ao){
		return response(openClassService.launchRecord(ao));
	}

	@ApiOperation("根据id查看公开课")
	@AuthenticationCheck
	@GetMapping("/getById/{openClassId}")
	public ResultResponse<OpenClassVO> getById(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		return response(openClassService.getById(openClassId));
	}

	@ApiOperation("听课教师列表")
	@AuthenticationCheck
	@GetMapping("/listen/list/{openClassId}")
	public ResultResponse<List<TeacherInfoVO>> listenList(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		return response(openClassService.listenList(openClassId));
	}

	@ApiOperation("根据id确认开课")
	@AuthenticationCheck
	@PutMapping("/confirm")
	public BaseResponse confirm(@RequestBody IDAO ao) {
		openClassService.confirm(ao.getId());
		return response();
	}

	@ApiOperation("根据id发布公开课")
	@AuthenticationCheck
	@PutMapping("/publishById")
	public BaseResponse publishById(@RequestBody IDAO ao) {
		openClassService.publishById(ao.getId());
		return response();
	}

	@ApiOperation("根据id删除公开课")
	@AuthenticationCheck
	@DeleteMapping
	public BaseResponse remove(@RequestBody IDAO ao) {
		openClassService.removeOpenClass(ao.getId());
		return response();
	}

	@ApiOperation("公开课统计--三种类型的公开课总数")
	@AuthenticationCheck
	@PostMapping("/statistics")
	public ResultResponse<OpenClassStatisticsVO> statistics(@RequestBody OpenClassTimeAO ao){
		return response(openClassService.statistics(ao));
	}

	@ApiOperation("公开课统计--教师")
	@AuthenticationCheck
	@PostMapping("/statistics/teachers")
	public PageResponse<List<TeacherStatisticsVO>> statisticsTeachers(@RequestBody OpenClassTeacherNameAO ao){
		return response(openClassService.statisticsTeachers(ao));
	}

	@ApiOperation("公开课统计--教师、科目--详情")
	@AuthenticationCheck
	@PostMapping("/statistics/teachers/detail")
	public PageResponse<List<OpenClassVO>> statisticsTeachersDetails(@RequestBody StaticTeachersDetailAO ao){
		return response(openClassService.statisticsTeachersDetails(ao));
	}

	@ApiOperation("公开课记录数据导出")
	@AuthenticationCheck
	@PostMapping("/export")
	public void export(@RequestBody OpenClassPageAO ao, HttpServletResponse response){
		openClassService.export(ao,response);
	}

	@ApiOperation("公开课统计--教师、科目--详情--数据导出")
	@AuthenticationCheck
	@PostMapping("/export/statistics")
	public void exportStatistics(@RequestBody StatisticsDetailAO ao, HttpServletResponse response){
		openClassService.exportStatistics(ao,response);
	}

	@ApiOperation("公开课统计--教师--数据导出")
	@AuthenticationCheck
	@PostMapping("/export/statistics/teachers")
	public void exportStatisticsTeachers(@RequestBody StatisticsTeacherAO ao,HttpServletResponse response){
		openClassService.exportStatisticsTeachers(ao,response);
	}

	@ApiOperation("公开课统计--科目")
	@AuthenticationCheck
	@PostMapping("/statistics/subject")
	public PageResponse<List<SubjectStatisticsVO>> statisticsSubject(@RequestBody OpenClassTimePageAO ao){
		return response(openClassService.statisticsSubjects(ao));
	}

	@ApiOperation("公开课统计--科目--数据导出")
	@AuthenticationCheck
	@PostMapping("/export/statistics/subject")
	public void exportStatisticsSubject(@RequestBody OpenClassSubjectAO ao,HttpServletResponse response){
		openClassService.exportStatisticsSubject(ao,response);
	}

	@ApiOperation("我要听课")
	@AuthenticationCheck
	@PutMapping("/me/wantListen/{openClassId}")
	public BaseResponse wantListen(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		openClassService.wantListen(openClassId);
		return response();
	}

	@ApiOperation("取消听课")
	@AuthenticationCheck
	@PutMapping("/me/cancelListen/{openClassId}")
	public BaseResponse cancelListen(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		openClassService.cancelListen(openClassId);
		return response();
	}

	@ApiOperation("个人端--我的评价")
	@AuthenticationCheck
	@GetMapping("/me/comment/{openClassId}")
	public ResultResponse<PersonalCommentVO> getComment(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		return response(openClassService.getComment(openClassId));
	}

	@ApiOperation("个人端--提交评价")
	@AuthenticationCheck
	@PutMapping("/me/comment/submit")
	public BaseResponse submitComment(@RequestBody OpenClassCommentAO ao){
		openClassService.submitComment(ao);
		return response();
	}

	@ApiOperation("公开课总评价")
	@AuthenticationCheck
	@GetMapping("/comment/general/{openClassId}")
	public ResultResponse<GeneralCommentVO> generalComment(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		return response(openClassService.generalComment(openClassId));
	}

	@ApiOperation("评价列表")
	@AuthenticationCheck
	@GetMapping("/comment/list/{openClassId}")
	public ResultResponse<List<PersonalCommentVO>> commentList(@PathVariable @ApiParam("公开课id") @NotNull @LongValid Long openClassId){
		return response(openClassService.commentList(openClassId));
	}

	@ApiOperation("个人端--取消new标签")
	@AuthenticationCheck
	@PutMapping("/me/cancelNew/{openClassId}")
	public BaseResponse cancelNew(@PathVariable @ApiParam("openClassId") @NotNull @LongValid Long openClassId){
		openClassService.cancelNew(openClassId);
		return response();
	}

	@ApiOperation("待确认、待发布数量")
	@AuthenticationCheck
	@PostMapping("/confirming/num")
	public ResultResponse<ConfirmAndPublishNumVO> redNum(@RequestBody OpenClassTimeAO ao){
		return response(openClassService.redNum(ao));
	}

	@ApiOperation("公开课审批")
	@AuthenticationCheck
	@PutMapping("/approve")
	public BaseResponse approve(@RequestBody OpenClassApproveAO approve){
		openClassService.approve(approve);
		return response();
	}

	@ApiOperation("开课证明pdf")
	@AuthenticationCheck
	@GetMapping("/pdf/{openClassId}")
	public void pdf(@PathVariable @ApiParam("openClassId") @NotNull @LongValid Long openClassId, HttpServletResponse response){
		openClassService.pdf(openClassId,response);
	}

	@ApiOperation("模拟获取节点")
	@AuthenticationCheck
	@PostMapping("/simulation")
	public ResultResponse<FlowNodesListVO> simulation(@RequestBody OpenClassSimulationAO ao){
		return response(openClassService.simulation(ao));
	}

	@ApiOperation("催办")
	@AuthenticationCheck
	@PutMapping("/urging")
	public BaseResponse urging(@RequestBody UrgeAO ao){
		openClassService.urging(ao);
		return response();
	}

	@ApiOperation("撤销")
	@AuthenticationCheck
	@PutMapping("/cancel")
	public BaseResponse cancel(@RequestBody CancelAO ao){
		openClassService.cancel(ao);
		return response();
	}

	@ApiOperation("科目列表")
	@AuthenticationCheck
	@GetMapping("/subject/list")
	public ResultResponse<List<String>> subjectList(){
		return response(openClassService.subjectList());

	}

	@ApiOperation("根据房间类型获取教室树形结构，-1-云课堂教室，1-普通教室，2-实验室，3-专业教室")
	@AuthenticationCheck
	@PostMapping("/classRoom")
	public ResultResponse<List<ClassBuildingTreeDTO>> getClassRoom(@RequestBody @NotNull @NotEmpty List<Integer> types){
		return response(openClassService.getClassRoom(types));
	}

	@ApiOperation("云课堂教室列表")
	@AuthenticationCheck
	@PostMapping("/cloudRoom/page")
	public ResultResponse<List<CloudRoomVO>> getCloudRoom(@RequestBody PageAO ao){
		return response(cloudRoomService.getCloudRoom(ao));
	}


	@ApiOperation("删除云课堂教室")
	@AuthenticationCheck
	@DeleteMapping("/cloudRoom/{id}")
	public BaseResponse getCloudRoomTree(@PathVariable @ApiParam("云课堂教室id") @NotNull @LongValid Long id){
		cloudRoomService.remove(id);
		return response();
	}

	@ApiOperation("添加云课堂教室")
	@AuthenticationCheck
	@PostMapping("/cloudRoom/add")
	public BaseResponse addCloudRoom(@RequestBody @NotNull List<CloudRoomAO> ao){
		cloudRoomService.addCloudRoom(ao);
		return response();
	}

	@ApiOperation("获取所有房间，除去云课堂房间")
	@AuthenticationCheck
	@PostMapping("/cloudRoom/all")
	public ResultResponse<List<CloudRoomVO>> all(@RequestBody RoomNameAO ao){
		return response(cloudRoomService.all(ao));
	}

	@ApiOperation("电子档案--公开课列表")
	@AuthenticationCheck
	@PostMapping("/archives/page")
	public PageResponse<List<OpenClassVO>> archives(@RequestBody OpeArchivesAO ao){
		return response(openClassService.archives(ao));
	}

	@ApiOperation("电子档案--查询老师的上课记录和听课记录")
	@AuthenticationCheck
	@PostMapping("/archives/all")
	public PageResponse<List<OpenClassVO>> archivesAll(@RequestBody OpeArchivesAO ao){
		return response(openClassService.archivesAll(ao));
	}

	@ApiOperation("公开课（我的档案）")
	@AuthenticationCheck
	@PostMapping("/open_course/mine")
	public PageResponse<List<OpenClassVO>> getMyOpenCourse(@RequestBody OpeArchivesAO ao){
		ao.setTeacherId(UserContextHolder.getUser().getId());
		return response(openClassService.archivesAll(ao));
	}

	@ApiOperation("查询老师的上课记录-不分页")
	@AuthenticationCheck
	@PostMapping("/classRecord")
	public ResultResponse<List<OpenClassVO>> classRecord(@RequestBody ClassRecordAO ao){
		return response(openClassService.classRecord(ao));
	}

}
