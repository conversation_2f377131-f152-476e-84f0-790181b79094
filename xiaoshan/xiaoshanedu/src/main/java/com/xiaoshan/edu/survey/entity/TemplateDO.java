package com.xiaoshan.edu.survey.entity;

import java.util.List;

import com.common.converter.survey.CurriculumListConverterEditor;
import com.xiaoshan.edu.enums.survey.SurveyObj;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.ScriptConverter;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.converter.FieldJson;

@Getter@Setter@ToString
@Entity("template")
@Table("sur_template")
@TableDef(comment = "调查模板表", charset = "utf8mb4", collate = "utf8mb4_bin")
public class TemplateDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;
	
	public TemplateDO(){}

	@ColumnDef(comment = "模板名称", length = 30)
	private String name;

	@ColumnDef(comment = "调查对象")
	private SurveyObj surveyObj;

	@ColumnDef(comment = "关联课程列表", isNull = true)
	@ScriptConverter(FieldJson.class)
	@PropertyConverter(CurriculumListConverterEditor.class)
	private List<String> curriculumList;

}
