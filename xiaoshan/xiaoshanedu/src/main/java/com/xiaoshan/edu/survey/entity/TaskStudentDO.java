package com.xiaoshan.edu.survey.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.IndexesUnion;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.NormalUnion;

@Getter@Setter@ToString
@Entity("taskStudent")
@Table("sur_task_student")
@TableDef(comment = "调查任务--学生关联表", indexes =
@IndexesUnion(normal = @NormalUnion(fields = {"taskId", "className"})))
public class TaskStudentDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;
	
	public TaskStudentDO(){}

	@ColumnDef(comment = "调查任务id")
	private Long taskId;

	@ColumnDef(comment = "学生id")
	private Long studentId;

	@ColumnDef(comment = "学生userId")
	private Long studentUserId;

	@ColumnDef(comment = "ic卡号")
	private String icCardNo;

	@ColumnDef(comment = "学号", isNull = true)
	private String studentNo;

	@ColumnDef(comment = "学生姓名")
	private String name;

	@ColumnDef(comment = "班级名称")
	private String className;

	@ColumnDef(comment = "是否完成")
	private Boolean isFinish;

}
