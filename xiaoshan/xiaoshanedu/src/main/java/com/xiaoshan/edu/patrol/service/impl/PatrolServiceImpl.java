package com.xiaoshan.edu.patrol.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.xiaoshan.basic.ao.CourseQuery;
import com.xiaoshan.basic.vo.*;
import com.xiaoshan.basic.vo.RoomVO;
import com.xiaoshan.edu.ao.patrol.*;
import com.xiaoshan.edu.api.facade.CourseFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import com.xiaoshan.edu.courseadjustment.entity.ReplaceSectionDO;
import com.xiaoshan.edu.courseadjustment.service.CourseAdjustmentService;
import com.xiaoshan.edu.courseadjustment.service.ReplaceSectionService;
import com.xiaoshan.edu.dto.PatrolElectronicDTO;
import com.xiaoshan.edu.enums.patrol.PatrolAppraise;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import com.xiaoshan.edu.timetable.utils.DateUtils;
import com.xiaoshan.edu.vo.patrol.*;
import com.xiaoshan.edu.vo.patrol.ClassVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

import com.alibaba.excel.EasyExcel;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.ao.timetable.StudentListQuery;
import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import com.xiaoshan.edu.dto.PatrolDTO;
import com.xiaoshan.edu.enums.patrol.NotifyObject;
import com.xiaoshan.edu.enums.patrol.PatrolType;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.patrol.dao.PatrolDao;
import com.xiaoshan.edu.patrol.entity.PatrolDO;
import com.xiaoshan.edu.patrol.entity.PatrolObjectDO;
import com.xiaoshan.edu.patrol.excel.CustomCellWriteHandler;
import com.xiaoshan.edu.patrol.excel.ExcelHeadsUtil;
import com.xiaoshan.edu.patrol.excel.export.PatrolRecordExport;
import com.xiaoshan.edu.patrol.excel.export.PatrolTeacherRecordExport;
import com.xiaoshan.edu.patrol.excel.export.StatisticsClassExport;
import com.xiaoshan.edu.patrol.excel.export.StatisticsStudentExport;
import com.xiaoshan.edu.patrol.excel.export.StatisticsTeacherExport;
import com.xiaoshan.edu.patrol.service.PatrolObjectService;
import com.xiaoshan.edu.patrol.service.PatrolService;
import com.xiaoshan.edu.patrol.util.NumberConvertUtil;
import com.xiaoshan.edu.service.BasicPushMessage;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.timetable.ClassesVO;
import com.xiaoshan.edu.vo.timetable.StudentTableDetailVO;
import com.xiaoshan.edu.vo.timetable.TeachClassesDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.oa.dto.ClassDTO;
import com.xiaoshan.oa.dto.StudentDto;
import com.xiaoshan.oa.dto.TeacherDto;
import com.xiaoshan.oa.vo.notify.NotifyParentVO;

import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.MapConvert;
import start.framework.commons.utils.TimeUtils;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;

@Service("patrolService")
public class PatrolServiceImpl extends SqlBaseServiceImplV2Ext<PatrolDO, Long>
        implements PatrolService {

    private final PatrolDao patrolDao;

    @Autowired
    private PatrolObjectService patrolObjectService;

    @Autowired
    private FoundationFacade foundationFacade;

    @Autowired
    private BasicPushMessage basicPushMessage;

    @Autowired
    private GeneralTableService generalTableService;

    @Autowired
    private CurriculumService curriculumService;

    @Autowired
    CourseAdjustmentService courseAdjustmentService;

    @Autowired
    ReplaceSectionService replaceSectionService;

    @Autowired
    private SettingFacade settingFacade;

    @Autowired
    private CourseFacade courseFacade;

    private final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            1, 10, 10, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(1),
            new ThreadPoolExecutor.DiscardOldestPolicy());

    public static final String ROOM_ID_CONTAINS = "ROOM";
    public static final String ROLE_HEAD_TEACHER = "ROLE_head_teacher";
    public static final String ROLE_PARENT = "ROLE_parent";


    public PatrolServiceImpl(@Qualifier("patrolDao") PatrolDao patrolDao) {
        super(patrolDao);
        this.patrolDao = patrolDao;
    }

    /**
     * 根据条件分页查询列表
     */
    @Override
    public QueryResult<List<PatrolVO>> queryPage(PatrolRecordPageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        QueryResult<List<PatrolDTO>> queryResult = queryForPage(PatrolDTO.class, "getPatrolAndObjects", map);
        QueryResult<List<PatrolVO>> queryResult1 = new QueryResult<>();
        queryResult1.setPageInfo(new PageResponse.PageInfo());
        List<PatrolVO> result1 = new ArrayList<>();
        if (queryResult != null && queryResult.getResult() != null && queryResult.getResult().size() > 0) {
            queryResult1.setPageInfo(queryResult.getPageInfo());
            for (PatrolDTO patrolDTO : queryResult.getResult()) {
                PatrolVO patrolVO = ClassConverter.aTob(patrolDTO, new PatrolVO());
                patrolVO.setAppraise(patrolDTO.getAppraise().getName());
                patrolVO.setType(patrolDTO.getType().getName());
                patrolVO.setObjectName(patrolVO.getObjectName());
                if (patrolDTO.getType().equals(PatrolType.TEACHER)) {
                    if (StringUtils.isEmpty(patrolDTO.getDepartmentName())) {
                        patrolVO.setDepartmentName("待分配");
                    }
                } else {
                    patrolVO.setDepartmentName("/");
                }
                patrolVO.setClassName(patrolVO.getClassName());
                result1.add(patrolVO);
            }
        }
        queryResult1.setResult(result1);
        return queryResult1;
    }

    /**
     * 根据条件分页查询列表（巡课统计-单个对象详情统计）
     */
    @Override
    public QueryResult<List<PatrolVO>> queryPage(PatrolSinglePageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        QueryResult<List<PatrolDTO>> queryResult = queryForPage(PatrolDTO.class, "getPatrolAndObjects", map);
        QueryResult<List<PatrolVO>> queryResult1 = new QueryResult<>();
        List<PatrolVO> result1 = new ArrayList<>();
        if (queryResult != null && queryResult.getResult() != null && queryResult.getResult().size() > 0) {
            queryResult1.setPageInfo(queryResult.getPageInfo());
            for (PatrolDTO patrolDTO : queryResult.getResult()) {
                PatrolVO patrolVO = ClassConverter.aTob(patrolDTO, new PatrolVO());
                patrolVO.setAppraise(patrolDTO.getAppraise().getName());
                patrolVO.setType(patrolDTO.getType().getName());
                patrolVO.setObjectName(patrolVO.getObjectName());
                if (patrolDTO.getType().equals(PatrolType.TEACHER)) {
                    if (StringUtils.isEmpty(patrolDTO.getDepartmentName())) {
                        patrolVO.setDepartmentName("待分配");
                    }
                } else {
                    patrolVO.setDepartmentName("/");
                }
                patrolVO.setClassName(patrolVO.getClassName());
                result1.add(patrolVO);
            }
        }
        queryResult1.setResult(result1);
        queryResult1.setPageInfo(new PageResponse.PageInfo());
        return queryResult1;
    }

    /**
     * 根据条件统计巡课数据
     */
    @Override
    public QueryResult<List<PatrolStatisticsVO>> getStatisticsPage(PatrolStatisticsPageAO ao) {

        String objectName = ao.getObjectName();
        String departmentName = ao.getDepartmentName();
        Date startDate = ao.getStartDate();
        Date endDate = null;
        if (ao.getEndDate() != null) {
            LocalDate localEndDate = ao.getEndDate().toLocalDate().plusDays(1);
            endDate = Date.from(localEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        // 因为是要统计所有数据，所以分页、姓名模糊查询、时间过滤、部门过滤 都在程序里做
        Map<String, Object> map = new HashMap<>();
        if (!StringUtils.isEmpty(ao.getClassName())) {
            map.put("className", ao.getClassName());
        }
        map.put("type", String.valueOf(ao.getType()));
        QueryResult<List<PatrolStatisticsVO>> queryResult = new QueryResult<>();
        queryResult.setResult(new ArrayList<>());
        queryResult.setPageInfo(new PageResponse.PageInfo());
        QueryResult<List<PatrolStatisticsVO>> result1 = queryForPage(PatrolStatisticsVO.class, "getPatrolStatistics", map);
        if (result1 != null) {
            queryResult = result1;
        } else {
            return queryResult;
        }
        List<PatrolStatisticsVO> result = queryResult.getResult();
        // 姓名筛选
        if (!StringUtils.isEmpty(objectName)) {
            result = result.stream().filter(patrolStatisticsVO -> patrolStatisticsVO.getObjectName().contains(objectName)).collect(Collectors.toList());
        }
        // 部门筛选
        if (!StringUtils.isEmpty(departmentName)) {
            result = result.stream().filter(patrolStatisticsVO -> patrolStatisticsVO.getDepartmentName().contains(departmentName)).collect(Collectors.toList());
        }
        // 时间过滤
        if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) {
            Date finalEndDate = endDate;
            result = result.stream().filter(patrolStatisticsVO -> patrolStatisticsVO.getRecordTime().after(startDate) && patrolStatisticsVO.getRecordTime().before(finalEndDate)).collect(Collectors.toList());
        }

        Map<Long, PatrolStatisticsVO> patrolStatisticsVoMap = new HashMap<>();
        for (PatrolStatisticsVO patrolStatisticsVO : result) {
            int excellentCount = 0;
            int commonCount = 0;
            int badCount = 0;
            switch (patrolStatisticsVO.getAppraise()) {
                case EXCELLENT:
                    excellentCount += 1;
                    break;
                case COMMON:
                    commonCount += 1;
                    break;
                case BAD:
                    badCount += 1;
                    break;
                default:
            }
            PatrolStatisticsVO patrolStatisticsVo1 = patrolStatisticsVoMap.get(patrolStatisticsVO.getObjectId());
            if (patrolStatisticsVo1 != null) {
                patrolStatisticsVo1.setExcellentCount((Integer.parseInt(patrolStatisticsVo1.getExcellentCount().substring(0, patrolStatisticsVo1.getExcellentCount().length() - 1)) + excellentCount) + "次");
                patrolStatisticsVo1.setCommonCount((Integer.parseInt(patrolStatisticsVo1.getCommonCount().substring(0, patrolStatisticsVo1.getCommonCount().length() - 1)) + commonCount) + "次");
                patrolStatisticsVo1.setBadCount((Integer.parseInt(patrolStatisticsVo1.getBadCount().substring(0, patrolStatisticsVo1.getBadCount().length() - 1)) + badCount) + "次");
                int total = badCount + commonCount + excellentCount;
                patrolStatisticsVo1.setTotalCount((Integer.parseInt(patrolStatisticsVo1.getTotalCount().substring(0, patrolStatisticsVo1.getTotalCount().length() - 1)) + total) + "次");
                patrolStatisticsVo1.setClassName(patrolStatisticsVo1.getClassName());
                if (StringUtils.isEmpty(patrolStatisticsVo1.getDepartmentName())) {
                    patrolStatisticsVo1.setDepartmentName("待分配");
                }
                patrolStatisticsVoMap.put(patrolStatisticsVO.getObjectId(), patrolStatisticsVo1);
            } else {
                patrolStatisticsVO.setExcellentCount(excellentCount + "次");
                patrolStatisticsVO.setCommonCount(commonCount + "次");
                patrolStatisticsVO.setBadCount(badCount + "次");
                int total = badCount + commonCount + excellentCount;
                patrolStatisticsVO.setTotalCount(total + "次");
                patrolStatisticsVO.setClassName(patrolStatisticsVO.getClassName());
                if (StringUtils.isEmpty(patrolStatisticsVO.getDepartmentName())) {
                    patrolStatisticsVO.setDepartmentName("待分配");
                }
                patrolStatisticsVoMap.put(patrolStatisticsVO.getObjectId(), patrolStatisticsVO);
            }
        }
        List<PatrolStatisticsVO> patrolStatisticsVoList = new ArrayList<>(patrolStatisticsVoMap.values());

        // 总记录数
        PageResponse.PageInfo pageInfo = new PageResponse.PageInfo();
        pageInfo.setTotalCount(patrolStatisticsVoList.size());

        // 分页
        if (ao.getPageSize() != -1) {
            patrolStatisticsVoList = patrolStatisticsVoList.stream().skip(ao.index()).limit(ao.getPageSize()).collect(Collectors.toList());
        }
        QueryResult<List<PatrolStatisticsVO>> queryResult1 = new QueryResult<>();
        queryResult1.setPageInfo(pageInfo);
        queryResult1.setResult(patrolStatisticsVoList);

        return queryResult1;
    }

    /**
     * 巡课记录数据导出
     */
    @Override
    public void recodeExcelExport(PatrolRecordPageAO ao, HttpServletResponse response) {
        ao.setPageSize(-1);
        QueryResult<List<PatrolVO>> listQueryResult = queryPage(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<PatrolVO> result = listQueryResult.getResult();
        List<PatrolTeacherRecordExport> list = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(0);
        if (result != null && result.size() > 0) {
            for (PatrolVO patrolVO : result) {
                PatrolTeacherRecordExport export = ClassConverter.aTob(patrolVO, new PatrolTeacherRecordExport());
                export.setIndex(index.incrementAndGet());
                list.add(export);
            }

        }
        String title = "教师巡课记录";
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition,Content-Type,Cache-control");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), PatrolTeacherRecordExport.class).head(ExcelHeadsUtil.patrolRecordHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_900001);
        }
    }

    /**
     * 单个对象详情数据导出
     */
    @Override
    public void singleRecodeExcelExport(PatrolSinglePageAO ao, HttpServletResponse response) {
        boolean isTeacher = ao.getType().equals(PatrolType.TEACHER);
        ao.setPageSize(-1);
        QueryResult<List<PatrolVO>> listQueryResult = queryPage(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<PatrolVO> result = listQueryResult.getResult();
        List<PatrolTeacherRecordExport> listTeacher = null;
        List<PatrolRecordExport> listOther = null;
        if (isTeacher) {
            listTeacher = new ArrayList<>();
        } else {
            listOther = new ArrayList<>();
        }
        AtomicInteger index = new AtomicInteger(0);
        if (result != null && result.size() > 0) {
            for (PatrolVO patrolVO : result) {
                if (isTeacher) {
                    PatrolTeacherRecordExport exportTeacher = ClassConverter.aTob(patrolVO, new PatrolTeacherRecordExport());
                    exportTeacher.setIndex(index.incrementAndGet());
                    listTeacher.add(exportTeacher);
                } else {
                    PatrolRecordExport exportOther = ClassConverter.aTob(patrolVO, new PatrolRecordExport());
                    exportOther.setIndex(index.incrementAndGet());
                    listOther.add(exportOther);
                }

            }

        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd");
        ao.setObjectName(ao.getObjectName());
        String title = ao.getObjectName() + " " + format.format(ao.getStartDate()) + " - " + format.format(ao.getEndDate()) + " 教师巡课统计";
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition,Content-Type,Cache-control");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            if (isTeacher) {
                EasyExcel.write(response.getOutputStream(), PatrolTeacherRecordExport.class).head(ExcelHeadsUtil.patrolRecordHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(listTeacher);
            } else {
                EasyExcel.write(response.getOutputStream(), PatrolRecordExport.class).head(ExcelHeadsUtil.singleRecordHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(listOther);
            }
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_900001);
        }
    }

    /**
     * 根据巡课教师姓名获取全部记录（时间降序）
     */
    @Override
    public QueryResult<List<PatrolVO>> getAppletRecord(PatrolPageAO ao, String name) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper
                .select("id as id",
                        "type as type",
                        "class_name as className",
                        "course_time as courseTime",
                        "course_name as courseName",
                        "created_date as recordTime")
                .eq("patrol_teacher_name", name)
                .orderByDesc("created_date");
        if (ao.getPageSize() != -1) {
            wrapper.last("limit " + ao.index() + "," + ao.getPageSize());
        }
        QueryResult<List<PatrolDTO>> queryResult = queryForPage(PatrolDTO.class, wrapper);
        if (queryResult == null) {
            return null;
        }
        List<PatrolDTO> result = queryResult.getResult();
        QueryResult<List<PatrolVO>> queryResult1 = new QueryResult<>();
        queryResult1.setPageInfo(queryResult.getPageInfo());
        List<PatrolVO> result1 = new ArrayList<>();
        if (result != null && result.size() > 0) {
            for (PatrolDTO patrolDTO : result) {
                List<String> nameList = patrolObjectService.getNamesByPatrolId(patrolDTO.getId());
                String name1 = nameList.get(0);
                if (patrolDTO.getType().equals(PatrolType.STUDENT)) {
                    if (nameList.size() > 1) {
                        patrolDTO.setObjectName(name1 + "等" + nameList.size() + "人");
                    } else {
                        patrolDTO.setObjectName(name1);
                    }
                } else {
                    patrolDTO.setObjectName(name1);
                }
                PatrolVO patrolVO = ClassConverter.aTob(patrolDTO, new PatrolVO());
                patrolVO.setType(patrolDTO.getType().getName());
                result1.add(patrolVO);
            }
        }
        queryResult1.setResult(result1);
        return queryResult1;
    }

    /**
     * 保存巡课记录并发送通知
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePatrol(PatrolSaveAO ao) {
        String objectId = ao.getObjectId();
        PatrolDO patrolDO = new PatrolDO();
        BeanUtils.copyProperties(ao, patrolDO);
        this.save(patrolDO);
        Long patrolId = patrolDO.getId();
        List<Long> classIdList;
        List<Long> headTeacherUserIds = null;
        if (ao.getType().equals(PatrolType.STUDENT)) {
            // 巡课对象是学生的时候需要通知巡课学生的班主任
            classIdList = ao.getClassIds();
        } else {
            // 巡课对象不是学生的时候通知的是通知所有听课学生的班主任
            classIdList = ao.getStudentIds();
        }
        if (classIdList != null && !classIdList.isEmpty()) {
            String classIds = classIdList.stream().distinct().map(String::valueOf).collect(Collectors.joining(","));
            if (!StringUtils.isEmpty(classIds)) {
                List<ClassDTO> allClass = foundationFacade.getAllClass(classIds);
                if (allClass != null && allClass.size() > 0) {
                    String headTeacherIds = allClass.stream().map(ClassDTO::getHeadTeacherId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
                    if (!StringUtils.isEmpty(headTeacherIds)) {
                        List<TeacherDto> headTeachers = foundationFacade.getTeachersByIds(headTeacherIds);
                        if (headTeachers != null && !headTeachers.isEmpty()) {
                            headTeacherUserIds = headTeachers.stream().map(TeacherDto::getUserId).collect(Collectors.toList());
                        }
                    }
                }
            }
        }

        switch (ao.getType()) {
            case TEACHER:
                MultiValueMap<String, String> map1 = new LinkedMultiValueMap<>();
                map1.add("current", "1");
                map1.add("size", "-1");
                map1.add("teacherIds", objectId);
                List<TeacherDto> teachers = foundationFacade.teachers(map1);
                if (teachers != null && teachers.size() != 0) {
                    TeacherDto teacherDto = get(teachers);
                    PatrolObjectDO patrolObjectDO = ClassConverter.aTob(teacherDto, new PatrolObjectDO());
                    patrolObjectDO.setId(null);
                    patrolObjectDO.setObjectId(Long.valueOf(objectId));
                    patrolObjectDO.setPatrolId(patrolId);
                    patrolObjectService.save(patrolObjectDO);

                    // 巡课通知异步发送
                    List<Long> finalHeadTeacherUserIds2 = headTeacherUserIds;
                    threadPool.execute(() -> {
                        if (ao.getIsNotify() && ao.getNotifyObject() != null) {
                            List<Long> userIds = new ArrayList<>();
                            switch (ao.getNotifyObject()) {
                                case COURSE_TEACHER:
                                    userIds.add(teacherDto.getUserId());
                                    basicPushMessage.patrolNotify(patrolDO, ao.getNotifyObject(), userIds, null, null);
                                    break;
                                case CLASS_ADVISER:
                                    if (!StringUtils.isEmpty(finalHeadTeacherUserIds2)) {
                                        userIds.addAll(finalHeadTeacherUserIds2);
                                        basicPushMessage.patrolNotify(patrolDO, ao.getNotifyObject(), userIds, null, teacherDto.getName());
                                    }
                                    break;
                                case CLASS_ADVISER_AND_COURSE_TEACHER:
                                    userIds.add(teacherDto.getUserId());
                                    basicPushMessage.patrolNotify(patrolDO, NotifyObject.COURSE_TEACHER, userIds, null, null);
                                    if (!StringUtils.isEmpty(finalHeadTeacherUserIds2)) {
                                        List<Long> userIds2 = new ArrayList<>(finalHeadTeacherUserIds2);
                                        basicPushMessage.patrolNotify(patrolDO, NotifyObject.CLASS_ADVISER, userIds2, null, teacherDto.getName());
                                    }
                                    break;
                                default:
                                    // 不通知家长
                                    throw new BusinessException(CodeRes.CODE_900002);
                            }
                        }
                    });
                } else {
                    throw new BusinessException(CodeRes.CODE_900003);
                }
                break;
            case CLASS:
                if (ao.getClassId().contains(ROOM_ID_CONTAINS)) {
                    // 走班
                    PatrolObjectDO patrolObjectDO = new PatrolObjectDO();
                    patrolObjectDO.setId(null);
                    if (objectId.contains(ROOM_ID_CONTAINS)) {
                        objectId = objectId.replaceAll("ROOM", "");
                    }
                    patrolObjectDO.setObjectId(Long.valueOf(objectId));
                    patrolObjectDO.setName(ao.getClassName());
                    patrolObjectDO.setPatrolId(patrolId);
                    patrolObjectService.save(patrolObjectDO);
                } else {
                    // 行政班
                    MultiValueMap<String, String> map2 = new LinkedMultiValueMap<>();
                    map2.add("current", "1");
                    map2.add("size", "-1");
                    map2.add("classIdStr", String.valueOf(ao.getClassId()));
                    List<ClassDTO> classDTOList = foundationFacade.classes(map2);
                    ClassDTO classDto;
                    if (classDTOList != null && classDTOList.size() > 0) {
                        // 巡课班级
                        classDto = classDTOList.get(0);
                        if (classDto != null) {
                            PatrolObjectDO patrolObjectDO = ClassConverter.aTob(classDto, new PatrolObjectDO());
                            patrolObjectDO.setId(null);
                            patrolObjectDO.setObjectId(Long.valueOf(objectId));
                            patrolObjectDO.setPatrolId(patrolId);
                            patrolObjectDO.setName(classDto.getName() + "班");
                            patrolObjectService.save(patrolObjectDO);
                        } else {
                            throw new BusinessException(CodeRes.CODE_900003);
                        }
                    }
                }


                // 巡课通知异步发送
                List<Long> finalHeadTeacherUserIds1 = headTeacherUserIds;
                threadPool.execute(() -> {
                    if (ao.getIsNotify() && ao.getNotifyObject() != null) {
                        List<Long> userIds = new ArrayList<>();
                        Long teacherId = ao.getCourseTeacherId();
                        MultiValueMap<String, String> map3 = new LinkedMultiValueMap<>();
                        map3.add("current", "1");
                        map3.add("size", "-1");
                        map3.add("teacherIds", String.valueOf(teacherId));
                        List<TeacherDto> teacher = foundationFacade.teachers(map3);
                        Long courseTeacherUserId = null;
                        if (teacher != null && teacher.size() > 0) {
                            courseTeacherUserId = get(teacher).getUserId();
                        }
                        switch (ao.getNotifyObject()) {
                            case COURSE_TEACHER:
                                if (courseTeacherUserId != null) {
                                    userIds.add(courseTeacherUserId);
                                    basicPushMessage.patrolNotify(patrolDO, ao.getNotifyObject(), userIds, null, null);
                                }
                                break;
                            case CLASS_ADVISER:
                                if (!StringUtils.isEmpty(finalHeadTeacherUserIds1)) {
                                    userIds.addAll(finalHeadTeacherUserIds1);
                                    basicPushMessage.patrolNotify(patrolDO, ao.getNotifyObject(), userIds, null, null);
                                }
                                break;
                            case CLASS_ADVISER_AND_COURSE_TEACHER:
                                if (courseTeacherUserId != null) {
                                    userIds.add(courseTeacherUserId);
                                    basicPushMessage.patrolNotify(patrolDO, NotifyObject.COURSE_TEACHER, userIds, null, null);
                                }
                                if (!StringUtils.isEmpty(finalHeadTeacherUserIds1)) {
                                    List<Long> userIds2 = new ArrayList<>(finalHeadTeacherUserIds1);
                                    basicPushMessage.patrolNotify(patrolDO, NotifyObject.CLASS_ADVISER, userIds2, null, null);
                                }
                                break;
                            default:
                                // 不通知家长
                                throw new BusinessException(CodeRes.CODE_900002);
                        }
                    }
                });
                break;
            case STUDENT:
                Date date = new Date();
                MultiValueMap<String, String> map4 = new LinkedMultiValueMap<>();
                map4.add("current", "1");
                map4.add("size", "-1");
                map4.add("stuIds", objectId);
                map4.add("scope", "3");
                map4.add("headTeacher", "true");
                List<StudentDto> students = foundationFacade.students(map4);
                if (students != null && students.size() != 0) {
                    List<PatrolObjectDO> patrolObjectDoList = new ArrayList<>();
                    for (StudentDto student : students) {
                        PatrolObjectDO patrolObjectDO = ClassConverter.aTob(student, new PatrolObjectDO());
                        TeacherVO headTeacherInfo = student.getHeadTeacherInfo();
                        List<NotifyParentVO> parents = student.getParents();
                        String faceData = student.getFaceData();
                        patrolObjectDO.setObjectId(patrolObjectDO.getId());
                        patrolObjectDO.setId(null);
                        patrolObjectDO.setPatrolId(patrolId);
                        if (headTeacherInfo != null) {
                            patrolObjectDO.setHeadTeacherId(headTeacherInfo.getId());
                        }
                        if (parents != null && !parents.isEmpty()) {
                            String parentUserIds = parents.stream().map(NotifyParentVO::getUserId).map(String::valueOf).collect(Collectors.joining(","));
                            patrolObjectDO.setParentUserIds(parentUserIds);
                        }
                        // 人脸
                        if (faceData == null || faceData.isEmpty()) {
                            // 默认头像
                            patrolObjectDO.setFaceData("https://oss.xshs.cn/frontend/xszx/defaultAvatar.png");
                        } else {
                            patrolObjectDO.setFaceData(faceData);
                        }
                        patrolObjectDO.setCreatedDate(date);
                        patrolObjectDO.setModifiedDate(date);
                        patrolObjectDoList.add(patrolObjectDO);
                    }
                    patrolObjectService.saveBatch(patrolObjectDoList);

                    // 巡课通知异步发送
                    List<Long> finalHeadTeacherUserIds = headTeacherUserIds;
                    threadPool.execute(() -> {
                        if (ao.getIsNotify() && ao.getNotifyObject() != null) {
                            List<Long> userIds = new ArrayList<>();
                            switch (ao.getNotifyObject()) {
                                case CLASS_ADVISER:
                                    if (!StringUtils.isEmpty(finalHeadTeacherUserIds)) {
                                        userIds.addAll(finalHeadTeacherUserIds);
                                        basicPushMessage.patrolNotify(patrolDO, ao.getNotifyObject(), userIds, null, null);
                                    }
                                    break;
                                case PARENT:
                                    if (students.size() > 0) {
                                        // 每个学生分别发送
                                        for (StudentDto student : students) {
                                            basicPushMessage.patrolNotify(patrolDO, ao.getNotifyObject(), null, student, null);
                                        }
                                    }
                                    break;
                                case CLASS_ADVISER_AND_PARENT:
                                    if (!StringUtils.isEmpty(finalHeadTeacherUserIds)) {
                                        userIds.addAll(finalHeadTeacherUserIds);
                                        basicPushMessage.patrolNotify(patrolDO, NotifyObject.CLASS_ADVISER, userIds, null, null);
                                    }
                                    if (students.size() > 0) {
                                        // 每个学生分别发送
                                        for (StudentDto student : students) {
                                            basicPushMessage.patrolNotify(patrolDO, NotifyObject.PARENT, null, student, null);
                                        }
                                    }
                                    break;
                                default:
                                    // 不通知任课教师
                                    throw new BusinessException(CodeRes.CODE_900002);
                            }
                        }
                    });
                } else {
                    throw new BusinessException(CodeRes.CODE_900003);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 统计数据导出
     */
    @Override
    public void statisticsExcelExport(PatrolStatisticsPageAO ao, HttpServletResponse response) {
        PatrolType type = ao.getType();
        ao.setPageSize(-1);
        QueryResult<List<PatrolStatisticsVO>> listQueryResult = getStatisticsPage(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<PatrolStatisticsVO> result = listQueryResult.getResult();
        List<StatisticsClassExport> listClass = null;
        List<StatisticsTeacherExport> listTeacher = null;
        List<StatisticsStudentExport> listStudent = null;
        switch (type) {
            case CLASS:
                listClass = new ArrayList<>();
                break;
            case TEACHER:
                listTeacher = new ArrayList<>();
                break;
            case STUDENT:
                listStudent = new ArrayList<>();
                break;
            default:
                break;
        }
        AtomicInteger index = new AtomicInteger(0);
        if (result != null && result.size() > 0) {
            for (PatrolStatisticsVO patrolStatisticsVO : result) {
                switch (type) {
                    case CLASS:
                        StatisticsClassExport classExport = new StatisticsClassExport();
                        classExport.setIndex(index.incrementAndGet());
                        classExport.setClassName(patrolStatisticsVO.getClassName());
                        classExport.setTotalCount(patrolStatisticsVO.getTotalCount());
                        classExport.setExcellentCount(patrolStatisticsVO.getExcellentCount());
                        classExport.setCommonCount(patrolStatisticsVO.getCommonCount());
                        classExport.setBadCount(patrolStatisticsVO.getBadCount());
                        listClass.add(classExport);
                        break;
                    case TEACHER:
                        StatisticsTeacherExport teacherExport = new StatisticsTeacherExport();
                        teacherExport.setIndex(index.incrementAndGet());
                        teacherExport.setObjectName(patrolStatisticsVO.getObjectName());
                        teacherExport.setDepartmentName(StringUtils.isEmpty(patrolStatisticsVO.getDepartmentName()) ? "未分配" : patrolStatisticsVO.getDepartmentName());
                        teacherExport.setTotalCount(patrolStatisticsVO.getTotalCount());
                        teacherExport.setExcellentCount(patrolStatisticsVO.getExcellentCount());
                        teacherExport.setCommonCount(patrolStatisticsVO.getCommonCount());
                        teacherExport.setBadCount(patrolStatisticsVO.getBadCount());
                        listTeacher.add(teacherExport);
                        break;
                    case STUDENT:
                        StatisticsStudentExport studentExport = new StatisticsStudentExport();
                        studentExport.setIndex(index.incrementAndGet());
                        studentExport.setStudentNo(patrolStatisticsVO.getStudentNo());
                        studentExport.setObjectName(patrolStatisticsVO.getObjectName());
                        studentExport.setGradeName(patrolStatisticsVO.getGradeName());
                        studentExport.setClassName(patrolStatisticsVO.getClassName());
                        studentExport.setTotalCount(patrolStatisticsVO.getTotalCount());
                        studentExport.setExcellentCount(patrolStatisticsVO.getExcellentCount());
                        studentExport.setCommonCount(patrolStatisticsVO.getCommonCount());
                        studentExport.setBadCount(patrolStatisticsVO.getBadCount());
                        listStudent.add(studentExport);
                        break;
                    default:
                        break;
                }

            }

        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd");
        String title;
        if (ao.getStartDate() != null && ao.getEndDate() != null) {
            title = format.format(ao.getStartDate()) + " - " + format.format(ao.getEndDate()) + " 教师巡课统计";
        } else {
            title = "教师巡课统计";
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition,Content-Type,Cache-control");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            switch (type) {
                case CLASS:
                    EasyExcel.write(response.getOutputStream(), StatisticsClassExport.class).head(ExcelHeadsUtil.statisticsClassHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(listClass);
                    break;
                case TEACHER:
                    EasyExcel.write(response.getOutputStream(), StatisticsTeacherExport.class).head(ExcelHeadsUtil.statisticsTeacherHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(listTeacher);
                    break;
                case STUDENT:
                    EasyExcel.write(response.getOutputStream(), StatisticsStudentExport.class).head(ExcelHeadsUtil.statisticsStudentHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(listStudent);
                    break;
                default:
                    break;
            }
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_900001);
        }

    }

    /**
     * 巡课详情
     */
    @Override
    public PatrolVO loadPatrolAndObjects(Long id, Boolean isFromParent, Long stuId) {
        JwtUser user = UserContextHolder.getUser();
        List<String> roles = user.getRoles();
        PatrolDO patrolDO = patrolDao.load(id);
        if (patrolDO == null) {
            return null;
        }
        PatrolVO patrolVO = ClassConverter.aTob(patrolDO, new PatrolVO());
        patrolVO.setRecordTime(patrolDO.getCreatedDate());
        patrolVO.setType(patrolDO.getType().getName());
        patrolVO.setAppraise(patrolDO.getAppraise().getName());
        List<PatrolObjectDO> patrolObjectDoList = patrolObjectService.getObjectsByPatrolId(id);
        String objectName = "";
        if (patrolObjectDoList != null) {
            if (patrolDO.getType().equals(PatrolType.STUDENT)) {
                List<PatrolStudentVO> patrolStudentVoList = new ArrayList<>();
                if (patrolObjectDoList.size() > 1) {
                    // 判断是否是从家长端详情页进入
                    if (isFromParent != null && isFromParent) {
                        // 家长（过滤出该家长的孩子）
                        if (stuId != null) {
                            for (PatrolObjectDO patrolObjectDO : patrolObjectDoList) {
                                if (patrolObjectDO.getObjectId().equals(stuId)) {
                                    objectName = patrolObjectDO.getName();
                                    break;
                                }
                            }
                        } else {
                            patrolObjectDoList = patrolObjectDoList.stream().filter(patrolObjectDO -> !StringUtils.isEmpty(patrolObjectDO.getParentUserIds()) && patrolObjectDO.getParentUserIds().contains(user.getUserId().toString())).collect(Collectors.toList());
                            objectName = patrolObjectDoList.stream().map(PatrolObjectDO::getName).collect(Collectors.joining("、"));
                        }
                    } else {
                        // 判断是否是发起人，发起人不过滤
                        if (!user.getName().equals(patrolDO.getPatrolTeacherName())) {
                            if (roles.contains(ROLE_HEAD_TEACHER)) {
                                // 班主任（过滤出该班主任班上的学生）
                                patrolObjectDoList = patrolObjectDoList.stream().filter(patrolObjectDO -> !StringUtils.isEmpty(patrolObjectDO.getHeadTeacherId()) && patrolObjectDO.getHeadTeacherId().equals(user.getId())).collect(Collectors.toList());
                                objectName = patrolObjectDoList.stream().map(PatrolObjectDO::getName).collect(Collectors.joining("、"));
                            } else if (roles.contains(ROLE_PARENT)) {
                                // 家长（过滤出该家长的孩子）
                                patrolObjectDoList = patrolObjectDoList.stream().filter(patrolObjectDO -> !StringUtils.isEmpty(patrolObjectDO.getParentUserIds()) && patrolObjectDO.getParentUserIds().contains(user.getUserId().toString())).collect(Collectors.toList());
                                objectName = patrolObjectDoList.stream().map(PatrolObjectDO::getName).collect(Collectors.joining("、"));
                            } else {
                                objectName = patrolObjectDoList.stream().map(PatrolObjectDO::getName).collect(Collectors.joining("、"));
                            }
                        } else {
                            // 发起人
                            objectName = patrolObjectDoList.stream().map(PatrolObjectDO::getName).collect(Collectors.joining("、"));
                        }
                    }
                    for (PatrolObjectDO patrolObjectDO : patrolObjectDoList) {
                        String name = patrolObjectDO.getName();
                        String faceData = patrolObjectDO.getFaceData();
                        if (!StringUtils.isEmpty(faceData)) {
                            PatrolStudentVO patrolStudentVO = new PatrolStudentVO();
                            patrolStudentVO.setObjectName(name);
                            patrolStudentVO.setFaceData(faceData);
                            patrolStudentVoList.add(patrolStudentVO);
                        }
                    }
                } else {
                    PatrolObjectDO patrolObjectDO = get(patrolObjectDoList);
                    objectName = patrolObjectDO.getName();
                    String faceData = patrolObjectDO.getFaceData();
                    if (!StringUtils.isEmpty(faceData)) {
                        PatrolStudentVO patrolStudentVO = new PatrolStudentVO();
                        patrolStudentVO.setObjectName(objectName);
                        patrolStudentVO.setFaceData(faceData);
                        patrolStudentVoList.add(patrolStudentVO);
                    }
                }
                patrolVO.setPatrolStudentVoList(patrolStudentVoList);
            } else if (patrolDO.getType().equals(PatrolType.TEACHER)) {
                PatrolObjectDO patrolObjectDO = get(patrolObjectDoList);
                if (!StringUtils.isEmpty(patrolObjectDO.getDepartmentName())) {
                    objectName = patrolObjectDO.getName() + "（" + patrolObjectDO.getDepartmentName() + "）";
                } else {
                    objectName = patrolObjectDO.getName();
                }
            } else {
                objectName = patrolDO.getClassName();
            }
            patrolVO.setObjectName(objectName);
            patrolVO.setClassName(patrolDO.getClassName());
        }
        return patrolVO;
    }

    @Override
    public PatrolVO loadPatrolAndObjects(Long id, Long objectId) {
        PatrolDO patrolDO = load(id);
        PatrolVO patrolVO = ClassConverter.aTob(patrolDO, new PatrolVO());
        patrolVO.setRecordTime(patrolDO.getCreatedDate());
        patrolVO.setType(patrolDO.getType().getName());
        patrolVO.setAppraise(patrolDO.getAppraise().getName());
        PatrolObjectDO patrolObjectDO = patrolObjectService.getObjectsByPatrolIdAndObjectId(id, objectId);
        String objectName;
        if (patrolObjectDO != null) {
            if (patrolDO.getType().equals(PatrolType.STUDENT)) {
                objectName = patrolObjectDO.getName();
            } else if (patrolDO.getType().equals(PatrolType.TEACHER)) {
                if (!StringUtils.isEmpty(patrolObjectDO.getDepartmentName())) {
                    objectName = patrolObjectDO.getName() + "（" + patrolObjectDO.getDepartmentName() + "）";
                } else {
                    objectName = patrolObjectDO.getName();
                }
            } else {
                objectName = patrolDO.getClassName();
            }
            patrolVO.setObjectName(objectName);
            patrolVO.setClassName(patrolDO.getClassName());
        }
        return patrolVO;
    }

    /**
     * 根据班级id和时间获取课表数据
     * 获取每周实时课表数据
     */
    @Override
    public SubjectAndObjectVO getCurriculum(PatrolTimeAndClassAO ao) {
        String courseTime = ao.getCourseTime();
        Integer classType = ao.getClassType();
        Long classOrRoomId = ao.getClassOrRoomId();

        // 周次
        int indexOf = courseTime.indexOf("周周");
        Integer weekNumber = CourseAdjustmentUtils.chinese2Number(courseTime.substring(1, indexOf));
        // 根据周次判断单双周
        WeekTypeEnum weekTypeEnum = weekNumber % 2 == 0 ? WeekTypeEnum.DOUBLE : WeekTypeEnum.SINGLE;
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap;

        // 获取当前学期
        SemesterVO currentSemesters = settingFacade.currentSemester();
        Long semesterId = currentSemesters.getId();
        List<ItemConfigVO> items = settingFacade.items(semesterId);
        Map<Integer, WeekConfigVO> weekMap = settingFacade.weekConfigNumberMap(semesterId);
        Map<WeekEnum, String> currentWeekMap = getWeekEnumStringMap(weekMap, weekNumber);

        if (classType == 1) {
            // 行政班
            // 获取班级课表
            weekEnumMapMap = generalTableService.classTableWeekDetail(classOrRoomId, weekNumber);
        } else {
            // 其他班级（获取教室课表）
            weekEnumMapMap = generalTableService.classRoomTableWeekDetail(classOrRoomId, weekNumber, false);
        }
        if (weekEnumMapMap == null || weekEnumMapMap.isEmpty()) {
            return null;
        }

        String classOrRoomIdStr;
        if (classType == 1) {
            // 处理教室id
            classOrRoomIdStr = classOrRoomId.toString();
        } else {
            classOrRoomIdStr = classOrRoomId + "ROOM";
        }
        // 获取课程全称和课程简称映射
        Map<String, String> courseNameMap = curriculumService.aliasMap(true);

        SubjectAndObjectVO vo = new SubjectAndObjectVO();
        // 获取需要的数据
        WeekEnum week = WeekEnum.getDayOfWeek(courseTime.substring(indexOf + 2, indexOf + 3));
        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> entry : weekEnumMapMap.entrySet()) {
            if (entry.getKey().equals(week)) {
                Map<String, List<TeacherAbbDetailVO>> value = entry.getValue();
                for (Map.Entry<String, List<TeacherAbbDetailVO>> entry1 : value.entrySet()) {
                    if (entry1.getKey() != null) {
                        if (entry1.getKey().equals(courseTime.substring(indexOf + 4, indexOf + 5))) {
                            List<TeacherAbbDetailVO> teacherAbbDetailVos = entry1.getValue();
                            StudentListQuery studentListAo = new StudentListQuery();
                            studentListAo.setClassOrRoomId(classOrRoomIdStr);
                            studentListAo.setSection(entry1.getKey());
                            studentListAo.setWeek(entry.getKey());
                            for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVos) {
                                Long teacherId1 = teacherAbbDetailVO.getTeacherId();
                                // 调休后需找到原课表节次
                                if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.NORMAL)) {
                                    Map<WeekEnum, List<WeekEnum>> compensatoryMap = getWeekEnumListMap(currentWeekMap, items);
                                    if (!compensatoryMap.isEmpty()) {
                                        for (Map.Entry<WeekEnum, List<WeekEnum>> weekEnumListEntry : compensatoryMap.entrySet()) {
                                            for (WeekEnum weekEnum : weekEnumListEntry.getValue()) {
                                                // 如果是调休过来的日期
                                                if (weekEnum.equals(week)) {
                                                    // 原时间
                                                    WeekEnum key = weekEnumListEntry.getKey();
                                                    studentListAo.setWeek(key);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                                // 如果该课程是调课 （调课需要找到原始课程）
                                if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.ADJUSTMENT)) {
                                    Long adjustmentId = teacherAbbDetailVO.getAdjustmentId();
                                    if (adjustmentId != null) {
                                        CourseAdjustmentDO courseAdjustmentDO = courseAdjustmentService.loadById(adjustmentId);
                                        if (courseAdjustmentDO != null) {
                                            Long teacherId = courseAdjustmentDO.getTeacherId();
                                            Long adjustedTeacherId = courseAdjustmentDO.getAdjustedTeacherId();
                                            String beforeOriginCourseTime = null;
                                            if (teacherId1.equals(teacherId)) {
                                                beforeOriginCourseTime = courseAdjustmentDO.getBeforeOriginCourseTime();
                                            } else if (teacherId1.equals(adjustedTeacherId)) {
                                                beforeOriginCourseTime = courseAdjustmentDO.getAdjustedBeforeOriginCourseTime();
                                            }
                                            if (beforeOriginCourseTime != null) {
                                                int index = beforeOriginCourseTime.indexOf("周周");
                                                WeekEnum week1 = WeekEnum.getDayOfWeek(beforeOriginCourseTime.substring(index + 2, index + 3));
                                                String section = beforeOriginCourseTime.substring(index + 4, index + 5);
                                                studentListAo.setWeek(week1);
                                                studentListAo.setSection(section);
                                            }
                                            if (courseAdjustmentDO.getIsSelfStudy()) {
                                                // 自修
                                                studentListAo.setAbbreviation(courseNameMap.get(courseAdjustmentDO.getCourseName()));
                                            }
                                        }
                                    }
                                }
                                // 代课
                                if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.SUBSTITUTE)) {
                                    Long adjustmentId = teacherAbbDetailVO.getAdjustmentId();
                                    if (adjustmentId != null) {
                                        ReplaceSectionDO replaceSectionDO = replaceSectionService.loadById(adjustmentId);
                                        if (replaceSectionDO != null) {
                                            String beforeOriginCourseTime = replaceSectionDO.getBeforeOriginCourseTime();
                                            if (beforeOriginCourseTime != null) {
                                                int index = beforeOriginCourseTime.indexOf("周周");
                                                WeekEnum week1 = WeekEnum.getDayOfWeek(beforeOriginCourseTime.substring(index + 2, index + 3));
                                                String section = beforeOriginCourseTime.substring(index + 4, index + 5);
                                                studentListAo.setWeek(week1);
                                                studentListAo.setSection(section);
                                            }
                                        }
                                    }
                                }
                                // 选修课
                                if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.ELECTIVE)) {
                                    classOrRoomIdStr = classOrRoomIdStr.replaceAll("ROOM", "");
                                }
                                studentListAo.setCurriculumTableId(teacherAbbDetailVO.getCurriculumTableId());
                                if (teacherAbbDetailVO.getClassOrRoomId().equals(classOrRoomIdStr)) {
                                    // classType == 1 行政班
                                    if (classType == 1) {
                                        // 行政班
                                        // 课程名称
                                        if (studentListAo.getAbbreviation() == null) {
                                            studentListAo.setAbbreviation(teacherAbbDetailVO.getAbbreviation());
                                        }
                                        Map<WeekTypeEnum, List<TeachClassesDetailVO>> weekTypeEnumListMap = generalTableService.studentListByClassOrRoomId(studentListAo);
                                        if (weekTypeEnumListMap != null && weekTypeEnumListMap.size() > 0) {
                                            List<StudentTableDetailVO> students = new ArrayList<>();
                                            for (Map.Entry<WeekTypeEnum, List<TeachClassesDetailVO>> entry3 : weekTypeEnumListMap.entrySet()) {
                                                if (weekTypeEnumListMap.size() == 1 || entry3.getKey().equals(weekTypeEnum)) {
                                                    List<TeachClassesDetailVO> value1 = entry3.getValue();
                                                    for (TeachClassesDetailVO teachClassesDetailVO : value1) {
                                                        students.addAll(teachClassesDetailVO.getStudents());
                                                    }
                                                }
                                            }
                                            if (!students.isEmpty()) {
                                                vo.setStudents(students);
                                            }
                                        }
                                        // 班级名
                                        vo.setClassOrRoomName(teacherAbbDetailVO.getClassOrRoomName() + "班");
                                    } else {
                                        studentListAo.setAbbreviation(teacherAbbDetailVos.stream().map(TeacherAbbDetailVO::getAbbreviation).collect(Collectors.joining("、")));
                                        if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.ELECTIVE)) {
                                            // 选修课听课学生从选修课接口获取
                                            List<StudentVO> courseStudents = courseFacade.getCourseStudents(teacherAbbDetailVO.getCourseCode());
                                            if (courseStudents != null && !courseStudents.isEmpty()) {
                                                List<Long> stuIds = courseStudents.stream().map(StudentVO::getId).collect(Collectors.toList());
                                                // 根据学生获取学生信息
                                                List<StudentDto> allStudent = foundationFacade.getAllStudent(start.magic.utils.StringUtils.listToString(stuIds));
                                                List<StudentTableDetailVO> list = new ArrayList<>();
                                                for (StudentDto studentDto : allStudent) {
                                                    StudentTableDetailVO studentTableDetailVO = ClassConverter.aTob(studentDto, new StudentTableDetailVO());
                                                    studentTableDetailVO.setStuId(studentDto.getId());
                                                    list.add(studentTableDetailVO);
                                                }
                                                vo.setStudents(list);
                                            }
                                        } else {
                                            Map<WeekTypeEnum, List<TeachClassesDetailVO>> weekTypeEnumListMap = generalTableService.studentListByClassOrRoomId(studentListAo);
                                            if (weekTypeEnumListMap != null && weekTypeEnumListMap.size() > 0) {
                                                List<StudentTableDetailVO> students = new ArrayList<>();
                                                for (Map.Entry<WeekTypeEnum, List<TeachClassesDetailVO>> entry3 : weekTypeEnumListMap.entrySet()) {
                                                    if (weekTypeEnumListMap.size() == 1 || entry3.getKey().equals(weekTypeEnum)) {
                                                        List<TeachClassesDetailVO> value1 = entry3.getValue();
                                                        for (TeachClassesDetailVO teachClassesDetailVO : value1) {
                                                            students.addAll(teachClassesDetailVO.getStudents());
                                                        }
                                                    }
                                                }
                                                if (!students.isEmpty()) {
                                                    vo.setStudents(students);
                                                }
                                            }
                                        }
                                        // 教室名
                                        vo.setClassOrRoomName(teacherAbbDetailVO.getClassOrRoomName());
                                    }
                                    // 课程名
                                    vo.setCourseName(teacherAbbDetailVO.getCourseName());
                                    // 班级或教室id
                                    if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.ELECTIVE)) {
                                        vo.setClassOrRoomId(teacherAbbDetailVO.getClassOrRoomId() + "ROOM");
                                    } else {
                                        vo.setClassOrRoomId(teacherAbbDetailVO.getClassOrRoomId());
                                    }
                                    // 教师id
                                    vo.setTeacherId(teacherId1);
                                    // 教师名
                                    vo.setTeacherName(teacherAbbDetailVO.getTeacherName());

                                }
                            }
                        }
                    }
                }
            }
        }
        if (vo.getStudents() != null && !vo.getStudents().isEmpty()) {
            List<Long> classIds = vo.getStudents().stream().map(StudentTableDetailVO::getClassId).distinct().collect(Collectors.toList());
            vo.setStudentIds(classIds);
        }
        return vo;
    }


    private Map<WeekEnum, List<WeekEnum>> getWeekEnumListMap(Map<WeekEnum, String> currentWeekMap, List<ItemConfigVO> items) {
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = new HashMap<>();
        for (ItemConfigVO item : items) {
            //3:节假日 6:调休
            if (item.getType() == 3 || item.getType() == 6) {
                for (AdjustmentVO day : item.getAdjustments()) {
                    String dStr = day.getDetailDate().toString();
                    if (currentWeekMap.containsValue(dStr)) {
                        WeekEnum kWeek = WeekEnum.getWeekMap().get(day.getDayOfWeek());
                        List<WeekEnum> weekList = compensatoryMap.computeIfAbsent(kWeek, k -> new ArrayList<>());
                        weekList.add(DateUtils.getDayOfWeekByDate(dStr, currentWeekMap));
                    }
                }
            }
        }
        return compensatoryMap;
    }

    private Map<WeekEnum, String> getWeekEnumStringMap(Map<Integer, WeekConfigVO> weekMap, Integer weekNumber) {
        WeekConfigVO weekVo = weekMap.get(Long.valueOf(weekNumber));
        if (weekVo == null) {
            throw new BusinessException(CodeRes.CODE_1000002);
        }
        //节假日调休课表变更逻辑
        return DateUtils.currentWeekMap(weekVo.getStartDay(), weekVo.getEndDay());
    }

    /**
     * 获取所有班级和教室
     */
    @Override
    public ClassAndRoomVO getRoom() {
        ClassAndRoomVO classAndRoomVO = new ClassAndRoomVO();
        // 获取当前学期
        SemesterVO currentSemesters = settingFacade.currentSemester();
        Integer orderNo = currentSemesters.getOrderNo();
        Long id = currentSemesters.getId();
        // 获取其他班级
        Map<WeekTypeEnum, List<ClassesVO>> weekTypeEnumListMap = generalTableService.allClassList(id, orderNo, true);
        List<com.xiaoshan.edu.vo.patrol.RoomVO> roomVos = new ArrayList<>();
        List<RoomVO> roomVoList;
        Map<Long, RoomVO> map = new HashMap<>();
        if (weekTypeEnumListMap != null) {
            for (Map.Entry<WeekTypeEnum, List<ClassesVO>> entry : weekTypeEnumListMap.entrySet()) {
                List<ClassesVO> value = entry.getValue();
                if (value != null && value.size() > 0) {
                    for (ClassesVO classesVO : value) {
                        RoomVO roomVO = new RoomVO();
                        roomVO.setId(classesVO.getClassId());
                        roomVO.setName(classesVO.getClassName());
                        map.put(classesVO.getClassId(), roomVO);
                    }
                }
            }
        }
        // 获取选修课教室
        CourseQuery courseQuery = new CourseQuery();
        if (id != null) {
            courseQuery.setSemesterId(id);
        }
        List<CourseVO> courseVos = courseFacade.postCourseInfo(courseQuery);
        if (courseVos != null) {
            for (CourseVO courseVO : courseVos) {
                RoomVO roomVO = new RoomVO();
                if (courseVO.getRoomId() != null) {
                    roomVO.setId(courseVO.getRoomId());
                    roomVO.setName(courseVO.getRoomName());
                    map.put(courseVO.getRoomId(), roomVO);
                }
            }
        }

        if (map.size() > 0) {
            roomVoList = new ArrayList<>(map.values());
            roomVos = roomVoList.stream().map(roomVO -> ClassConverter.aTob(roomVO, new com.xiaoshan.edu.vo.patrol.RoomVO())).collect(Collectors.toList());
        }
        if (!roomVos.isEmpty()) {
            classAndRoomVO.setRoomList(roomVos);
        }

        // 获取行政班班级
        Map<WeekTypeEnum, List<ClassesVO>> weekTypeEnumListMap1 = generalTableService.allClassList(id, orderNo, false);
        if (weekTypeEnumListMap1 == null) {
            classAndRoomVO.setGrades(null);
        } else {
            List<Long> classIdList = new ArrayList<>();
            for (Map.Entry<WeekTypeEnum, List<ClassesVO>> entry : weekTypeEnumListMap1.entrySet()) {
                List<ClassesVO> value = entry.getValue();
                if (value != null && value.size() > 0) {
                    for (ClassesVO classesVO : value) {
                        classIdList.add(classesVO.getClassId());
                    }
                }
            }
            if (classIdList.size() > 0) {
                // 获取班级数据
                String classIdStr = classIdList.stream().distinct().map(String::valueOf).collect(Collectors.joining(","));
                List<ClassDTO> classDTOList = foundationFacade.getAllClass(classIdStr);
                for (ClassDTO classDto : classDTOList) {
                    classDto.setName(classDto.getName() + "班");
                }
                Map<String, List<ClassVO>> collect = classDTOList.stream().map(classDto -> ClassConverter.aTob(classDto, new ClassVO())).collect(Collectors.groupingBy(ClassVO::getGradeName));
                if (collect != null) {
                    List<PatrolClassVO> grades = new ArrayList<>();
                    for (Map.Entry<String, List<ClassVO>> entry : collect.entrySet()) {
                        List<ClassVO> value = entry.getValue();
                        PatrolClassVO patrolClassVO = new PatrolClassVO();
                        patrolClassVO.setGradeName(entry.getKey());
                        patrolClassVO.setSectionType(value.get(0).getSectionType());
                        patrolClassVO.setEnrollmentYear(value.get(0).getEnrollmentYear());
                        patrolClassVO.setClassList(value);
                        grades.add(patrolClassVO);
                    }
                    // 排序
                    grades.sort((patrolClassVO, t1) -> {
                        if (patrolClassVO.getSectionType().equals(t1.getSectionType())) {
                            return patrolClassVO.getEnrollmentYear() - t1.getEnrollmentYear();
                        }
                        return patrolClassVO.getSectionType() - t1.getSectionType();
                    });
                    classAndRoomVO.setGrades(grades);
                }
            } else {
                classAndRoomVO.setGrades(null);
            }
        }
        if (weekTypeEnumListMap == null && weekTypeEnumListMap1 == null) {
            return null;
        }
        return classAndRoomVO;
    }

    /**
     * 获取当前节次
     *
     * @param classId 班级ID
     * @return 当前节次
     */
    @Override
    public String getCurrentSection(Long classId) {
        Date currentDateTime = new Date();
        String format = TimeUtils.format(currentDateTime, TimeUtils.YYYYMMDD_F);
        List<ClassDTO> allClass = foundationFacade.getAllClass(classId.toString());
        ClassDTO classDto = allClass.get(0);
        String gradeName = classDto.getGradeName();
        RestTimeVO currentRest = settingFacade.getCurrentRest(gradeName);
        int sectionOrder = 1;
        if (currentRest != null) {
            List<RestSectionDetailVO> details = currentRest.getDetails();
            if (details != null && details.size() != 0) {
                for (RestSectionDetailVO detail : details) {
                    String startTime = detail.getStartTime();
                    String endTime = detail.getEndTime();
                    Date startDateTime = TimeUtils.format(format + " " + startTime + ":00", TimeUtils.YYYYMMDDHHMMSS_F);
                    Date endDateTime = TimeUtils.format(format + " " + endTime + ":00", TimeUtils.YYYYMMDDHHMMSS_F);
                    if (currentDateTime.after(startDateTime) && currentDateTime.before(endDateTime)) {
                        sectionOrder = detail.getSectionOrder() + 1;
                        break;
                    }
                }
            }
        }
        String s = NumberConvertUtil.number2Chinese(sectionOrder);
        return "第" + s + "节";
    }

    @Override
    public PatrolElectronicArchivesVO getPatrolElectronicRecords(PatrolElectronicArchivesAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        QueryResult<List<PatrolElectronicDTO>> queryResult = queryForPage(PatrolElectronicDTO.class, "getPatrolElectronicAndObjects", map);
        List<PatrolElectronicVO> result1 = new ArrayList<>();
        if (queryResult != null && queryResult.getResult() != null && queryResult.getResult().size() > 0) {
            int excellentCount = 0;
            int commonCount = 0;
            int badCount = 0;
            for (PatrolElectronicDTO patrolElectronicDTO : queryResult.getResult()) {
                if (patrolElectronicDTO.getAppraise().equals(PatrolAppraise.EXCELLENT)) {
                    excellentCount++;
                } else if (patrolElectronicDTO.getAppraise().equals(PatrolAppraise.COMMON)) {
                    commonCount++;
                } else {
                    badCount++;
                }
                // 根据课程时间排序
                String courseTime = patrolElectronicDTO.getCourseTime();
                // 周次
                int indexOf = courseTime.indexOf("周周");
                Integer weekNumber = CourseAdjustmentUtils.chinese2Number(courseTime.substring(1, indexOf));
                Integer dayNumber = CourseAdjustmentUtils.chinese2Number(courseTime.substring(indexOf + 2, indexOf + 3));
                Integer sectionNumber = CourseAdjustmentUtils.chinese2Number(courseTime.substring(indexOf + 4, indexOf + 5));
                PatrolElectronicVO patrolElectronicVO = ClassConverter.aTob(patrolElectronicDTO, new PatrolElectronicVO());
                patrolElectronicVO.setWeekNumber(weekNumber);
                patrolElectronicVO.setDayNumber(dayNumber);
                patrolElectronicVO.setSectionNumber(sectionNumber);
                patrolElectronicVO.setAppraise(patrolElectronicDTO.getAppraise().getName());
                result1.add(patrolElectronicVO);
            }
            // 根据 PatrolElectronicVO对象的排序值 降序排序
            result1.sort((patrolElectronicVO, t1) -> {
                if (patrolElectronicVO.getWeekNumber().equals(t1.getWeekNumber())) {
                    if (patrolElectronicVO.getDayNumber().equals(t1.getDayNumber())) {
                        return patrolElectronicVO.getSectionNumber() - t1.getSectionNumber();
                    }
                    return patrolElectronicVO.getDayNumber() - t1.getDayNumber();
                }
                return patrolElectronicVO.getWeekNumber() - t1.getWeekNumber();
            });
            PatrolElectronicArchivesVO patrolElectronicArchivesVO = new PatrolElectronicArchivesVO();
            patrolElectronicArchivesVO.setTotalCount(result1.size());
            // 分页
            if (ao.getPageSize() != -1) {
                result1 = result1.stream().skip(ao.index()).limit(ao.getPageSize()).collect(Collectors.toList());
            }
            patrolElectronicArchivesVO.setExcellentCount(excellentCount);
            patrolElectronicArchivesVO.setCommonCount(commonCount);
            patrolElectronicArchivesVO.setBadCount(badCount);
            patrolElectronicArchivesVO.setPatrolElectronicVoList(result1);
            return patrolElectronicArchivesVO;
        }
        return null;
    }
}
