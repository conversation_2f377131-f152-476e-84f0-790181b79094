package com.xiaoshan.edu.courseadjustment.service;

import com.xiaoshan.edu.courseadjustment.entity.ReplaceSectionDO;
import com.xiaoshan.edu.vo.courseadjustment.AdjustmentOrReplaceVO;
import start.framework.service.SqlBaseService;

import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ReplaceSectionService extends SqlBaseService<ReplaceSectionDO,Long> {
    /**
     * 根据代课id获取代课节次数据
     * @param id
     * @return
     */
    List<ReplaceSectionDO> getListByReplaceId(Long id);

    /**
     * 根据id获取代课节次数据
     * @param id
     * @return
     */
    ReplaceSectionDO loadById(Long id);

    /**
     * 根据父id获取所有代课节次子数据
     * @param id
     * @return
     */
    List<ReplaceSectionDO> getByFatherId(Long id);

    /**
     * 根据代课id删除代课节次数据
     * @param id
     */
    void removeByReplaceId(Long id);

    /**
     * 根据代课ids获取代课节次数据
     * @param ids
     * @return
     */
    List<ReplaceSectionDO> getListByReplaceIds(List<Long> ids);

    /**
     * 根据课表id获取代课ids
     * @param curriculumTableId
     * @return
     */
    Set<Long> getReplaceIdsByCurriculumTableId(Long curriculumTableId);

    /**
     * 根据代课时间范围获取代课ids
     * @param startDate
     * @param endDate
     * @return
     */
    Set<Long> getReplaceIdsByReplaceDateScope(Date startDate, Date endDate);

    /**
     * 根据课程日期获取代课ids
     * @param dates
     * @return
     */
    Set<Long> getReplaceIdsByCourseDates(List<Date> dates);

    /**
     * 根据代课id更新失效数据
     * @param ids
     */
    void setInvalidByReplaceIds(List<Long> ids);

    /**
     * 根据代课节次id获取代课详情（课表）
     * @param sectionId
     * @return
     */
    AdjustmentOrReplaceVO getReplaceByIdAndCourseTime(Long sectionId);

    /**
     * 获取代课或被代课数
     * @param map
     * @param type
     * @return
     */
    Map<Long, Set<Long>> getReplaceOrReplacedCount(Map<String, Object> map, int type);

    /**
     * 获取未失效的代课节次数据
     * @param teacherId
     * @param courseTime
     * @return
     */
    List<ReplaceSectionDO> getReplaceSectionByIsNotInvalid(Long teacherId, String courseTime);
}
