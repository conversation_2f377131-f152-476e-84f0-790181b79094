package com.xiaoshan.edu.api.facade.impl;

import com.alibaba.fastjson.JSON;
import com.xiaoshan.basic.dto.MessageDTO;
import com.xiaoshan.edu.api.facade.MessageFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MessageFacadeImpl implements MessageFacade {

    private final static String MESSAGE = "https://message";


    @Autowired
    @Qualifier("webClient")
    private WebClient webClient;

    @Override
    public void createMessage(MessageDTO dto) {
        log.info("新增消息：" + JSON.toJSONString(dto));
        ParameterizedTypeReference<Long> responseBodyType = new ParameterizedTypeReference<Long>() {
        };
        webClient.post().uri(MESSAGE + "/messages").body(BodyInserters.fromPublisher(Mono.just(dto), MessageDTO.class)).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public void createMessageToParent(MessageDTO dto) {
        log.info("新增消息给家长：" + JSON.toJSONString(dto));
        ParameterizedTypeReference<Long> responseBodyType = new ParameterizedTypeReference<Long>() {
        };
        webClient.post().uri(MESSAGE + "/messages/parents").body(BodyInserters.fromPublisher(Mono.just(dto), MessageDTO.class)).retrieve().bodyToMono(responseBodyType).block();
    }
}
