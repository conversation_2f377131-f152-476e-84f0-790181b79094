package com.xiaoshan.edu.model.ao;

import com.xiaoshan.basic.vo.ItemConfigVO;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import com.xiaoshan.edu.courseadjustment.utils.CourseTimeUtils;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ReplaceCourseDetailAO {

    @ApiModelProperty("课程时间：如：第一周周一第一节")
    private String courseTime;

    @ApiModelProperty("课程班级或教室")
    private String courseRoom;

    @ApiModelProperty("课程班级或教室id（教室以ROOM结尾）")
    private String courseRoomId;

    @ApiModelProperty("课程开始时间")
    private String startTime;

    @ApiModelProperty("课程结束时间")
    private String endTime;

    @ApiModelProperty("（二）调代类型")
    private AbbTypeEnum type;

    @ApiModelProperty("（二）调代课ID")
    private Long adjustmentId;

    @ApiModelProperty("代课课表ID")
    private Long curriculumTableId;


    public String getBeforeOriginCourseTime(List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        Map<WeekEnum, String> currentWeekMap = CourseAdjustmentUtils.getWeekEnumStringMap(weekMap, getWeekNumber());
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = CourseAdjustmentUtils.getWeekEnumListMap(currentWeekMap, items);
        if (!compensatoryMap.isEmpty()) {
            for (Map.Entry<WeekEnum, List<WeekEnum>> weekEnumListEntry : compensatoryMap.entrySet()) {
                for (WeekEnum weekEnum : weekEnumListEntry.getValue()) {
                    if (weekEnum.equals(getDayOfWeek())) {
                        WeekEnum key = weekEnumListEntry.getKey();
                        return getCourseTime().substring(0, getIndexOfWeek() + 1) + WeekEnum.getDayOfWeek(key) + getCourseTime().substring(getIndexOfWeek() + 3);
                    }
                }
            }
        }
        return getCourseTime();
    }


    public Integer getIndexOfWeek() {
        return CourseTimeUtils.getIndexOfWeek(courseTime);
    }


    public WeekEnum getDayOfWeek() {
        return CourseTimeUtils.getDayOfWeek(courseTime);
    }

    public Integer getWeekNumber() {
        return CourseTimeUtils.getWeekNumber(courseTime);
    }

    public String getFullChineseDayOfWeek() {
        return CourseTimeUtils.getFullChineseDayOfWeek(courseTime);
    }

    public String getFullSection() {
        return CourseTimeUtils.getFullSection(courseTime);
    }

    public String getNoWeekCourseTime() {
        return CourseTimeUtils.getNoWeekCourseTime(courseTime);
    }


}
