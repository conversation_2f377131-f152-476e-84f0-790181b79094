package com.xiaoshan.edu.timetable.utils;

import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.basic.vo.StudentVO;

/**
 * <AUTHOR>
 */
public class CheckUtils {

    public static Boolean isTableSectionIdAndEnrollmentYear(Integer enrollmentYear, Integer sectionId, StudentVO student) {
        if (!sectionId.equals(student.getSectionId().intValue())) {
            return false;
        }
        return enrollmentYear.equals(student.getEnrollmentYear());
    }

    public static Boolean isTableSectionIdAndEnrollmentYear(Integer enrollmentYear, Integer sectionId, ClassVO classes) {
        if (!sectionId.equals(classes.getSectionId())) {
            return false;
        }
        if (!enrollmentYear.equals(classes.getEnrollmentYear())) {
            return false;
        }
        return true;
    }

}
