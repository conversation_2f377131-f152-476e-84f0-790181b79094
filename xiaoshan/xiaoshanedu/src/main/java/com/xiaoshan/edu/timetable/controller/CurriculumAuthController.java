package com.xiaoshan.edu.timetable.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.common.annotation.AuthenticationCheck;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.timetable.service.impl.CurriculumAuthService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/auth/table")
@Api(tags = "课表权限数据")
public class CurriculumAuthController extends BaseController {

	@Autowired
	private CurriculumAuthService curriculumAuthService;

	@ApiOperation("学生数据")
	@AuthenticationCheck
	@GetMapping("/student/{tableId}")
	public ResultResponse<List<StudentTableDTO>> mainTable(
			@PathVariable @ApiParam("课表ID") @NotNull @LongValid Long tableId){
		JwtUser user=UserContextHolder.getUser();
		return response(curriculumAuthService.getAllStudent(user.getId(), tableId));
	}
	
}
