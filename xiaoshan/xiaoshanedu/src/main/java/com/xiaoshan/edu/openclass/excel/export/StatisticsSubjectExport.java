package com.xiaoshan.edu.openclass.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;

@Getter@Setter@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class StatisticsSubjectExport {

    @ExcelProperty(value = "序号", index = 0)
    private Integer index;

    @ExcelProperty(value = "科目",index = 1)
    private String subject;

    @ExcelProperty(value = "总计（节）", index = 2)
    private Integer total;

    @ExcelProperty(value = "校内公开课（节）", index = 3)
    private Long innerOpenClassNum;

    @ExcelProperty(value = "校际公开课（节）", index = 4)
    private Long schoolOpenClassNum;

    @ExcelProperty(value = "校内讲座（节）", index = 5)
    private Long innerLectureNum;

    @ExcelProperty(value = "云课堂（节）", index = 6)
    private Long cloudClassNum;
}
