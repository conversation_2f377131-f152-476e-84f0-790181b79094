package com.xiaoshan.edu.timetable.utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.xiaoshan.edu.dto.StudentTableDTO;

/**
 * <AUTHOR>
 */
public class StudentTableUtils {

    public static Boolean checkAbbTeacherClassOrRoomId(Map<Long, List<StudentTableDTO>> studentTable, String abb, Long teacherId, String classOrRoomId) {
        for (Long classId : studentTable.keySet()) {
            List<StudentTableDTO> list = studentTable.get(classId);
            for (StudentTableDTO dto : list) {
                if (dto.getDetail().containsKey(abb)) {
                    Map<Long, String> detail = dto.getDetail().get(abb);
                    if (detail.containsKey(teacherId)) {
                        if (detail.get(teacherId).equals(classOrRoomId)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取当前班级该课程的所有老师ID
     */
    public static Set<Long> getTeacherIds(List<StudentTableDTO> studentTable, String abb, Long classId) {
        Set<Long> teacherIds = new HashSet<>();
        for (StudentTableDTO dto : studentTable) {
            if (dto.getClassId().equals(classId)) {
                if (dto.getDetail().containsKey(abb)) {
                    teacherIds.addAll(dto.getDetail().get(abb).keySet());
                }
            }
        }
        return teacherIds;
    }

    /**
     * 获取班级下上该课程的学生
     */
    public static List<StudentTableDTO> getStudentAbbClassGroup(List<StudentTableDTO> studentTable, String abbreviation, Long classId) {
        List<StudentTableDTO> students = new ArrayList<>();
        for (StudentTableDTO dto : studentTable) {
            if (dto.getClassId().equals(classId)) {
                if (dto.getDetail().containsKey(abbreviation)) {
                    students.add(dto);
                    continue;
                }
            }
        }
        return students;
    }

    /**
     * 获取班级该课程下所有在非本班上课的学生
     */
    public static List<StudentTableDTO> getStudentLeavelClass(List<StudentTableDTO> studentTable, String abbreviation, Long classId) {
        List<StudentTableDTO> students = new ArrayList<>();
        for (StudentTableDTO dto : studentTable) {
            if (dto.getClassId().equals(classId)) {
                if (dto.getDetail().containsKey(abbreviation)) {
                    if (dto.getLeaveClass().contains(abbreviation)) {
                        students.add(dto);
                        continue;
                    }
                }
            }
        }
        return students;
    }

    public static List<StudentTableDTO> getStudentNoLeavelClass(List<StudentTableDTO> studentTable, String abbreviation, Long classId) {
        List<StudentTableDTO> students = new ArrayList<>();
        for (StudentTableDTO dto : studentTable) {
            if (dto.getClassId().equals(classId)) {
                if (dto.getDetail().containsKey(abbreviation)) {
                    if (!dto.getLeaveClass().contains(abbreviation)) {
                        students.add(dto);
                        continue;
                    }
                }
            }
        }
        return students;
    }

    /**
     * 获取所有上课程走班老师的学生
     */
    public static List<StudentTableDTO> getStudentLeavelTeacher(List<StudentTableDTO> studentTable, String abbreviation, Long teacherId) {
        List<StudentTableDTO> students = new ArrayList<>();
        for (StudentTableDTO dto : studentTable) {
            if (dto.getDetail().containsKey(abbreviation)) {
                if (dto.getLeaveClass().contains(abbreviation)) {
                    Map<Long, String> dtodetail = dto.getDetail().get(abbreviation);
                    if (dtodetail.containsKey(teacherId)) {
                        students.add(dto);
                    }
                    continue;
                }
            }
        }
        return students;
    }

    public static Map<Long, List<StudentTableDTO>> getStudentTable(List<StudentTableDTO> studentList) {
        Map<Long, List<StudentTableDTO>> table = new LinkedHashMap<>();
        for (StudentTableDTO dto : studentList) {
            List<StudentTableDTO> studentListDto = table.get(dto.getClassId());
            if (studentListDto == null) {
                studentListDto = new ArrayList<>();
                table.put(dto.getClassId(), studentListDto);
            }
            studentListDto.add(dto);
        }
        return table;
    }

    public static Map<Long, List<StudentTableDTO>> getStudentTableByTeacherAbbGroup(List<StudentTableDTO> studentList, String abb) {
        Map<Long, List<StudentTableDTO>> table = new LinkedHashMap<>();
        for (StudentTableDTO dto : studentList) {
            if (dto.getDetail().containsKey(abb)) {
                for (Long teacherId : dto.getDetail().get(abb).keySet()) {
                    List<StudentTableDTO> studentListDto = table.get(teacherId);
                    if (studentListDto == null) {
                        studentListDto = new ArrayList<>();
                        table.put(teacherId, studentListDto);
                    }
                    studentListDto.add(dto);
                }
            }
        }
        return table;
    }

}
