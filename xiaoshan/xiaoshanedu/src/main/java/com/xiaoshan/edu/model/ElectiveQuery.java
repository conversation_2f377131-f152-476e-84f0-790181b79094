package com.xiaoshan.edu.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class ElectiveQuery {
    private Long semesterId;
    private Long classId;
    private Long roomId;
    private Long teacherId;
    private Long studentId;
    private Long sectionType;
    private Integer enrollmentYear;
    private List<Long> teacherIds;
}
