package com.xiaoshan.edu.api.facade;

import com.xiaoshan.basic.vo.*;
import com.xiaoshan.oa.dto.ClassDTO;
import com.xiaoshan.oa.dto.StudentDto;
import com.xiaoshan.oa.dto.TeacherDto;
import org.springframework.util.MultiValueMap;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FoundationFacade {


    /**
     * 获取教师
     * @return
     */
    List<TeacherVO> getTeachers();

    /**
     * 获取字典
     * @param dicCode
     * @return
     */
    List<DictionaryVO> getDictionaryValues(String dicCode);

    /**
     * 查询学生列表
     * @param stuIds
     * @return
     */
    List<StudentVO> findBasicByStuIds(List<Long> stuIds);

    /**
     * 获取班级
     * @return
     */
    List<ClassVO> allClass();

    /**
     * 获取教师
     * @param param
     * @return
     */
    List<TeacherDto> getTeachers(Map<String, Object> param);

    /**
     * 教师map
     * @return
     */
    Map<Long, TeacherDto> getAvatarMap();

    /**
     * 教师列表
     * @param ids
     * @return
     */
    List<TeacherDto> getTeachersByIds(String ids);

    /**
     * xxx
     * @param teacherIds
     * @return
     */
    List<TeacherDto> getAllTeacherByIds(String teacherIds);

    /**
     * xxx
     * @param map
     * @return
     */
    List<TeacherDto> teachers(MultiValueMap<String, String> map);

    /**
     * 学生
     * @param stuIds
     * @return
     */
    List<StudentDto> getAllStudent(String stuIds);

    /**
     * xxx
     * @param map
     * @return
     */
    List<StudentDto> students(MultiValueMap<String, String> map);

    /**
     * 学生数据
     * @param map
     * @return
     */
    List<StudentDto> studentsPost(Map<String, String> map);

    /**
     * xxx
     * @param stuIds
     * @return
     */
    List<StudentDto> studentsOfPost(String stuIds);

    /**
     * 班级列表
     * @param classIdStr
     * @return
     */
    List<ClassDTO> getAllClass(String classIdStr);

    /**
     * xxx
     * @param map
     * @return
     */
    List<ClassDTO> classes(MultiValueMap<String, String> map);

    /**
     * xxx
     * @param params
     * @return
     */
    List<StudentVO> getStudent(Map<String, String> params);

    /**
     * xxx
     * @param teacherIds
     * @param weekNumbers
     * @return
     */
    List<LeavingAndBusinessVO> getTeacherLeavingOrBusiness(List<Long> teacherIds, List<Integer> weekNumbers);

}
