package com.xiaoshan.edu.timetable.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StudentTableExportDTO {

    @ExcelProperty(value = "身份证号", index = 0)
    private String idNo;

    @ExcelProperty(value = "班级", index = 1)
    private String className;

    @ExcelProperty(value = "学号", index = 2)
    private String studentNo;

    @ExcelProperty(value = "姓名", index = 3)
    private String name;

    @ExcelProperty(value = "性别", index = 4)
    private String genderName;

    @ExcelProperty(value = "班主任", index = 5)
    private String headerName;

    @ExcelProperty(value = "选课", index = 6)
    private String courseName;

    @ExcelProperty(value = "语文", index = 7)
    private String chinese;

    @ExcelProperty(value = "上课地点", index = 8)
    private String chineseRoom;

    @ExcelProperty(value = "数学", index = 9)
    private String math;

    @ExcelProperty(value = "上课地点", index = 10)
    private String mathRoom;

    @ExcelProperty(value = "外语", index = 11)
    private String english;

    @ExcelProperty(value = "上课地点", index = 12)
    private String englishRoom;

    @ExcelProperty(value = "物理", index = 13)
    private String physical;

    @ExcelProperty(value = "上课地点", index = 14)
    private String physicalRoom;

    @ExcelProperty(value = "化学", index = 15)
    private String chemical;

    @ExcelProperty(value = "上课地点", index = 16)
    private String chemicalRoom;

    @ExcelProperty(value = "生物", index = 17)
    private String biological;

    @ExcelProperty(value = "上课地点", index = 18)
    private String biologicalRoom;

    @ExcelProperty(value = "政治", index = 19)
    private String political;

    @ExcelProperty(value = "上课地点", index = 20)
    private String politicalRoom;

    @ExcelProperty(value = "历史", index = 21)
    private String history;

    @ExcelProperty(value = "上课地点", index = 22)
    private String historyRoom;

    @ExcelProperty(value = "地理", index = 23)
    private String geographic;

    @ExcelProperty(value = "上课地点", index = 24)
    private String geographicRoom;

    @ExcelProperty(value = "信息技术", index = 25)
    private String informationTech;

    @ExcelProperty(value = "上课地点", index = 26)
    private String informationTechRoom;

    @ExcelProperty(value = "通用技术", index = 27)
    private String generalTech;

    @ExcelProperty(value = "上课地点", index = 28)
    private String generalTechRoom;

}
