package com.xiaoshan.edu.survey.service.impl;

import com.xiaoshan.edu.ao.survey.TaskStudentAO;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.service.BasicPushMessage;
import com.xiaoshan.edu.survey.dao.TaskStudentDao;
import com.xiaoshan.edu.survey.entity.TaskDO;
import com.xiaoshan.edu.survey.entity.TaskStudentDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.*;
import com.xiaoshan.edu.survey.utils.excel.SurveyExcelUtils;
import com.xiaoshan.edu.vo.survey.StatisticNumVO;
import com.xiaoshan.edu.vo.survey.StudentQuestionVO;
import com.xiaoshan.edu.vo.survey.StudentTaskTemplateVO;
import com.xiaoshan.edu.vo.survey.TaskStudentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.QueryResult;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;
import start.magic.persistence.source.jdbc.sqlplus.toolkit.EmptyUtils;
import start.magic.utils.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Service("taskStudentService")
public class TaskStudentServiceImpl extends SqlBaseServiceImplV2Ext<TaskStudentDO, Long>
        implements TaskStudentService {

    @SuppressWarnings("unused")
    private TaskStudentDao taskStudentDao;
    @Autowired
    private TaskTemplateStudentTeacherQuestionService teacherQuestionService;
    @Autowired
    private BasicPushMessage basicPushMessage;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TaskTemplateService taskTemplateService;
    @Autowired
    private TaskTemplateStudentTeacherQuestionService taskTemplateStudentTeacherQuestionService;
    @Autowired
    private TaskTemplateStudentService taskTemplateStudentService;

    public TaskStudentServiceImpl(@Qualifier("taskStudentDao") TaskStudentDao taskStudentDao) {
        super(taskStudentDao);
        this.taskStudentDao = taskStudentDao;
    }

    /**
     * 小程序 -- 调查模板列表页提交(当所有模板都已完成时调用，否则不需要调)
     */
    @Override
    public void submitTemplate(Long taskId, Long studentId) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("taskId", taskId).eq("studentId", studentId).set("isFinish", "true");
        taskStudentDao.executeUpdate(wrapper);
    }

    @Override
    public void submitTemplate(Long taskId, Collection<Long> studentIds) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("taskId", taskId).in("studentId", studentIds).set("isFinish", "true");
        taskStudentDao.executeUpdate(wrapper);
    }

    /**
     * 根据taskId删除
     */
    @Override
    public void removeByTaskId(Long taskId) {
        remove("taskId", taskId);
    }

    /**
     * 完成情况 -- 分页查询
     */
    @Override
    public QueryResult<List<TaskStudentVO>> page(TaskStudentAO ao) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("taskId", ao.getTaskId());
        wrapper.eq(!StringUtils.isEmpty(ao.getClassName()), "className", ao.getClassName());
        wrapper.eq(ao.getIsFinish()!=null, "isFinish", String.valueOf(ao.getIsFinish()));
        wrapper.like(!StringUtils.isEmpty(ao.getName()), "name", ao.getName());
        wrapper.orderByAsc("className");
        wrapper.last(String.format("limit %d,%d", ao.index(), ao.getPageSize()));
        return queryForPage(TaskStudentVO.class, wrapper);
    }

    /**
     * 完成情况 -- 总人数、已完成、未完成人数
     */
    @Override
    public StatisticNumVO getNum(Long taskId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        StatisticNumVO vo = get(queryForMapMapper(StatisticNumVO.class, "getFinishNum", map));
        vo.setCompleted(vo.getTotal() - vo.getUndo());
        return vo;
    }

    /**
     * 完成情况 -- 查看详情
     */
    @Override
    public List<StudentQuestionVO> look(Long taskId, Long templateId, Long studentId) {
        List<TaskTemplateStudentTeacherQuestionDO> list = teacherQuestionService.getByTaskTemplateStudentId(taskId, templateId, studentId);
        Map<Integer, List<TaskTemplateStudentTeacherQuestionDO>> collect = list.stream().collect(Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getQuestionSort));
        List<StudentQuestionVO> res = new ArrayList<>();
        for (List<TaskTemplateStudentTeacherQuestionDO> questionList : collect.values()) {
            HashMap<String, List<QuestionOptionDTO>> map = new HashMap<>();
            StudentQuestionVO vo = new StudentQuestionVO();
            vo.setTitle(questionList.get(0).getQuestionTitle());
            vo.setQuestionType(questionList.get(0).getQuestionType());
            for (TaskTemplateStudentTeacherQuestionDO curriculumDto : questionList) {
                if (curriculumDto.getCurriculum() == null) {
                    map.put(String.format("%s老师", curriculumDto.getTeacherName()), curriculumDto.getAnswer());
                } else {
                    map.put(String.format("%s(%s)", curriculumDto.getCurriculum(), curriculumDto.getTeacherName()), curriculumDto.getAnswer());
                }
            }
            vo.setData(map);
            res.add(vo);
        }
        return res;
    }

    /**
     * 完成情况 -- 导出
     */
    @Override
    public void export(HttpServletResponse response, TaskStudentAO ao) {
        List<List<String>> data = new ArrayList<>();
        TaskDO taskDo = taskService.load(ao.getTaskId());
        data.add(Arrays.asList("IC卡号", "学号", "学生姓名", "班级", "完成状态"));
        ao.setPageIndex(1);
        ao.setPageSize(Integer.MAX_VALUE);
        QueryResult<List<TaskStudentVO>> page = page(ao);
        if (page != null){
            List<TaskStudentVO> result = page.getResult();
            for (TaskStudentVO vo : result) {
                ArrayList<String> list = new ArrayList<>();
                list.add(vo.getIcCardNo());
                list.add(String.valueOf(vo.getStudentNo()));
                list.add(vo.getName());
                list.add(vo.getClassName());
                list.add(vo.getIsFinish() ? "已完成" : "未完成");
                data.add(list);
            }
        }
        String fileName = "";
        if (Objects.isNull(ao.getIsFinish())) {
            fileName = taskDo.getName() + "-全部";
        }else {
            if (ao.getIsFinish()) {
                fileName = taskDo.getName() + "-已完成";
            }else {
                fileName = taskDo.getName() + "-未完成";
            }
        }
        SurveyExcelUtils.writeExcelData(response, data, fileName);

    }

    /**
     * 完成情况 -- 一键通知未完成学生
     */
    @Override
    public void notify(Long taskId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("taskId", taskId).eq("isFinish", "false");
        List<TaskStudentDO> list = taskStudentDao.queryForList(wrapper);
        List<Long> idList = list.stream().map(TaskStudentDO::getStudentId).collect(Collectors.toList());
        if (idList.isEmpty()){
            return;
        }
        TaskDO load = taskService.load(taskId);
        basicPushMessage.surveyUndoMessage(load, idList);
    }

    /**
     * 完成调查问卷
     */
    @Override
    public void finish(Long taskId, Long studentId) {
        // 如果所有模板都已完成，则往task_student中添加一条记录，代表该学生完成了该调查任务
        List<StudentTaskTemplateVO> templateVos = taskTemplateService.studentGetByTaskId(taskId, studentId);
        boolean allTemplateIsFinish = templateVos.stream().allMatch(StudentTaskTemplateVO::getIsFinish);
        if (allTemplateIsFinish) {
            // 状态修改成已完成
            submitTemplate(taskId, studentId);
        }else{
            throw new BusinessException("请完成所有调查模板后再提交");
        }
        // 将题目结果纳入统计
        taskTemplateStudentTeacherQuestionService.taskSubmit(taskId, studentId);
    }

    /**
     * 获取学生id和姓名
     */
    @Override
    public Map<Long, String> getStudentMap(Long taskId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("taskId", taskId);
        List<TaskStudentDO> list = taskStudentDao.queryForList(wrapper);
        HashMap<Long, String> map = new HashMap<>();
        for (TaskStudentDO t : list) {
            map.put(t.getStudentId(), t.getName());
        }
        return map;
    }

    @Override
    public Map<String, Long> getByTaskId(Long taskId) {
        List<TaskStudentDO> list = queryForList("taskId", taskId);
        HashMap<String, Long> map = new HashMap<>();
        for (TaskStudentDO t : list) {
            map.put(t.getStudentNo(), t.getStudentId());
        }
        return map;
    }

    @Override
    public void reset(Long taskId, Collection<Long> studentIds) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("isFinish", "false");
        wrapper.eq("taskId", taskId);
        wrapper.in("studentId", studentIds);
        taskStudentDao.executeUpdate(wrapper);
    }

    @Override
    public void reset(Long taskId, Long templateId) {
        List<Long> studentIds = taskTemplateStudentTeacherQuestionService.getStudentIdByTaskAndTemplateId(taskId, templateId);
        if (EmptyUtils.isEmpty(studentIds)) {
            return;
        }
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("isFinish", "false");
        wrapper.eq("taskId", taskId);
        wrapper.in("studentId", studentIds);
        taskStudentDao.executeUpdate(wrapper);
    }
}
