package com.xiaoshan.edu.courseadjustment.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.model.JwtUser;
import com.common.utils.DateUtils;
import com.xiaoshan.basic.ao.NewWorkFlowAO;
import com.xiaoshan.basic.vo.BatchFlowsVO;
import com.xiaoshan.edu.ao.courseadjustment.StartFlowAO;
import com.xiaoshan.edu.api.facade.FlowFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.courseadjustment.dao.CourseAdjustmentDao;
import com.xiaoshan.edu.courseadjustment.dao.CourseReplaceDao;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import com.xiaoshan.edu.courseadjustment.entity.CourseReplaceDO;
import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import com.xiaoshan.oa.dto.TeacherDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;
import start.magic.utils.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class StartApprovalAsncy {

    @Autowired
    private CourseAdjustmentDao courseAdjustmentDao;

    @Autowired
    private CourseReplaceDao courseReplaceDao;

    @Autowired
    private FoundationFacade foundationFacade;

    @Autowired
    private FlowFacade flowFacade;


    public static final String FLOW_IS_START = "1";

    @Value("${xiaoshan.course_adjustment.related_type}")
    private Integer relatedType;


    @Async
    public void startApprovalApply(JwtUser jwtUser,StartFlowAO startFlowAo, CourseAdjustmentDO courseAdjustmentDO, String result) {
        String approvalNo = null;
        if (FLOW_IS_START.equals(result)) {
            startFlowAo.setRelatedId(courseAdjustmentDO.getId());
            this.startFlow(jwtUser,startFlowAo, courseAdjustmentDO, null);
        } else {
            return;
        }
        List<BatchFlowsVO> batchFlowsVos = flowFacade.batchFlows(courseAdjustmentDO.getId(), startFlowAo.getRelatedType());
        if (!CollectionUtils.isEmpty(batchFlowsVos)) {
            approvalNo = batchFlowsVos.get(0).getSerialNumber();
        }
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.nested(w -> w.eq("id", courseAdjustmentDO.getId()).or().eq("main_id", courseAdjustmentDO.getId()));
        updateWrapper.set("approval_no", approvalNo);
        courseAdjustmentDao.executeUpdate(updateWrapper);
    }

    @Async
    public void startReplaceApprovalApply(JwtUser jwtUser,StartFlowAO startFlowAo, CourseReplaceDO courseReplaceDO, String result) {
        // 如果流程已开启，则开始审批流程
        if (FLOW_IS_START.equals(result)) {
            startFlowAo.setRelatedId(courseReplaceDO.getId());
            log.info("开始一个新的代课审批流程，请求JSON: " + JSON.toJSONString(startFlowAo));
            this.startFlow(jwtUser,startFlowAo, null, courseReplaceDO);
        } else {
            return;
        }
        String approvalNo = null;
        List<BatchFlowsVO> batchFlowsVos = flowFacade.batchFlows(courseReplaceDO.getId(), startFlowAo.getRelatedType());
        if (!CollectionUtils.isEmpty(batchFlowsVos)) {
            approvalNo = batchFlowsVos.get(0).getSerialNumber();
        }
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("id", courseReplaceDO.getId());
        updateWrapper.set("approval_no", approvalNo);
        courseReplaceDao.executeUpdate(updateWrapper);
    }

    public void startFlow(JwtUser user,StartFlowAO ao, CourseAdjustmentDO courseAdjustmentDO, CourseReplaceDO courseReplaceDO) {
        String type = "";
        Integer typeNum = null;
        String teacherName = "";
        List<TeacherDto> list = foundationFacade.getTeachersByIds(String.valueOf(user.getId()));
        TeacherDto teacherDto = list.get(0);
        String deptIds = teacherDto.getDepartmentIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        String roleIds = StringUtils.listToString(user.getRoleIds(), ",");
        NewWorkFlowAO flowAo = new NewWorkFlowAO();
        flowAo.setApplicantId(user.getId());
        flowAo.setApplicantName(user.getName());
        flowAo.setApplicantType(1);
        flowAo.setApprover(ao.getApprover());
        flowAo.setApplicationDepts(deptIds);
        flowAo.setApplicationRoles(roleIds);
        flowAo.setAssigneeIds(String.valueOf(user.getId()));
        flowAo.setConditions(ao.getConditions());
        flowAo.setNodeInfo(ao.getNodeInfo());
        flowAo.setProcessDefinitionKey(ao.getProcessDefinitionKey());
        flowAo.setRelatedType(relatedType);
        JsonArray j = new JsonArray();
        if (courseAdjustmentDO != null) {
            type = AdjustmentType.ADJUSTMENT.getName();
            typeNum = AdjustmentType.ADJUSTMENT.getType();
            teacherName = courseAdjustmentDO.getTeacherName();
            flowAo.setBusinessKey(String.valueOf(courseAdjustmentDO.getId()));
            flowAo.setToDoContent(teacherName + "教师的调课申请");
            flowAo.setRelatedId(String.valueOf(courseAdjustmentDO.getId()));
            j.put(type);
            j.put("调课时间：" + courseAdjustmentDO.getCourseTime());
            if (courseAdjustmentDO.getIsSelfStudy() && StringUtils.isEmpty(courseAdjustmentDO.getAdjustedCourseTime())) {
                j.put("课程情况：" + "自修");

            } else {
                j.put("被调时间：" + courseAdjustmentDO.getAdjustedCourseTime());
            }
        }
        if (courseReplaceDO != null) {
            type = AdjustmentType.REPLACE.getName();
            typeNum = AdjustmentType.REPLACE.getType();
            teacherName = courseReplaceDO.getTeacherName();
            flowAo.setBusinessKey(String.valueOf(courseReplaceDO.getId()));
            flowAo.setToDoContent(teacherName + "教师的代课申请");
            flowAo.setRelatedId(String.valueOf(courseReplaceDO.getId()));
            j.put(type);
            j.put("开始时间：" + courseReplaceDO.getReplaceStartDate() + " " + courseReplaceDO.getStartSection());
            j.put("结束时间：" + courseReplaceDO.getReplaceEndDate() + " " + courseReplaceDO.getEndSection());
        }
        flowAo.setExtra(j.toString());
        JsonObject jsonObject = new JsonObject();
        jsonObject.put("adjustmentType", typeNum);
        jsonObject.put("first", "老师您好，" + teacherName + "老师的" + type + "申请正等待您的审批，请及时处理！");
        jsonObject.put("taskName", type + "申请");
        jsonObject.put("date", DateUtils.formatDateTime(new Date()));
        jsonObject.put("remark", "请尽快处理");
        flowAo.setOfficeAccountData(jsonObject.toString());

        flowFacade.beginTheWorkFlow(flowAo);
    }

}
