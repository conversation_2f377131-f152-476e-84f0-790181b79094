package com.xiaoshan.edu.timetable.excel;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;

/**
 * <AUTHOR>
 */
public abstract class AbstractThreadLocalStyle implements CellStyleCreator {

    private final ThreadLocal<CellStyle> THREAD_LOCAL_STYLE = new ThreadLocal<>();

    @Override
    public CellStyle createStyle(Cell cell) {
        CellStyle cellStyle = THREAD_LOCAL_STYLE.get();
        if (cellStyle == null) {
            cellStyle = doCreateStyle(cell);
            THREAD_LOCAL_STYLE.set(cellStyle);
        }
        return cellStyle;
    }

    /**
     * 创建单元格样式
     *
     * @param cell
     * @return
     */
    public abstract CellStyle doCreateStyle(Cell cell);

    public void releaseStyle() {
        THREAD_LOCAL_STYLE.remove();
    }

}
