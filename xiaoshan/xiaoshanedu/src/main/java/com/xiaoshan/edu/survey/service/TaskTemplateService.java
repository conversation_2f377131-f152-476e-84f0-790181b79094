package com.xiaoshan.edu.survey.service;

import com.xiaoshan.edu.ao.survey.TaskTemplateAO;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.vo.survey.StudentTaskTemplateVO;
import com.xiaoshan.edu.vo.survey.TaskTemplateVO;
import start.framework.service.SqlBaseService;

import java.util.List;

public interface TaskTemplateService extends SqlBaseService<TaskTemplateDO,Long> {

    /**
     * 调查模板 -- 根据调查任务id查询调查模板列表
     * @param taskId
     * @return
     */
    List<TaskTemplateVO> getByTaskId(Long taskId);

    /**
     * 删除调查任务关联的调查模板
     * @param taskId
     */
    void removeByTaskId(Long taskId);

    /**
     * 调查任务添加调查模板
     * @param taskId
     * @param templateList
     */
    void add(Long taskId, List<TaskTemplateAO> templateList);

    /**
     * 小程序 -- 根据调查任务id查询调查模板列表
     * @param taskId
     * @param studentId
     * @return
     */
    List<StudentTaskTemplateVO> studentGetByTaskId(Long taskId, Long studentId);

    /**
     * 教师端 -- 调查模板列表
     * @param taskId
     * @return
     */
    List<TaskTemplateVO> templateList(Long taskId);

    /**
     * 根据模板id查询
     * @param id
     * @return
     */
    List<TaskTemplateDO> getByTemplate(Long id);

    /**
     * 根据调查任务id和模板id查询
     * @param taskId
     * @param templateId
     * @return
     */
    TaskTemplateDO getByTaskTemplateId(Long taskId, Long templateId);
}
