package com.xiaoshan.edu.api.facade;

import com.xiaoshan.basic.vo.*;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import com.xiaoshan.edu.model.RoomClassVO;
import com.xiaoshan.edu.model.vo.WeekConfigVO;


import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SettingFacade {

    /**
     * 获取当前学期
     *
     * @return
     */
    SemesterVO currentSemester();


    /**
     * 获取周配置信息
     *
     * @param id
     * @return
     */
    Map<Integer, WeekConfigVO> weekConfigNumberMap(Long id);


    /**
     * 获取周配置列表
     *
     * @param id
     * @return
     */
    List<WeekConfigVO> weekConfig(Long id);


    /**
     * 获取事项列表
     *
     * @param id
     * @return
     */
    List<ItemConfigVO> items(Long id);


    /**
     * 获取学期列表
     *
     * @return
     */
    List<SemesterVO> semesters();

    /**
     * 获取学期map
     *
     * @return
     */
    Map<Long, SemesterVO> semesterMap();


    /**
     * 消息推送配置
     *
     * @param itemCode
     * @return
     */
    PushItemVO pushItems(String itemCode);


    /**
     * 获取作息时间
     *
     * @return
     */
    List<RestSectionVO> getRestAllBySection();

    /**
     * 获取作息时间map
     *
     * @param sectionId
     * @param enrollmentYear
     * @return
     */
    Map<String, RestSectionDetailVO> getRestAll(Long sectionId, Integer enrollmentYear);


    /**
     * 获取当前作息时间
     *
     * @param gradeName
     * @return
     */
    RestTimeVO getCurrentRest(String gradeName);

    /**
     * 获取房间列表
     *
     * @return
     */
    List<RoomVO> rooms();

    /**
     * 获取房间树
     *
     * @return
     */
    List<RoomClassVO> getRoomsTree();

    /**
     * 按类型获取房间
     *
     * @param roomType
     * @return
     */
    List<ClassBuildingTreeDTO> getRoomByRoomType(Integer roomType);

    /**
     * 获取日期配置
     *
     * @param day
     * @return
     */
    DayConfigVO dayConfig(Date day);

    /**
     * 按日志获取星期配置
     *
     * @param date
     * @return
     */
    WeekConfigVO getDateWeek(java.sql.Date date);

    /**
     * 获取当前周
     *
     * @return
     */
    WeekConfigVO currentWeek();
}
