package com.xiaoshan.edu.survey.entity;

import java.util.List;

import com.common.converter.survey.CurriculumListConverterEditor;
import com.common.converter.survey.QuestionOptionListConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.ScriptConverter;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.converter.FieldJson;

@Getter@Setter@ToString
@Entity("templateQuestion")
@Table("sur_template_question")
@TableDef(comment = "调查模板题目表", charset = "utf8mb4", collate = "utf8mb4_bin")
public class TemplateQuestionDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;

	public TemplateQuestionDO(){}

	@ColumnDef(comment = "调查模板id")
	private Long templateId;

	@ColumnDef(comment = "题目类型")
	private QuestionType questionType;

	@ColumnDef(comment = "题干内容", length = 100)
	private String title;

	@ColumnDef(comment = "题目选项", isNull = true)
	@ScriptConverter(FieldJson.class)
	@PropertyConverter(QuestionOptionListConverterEditor.class)
	private List<QuestionOptionDTO> items;

	@ColumnDef(comment = "是否必填")
	private Boolean isRequired;

	@ColumnDef(comment = "结果是否教师可见", defaultValue = "'true'")
	private Boolean isVisible;

	@ColumnDef(comment = "关联课程列表", isNull = true)
	@ScriptConverter(FieldJson.class)
	@PropertyConverter(CurriculumListConverterEditor.class)
	private List<String> curriculumList;

	@ColumnDef(comment = "题号，按题号升序排序")
	private Integer questionSort;

}
