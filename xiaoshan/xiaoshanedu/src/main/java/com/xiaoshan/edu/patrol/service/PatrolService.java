package com.xiaoshan.edu.patrol.service;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.xiaoshan.edu.ao.patrol.*;
import com.xiaoshan.edu.patrol.entity.PatrolDO;
import com.xiaoshan.edu.vo.patrol.*;

import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

public interface PatrolService extends SqlBaseService<PatrolDO, Long> {
    /**
     * 分页查询
     * @param ao
     * @return
     */
    QueryResult<List<PatrolVO>> queryPage(PatrolRecordPageAO ao);

    /**
     * 分页
     * @param ao
     * @return
     */
    QueryResult<List<PatrolVO>> queryPage(PatrolSinglePageAO ao);

    /**
     * 统计分页
     * @param ao
     * @return
     */
    QueryResult<List<PatrolStatisticsVO>> getStatisticsPage(PatrolStatisticsPageAO ao);

    /**
     * 表格导出
     * @param ao
     * @param response
     */
    void recodeExcelExport(PatrolRecordPageAO ao, HttpServletResponse response);

    /**
     * 单个老师记录表格导出
     * @param ao
     * @param response
     */
    void singleRecodeExcelExport(PatrolSinglePageAO ao, HttpServletResponse response);

    /**
     * 小程序分页
     * @param ao
     * @param name
     * @return
     */
    QueryResult<List<PatrolVO>> getAppletRecord(PatrolPageAO ao, String name);

    /**
     * 保存巡课数据
     * @param ao
     */
    void savePatrol(PatrolSaveAO ao);

    /**
     * 统计数据表格导出
     * @param ao
     * @param response
     */
    void statisticsExcelExport(PatrolStatisticsPageAO ao, HttpServletResponse response);

    /**
     * 获取巡课以及巡课对象数据
     * @param id
     * @param isFromParent
     * @param stuId
     * @return
     */
    PatrolVO loadPatrolAndObjects(Long id, Boolean isFromParent, Long stuId);

    /**
     * 获取巡课以及巡课对象数据
     * @param id
     * @param objectId
     * @return
     */
    PatrolVO loadPatrolAndObjects(Long id, Long objectId);

    /**
     * 根据班级和时间获取课表数据
     * @param ao
     * @return
     */
    SubjectAndObjectVO getCurriculum(PatrolTimeAndClassAO ao);

    /**
     * 获取所有班级和教室
     * @return
     */
    ClassAndRoomVO getRoom();

    /**
     * 获取当前节次
     * @param classId
     * @return
     */
    String getCurrentSection(Long classId);

    /**
     * 根据教师id查询巡课记录
     * @param ao
     * @return
     */
    PatrolElectronicArchivesVO getPatrolElectronicRecords(PatrolElectronicArchivesAO ao);
}
