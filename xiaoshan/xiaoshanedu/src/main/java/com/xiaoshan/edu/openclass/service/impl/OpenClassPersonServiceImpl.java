package com.xiaoshan.edu.openclass.service.impl;

import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.openclass.dao.OpenClassPersonDao;
import com.xiaoshan.edu.openclass.entity.OpenClassPersonDO;
import com.xiaoshan.edu.openclass.service.OpenClassPersonService;
import com.xiaoshan.edu.vo.openclass.TeacherInfoVO;
import com.xiaoshan.oa.dto.TeacherDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.service.impl.SqlBaseServiceImpl;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;

import java.util.List;
import java.util.Map;

@Service("openClassPersonService")
public class OpenClassPersonServiceImpl extends SqlBaseServiceImpl<OpenClassPersonDO,Long>
implements OpenClassPersonService {

	private OpenClassPersonDao openClassPersonDao;

	@Autowired
    private FoundationFacade foundationFacade;

	public OpenClassPersonServiceImpl(@Qualifier("openClassPersonDao")OpenClassPersonDao openClassPersonDao) {
		super(openClassPersonDao);
		this.openClassPersonDao=openClassPersonDao;
	}


    @Override
    public Boolean exist(Long openClassId, Long teacherUserId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select("count(1)");
        wrapper.eq("open_class_id",openClassId);
        wrapper.eq("teacher_user_id",teacherUserId);
        int count = openClassPersonDao.queryForInt(wrapper);
        return count == 1;
    }


    @Override
    public void removeByOpenClassId(Long id) {
        DeleteWrapper wrapper = new DeleteWrapper();
        wrapper.eq("open_class_id",id);
        openClassPersonDao.executeUpdate(wrapper);
    }

    @Override
    public void removeByUserIdAndOpenClassId(Long userId, Long openClassId) {
        DeleteWrapper wrapper = new DeleteWrapper();
        wrapper.eq("teacher_user_id",userId).eq("open_class_id",openClassId);
        openClassPersonDao.executeUpdate(wrapper);
    }

    @Override
    public OpenClassPersonDO getByOpenClassIdAndUserId(Long openClassId, Long userId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("open_class_id",openClassId).eq("teacher_user_id",userId);
        return get(openClassPersonDao.queryForList(wrapper));
    }

    @Override
    public List<OpenClassPersonDO> getByOpenClassId(Long openClassId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("open_class_id",openClassId).eq("evaluated",String.valueOf(true));
        return openClassPersonDao.queryForList(wrapper);
    }

    @Override
    public List<TeacherInfoVO> getListenList(Long openClassId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select(
                "teacher_id AS teacherId",
                "teacher_department AS teacherDepartment",
                "teacher_name AS teacherName",
                "teacher_user_id AS teacherUserId")
                .eq("open_class_id",openClassId);
        Map<Long, TeacherDto> avatarMap = foundationFacade.getAvatarMap();
        List<TeacherInfoVO> teacherInfoVos = queryForMap(TeacherInfoVO.class, wrapper);
        for (TeacherInfoVO vo : teacherInfoVos) {
            TeacherDto teacherDto = avatarMap.get(vo.getTeacherId());
            if (teacherDto!=null){
                vo.setFaceUrl(teacherDto.getUrl()==null ? "" : teacherDto.getUrl());
            }
        }
        return teacherInfoVos;
    }

}
