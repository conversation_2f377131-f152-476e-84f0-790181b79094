package com.xiaoshan.edu.survey.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.common.utils.ResponseUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import start.magic.core.ApplicationException;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public class SurveyExcelUtils {

    public static void writeExcelData(HttpServletResponse response, List<List<String>> data, String name) {
        try{
            ResponseUtils.setExcel(response, name);
            EasyExcel.write(response.getOutputStream()).sheet("sheet1").registerWriteHandler(new AbstractMergeStrategy() {
                @Override
                protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
                    com.xiaoshan.edu.timetable.utils.ExcelUtils.setCell(sheet,cell);
                }
            }).doWrite(data);
        } catch (IOException e){
            throw new ApplicationException(e);
        }
    }

    public static void exportExcelStyle(XSSFWorkbook workbook, Map<Integer, Integer> mergeMap) {
        XSSFCellStyle cellStyleGreen = workbook.createCellStyle();
        setCell(cellStyleGreen, IndexedColors.LIGHT_GREEN.index);

        XSSFCellStyle defaultCellStyle = workbook.createCellStyle();
        setCell(defaultCellStyle, null);

        XSSFSheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (mergeMap.get(cell.getColumnIndex())!=null && mergeMap.get(cell.getColumnIndex())%2==0){
                    cell.setCellStyle(cellStyleGreen);
                }else {
                    cell.setCellStyle(defaultCellStyle);
                }
            }
        }
        for (CellRangeAddress mergedRegion : sheet.getMergedRegions()) {
            RegionUtil.setBorderLeft(BorderStyle.THIN,mergedRegion, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN,mergedRegion, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN,mergedRegion, sheet);
            RegionUtil.setBorderBottom(BorderStyle.THIN,mergedRegion, sheet);
        }
    }

    public static void setCell(XSSFCellStyle cellStyle, Short indexColorIndex){
        if (indexColorIndex != null){
            cellStyle.setFillForegroundColor(indexColorIndex);
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
    }

    public static void exportExcelStyleNew(XSSFWorkbook workbook, Map<Integer, Integer> mergeMap, int index) {
        XSSFCellStyle cellStyleGreen = workbook.createCellStyle();
        setCell(cellStyleGreen, IndexedColors.LIGHT_GREEN.index);

        XSSFCellStyle defaultCellStyle = workbook.createCellStyle();
        setCell(defaultCellStyle, null);

        XSSFSheet sheet = workbook.getSheetAt(index);
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (mergeMap.get(cell.getColumnIndex())!=null && mergeMap.get(cell.getColumnIndex())%2==0){
                    cell.setCellStyle(cellStyleGreen);
                }else {
                    cell.setCellStyle(defaultCellStyle);
                }
            }
        }
        for (CellRangeAddress mergedRegion : sheet.getMergedRegions()) {
            RegionUtil.setBorderLeft(BorderStyle.THIN,mergedRegion, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN,mergedRegion, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN,mergedRegion, sheet);
            RegionUtil.setBorderBottom(BorderStyle.THIN,mergedRegion, sheet);
        }
    }
}
