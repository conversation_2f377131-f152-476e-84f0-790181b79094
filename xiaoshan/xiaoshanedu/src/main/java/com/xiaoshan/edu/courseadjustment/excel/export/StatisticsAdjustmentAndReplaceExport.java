package com.xiaoshan.edu.courseadjustment.excel.export;

import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import org.apache.poi.ss.usermodel.FillPatternType;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class StatisticsAdjustmentAndReplaceExport {
    @ExcelProperty(value = "序号", index = 0)
    private Integer index;

    @ExcelProperty(value = "教师名称", index = 1)
    private String teacherName;

    @ExcelProperty(value = "部门", index = 2)
    private String departmentName;

    @ExcelProperty(value = "调课次数", index = 3)
    private String adjustmentCount;

    @ExcelProperty(value = "代课次数", index = 4)
    private String toReplaceCount;

    @ExcelProperty(value = "代课节数", index = 5)
    private String toReplaceSection;

    @ExcelProperty(value = "被代次数", index = 6)
    private String beReplacedCount;

    @ExcelProperty(value = "被代节数", index = 7)
    private String beReplacedSection;
}
