package com.xiaoshan.edu.api.facade.impl;

import com.alibaba.fastjson.JSONObject;
import com.xiaoshan.basic.dto.OssFileInfoDTO;
import com.xiaoshan.basic.vo.PageVO;
import com.xiaoshan.edu.api.facade.OssFacade;
import com.xiaoshan.oa.dto.StudentDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class OssFacadeImpl implements OssFacade {

    private final static String OSS = "https://oss";

    @Autowired
    @Qualifier("webClient")
    private WebClient webClient;

    @Override
    public OssFileInfoDTO uploadFile(String filePath, String bucketName, String objectName) {
        FileSystemResource fileSystemResource = new FileSystemResource(new File(filePath));
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("file", fileSystemResource);
        if (StringUtils.isNotEmpty(bucketName)) {
            form.add("bucketName", bucketName);
        }
        if (StringUtils.isNotEmpty(objectName)) {
            form.add("objectName", objectName);
        }
        ParameterizedTypeReference<OssFileInfoDTO> responseBodyType = new ParameterizedTypeReference<OssFileInfoDTO>() {
        };
        return webClient.post().uri(OSS + "/files").contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromMultipartData(form)).retrieve().bodyToMono(responseBodyType).block();
    }
}
