package com.xiaoshan.edu.patrol.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

@Getter@Setter@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class PatrolRecordExport {
    @ExcelProperty(value = "序号",index = 0)
    private Integer index;

    @ExcelProperty(value = "巡课对象",index = 1)
    private String objectName;

    @ExcelProperty(value = "巡课类型", index = 2)
    private String type;

    @ExcelProperty(value = "巡课班级", index = 3)
    private String className;

    @ExcelProperty(value = "巡课时间", index = 4)
    private String courseTime;

    @ExcelProperty(value = "巡课科目", index = 5)
    private String courseName;

    @ExcelProperty(value = "巡课老师", index = 6)
    private String patrolTeacherName;

    @ExcelProperty(value = "巡课评价", index = 7)
    private String appraise;

    @ColumnWidth(24)
    @ExcelProperty(value = "记录时间", index = 8)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date recordTime;

    @ColumnWidth(24)
    @ExcelProperty(value = "巡课内容", index = 9)
    private String content;
}
