package com.xiaoshan.edu.courseadjustment.controller;


import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.topnetwork.ao.IDAO;
import com.xiaoshan.edu.ao.courseadjustment.*;
import com.xiaoshan.edu.courseadjustment.service.CourseReplaceService;
import com.xiaoshan.edu.model.ao.CourseReplaceApplyAO;
import com.xiaoshan.edu.model.ao.GetReplaceTeacherListAO;
import com.xiaoshan.edu.model.ao.GetSectionAO;
import com.xiaoshan.edu.vo.courseadjustment.CourseReplaceVO;
import com.xiaoshan.edu.vo.courseadjustment.ReplaceTeacherVO;
import com.xiaoshan.edu.vo.courseadjustment.SectionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/courseAdjustment")
@Api(tags = "调代课接口")
public class CourseReplaceController extends BaseController {

    @Autowired
    private CourseReplaceService courseReplaceService;

    @ApiOperation(value = "代课老师列表")
    @AuthenticationCheck
    @PostMapping("/pc/replace/getReplaceTeacherList")
    public ResultResponse<List<ReplaceTeacherVO>> getReplaceTeacherList(@RequestBody GetReplaceTeacherListAO ao) {
        return response(courseReplaceService.getReplaceTeacherList(ao));
    }

    @ApiOperation(value = "（二）pc管理/pc个人/小程序-根据教师id和时间范围获取上课节次信息列表")
    @AuthenticationCheck
    @PostMapping("/pc/replace/getSectionInfos")
    public ResultResponse<SectionVO> getSections(@RequestBody GetSectionAO getSectionAo) {
        return response(courseReplaceService.getSection(getSectionAo));
    }

    @ApiOperation(value = "设置代课老师【教务处 审批通过 时调用】", notes = "设置代课老师，以及抄送消息给代课老师")
    @AuthenticationCheck
    @PostMapping("/ArrangeReplaceTeacher")
    public BaseResponse arrangeReplaceTeacher(@RequestBody SetReplaceTeacherAO ao) {
        courseReplaceService.updateReplaceTeacher(ao);
        return response();
    }

    @ApiOperation("pc管理-代课记录")
    @AuthenticationCheck
    @PostMapping("/pc/replace/page")
    public PageResponse<List<CourseReplaceVO>> page(@RequestBody ReplacePageAO ao) {
        return response(courseReplaceService.queryPage(ao));
    }

    @ApiOperation("pc个人-代课记录")
    @AuthenticationCheck
    @PostMapping("/me/replace/page")
    public PageResponse<List<CourseReplaceVO>> myReplacePage(@RequestBody ReplacePageAO ao) {
        return response(courseReplaceService.queryMyPage(ao));
    }

    @ApiOperation("代课记录数据导出")
    @AuthenticationCheck
    @PostMapping("/pc/replace/page/export")
    public void replaceExport(@RequestBody ReplacePageAO ao, HttpServletResponse response) {
        courseReplaceService.replaceExport(ao, response);
    }

    @ApiOperation("根据id删除代课记录")
    @AuthenticationCheck
    @DeleteMapping("/remove/replace")
    public BaseResponse remove(@RequestBody IDAO ao) {
        courseReplaceService.removeCourseReplaceById(ao.getId());
        return response();
    }

    @ApiOperation("代课详情")
    @AuthenticationCheck
    @GetMapping("/detail/replace/{id}")
    public ResultResponse<CourseReplaceVO> detail(
            @PathVariable
            @ApiParam("代课id")
            @NotNull
            @LongValid Long id) {
        return response(courseReplaceService.getReplaceById(id));
    }

    @ApiOperation(value = "（二）pc管理/pc个人/小程序-发起代课", notes = "个人端和小程序发起，isAdminApply填false")
    @AuthenticationCheck
    @PostMapping("/pc/replace/apply")
    public BaseResponse applyReplace(@RequestBody CourseReplaceApplyAO ao) {
        courseReplaceService.applyReplace(ao);
        return response();
    }

    @ApiOperation("调代课统计-单个教师详情-代课")
    @AuthenticationCheck
    @PostMapping("/pc/statistics/detail/replace")
    public PageResponse<List<CourseReplaceVO>> singleReplacePage(@RequestBody SinglePageAO ao) {
        return response(courseReplaceService.singleReplacePage(ao));
    }
}
