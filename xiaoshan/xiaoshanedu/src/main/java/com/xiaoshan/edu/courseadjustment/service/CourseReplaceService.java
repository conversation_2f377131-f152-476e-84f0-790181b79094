package com.xiaoshan.edu.courseadjustment.service;

import com.xiaoshan.edu.ao.courseadjustment.*;
import com.xiaoshan.edu.courseadjustment.dto.ReplaceAndSectionDTO;
import com.xiaoshan.edu.courseadjustment.entity.CourseReplaceDO;
import com.xiaoshan.edu.dto.ReplaceCountDTO;
import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import com.xiaoshan.edu.model.ao.CourseReplaceApplyAO;
import com.xiaoshan.edu.model.ao.GetReplaceTeacherListAO;
import com.xiaoshan.edu.model.ao.GetSectionAO;
import com.xiaoshan.edu.vo.courseadjustment.CourseReplaceVO;
import com.xiaoshan.edu.vo.courseadjustment.ReplaceTeacherVO;
import com.xiaoshan.edu.vo.courseadjustment.SectionVO;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CourseReplaceService extends SqlBaseService<CourseReplaceDO,Long> {

    /**
     * pc管理-代课记录分页
     * @param ao
     * @return
     */
    QueryResult<List<CourseReplaceVO>> queryPage(ReplacePageAO ao);

    /**
     * pc个人-代课记录分页
     * @param ao
     * @return
     */
    QueryResult<List<CourseReplaceVO>> queryMyPage(ReplacePageAO ao);

    /**
     * 代课数据导出
     * @param ao
     * @param response
     */
    void replaceExport(ReplacePageAO ao, HttpServletResponse response);

    /**
     * 根据id删除代课记录（逻辑删除）
     * @param id
     */
    void removeCourseReplaceById(Long id);

    /**
     * 管理端-发起代课
     * @param ao
     */
    void applyReplace(CourseReplaceApplyAO ao);

    /**
     * 单个教师在时间范围内代课记录分页（审批通过）
     * @param ao
     * @return
     */
    QueryResult<List<CourseReplaceVO>> singleReplacePage(SinglePageAO ao);

    /**
     * 根据id获取代课详情
     * @param id
     * @return
     */
    CourseReplaceVO getReplaceById(Long id);

    /**
     * 校验该代课老师在该时间段内是否已经有其他代课课程
     * @param courseTimeList
     * @param teacherIds
     * @param isQueryReplaced
     * @return
     */
    List<Long> replaceIsConflict(List<String> courseTimeList, List<Long> teacherIds, Boolean isQueryReplaced);

    /**
     * 更新代课老师
     * @param ao
     */
    void updateReplaceTeacher(SetReplaceTeacherAO ao);

    /**
     * 根据教师id和时间范围获取课表对应节次
     * @param ao
     * @return
     */
    SectionVO getSection(GetSectionAO ao);

    /**
     * 根据课程和节次过滤出对应的代课教师列表
     * @param ao
     * @return
     */
    List<ReplaceTeacherVO> getReplaceTeacherList(GetReplaceTeacherListAO ao);

    /**
     * 根据id更新审批状态
     * @param id 代课id
     * @param approvalState 审批状态
     */
    void updateReplaceApprovalState(Long id, ApprovalState approvalState);

    /**
     * 根据课表ID获取审批中或已通过的代课以及对应节次
     * @param curriculumTableId 课表Id
     * @return 代课以及对应的节次
     */
    List<ReplaceAndSectionDTO> getReplaceAndSectionByCurriculumTableId(Long curriculumTableId);

    /**
     * 根据ids 更新数据为 失效异常
     * @param ids 代课主键
     * @param isUnusual
     */
    void updateStartByIds(List<Long> ids, Boolean isUnusual);

    /**
     * 根据代课日期获取涉及的代课数据
     * @param dates 日期集合
     * @return 代课数据
     */
    List<ReplaceAndSectionDTO> getReplaceAndSectionByCourseDates(List<Date> dates);

    /**
     * 根据代课ids获取代课与被代课数的教师id映射
     * @param ids 代课ids
     * @param teacherName
     * @param departmentName
     * @return 教师id 与代课和被代课次数的映射
     */
    Map<Long, ReplaceCountDTO> getReplaceCount(Set<Long> ids , String teacherName, String departmentName);

    /**
     * 根据代课老师课表id获取代课记录
     * @param curriculumTableId 课表id
     * @return
     */
    List<ReplaceAndSectionDTO> getReplaceAndSectionByReplaceCurriculumTableId(Long curriculumTableId);

    /**
     * 获取代课以及代课节次
     * @param replaceIds
     * @return
     */
    List<ReplaceAndSectionDTO> getReplaceAndSectionDtos(Set<Long> replaceIds);

    /**
     * 根据教师id获取未失效代课数据
     * @param teacherId
     * @return
     */
    List<CourseReplaceDO> getByReplaceTeacherAndIdIsNotInvalid(Long teacherId);
}
