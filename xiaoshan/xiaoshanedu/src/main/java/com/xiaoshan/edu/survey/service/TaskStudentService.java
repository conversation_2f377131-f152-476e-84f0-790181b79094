package com.xiaoshan.edu.survey.service;

import com.xiaoshan.edu.ao.survey.TaskStudentAO;
import com.xiaoshan.edu.survey.entity.TaskStudentDO;
import com.xiaoshan.edu.vo.survey.StatisticNumVO;
import com.xiaoshan.edu.vo.survey.StudentQuestionVO;
import com.xiaoshan.edu.vo.survey.TaskStudentVO;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface TaskStudentService extends SqlBaseService<TaskStudentDO,Long> {

    /**
     * 小程序 -- 调查模板列表页提交(当所有模板都已完成时调用，否则不需要调)
     * @param taskId
     * @param studentId
     */
    void submitTemplate(Long taskId, Long studentId);

    /**
     * 调查模板列表页提交
     * @param taskId
     * @param studentIds
     */
    void submitTemplate(Long taskId, Collection<Long> studentIds);

    /**
     * 根据taskId删除
     * @param taskId
     */
    void removeByTaskId(Long taskId);

    /**
     * 完成情况 -- 分页查询
     * @param ao
     * @return
     */
    QueryResult<List<TaskStudentVO>> page(TaskStudentAO ao);

    /**
     * 完成情况 -- 总人数、已完成、未完成人数
     * @param taskId
     * @return
     */
    StatisticNumVO getNum(Long taskId);

    /**
     * 完成情况 -- 查看详情
     * @param taskId
     * @param studentId
     * @param templateId
     * @return
     */
    List<StudentQuestionVO> look(Long taskId, Long templateId, Long studentId);

    /**
     * 完成情况 -- 导出
     * @param ao
     * @param response
     */
    void export(HttpServletResponse response, TaskStudentAO ao);

    /**
     * 完成情况 -- 一键通知未完成学生
     * @param taskId
     */
    void notify(Long taskId);

    /**
     * 完成调查问卷
     * @param studentId
     * @param taskId
     */
    void finish(Long taskId, Long studentId);

    /**
     * 获取学生id和姓名
     * @param taskId
     * @return
     */
    Map<Long, String> getStudentMap(Long taskId);

    /**
     * 学号 -> 学生id
     * @param taskId
     * @return
     */
    Map<String, Long> getByTaskId(Long taskId);

    /**
     * 完成状态重置
     * @param taskId
     * @param studentIds
     */
    void reset(Long taskId, Collection<Long> studentIds);

    /**
     * 完成状态重置
     * @param taskId
     * @param templateId
     */
    void reset(Long taskId, Long templateId);
}
