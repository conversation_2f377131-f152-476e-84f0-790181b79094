package com.xiaoshan.edu.api.facade.impl;

import com.xiaoshan.basic.ao.CourseQuery;
import com.xiaoshan.basic.vo.CourseVO;
import com.xiaoshan.basic.vo.PageVO;
import com.xiaoshan.basic.vo.StudentCourseListVO;
import com.xiaoshan.basic.vo.StudentVO;
import com.xiaoshan.edu.api.facade.CourseFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CourseFacadeImpl implements CourseFacade {

    private final static String COURSE_URI = "https://course/courses/courseInfo";

    @Autowired
    @Qualifier("webClient")
    private WebClient webClient;

    @Override
    public List<CourseVO> postCourseInfo(CourseQuery courseQuery) {
        ParameterizedTypeReference<PageVO<List<CourseVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<CourseVO>>>() {
        };
        PageVO<List<CourseVO>> page = webClient.post().uri(COURSE_URI).body(BodyInserters.fromPublisher(Mono.just(courseQuery),CourseQuery.class)).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<StudentVO> getCourseStudents(String courseCode) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.set("current", "-1");
        formData.set("size", "-1");
        ParameterizedTypeReference<PageVO<List<StudentVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<StudentVO>>>() {
        };
        PageVO<List<StudentVO>> page = webClient.get().uri("https://course/courses/" + courseCode + "/student", formData).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<StudentCourseListVO> studentCourseList(List<Long> studentIds) {
        ParameterizedTypeReference<List<StudentCourseListVO>> responseBodyType = new ParameterizedTypeReference<List<StudentCourseListVO>>() {
        };
        return webClient.post().uri("https://course/courses/studentCourseList").body(BodyInserters.fromPublisher(Mono.just(studentIds),Object.class)).retrieve().bodyToMono(responseBodyType).block();
    }
}
