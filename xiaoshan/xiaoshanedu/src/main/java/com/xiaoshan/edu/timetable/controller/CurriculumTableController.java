package com.xiaoshan.edu.timetable.controller;


import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.xiaoshan.edu.api.facade.SettingFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.common.annotation.AuthenticationCheck;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.mvc.BaseController;
import com.common.utils.ResponseUtils;
import com.xiaoshan.basic.vo.SemesterVO;
import com.xiaoshan.edu.ao.timetable.CheckAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTableAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTablePageAO;
import com.xiaoshan.edu.ao.timetable.PublishedAO;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.vo.timetable.CurriculumTableVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.ApplicationException;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/curriculumtable")
@Api(tags = "课表接口")
public class CurriculumTableController extends BaseController {

	@Autowired
	private CurriculumTableService curriculumTableService;
	@Autowired
	private SettingFacade settingFacade;
	
	@ApiOperation("批量删除-管理员")
	@AuthenticationCheck
	@DeleteMapping
	public BaseResponse remove(@RequestBody List<Long> ao) {
		curriculumTableService.remove(ao);
		return response();
	}
	
	@ApiOperation("分页查询-管理员")
	@AuthenticationCheck
	@PostMapping("/admin/page")
	public PageResponse<List<CurriculumTableVO>> managerPage(@RequestBody CurriculumTablePageAO curriculumTablePage) {
		return response(curriculumTableService.page(curriculumTablePage,null,null));
	}
	
	@ApiOperation("分页查询-班主任、教师")
	@AuthenticationCheck
	@PostMapping("/teacher/page")
	public PageResponse<List<CurriculumTableVO>> teacherPage(@RequestBody CurriculumTablePageAO ao) {
		JwtUser user=UserContextHolder.getUser();
		SemesterVO semester = settingFacade.currentSemester();
		List<String> auths=curriculumTableService.getAuth(semester.getId(), semester.getOrderNo(), user.getId());
		String s="3";
		if(auths.contains(s)) {
			user.setId(null);
		}
		return response(curriculumTableService.page(ao,null,user.getId()));
	}
	
	@ApiOperation("获取上传ID-管理员")
	@AuthenticationCheck
	@PostMapping("/uploadId")
	public ResultResponse<Long> upload(@RequestBody CurriculumTableAO ao){
		return response(curriculumTableService.nextId(ao));
	}
	
	@ApiOperation("上传-管理员")
	@PostMapping("/upload/{uploadId}/{type}/{weekType}")
	public BaseResponse upload(
			@PathVariable @ApiParam("上传ID") @NotNull @LongValid Long uploadId,
			@PathVariable @ApiParam("课表类型") @NotNull @Enum CurriculumTableTypeEnum type,
			@PathVariable @ApiParam("周类型") @NotNull @Enum WeekTypeEnum weekType,
			@PathVariable @ApiParam("课表文件") @NotNull MultipartFile file){
		curriculumTableService.upload(uploadId,type,weekType,file);
		return response();
	}
	
	@ApiOperation("下载-管理员")
	@GetMapping("/download/{uploadId}/{type}/{weekType}")
	public void download(@PathVariable @ApiParam("上传ID") @NotNull @LongValid Long uploadId,
			@PathVariable @ApiParam("课表类型") @NotNull @Enum CurriculumTableTypeEnum type,
			@PathVariable @ApiParam("周类型") @NotNull @Enum WeekTypeEnum weekType){
		curriculumTableService.download(response,uploadId,type,weekType);
	}
	
	@ApiOperation("下载模版-管理员")
	@GetMapping("/download/template")
	public void downloadTemplate(){
		curriculumTableService.downloadTemplate(response);
	}
	
	@ApiOperation("生成课表-管理员")
	@AuthenticationCheck
	@PutMapping("/build")
	public BaseResponse build(@RequestBody PublishedAO ao) {
		curriculumTableService.build(ao);
		return response();
	}

	@ApiOperation("校验-管理员")
	@AuthenticationCheck
	@PostMapping("/check")
	public void check(@RequestBody CheckAO ao) {
		ResponseUtils.setExcel(response, ao.getCurriculumTableType().getDescription());
		try {
			curriculumTableService.check(response.getOutputStream(), ao);
		} catch (IOException e) {
			throw new ApplicationException(e);
		}
	}
	
	@ApiOperation("校验信息-管理员")
	@AuthenticationCheck
	@PostMapping("/check/message")
	public ResultResponse<Map<CurriculumTableTypeEnum,Set<String>>> message(@RequestBody CheckAO ao) {
		return response(curriculumTableService.checkMessage(ao));
	}
	
	@ApiOperation("当前用户权限(1:班主任2:任课老师3:校领导)")
	@AuthenticationCheck
	@GetMapping("/check/auth/{semesterId}/{orderNo}")
	public ResultResponse<List<String>> getAuth(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("学期")@IntegerValid Integer orderNo) {
		JwtUser user=UserContextHolder.getUser();
		return response(curriculumTableService.getAuth(semesterId, orderNo, user.getId()));
	}
	
	@ApiOperation("获取已上传的映射-管理员")
	@AuthenticationCheck
	@GetMapping("/upload/table/{uploadId}")
	public ResultResponse<Map<WeekTypeEnum,List<CurriculumTableTypeEnum>>> getUploadTableMap(@PathVariable @ApiParam("上传ID") @NotNull @LongValid Long uploadId) {
		return response(curriculumTableService.getUploadTableMap(uploadId));
	}
	
}
