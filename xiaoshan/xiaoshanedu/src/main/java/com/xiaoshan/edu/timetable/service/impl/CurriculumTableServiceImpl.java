package com.xiaoshan.edu.timetable.service.impl;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.xiaoshan.edu.api.facade.CourseFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.mvc.Constants;
import com.common.utils.IoUtil;
import com.common.utils.ResponseUtils;
import com.xiaoshan.basic.ao.CourseQuery;
import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.basic.vo.CourseVO;
import com.xiaoshan.basic.vo.RoomVO;
import com.xiaoshan.basic.vo.SemesterVO;
import com.xiaoshan.basic.vo.StudentVO;
import com.xiaoshan.basic.vo.TeacherVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.ao.timetable.CheckAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTableAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTablePageAO;
import com.xiaoshan.edu.ao.timetable.PublishedAO;
import com.xiaoshan.edu.courseadjustment.service.CourseAdjustmentService;
import com.xiaoshan.edu.dto.TempCurriculumTableDTO;
import com.xiaoshan.edu.enums.courseadjustment.UpdateType;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.dao.CurriculumTableDao;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.timetable.excel.ClassesMergeStrategy;
import com.xiaoshan.edu.timetable.excel.GeneralMergeStrategy;
import com.xiaoshan.edu.timetable.excel.StudentMergeStrategy;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.timetable.utils.CurriculumTableBuild;
import com.xiaoshan.edu.timetable.utils.CurriculumTableCheck;
import com.xiaoshan.edu.timetable.utils.CurriculumTableDiff;
import com.xiaoshan.edu.timetable.utils.CurriculumTableExcelListener;
import com.xiaoshan.edu.timetable.utils.RuleUtils;
import com.xiaoshan.edu.vo.timetable.CurriculumTableVO;

import start.framework.commons.exception.BusinessAdviceException;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.Assert;
import start.framework.commons.utils.IoUtils;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.core.ApplicationException;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;

/**
 * <AUTHOR>
 */
@Service("curriculumTableService")
public class CurriculumTableServiceImpl extends SqlBaseServiceImplV2Ext<CurriculumTableDO, Long>
        implements CurriculumTableService {

    private final CurriculumTableDao curriculumTableDao;
    @Autowired
    private CurriculumService curriculumService;
    @Autowired
    private GeneralTableService generalTableService;
    @Autowired
    private SettingFacade settingFacade;
    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private CurriculumTableDiff curriculumTableDiff;
    @Autowired
    private CourseFacade courseFacade;
    @Lazy
    @Autowired
    private CourseAdjustmentService courseAdjustmentService;
    @Autowired
    private RedisTemplate<String, Map<String, TempCurriculumTableDTO>> redisTemplate;

    @Value("${upload.file.path}")
    private String uploadFilePath;

    @Value("${xiaoshan.role.curriculum.role_head_teacher}")
    private String roleHeadTeacherId;

    @Value("${xiaoshan.role.curriculum.school_leader}")
    private String schoolLeaderId;

    public CurriculumTableServiceImpl(@Qualifier("curriculumTableDao") CurriculumTableDao curriculumTableDao) {
        super(curriculumTableDao);
        this.curriculumTableDao = curriculumTableDao;
    }

    @Override
    public Long nextId(CurriculumTableAO ao) {
        checkExists(ao);
        return curriculumTableDao.getSnowflakeIdWorkerNextId();
    }

    @Override
    public List<CurriculumTableDO> getTable(Long semesterId, Integer orderNo) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("semesterId", semesterId).eq("firstPublished", String.valueOf(true));
        return curriculumTableDao.queryForList(wrapper);
    }

    @Override
    public CurriculumTableDO getTable(Long sectionId, int enrollmentYear, Long semesterId, Integer orderNo) {
        return getTable(sectionId, enrollmentYear, semesterId, orderNo, true);
    }

    @Override
    public CurriculumTableDO getTable(Long sectionId, int enrollmentYear, Long semesterId, Integer orderNo, Boolean firstPublished) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper
                .eq("sectionId", sectionId)
                .eq("enrollmentYear", enrollmentYear)
                .eq("semesterId", semesterId)
                .eq(firstPublished != null, "firstPublished", String.valueOf(firstPublished));
        return get(curriculumTableDao.queryForList(wrapper));
    }

    public SemesterVO checkExists(CurriculumTableAO ao) {
        if (ao.getCurriculumTableId() != null) {
            CurriculumTableDO table = load("id", ao.getCurriculumTableId());
            Assert.isNull(table, "课表信息不存在");
        }
        SemesterVO semesterVO = settingFacade.currentSemester();
        CurriculumTableDO table = getTable(ao.getSectionId(), ao.getEnrollmentYear(), semesterVO.getId(),
                semesterVO.getOrderNo(), null);
        if (table != null) {
            if (ao.getCurriculumTableId() != null) {
                if (!ao.getCurriculumTableId().equals(table.getId())) {
                    throw new BusinessException(CodeRes.CODE_700006);
                }
            } else {
                throw new BusinessException(CodeRes.CODE_700006);
            }
        }
        return semesterVO;
    }

    @Override
    public List<CurriculumTableVO> getCurrentCurriculums(Long studentId, Long teacherId) {
        SemesterVO semester = settingFacade.currentSemester();
        return getCurrentCurriculums(semester.getId(), studentId, teacherId);
    }

    @Override
    public List<CurriculumTableVO> getCurrentCurriculums(Long semesterId, Long studentId, Long teacherId) {
        CurriculumTablePageAO ao = new CurriculumTablePageAO();
        ao.setSemesterId(semesterId);
        ao.setClassTeacher(false);
        ao.setOnly(true);
        ao.setHistory(false);
        ao.setPageSize(10000);
        QueryResult<List<CurriculumTableVO>> pages = page(ao, studentId, teacherId);
        if (pages == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(pages.getResult())) {
            return null;
        }
        return pages.getResult();
    }

    public List<CurriculumTableVO> getCurrentCurriculumsList(Long semesterId, List<Long> studentId, List<Long> teacherIds) {
        CurriculumTablePageAO ao = new CurriculumTablePageAO();
        ao.setSemesterId(semesterId);
        ao.setClassTeacher(false);
        ao.setOnly(true);
        ao.setHistory(false);
        ao.setPageSize(10000);
        QueryResult<List<CurriculumTableVO>> pages = pageList(ao, studentId, teacherIds);
        if (pages == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(pages.getResult())) {
            return null;
        }
        return pages.getResult();
    }

    @Override
    public List<Long> getCurrentCurriculumIds(Long studentId, Long teacherId) {
        SemesterVO semester = settingFacade.currentSemester();
        return getCurrentCurriculumIds(semester.getId(), studentId, teacherId);
    }

    @Override
    public List<Long> getCurrentCurriculumIds(Long semesterId, Long studentId, Long teacherId) {
        List<CurriculumTableVO> result = getCurrentCurriculums(semesterId, studentId, teacherId);
        if (result == null) {
            return null;
        }
        List<Long> ids = result.stream().map(CurriculumTableVO::getCurriculumTableId).distinct()
                .collect(Collectors.toList());
        if (ids.isEmpty()) {
            return null;
        }
        return ids;
    }

    @Override
    public List<Long> getCurrentCurriculumList(Long semesterId, List<Long> studentIds, List<Long> teacherIds) {
        List<CurriculumTableVO> result = getCurrentCurriculumsList(semesterId, studentIds, teacherIds);
        if (result == null) {
            return null;
        }
        List<Long> ids = result.stream().map(CurriculumTableVO::getCurriculumTableId).distinct()
                .collect(Collectors.toList());
        if (ids.isEmpty()) {
            return null;
        }
        return ids;
    }

    @Override
    public QueryResult<List<CurriculumTableVO>> page(CurriculumTablePageAO curriculumTablePage, Long studentId, Long teacherId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select(
                "id as curriculumTableId",
                "semesterId as semesterId",
                "orderNo as orderNo",
                "sectionId as sectionId",
                "enrollmentYear as enrollmentYear",
                "singleWeek as singleWeek",
                "uploadId as uploadId",
                "published as published",
                "firstPublished as firstPublished");
        if (curriculumTablePage.getHistory()) {
            SemesterVO semester = settingFacade.currentSemester();
            // 历史数据不包括当前学期数据
            wrapper.ne("semesterId", semester.getId());
        }
        // 当前学期数据
        wrapper.eq(curriculumTablePage.getSemesterId() != null, "semesterId", curriculumTablePage.getSemesterId());
        wrapper.eq(curriculumTablePage.getSectionId() != null, "sectionId", curriculumTablePage.getSectionId());
        wrapper.eq(curriculumTablePage.getEnrollmentYear() != null, "enrollmentYear", curriculumTablePage.getEnrollmentYear());
        wrapper.eq(curriculumTablePage.getPublished() != null, "published", String.valueOf(curriculumTablePage.getPublished()));
        wrapper.eq(curriculumTablePage.getFirstPublished() != null, "firstPublished", String.valueOf(curriculumTablePage.getFirstPublished()));
        if (studentId != null || teacherId != null) {
            List<Long> curriculumTableIds = generalTableService.getCurriculumTableIds(studentId, teacherId,
                    curriculumTablePage.getClassTeacher(), curriculumTablePage.getOnly());
            if (curriculumTableIds.isEmpty()) {
                return null;
            }
            wrapper.in(true, "id", curriculumTableIds);
        }
        if (wrapper.isEmptyOfWhere()) {
            wrapper.eq("1", "1");
        }
        wrapper.orderByDesc("id");
        wrapper.last("limit " + curriculumTablePage.index() + "," + curriculumTablePage.getPageSize());
        QueryResult<List<CurriculumTableVO>> result = queryForPage(CurriculumTableVO.class, wrapper);
        if (result == null) {
            return null;
        }
        Map<Long, SemesterVO> semesterMap = settingFacade.semesterMap();
        for (CurriculumTableVO item : result.getResult()) {
            SemesterVO vo = semesterMap.get(item.getSemesterId());
            if (vo != null) {
                item.setStartYear(vo.getStartYear());
                item.setEndYear(vo.getEndYear());
            }
        }
        return result;
    }

    @Override
    public QueryResult<List<CurriculumTableVO>> pageList(CurriculumTablePageAO curriculumTablePage, List<Long> studentIds, List<Long> teacherIds) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select(
                "id as curriculumTableId",
                "semesterId as semesterId",
                "orderNo as orderNo",
                "sectionId as sectionId",
                "enrollmentYear as enrollmentYear",
                "singleWeek as singleWeek",
                "uploadId as uploadId",
                "published as published",
                "firstPublished as firstPublished");
        if (curriculumTablePage.getHistory()) {
            SemesterVO semester = settingFacade.currentSemester();
            // 历史数据不包括当前学期数据
            wrapper.ne("semesterId", semester.getId());
        }
        // 当前学期数据
        wrapper.eq(curriculumTablePage.getSemesterId() != null, "semesterId", curriculumTablePage.getSemesterId());
        wrapper.eq(curriculumTablePage.getSectionId() != null, "sectionId", curriculumTablePage.getSectionId());
        wrapper.eq(curriculumTablePage.getEnrollmentYear() != null, "enrollmentYear", curriculumTablePage.getEnrollmentYear());
        wrapper.eq(curriculumTablePage.getPublished() != null, "published", String.valueOf(curriculumTablePage.getPublished()));
        wrapper.eq(curriculumTablePage.getFirstPublished() != null, "firstPublished", String.valueOf(curriculumTablePage.getFirstPublished()));
        if (!CollectionUtils.isEmpty(studentIds) || !CollectionUtils.isEmpty(teacherIds)) {
            List<Long> curriculumTableIds = generalTableService.getCurriculumTableList(studentIds, teacherIds);
            if (curriculumTableIds.isEmpty()) {
                return null;
            }
            wrapper.in(true, "id", curriculumTableIds);
        }
        if (wrapper.isEmptyOfWhere()) {
            wrapper.eq("1", "1");
        }
        wrapper.orderByDesc("id");
        wrapper.last("limit " + curriculumTablePage.index() + "," + curriculumTablePage.getPageSize());
        QueryResult<List<CurriculumTableVO>> result = queryForPage(CurriculumTableVO.class, wrapper);
        if (result == null) {
            return null;
        }
        Map<Long, SemesterVO> semesterMap = settingFacade.semesterMap();
        for (CurriculumTableVO item : result.getResult()) {
            SemesterVO vo = semesterMap.get(item.getSemesterId());
            if (vo != null) {
                item.setStartYear(vo.getStartYear());
                item.setEndYear(vo.getEndYear());
            }
        }
        return result;
    }

    @Override
    public void upload(Long uploadId, CurriculumTableTypeEnum type, WeekTypeEnum weekType, MultipartFile file) {
        TempCurriculumTableDTO emp = new TempCurriculumTableDTO();
        emp.setUploadId(uploadId);
        emp.setType(type);
        emp.setWeekType(weekType);
        emp.setData(new LinkedHashMap<>());
        try {
            ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(file.getInputStream(),
                    new CurriculumTableExcelListener(emp, redisTemplate));
            ExcelReader excelReader = excelReaderBuilder.build();
            List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
            for (ReadSheet sheet : sheets) {
                excelReader.read(sheet);
            }
        } catch (IOException e) {
            throw new ApplicationException(e);
        } catch (Exception e) {
            throw new BusinessAdviceException(e, CodeRes.CODE_700010);
        }
        String fileName = RuleUtils.getFileName(uploadId, type, weekType);
        File dest = new File(new File(uploadFilePath).getAbsolutePath() + "/" + fileName);
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
            file.transferTo(dest);
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }

    @Override
    public void download(HttpServletResponse response, Long uploadId, CurriculumTableTypeEnum type,
                         WeekTypeEnum weekType) {
        String fileName = RuleUtils.getFileName(uploadId, type, weekType);
        File dest = new File(new File(uploadFilePath).getAbsolutePath() + "/" + fileName);
        if (dest.exists()) {
            ResponseUtils.setExcel(response, type.getDescription() + "_" + WeekTypeEnum.getWeekType(weekType));
            try (InputStream in = new FileInputStream(dest)) {
                int len;
                byte[] buffer = new byte[1024];
                OutputStream out = response.getOutputStream();
                while ((len = in.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }
            } catch (IOException e) {
                throw new ApplicationException(e);
            }

        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try (InputStream in = getClass().getClassLoader().getResourceAsStream("resource/template.zip");
             BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());) {
            ResponseUtils.setZip(response, "课表模版");
            bos.write(IoUtils.inputStreamConvertBytes(in, -1));
            response.flushBuffer();
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }

    @Override
    public void check(OutputStream outputStream, CheckAO ao) {
        String curriculumTableKey = CurriculumTableExcelListener.CURRICULUMTABLEKEY + ao.getUploadId();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(curriculumTableKey))) {
            Map<String, TempCurriculumTableDTO> cache = redisTemplate.opsForValue().get(curriculumTableKey);
            Map<String, String> curriculumAliasMap = curriculumService.aliasMap(true);
            List<StudentVO> students = foundationFacade.findBasicByStuIds(Collections.emptyList());
            List<TeacherVO> teachers = foundationFacade.getTeachers();
            List<ClassVO> classes = foundationFacade.allClass();
            List<RoomVO> rooms = settingFacade.rooms();
            CurriculumTableCheck check = new CurriculumTableCheck(ao, cache, ao.getWeekType(), students, teachers, classes, rooms, curriculumAliasMap);
            ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(outputStream);
            if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.GENERAL) {
                ExcelWriter excelWriter = excelWriterBuilder.registerWriteHandler(new GeneralMergeStrategy()).build();
                check.checkMainTable();
                Map<String, List<List<String>>> tableMap = check.getCheckMainTable();
                for (String key : tableMap.keySet()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet().build();
                    writeSheet.setSheetName(key);
                    excelWriter.write(tableMap.get(key), writeSheet);
                }
                excelWriter.finish();
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.TEACHER) {
                ExcelWriter excelWriter = excelWriterBuilder.registerWriteHandler(new StudentMergeStrategy()).build();
                check.checkTeacherTable();
                Map<String, List<List<String>>> tableMap = check.getCheckTeacherTable();
                for (String key : tableMap.keySet()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet().build();
                    writeSheet.setSheetName(key);
                    excelWriter.write(tableMap.get(key), writeSheet);
                }
                excelWriter.finish();
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.CLASSES) {
                ExcelWriter excelWriter = excelWriterBuilder.registerWriteHandler(new ClassesMergeStrategy()).build();
                check.checkClassTable();
                Map<String, List<List<String>>> tableMap = check.getCheckClassTable();
                for (String key : tableMap.keySet()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet().build();
                    writeSheet.setSheetName(key);
                    excelWriter.write(tableMap.get(key), writeSheet);
                }
                excelWriter.finish();
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.STUDENT) {
                ExcelWriter excelWriter = excelWriterBuilder.registerWriteHandler(new StudentMergeStrategy()).build();
                check.checkStudentTable();
                Map<String, List<List<String>>> tableMap = check.getCheckStudentTable();
                for (String key : tableMap.keySet()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet().build();
                    writeSheet.setSheetName(key);
                    excelWriter.write(tableMap.get(key), writeSheet);
                }
                excelWriter.finish();
            }
        } else {
            throw new BusinessException("上传课表文件不存在");
        }
    }

    @Override
    public Map<CurriculumTableTypeEnum, Set<String>> checkMessage(CheckAO ao) {
        String curriculumTableKey = CurriculumTableExcelListener.CURRICULUMTABLEKEY + ao.getUploadId();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(curriculumTableKey))) {
            Map<String, TempCurriculumTableDTO> cache = redisTemplate.opsForValue().get(curriculumTableKey);
            Map<String, String> curriculumAliasMap = curriculumService.aliasMap(true);
            List<StudentVO> students = foundationFacade.findBasicByStuIds(Collections.emptyList());
            List<TeacherVO> teachers = foundationFacade.getTeachers();
            List<ClassVO> classes = foundationFacade.allClass();
            List<RoomVO> rooms = settingFacade.rooms();
            CurriculumTableCheck check = new CurriculumTableCheck(ao, cache, ao.getWeekType(), students, teachers,
                    classes, rooms, curriculumAliasMap);
            Map<CurriculumTableTypeEnum, Set<String>> messages = new LinkedHashMap<>();
            if (ao.getCurriculumTableType() == null) {
                messages.put(CurriculumTableTypeEnum.GENERAL, check.checkMainTable());
                messages.put(CurriculumTableTypeEnum.TEACHER, check.checkTeacherTable());
                messages.put(CurriculumTableTypeEnum.CLASSES, check.checkClassTable());
                messages.put(CurriculumTableTypeEnum.STUDENT, check.checkStudentTable());
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.GENERAL) {
                messages.put(CurriculumTableTypeEnum.GENERAL, check.checkMainTable());
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.TEACHER) {
                messages.put(CurriculumTableTypeEnum.TEACHER, check.checkTeacherTable());
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.CLASSES) {
                messages.put(CurriculumTableTypeEnum.CLASSES, check.checkClassTable());
            } else if (ao.getCurriculumTableType() == CurriculumTableTypeEnum.STUDENT) {
                messages.put(CurriculumTableTypeEnum.STUDENT, check.checkStudentTable());
            }
            return messages;
        } else {
            throw new BusinessException("上传课表文件不存在");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void build(PublishedAO ao) {
        SemesterVO semesterVO = checkExists(ao);
        //获取更新前的
        List<GeneralTableDO> pretables = new ArrayList<>();
        if (ao.getCurriculumTableId() != null) {
            pretables = generalTableService.queryByCurriculumTableId(ao.getCurriculumTableId());
        }
        CurriculumTableDO table;
        if (ao.getCurriculumTableId() != null) {
            table = load(ao.getCurriculumTableId());
            if (!table.getUploadId().equals(ao.getUploadId())) {
                copyUploadId(table.getUploadId(), ao.getUploadId(), WeekTypeEnum.SINGLE);
                copyUploadId(table.getUploadId(), ao.getUploadId(), WeekTypeEnum.DOUBLE);
            }
        } else {
            table = new CurriculumTableDO();
            table.setFirstPublished(false);
            table.setSemesterId(semesterVO.getId());
            table.setOrderNo(semesterVO.getOrderNo());
        }
        String curriculumTableKey = CurriculumTableExcelListener.CURRICULUMTABLEKEY + ao.getUploadId();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(curriculumTableKey))) {
            Map<String, TempCurriculumTableDTO> cache = redisTemplate.opsForValue().get(curriculumTableKey);
            List<GeneralTableDO> allSemesterTable = generalTableService.getAllSemesterTable(semesterVO.getId(),
                    semesterVO.getOrderNo(), ao.getCurriculumTableId());
            //编辑前为双周现在改为单周课表
            GeneralTableDO preDoubleGeneralTable = null;
            if (ao.getCurriculumTableId() != null) {
                if (ao.getSingleWeek()) {
                    preDoubleGeneralTable = generalTableService.getGeneralTableDO(ao.getCurriculumTableId(), WeekTypeEnum.DOUBLE);
                }
            }
            GeneralTableDO singleData = dataAnalysis(ao, cache, WeekTypeEnum.SINGLE, allSemesterTable, preDoubleGeneralTable);
            GeneralTableDO doubleData = null;
            if (!ao.getSingleWeek()) {
                doubleData = dataAnalysis(ao, cache, WeekTypeEnum.DOUBLE, allSemesterTable, singleData);
            }
            table.setId(ao.getCurriculumTableId());
            BeanUtils.copyProperties(ao, table);
            table.setPublished(ao.getPublished());
            if (ao.getPublished()) {
                table.setFirstPublished(true);
            }
            save(table);
            if (ao.getPublished()) {
                String key = Constants.getKey(Constant.CACHE_MAIN_TABLE_ID, table.getId());
                if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
                    redisTemplate.delete(key);
                }
                key = Constants.getKey(Constant.CACHE_TEACHER_TABLE_ID, table.getId());
                if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
                    redisTemplate.delete(key);
                }
                key = Constants.getKey(Constant.CACHE_CLASS_TABLE_ID, table.getId());
                if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
                    redisTemplate.delete(key);
                }
                singleData.setCurriculumTableId(table.getId());
                generalTableService.save(singleData);
                if (doubleData != null) {
                    doubleData.setCurriculumTableId(table.getId());
                    generalTableService.save(doubleData);
                } else {
                    //没有双则删除双周数据
                    generalTableService.removeByCurriculumTableIdDouble(table.getId());
                }
                //获取当前最新的课表
                List<GeneralTableDO> curtables = generalTableService.queryByCurriculumTableId(table.getId());
                if (ao.getCurriculumTableId() != null) {
                    courseAdjustmentService.cancelAdjustment(ao.getCurriculumTableId(), UpdateType.UPDATE);
                }
                curriculumTableDiff.start(table, pretables, curtables);
            }
        } else {
            throw new BusinessException("上传课表文件不存在");
        }
    }

    public GeneralTableDO dataAnalysis(CurriculumTableAO ao, Map<String, TempCurriculumTableDTO> cache, WeekTypeEnum weekType,
                                       List<GeneralTableDO> allSemesterTable, GeneralTableDO generalTable) {
        Map<String, String> curriculumAliasMap = curriculumService.aliasMap(true);
        List<StudentVO> students = foundationFacade.findBasicByStuIds(Collections.emptyList());
        List<TeacherVO> teachers = foundationFacade.getTeachers();
        List<ClassVO> classes = foundationFacade.allClass();
        List<RoomVO> rooms = settingFacade.rooms();
        CurriculumTableBuild single = new CurriculumTableBuild(ao, cache, weekType, students, teachers,
                classes, rooms, curriculumAliasMap, generalTable);
        GeneralTableDO data = null;
        if (ao.getCurriculumTableId() != null) {
            data = generalTableService.getGeneralTableDO(ao.getCurriculumTableId(), weekType);
        }
        if (data == null) {
            data = new GeneralTableDO();
            data.setType(weekType);
        }
        single.analysis(data, allSemesterTable);
        data.setCurriculumAlias(curriculumService.aliasMap(false));
        return data;
    }

    @Override
    public List<String> getAuth(Long semesterId, Integer orderNo, Long userId) {
        JwtUser user = UserContextHolder.getUser();
        List<String> auths = new ArrayList<>();
        //任课老师
        CurriculumTablePageAO ao = new CurriculumTablePageAO();
        ao.setSemesterId(semesterId);
        ao.setClassTeacher(false);
        ao.setOnly(false);
        QueryResult<List<CurriculumTableVO>> result = page(ao, null, user.getId());
        if (result != null) {
            if (!CollectionUtils.isEmpty(result.getResult())) {
                auths.add("2");
            }
        }
        if (user.getId() != null) {
            CourseQuery ao1 = new CourseQuery();
            ao1.setSemesterId(semesterId);
            ao1.setTeacherIds(Collections.singletonList(user.getId()));
            List<CourseVO> course = courseFacade.postCourseInfo(ao1);
            if (!CollectionUtils.isEmpty(course)) {
                auths.add("2");
            }
        }
        if (!CollectionUtils.isEmpty(user.getRoles())) {
            //班主任,如果不是当前则去除班主任角色
            SemesterVO semester = settingFacade.currentSemester();
            if (semester.getId().equals(semesterId) && semester.getOrderNo().equals(orderNo)) {
                for (String role : user.getRoles()) {
                    if (role.equals(roleHeadTeacherId)) {
                        auths.add("1");
                    }
                }
            }
            //校领导
            for (String role : user.getRoles()) {
                if (role.equals(schoolLeaderId)) {
                    auths.add("3");
                }
            }
        }

        return auths;
    }

    @Override
    public Map<WeekTypeEnum, List<CurriculumTableTypeEnum>> getUploadTableMap(Long uploadId) {
        Map<WeekTypeEnum, List<CurriculumTableTypeEnum>> wMap = new LinkedHashMap<>();
        String curriculumTableKey = CurriculumTableExcelListener.CURRICULUMTABLEKEY + uploadId;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(curriculumTableKey))) {
            Map<String, TempCurriculumTableDTO> cache = redisTemplate.opsForValue().get(curriculumTableKey);
            wMap.put(WeekTypeEnum.SINGLE, getTableTypeList(cache, WeekTypeEnum.SINGLE));
            wMap.put(WeekTypeEnum.DOUBLE, getTableTypeList(cache, WeekTypeEnum.DOUBLE));
        }
        return wMap;
    }

    public List<CurriculumTableTypeEnum> getTableTypeList(Map<String, TempCurriculumTableDTO> cache, WeekTypeEnum weekType) {
        List<CurriculumTableTypeEnum> types = new ArrayList<>();
        String mainTableKey = CurriculumTableTypeEnum.GENERAL + ":" + weekType;
        if (cache.containsKey(mainTableKey)) {
            types.add(CurriculumTableTypeEnum.GENERAL);
        }
        String teacherKey = CurriculumTableTypeEnum.TEACHER + ":" + weekType;
        if (cache.containsKey(teacherKey)) {
            types.add(CurriculumTableTypeEnum.TEACHER);
        }
        String classesKey = CurriculumTableTypeEnum.CLASSES + ":" + weekType;
        if (cache.containsKey(classesKey)) {
            types.add(CurriculumTableTypeEnum.CLASSES);
        }
        String studentKey = CurriculumTableTypeEnum.STUDENT + ":" + weekType;
        if (cache.containsKey(studentKey)) {
            types.add(CurriculumTableTypeEnum.STUDENT);
        }
        return types;
    }

    public void copyUploadId(Long oldUpadLodId, Long newUploadId, WeekTypeEnum weekType) {
        String oldCurriculumTableKey = CurriculumTableExcelListener.CURRICULUMTABLEKEY + oldUpadLodId;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(oldCurriculumTableKey))) {
            return;
        }
        Map<String, TempCurriculumTableDTO> oldCache = redisTemplate.opsForValue().get(oldCurriculumTableKey);
        if (oldCache == null) {
            return;
        }
        String newCurriculumTableKey = CurriculumTableExcelListener.CURRICULUMTABLEKEY + newUploadId;
        Map<String, TempCurriculumTableDTO> newCache = redisTemplate.opsForValue().get(newCurriculumTableKey);
        if (newCache == null) {
            newCache = new LinkedHashMap<>();
        }
        int i = 0;
        i += copyCache(oldCache, oldUpadLodId, newCache, newUploadId, weekType, CurriculumTableTypeEnum.GENERAL);
        i += copyCache(oldCache, oldUpadLodId, newCache, newUploadId, weekType, CurriculumTableTypeEnum.TEACHER);
        i += copyCache(oldCache, oldUpadLodId, newCache, newUploadId, weekType, CurriculumTableTypeEnum.CLASSES);
        i += copyCache(oldCache, oldUpadLodId, newCache, newUploadId, weekType, CurriculumTableTypeEnum.STUDENT);
        if (i > 0) {
            redisTemplate.opsForValue().set(newCurriculumTableKey, newCache);
        }
    }

    public int copyCache(
            Map<String, TempCurriculumTableDTO> oldCache, Long oldUploadId,
            Map<String, TempCurriculumTableDTO> newCache, Long newUploadId,
            WeekTypeEnum weekType, CurriculumTableTypeEnum type) {
        String tableKey = type + ":" + weekType;
        if (!newCache.containsKey(tableKey)) {
            TempCurriculumTableDTO tmp = oldCache.get(tableKey);
            if (tmp != null) {
                tmp.setUploadId(newUploadId);
                newCache.put(tableKey, tmp);
                //拷贝文件
                String fileNameSource = RuleUtils.getFileName(oldUploadId, type, weekType);
                File sourceFile = new File(uploadFilePath + "/" + fileNameSource);
                String fileNameTarget = RuleUtils.getFileName(newUploadId, type, weekType);
                File targetFile = new File(uploadFilePath + "/" + fileNameTarget);
                IoUtil.copyFile(sourceFile, targetFile);
                return 1;
            }
        }
        return 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int remove(List<Long> ids) {
        int c = super.remove(ids);
        for (Long id : ids) {
            courseAdjustmentService.cancelAdjustment(id, UpdateType.DELETE);
        }
        return c;
    }

}
