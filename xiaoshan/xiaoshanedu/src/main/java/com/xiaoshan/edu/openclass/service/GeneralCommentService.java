package com.xiaoshan.edu.openclass.service;

import com.xiaoshan.edu.openclass.entity.GeneralCommentDO;
import start.framework.service.SqlBaseService;

public interface GeneralCommentService extends SqlBaseService<GeneralCommentDO,Long> {

    /**
     * 更新分数
     * @param openClassId
     * @return
     */
    Double updateScore(Long openClassId);

    /**
     * 根据公开课id删除记录
     * @param id
     */
    void removeByOpenClassId(Long id);

    /**
     * 根据公开课id获取总评价
     * @param openClassId
     * @return
     */
    GeneralCommentDO getByOpenClassId(Long openClassId);
}
