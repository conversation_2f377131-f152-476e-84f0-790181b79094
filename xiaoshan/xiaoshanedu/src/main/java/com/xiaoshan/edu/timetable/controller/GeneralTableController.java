package com.xiaoshan.edu.timetable.controller;


import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.model.RoomClassVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.common.annotation.AuthenticationCheck;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.ao.timetable.ClassesQuery;
import com.xiaoshan.edu.ao.timetable.StudentQuery;
import com.xiaoshan.edu.ao.timetable.StudentListQuery;
import com.xiaoshan.edu.ao.timetable.TeacherQuery;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.timetable.CurriculumTableVO;
import com.xiaoshan.edu.vo.timetable.GeneralTableVO;
import com.xiaoshan.edu.vo.timetable.LessonPrepareVO;
import com.xiaoshan.edu.vo.timetable.StudentDataVO;
import com.xiaoshan.edu.vo.timetable.StudentTableVO;
import com.xiaoshan.edu.vo.timetable.TeachClassesDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherDataVO;
import com.xiaoshan.edu.vo.timetable.TeacherTableVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/general/table")
@Api(tags = "课表")
public class GeneralTableController extends BaseController {

	@Autowired
	private CurriculumTableService curriculumTableService;

	@Autowired
	private GeneralTableService generalTableService;


	////////////////////////////课表
	
	@ApiOperation("总课表")
	@AuthenticationCheck
	@GetMapping("/main/{id}")
	public ResultResponse<List<GeneralTableVO>> mainTable(
			@PathVariable @ApiParam("课表ID") @NotNull @LongValid Long id){
		return response(generalTableService.mainTable(id));
	}
	
	@ApiOperation("班级课表")
	@AuthenticationCheck
	@GetMapping("/classes/{id}")
	public ResultResponse<List<GeneralTableVO>> classesTable(
			@PathVariable @ApiParam("课表ID") @NotNull @LongValid Long id){
		return response(generalTableService.classesTable(id));
	}
	
	@ApiOperation("教师课表")
	@AuthenticationCheck
	@GetMapping("/teacher/{id}")
	public ResultResponse<List<TeacherTableVO>> teacherTable(
			@PathVariable @ApiParam("课表ID") @NotNull @LongValid Long id){
		return response(generalTableService.teacherTable(id));
	}
	
	@ApiOperation("学生课表")
	@AuthenticationCheck
	@GetMapping("/student/{id}")
	public ResultResponse<List<StudentTableVO>> studentTable(
			@PathVariable @ApiParam("课表ID") @NotNull @LongValid Long id){
		return response(generalTableService.studentTable(id));
	}
	
	////////////////////////////详情
	
	@ApiOperation("总表班级或教室详情")
	@AuthenticationCheck
	@PostMapping("/class/room/detail")
	public ResultResponse<Map<WeekTypeEnum,DataTableDTO>> mainTableDetail(@RequestBody ClassesQuery ao){
		return response(generalTableService.mainTableDetail(ao));
	}
	
	@ApiOperation("班级详情")
	@AuthenticationCheck
	@PostMapping("/classes/detail")
	public ResultResponse<Map<WeekTypeEnum,DataTableDTO>> classesTableDetail(@RequestBody ClassesQuery ao){
		return response(generalTableService.classesTableDetail(ao));
	}
	
	@ApiOperation("教师详情")
	@AuthenticationCheck
	@PostMapping("/teacher/detail")
	public ResultResponse<Map<WeekTypeEnum,TeacherTableDTO>> teacherTableDetail(@RequestBody TeacherQuery ao){
		return response(generalTableService.teacherTableDetail(ao));
	}
	
	@ApiOperation("学生详情")
	@AuthenticationCheck
	@PostMapping("/student/detail")
	public ResultResponse<Map<WeekTypeEnum,DataTableDTO>> studentTableDetail(@RequestBody StudentQuery ao){
		return response(generalTableService.studentTableDetail(ao));
	}
	
	@ApiOperation("教室详情(周期)")
	@AuthenticationCheck
	@GetMapping("/class/room/week/detail/cur/{classRoomId}/{week}")
	public ResultResponse<Map<WeekEnum, Map<String,List<TeacherAbbDetailVO>>>> classRoomTableWeekDetail(
			@PathVariable @ApiParam("教室ID")@NotNull @LongValid Long classRoomId,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week){
		return response(generalTableService.classRoomTableWeekDetail(classRoomId,week,true));
	}
	
	@ApiOperation("班级详情(周期)")
	@AuthenticationCheck
	@GetMapping("/classes/week/detail/cur/{classId}/{week}")
	public ResultResponse<Map<WeekEnum, Map<String,List<TeacherAbbDetailVO>>>> classTableWeekDetail(
			@PathVariable @ApiParam("班级ID")@NotNull @LongValid Long classId,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week){
		return response(generalTableService.classTableWeekDetail(classId,week));
	}
	
	@ApiOperation("教师详情(周期)")
	@AuthenticationCheck
	@GetMapping("/teacher/week/detail/cur/{week}/{fulldata}")
	public ResultResponse<Map<WeekEnum, Map<String,List<TeacherAbbDetailVO>>>> teacherTableWeekDetail1(
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week,
			@PathVariable @ApiParam("所有数据") @NotNull @BooleanValid Boolean fulldata){
		JwtUser user=UserContextHolder.getUser();
		return response(generalTableService.teacherTableWeekDetail(user.getId(),week));
	}
	
	@ApiOperation("教师详情(周期)")
	@AuthenticationCheck
	@GetMapping("/teacher/week/detail/cur/{teacherId}/{week}/{fulldata}")
	public ResultResponse<Map<WeekEnum, Map<String,List<TeacherAbbDetailVO>>>> teacherTableWeekDetail3(
			@PathVariable @ApiParam("教师ID")@NotNull @LongValid Long teacherId,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week,
			@PathVariable @ApiParam("所有数据") @NotNull @BooleanValid Boolean fulldata){
		return response(generalTableService.teacherTableWeekDetail(teacherId,week));
	}
	
	@ApiOperation("教师详情(周期)-按学期")
	@AuthenticationCheck
	@GetMapping("/teacher/week/detail/cur/{semesterId}/{teacherId}/{week}/{fulldata}")
	public ResultResponse<Map<WeekEnum, Map<String,List<TeacherAbbDetailVO>>>> teacherTableWeekDetail2(
			@PathVariable @ApiParam("学期ID")@NotNull @LongValid Long semesterId,
			@PathVariable @ApiParam("教师ID")@NotNull @LongValid Long teacherId,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week,
			@PathVariable @ApiParam("所有数据") @NotNull @BooleanValid Boolean fulldata){
		return response(generalTableService.teacherTableWeekDetail(semesterId,teacherId,week));
	}
	
	@ApiOperation("教师详情-按学期")
	@AuthenticationCheck
	@GetMapping("/teacher/detail/cur/{semesterId}/{teacherId}/{fulldata}")
	public ResultResponse<Map<WeekTypeEnum,Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetail3(
			@PathVariable @ApiParam("学期ID")@NotNull @LongValid Long semesterId,
			@PathVariable @ApiParam("教师ID")@NotNull @LongValid Long teacherId,
			@PathVariable @ApiParam("所有数据") @NotNull @BooleanValid Boolean fulldata){
		return response(generalTableService.teacherTableSemesterDetail(semesterId,teacherId));
	}

	@ApiOperation("授课信息（我的档案）")
	@AuthenticationCheck
	@GetMapping("/{semesterId}/mine")
	public ResultResponse<Map<WeekTypeEnum,Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetail3(
			@PathVariable @ApiParam("学期ID")@NotNull @LongValid Long semesterId){
		return response(generalTableService.teacherTableSemesterDetail(semesterId, UserContextHolder.getUser().getId()));
	}
	
	@ApiOperation("教师学期对应的课程组")
	@AuthenticationCheck
	@GetMapping("/teacher/detail/cur/abbs/{semesterId}/{teacherId}")
	public ResultResponse<String> teacherTableWeekDetail3(
			@PathVariable @ApiParam("学期ID")@NotNull @LongValid Long semesterId,
			@PathVariable @ApiParam("教师ID")@NotNull @LongValid Long teacherId){
		return response(generalTableService.teacherTableSemesterDetailAbbs(semesterId,teacherId));
	}

	
	@ApiOperation("学生课表ID")
	@AuthenticationCheck
	@GetMapping("/student/cur/tableid/{stuId}")
	public ResultResponse<List<CurriculumTableVO>> curStudentTableId(
			@PathVariable @ApiParam("学生ID")@NotNull @LongValid Long stuId){
		return response(curriculumTableService.getCurrentCurriculums(stuId,null));
	}
	
	@ApiOperation("学生详情(周期)")
	@AuthenticationCheck
	@GetMapping("/student/week/detail/cur/{stuId}/{week}/{fulldata}")
	public ResultResponse<Map<WeekEnum, Map<String,List<TeacherAbbDetailVO>>>> studentTableWeekDetail(
			@PathVariable @ApiParam("学生ID")@NotNull @LongValid Long stuId,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week,
			@PathVariable @ApiParam("所有数据") @NotNull @BooleanValid Boolean fulldata){
		return response(generalTableService.studentTableWeekDetail(stuId,week));
	}

	
	@ApiOperation("学生列表-按所在班级")
	@AuthenticationCheck
	@PostMapping("/student/list")
	public ResultResponse<Map<WeekTypeEnum,List<TeachClassesDetailVO>>> studentListClass(@RequestBody StudentListQuery ao){
		return response(generalTableService.studentListByClassId(ao));
	}
	
	@ApiOperation("学生列表-按上课教室")
	@AuthenticationCheck
	@PostMapping("/student/list/room")
	public ResultResponse<Map<WeekTypeEnum,List<TeachClassesDetailVO>>> studentListRoom(@RequestBody StudentListQuery ao){
		return response(generalTableService.studentListByClassOrRoomId(ao));
	}

	
	@ApiOperation("教室列表")
	@AuthenticationCheck
	@GetMapping("/room/tree/{id}")
	public ResultResponse<List<RoomClassVO>>  getAllRoomClass(
			@PathVariable @ApiParam("课表ID") @NotNull @LongValid Long id){
		return response(generalTableService.getAllRoomClass(id));
	}
	
	@ApiOperation("备课组")
	@AuthenticationCheck
	@GetMapping("/lesson/prepare/{semesterId}/{orderNo}")
	public ResultResponse<List<LessonPrepareVO>>  getLessonPrepare(
			@PathVariable @ApiParam("学期ID") @NotNull @LongValid Long semesterId,
			@PathVariable @ApiParam("学期") @NotNull @IntegerValid Integer orderNo){
		return response(generalTableService.getLessonPrepare(semesterId,orderNo));
	}
	
	@ApiOperation("工作量")
	@AuthenticationCheck
	@GetMapping("/workload/{semesterId}/{teacherId}/{weekNumber}")
	public ResultResponse<Long>  workload(
			@PathVariable @ApiParam("学期ID") @NotNull @LongValid Long semesterId,
			@PathVariable @ApiParam("教师ID") @NotNull @LongValid Long teacherId,
			@PathVariable @ApiParam("周次") @NotNull @LongValid Integer weekNumber){
		return response(generalTableService.workload(semesterId,teacherId,weekNumber));
	}
	
	@ApiOperation("老师教学数据")
	@AuthenticationCheck
	@GetMapping("/teacher/data/{semesterId}")
	public ResultResponse<List<TeacherDataVO>>  teacherData(
			@PathVariable @ApiParam("学期ID") @NotNull @LongValid Long semesterId){
		return response(generalTableService.teacherData(semesterId));
	}
	
	@ApiOperation("学生走班数据")
	@AuthenticationCheck
	@GetMapping("/student/data/{semesterId}")
	public ResultResponse<List<StudentDataVO>>  studentData(
			@PathVariable @ApiParam("学期ID") @NotNull @LongValid Long semesterId){
		return response(generalTableService.studentData(semesterId));
	}
	
}
