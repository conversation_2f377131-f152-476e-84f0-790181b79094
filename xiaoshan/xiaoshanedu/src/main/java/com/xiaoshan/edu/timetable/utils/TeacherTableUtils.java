package com.xiaoshan.edu.timetable.utils;

import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class TeacherTableUtils {

    /**
     * 同一老师是否教不同课程
     */
    public static Boolean checkTeacherIdDiffAbb(Map<String, List<TeacherTableDTO>> teacherTable, Long teacherId, String abbreviation) {
        for (String abb : teacherTable.keySet()) {
            List<TeacherTableDTO> teacherList = teacherTable.get(abb);
            if (teacherList != null) {
                for (TeacherTableDTO dt : teacherList) {
                    if (dt.getTeacherId().equals(teacherId)) {
                        if (!abb.equals(abbreviation)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Boolean checkTeacherIdAbbreviation(Map<String, List<TeacherTableDTO>> teacherTable, Long teacherId, String abbreviation) {
        List<TeacherTableDTO> teacherList = teacherTable.get(abbreviation);
        if (teacherList != null) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getTeacherId().equals(teacherId)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static Boolean checkTeacherIdAbbreviationAtClassOrRoomId(Map<String, List<TeacherTableDTO>> teacherTable, Long teacherId, String abbreviation, String classOrRoomId) {
        List<TeacherTableDTO> data = teacherTable.get(abbreviation);
        if (data != null) {
            for (TeacherTableDTO dt : data) {
                if (dt.getTeacherId().equals(teacherId)) {
                    for (WeekEnum week : dt.getDetail().keySet()) {
                        Map<String, String> detail = dt.getDetail().get(week);
                        for (String section : detail.keySet()) {
                            if (classOrRoomId.equals(detail.get(section))) {
                                return true;
                            }
                        }

                    }
                }
            }
        }
        return false;
    }

    public static Boolean checkTeacherIdClassOrRoomIdWeekSection(Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, String classOrRoomId, Long teacherId) {
        for (List<TeacherTableDTO> teacherList : teacherTable.values()) {
            for (TeacherTableDTO dt : teacherList) {
                Map<String, String> detailMap = dt.getDetail().get(week);
                if (detailMap != null) {
                    if (classOrRoomId.equals(detailMap.get(section))) {
                        if (dt.getTeacherId().equals(teacherId)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 教师列表中是否存在除自己外同周节次及上课地位的任课老师
     */
    public static Boolean checkEquals(Long teacherId, Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, String classOrRoomId) {
        for (List<TeacherTableDTO> teacherList : teacherTable.values()) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getTeacherId().equals(teacherId)) {
                    //跳过自己
                    continue;
                }
                if (dt.getDetail() != null) {
                    Map<String, String> detailMap = dt.getDetail().get(week);
                    if (detailMap != null) {
                        if (classOrRoomId.equals(detailMap.get(section))) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Set<String> getCheckEqualsTeacherName(Long teacherId, Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, String classOrRoomId) {
        Set<String> teacherNames = new HashSet<>();
        for (List<TeacherTableDTO> teacherList : teacherTable.values()) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getTeacherId().equals(teacherId)) {
                    //跳过自己
                    continue;
                }
                if (dt.getDetail() != null) {
                    Map<String, String> detailMap = dt.getDetail().get(week);
                    if (detailMap != null) {
                        if (classOrRoomId.equals(detailMap.get(section))) {
                            teacherNames.add(dt.getTeacherName());
                        }
                    }
                }
            }
        }
        return teacherNames;
    }

    public static Boolean checkWeekSectionTeacherId(Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, Long teacherId) {
        for (String abb : teacherTable.keySet()) {
            List<TeacherTableDTO> teacherList = teacherTable.get(abb);
            if (teacherList != null) {
                for (TeacherTableDTO dt : teacherList) {
                    if (teacherId.equals(dt.getTeacherId())) {
                        Map<String, String> detailMap = dt.getDetail().get(week);
                        if (detailMap != null) {
                            if (detailMap.containsKey(section)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Boolean checkWeekSectionClassOrRoomeId(Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, String classOrRoomeId) {
        for (String abb : teacherTable.keySet()) {
            List<TeacherTableDTO> teacherList = teacherTable.get(abb);
            if (teacherList != null) {
                for (TeacherTableDTO dt : teacherList) {
                    Map<String, String> detailMap = dt.getDetail().get(week);
                    if (detailMap != null) {
                        String corId = detailMap.get(section);
                        if (classOrRoomeId.equals(corId)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }


    public static Boolean check(Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, String abbreviation) {
        List<TeacherTableDTO> teacherList = teacherTable.get(abbreviation);
        if (teacherList != null) {
            for (TeacherTableDTO dt : teacherList) {
                Map<String, String> detailMap = dt.getDetail().get(week);
                if (detailMap != null) {
                    if (detailMap.containsKey(section)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static TeacherTableDTO getTeacherTableDTO(Map<String, List<TeacherTableDTO>> data, WeekEnum week, String section, String abbreviation, Long teacherId) {
        List<TeacherTableDTO> teacherList = data.get(abbreviation);
        if (teacherList != null) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getTeacherId().equals(teacherId)) {
                    Map<String, String> detailMap = dt.getDetail().get(week);
                    if (detailMap != null) {
                        if (detailMap.get(section) != null) {
                            return dt;
                        }
                    }
                }
            }
        }
        return null;
    }

    public static Boolean check(Map<String, List<TeacherTableDTO>> data, WeekEnum week, String section, String abbreviation, Long teacherId, String classOrRoomId) {
        List<TeacherTableDTO> teacherList = data.get(abbreviation);
        if (teacherList != null) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getTeacherId().equals(teacherId)) {
                    if (dt.getDetail() != null) {
                        Map<String, String> detailMap = dt.getDetail().get(week);
                        if (detailMap != null) {
                            if (classOrRoomId.equals(detailMap.get(section))) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Boolean check(Map<String, List<TeacherTableDTO>> data, WeekEnum week, String section, String abbreviation, Long teacherId) {
        List<TeacherTableDTO> teacherList = data.get(abbreviation);
        if (teacherList != null) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getTeacherId().equals(teacherId)) {
                    if (dt.getDetail() != null) {
                        Map<String, String> detailMap = dt.getDetail().get(week);
                        if (detailMap != null) {
                            if (detailMap.get(section) != null) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Boolean check(Map<String, List<TeacherTableDTO>> data, WeekEnum week, String section, String abbreviation, String classOrRoomId) {
        List<TeacherTableDTO> teacherList = data.get(abbreviation);
        if (teacherList != null) {
            for (TeacherTableDTO dt : teacherList) {
                if (dt.getDetail() != null) {
                    Map<String, String> detailMap = dt.getDetail().get(week);
                    if (detailMap != null) {
                        if (classOrRoomId.equals(detailMap.get(section))) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public static TeacherTableDTO getTeacherTable(Map<String, List<TeacherTableDTO>> teacherTable, String abbreviation, Long teacherId) {
        List<TeacherTableDTO> data = teacherTable.get(abbreviation);
        if (data != null) {
            for (TeacherTableDTO dt : data) {
                if (dt.getTeacherId().equals(teacherId)) {
                    return dt;
                }
            }
        }
        return null;
    }


    /**
     * 获取教师课表
     *
     * @param teacherTable
     * @param dayOfWeek
     * @param section
     * @param courseNameAbbreviation
     * @param classOrRoomId
     * @return
     */
    public static TeacherTableDTO getTeacherTable(Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum dayOfWeek, String section, String courseNameAbbreviation,
                                                  String classOrRoomId) {
        List<TeacherTableDTO> teacherTableDTOList = teacherTable.get(courseNameAbbreviation);
        if (teacherTableDTOList != null) {
            for (TeacherTableDTO teacherTableDTO : teacherTableDTOList) {
                Map<String, String> sectionClassMap = teacherTableDTO.getDetail().get(dayOfWeek);
                if (sectionClassMap != null) {
                    if (classOrRoomId.equals(sectionClassMap.get(section))) {
                        return teacherTableDTO;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取老师对应的上课班级
     */
    public static String getTeacherClassOrRoomId(Map<String, List<TeacherTableDTO>> teacherTable, WeekEnum week, String section, String abbreviation, Long teacherId) {
        List<TeacherTableDTO> list = teacherTable.get(abbreviation);
        if (list != null) {
            for (TeacherTableDTO dto : list) {
                if (dto.getTeacherId().equals(teacherId)) {
                    Map<String, String> detail = dto.getDetail().get(week);
                    if (detail != null) {
                        return detail.get(section);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取老师对应的课程
     */
    public static String getTeacherAbbreviation(Map<String, List<TeacherTableDTO>> teacherTable, Long teacherId) {
        for (String abb : teacherTable.keySet()) {
            for (TeacherTableDTO dto : teacherTable.get(abb)) {
                if (dto.getTeacherId().equals(teacherId)) {
                    return abb;
                }
            }
        }
        return null;
    }

}
