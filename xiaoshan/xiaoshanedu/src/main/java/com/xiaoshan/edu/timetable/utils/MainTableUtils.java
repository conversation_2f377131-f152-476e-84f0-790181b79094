package com.xiaoshan.edu.timetable.utils;

import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.WeekSectionDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;

/**
 * <AUTHOR>
 */
public class MainTableUtils {

    public static Boolean checkAbb(List<DataTableDTO> mainTable, String abbreviation) {
        for (DataTableDTO dto : mainTable) {
            for (Map<String, String> detail : dto.getDetail().values()) {
                if (detail.containsValue(abbreviation)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static Boolean checkClassOrRoomId(List<DataTableDTO> mainTable, String classOrRoomId) {
        if (!ClassTableUtils.isClass(classOrRoomId)) {
            return false;
        }
        return getDataTableDTO(mainTable, classOrRoomId) != null;
    }

    public static Boolean checkClassOrRoomIdWeekSectionAbb(List<DataTableDTO> mainTable, String classOrRoomId, WeekEnum week, String section, String abb) {
        for (DataTableDTO dto : mainTable) {
            if (dto.getClassOrRoomId().equals(classOrRoomId)) {
                if (dto.getDetail() != null) {
                    Map<String, String> detailMap = dto.getDetail().get(week);
                    if (detailMap != null) {
                        if (abb.equals(detailMap.get(section))) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public static DataTableDTO getDataTableDTO(List<DataTableDTO> mainTable, String classOrRoomId) {
        for (DataTableDTO dt : mainTable) {
            if (dt.getClassOrRoomId().equals(classOrRoomId)) {
                return dt;
            }
        }
        return null;
    }

    public static String getCourseNameClassOrRoomIdWeekSection(List<DataTableDTO> mainTable, String classOrRoomId, WeekEnum week, String section) {
        for (DataTableDTO dto : mainTable) {
            if (dto.getClassOrRoomId().equals(classOrRoomId)) {
                if (dto.getDetail() != null) {
                    Map<String, String> detailMap = dto.getDetail().get(week);
                    if (detailMap != null) {
                        return detailMap.get(section);
                    }
                }
            }
        }
        return null;
    }

    public static WeekSectionDTO getWeekSectionFirst(List<DataTableDTO> mainTable, String classOrRoomId, String abbreviation) {
        for (DataTableDTO dto : mainTable) {
            if (dto.getClassOrRoomId().equals(classOrRoomId)) {
                for (WeekEnum week : dto.getDetail().keySet()) {
                    Map<String, String> detailMap = dto.getDetail().get(week);
                    for (String section : detailMap.keySet()) {
                        if (abbreviation.equals(detailMap.get(section))) {
                            WeekSectionDTO weekSectionDTO = new WeekSectionDTO();
                            weekSectionDTO.setWeek(week);
                            weekSectionDTO.setSection(section);
                            return weekSectionDTO;
                        }
                    }
                }
            }
        }
        return null;
    }

}
