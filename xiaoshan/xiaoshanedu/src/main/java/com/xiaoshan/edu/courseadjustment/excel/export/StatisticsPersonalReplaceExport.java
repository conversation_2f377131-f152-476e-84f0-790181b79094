package com.xiaoshan.edu.courseadjustment.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class StatisticsPersonalReplaceExport {
    @ExcelProperty(value = "审批编号", index = 0)
    private String approvalNo;

    @ExcelProperty(value = "课程名称", index = 1)
    private String courseName;

    @ExcelProperty(value = "代课时间", index = 2)
    @ColumnWidth(40)
    private String replaceTime;

    @ExcelProperty(value = "代课节次", index = 3)
    private String section;

    @ExcelProperty(value = "课程老师", index = 4)
    private String teacherName;

    @ExcelProperty(value = "代课老师", index = 5)
    private String replaceTeacherName;

    @ExcelProperty(value = "申请时间", index = 6)
    @ColumnWidth(24)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;
}
