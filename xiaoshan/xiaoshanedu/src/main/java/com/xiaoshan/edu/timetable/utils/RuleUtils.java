package com.xiaoshan.edu.timetable.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.ao.timetable.CurriculumTableAO;
import com.xiaoshan.edu.dto.TempCurriculumTableDTO;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import start.framework.commons.exception.BusinessException;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class RuleUtils {

    public static String separate(Object... values) {
        List<String> args = new ArrayList<>();
        for (Object a : values) {
            if (a == null) {
                args.add("");
            } else {
                args.add(String.valueOf(a));
            }
        }
        return StringUtils.listToString(args, Constant.SPLITDATASTRING);
    }

    public static int getWeekIndex(String week) {
        return getWeekIndex(week, true);
    }

    public static int getWeekIndex(String week, Boolean isWeek) {
        if (isWeek) {
            switch (week) {
                case "周一":
                    return 0;
                case "周二":
                    return 1;
                case "周三":
                    return 2;
                case "周四":
                    return 3;
                case "周五":
                    return 4;
                case "周六":
                    return 5;
                case "周日":
                    return 6;
                default:
                    return -1;
            }
        } else {
            switch (week) {
                case "一":
                    return 0;
                case "二":
                    return 1;
                case "三":
                    return 2;
                case "四":
                    return 3;
                case "五":
                    return 4;
                case "六":
                    return 5;
                case "日":
                    return 6;
                default:
                    return -1;
            }
        }
    }

    public static int getSectionIndex(String section) {
        switch (section) {
            case "一":
                return 0;
            case "二":
                return 1;
            case "三":
                return 2;
            case "四":
                return 3;
            case "五":
                return 4;
            case "六":
                return 5;
            case "七":
                return 6;
            case "八":
                return 7;
            case "九":
                return 8;
            default:
                return -1;
        }
    }

    public static String getSectionIndexKey(String section) {
        switch (section) {
            case "一":
                return "0-第1节-0";
            case "二":
                return "0-第2节-0";
            case "三":
                return "0-第3节-0";
            case "四":
                return "0-第4节-0";
            case "五":
                return "0-第5节-0";
            case "六":
                return "1-第1节-0";
            case "七":
                return "1-第2节-0";
            case "八":
                return "1-第3节-0";
            case "九":
                return "1-第4节-0";
            default:
                return "";
        }
    }

    public static String getSection(Integer sectionT, String section) {
        String s1 = "第1节";
        String s2 = "第2节";
        String s3 = "第3节";
        String s4 = "第4节";
        String s5 = "第5节";
        if (sectionT == 0) {
            if (s1.equals(section)) {
                return "一";
            } else if (s2.equals(section)) {
                return "二";
            } else if (s3.equals(section)) {
                return "三";
            } else if (s4.equals(section)) {
                return "四";
            } else if (s5.equals(section)) {
                return "五";
            }
        } else if (sectionT == 1) {
            if (s1.equals(section)) {
                return "六";
            } else if (s2.equals(section)) {
                return "七";
            } else if (s3.equals(section)) {
                return "八";
            } else if (s4.equals(section)) {
                return "九";
            }
        }
        return null;
    }


    /////////////////////////////

    public static String messageFormat(String message, Object... args) {
        return String.format(message, args);
    }

    public static void throwMessage(String message, Object... args) {
        throw new BusinessException(messageFormat(message, args));
    }

    public static void getMessage(CurriculumTableAO ao, TempCurriculumTableDTO source, String msg, Object... args) {
        String message;
        if (ao.getSingleWeek()) {
            message = source.getType().getDescription() + " " + String.format(msg, args);
        } else {
            message = WeekTypeEnum.getWeekTypeName(source.getWeekType()) + " " + source.getType().getDescription() + " " + String.format(msg, args);
        }
        throw new BusinessException(message);
    }

    /**
     * 是否是需要忽略的课程名称
     *
     * @param courseAbbreviation
     * @return
     */
    public static Boolean ignoreCourse(String courseAbbreviation) {
        List<String> courses = new ArrayList<>();
        courses.add("班会");
        courses.add("课外活动");
        courses.add("自修");
        courses.add("会");
        courses.add("课");
        courses.add("自");
        return courses.contains(courseAbbreviation);
    }

    public static String getCourseName(String abb, Map<String, String> curriculumAliasMap) {
        for (String courseName : curriculumAliasMap.keySet()) {
            if (curriculumAliasMap.get(courseName).equals(abb)) {
                return courseName;
            }
        }
        return null;
    }

    public static String getFileName(Long uploadId, CurriculumTableTypeEnum type, WeekTypeEnum weekType) {
        return uploadId + "_" + type.name() + "_" + weekType.name();
    }

}
