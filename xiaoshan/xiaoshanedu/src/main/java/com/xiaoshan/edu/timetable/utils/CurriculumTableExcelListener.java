package com.xiaoshan.edu.timetable.utils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.data.redis.core.RedisTemplate;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.xiaoshan.edu.dto.TempCurriculumTableDTO;

/**
 * <AUTHOR>
 */
public class CurriculumTableExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    public static String CURRICULUMTABLEKEY = "CURRICULUMTABLEKEY:";

    private TempCurriculumTableDTO temp;
    private List<Map<Integer, String>> datas;

    private RedisTemplate<String, Map<String, TempCurriculumTableDTO>> redisTemplate;

    public CurriculumTableExcelListener(TempCurriculumTableDTO dto, RedisTemplate<String, Map<String, TempCurriculumTableDTO>> redisTemplate) {
        this.temp = dto;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        this.datas = new ArrayList<>();
        invoke(headMap, context);
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        datas.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (datas != null) {
            this.temp.getData().put(context.readSheetHolder().getSheetName(), datas);
            String curriculumTableKey = CURRICULUMTABLEKEY + this.temp.getUploadId();
            Map<String, TempCurriculumTableDTO> cache;
            if (redisTemplate.hasKey(curriculumTableKey)) {
                cache = redisTemplate.opsForValue().get(curriculumTableKey);
            } else {
                cache = new LinkedHashMap<>();
            }
            String key = this.temp.getType().name() + ":" + this.temp.getWeekType().name();
            cache.put(key, this.temp);
            redisTemplate.opsForValue().set(curriculumTableKey, cache);
            datas = null;
        }
    }

}
