package com.xiaoshan.edu.timetable.mapper;


import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface TableCopyConvert {

    TableCopyConvert INSTANCE = Mappers.getMapper(TableCopyConvert.class);

    /**
     * 课表拷贝
     *
     * @param source
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> copy(Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> source);

    /**
     * 课表拷贝
     * @param source
     * @return
     */
    Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> weekCopy(Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> source);

    /**
     * 拷贝
     * @param teacherAbbDetailVos
     * @return
     */
    List<TeacherAbbDetailVO> listCopy(List<TeacherAbbDetailVO> teacherAbbDetailVos);



}
