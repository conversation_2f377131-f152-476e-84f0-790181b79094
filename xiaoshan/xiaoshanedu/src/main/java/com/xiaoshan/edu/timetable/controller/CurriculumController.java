package com.xiaoshan.edu.timetable.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.ao.timetable.CurriculumAO;
import com.xiaoshan.edu.ao.timetable.CurriculumPageAO;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.vo.timetable.CurriculumVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/curriculum")
@Api(tags = "课程管理")
public class CurriculumController extends BaseController {

	@Autowired
	private CurriculumService curriculumService;
	
	@ApiOperation("保存或更新-管理员")
	@AuthenticationCheck
	@PostMapping
	public BaseResponse saveForUpdate(@RequestBody CurriculumAO ao) {
		curriculumService.saveForUpdate(ao);
		return response();
	}
	
	@ApiOperation("批量删除-管理员")
	@AuthenticationCheck
	@DeleteMapping
	public BaseResponse remove(@RequestBody List<Long> ao) {
		curriculumService.remove(ao);
		return response();
	}
	
	@ApiOperation("分页查询-管理员")
	@AuthenticationCheck
	@PostMapping("/page")
	public PageResponse<List<CurriculumVO>> page(@RequestBody CurriculumPageAO ao) {
		return response(curriculumService.page(ao));
	}
	
	@ApiOperation("同步-管理员")
	@AuthenticationCheck
	@PostMapping("/sync")
	public BaseResponse sync() {
		curriculumService.sync();
		return response();
	}
	
}
