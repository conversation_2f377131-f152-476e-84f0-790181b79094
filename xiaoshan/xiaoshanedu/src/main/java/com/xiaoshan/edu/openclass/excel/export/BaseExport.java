package com.xiaoshan.edu.openclass.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter@Setter@ToString
public class BaseExport {

    @ExcelProperty(value = "课题",index = 0)
    private String courseName;

    @ExcelProperty(value = "科目", index = 1)
    private String subject;

    @ExcelProperty(value = "教师", index = 2)
    private String teacherName;

    @ExcelProperty(value = "公开课类型", index = 3)
    private String type;

    @ColumnWidth(24)
    @ExcelProperty(value = "上课日期", index = 4)
    private String date;

    @ExcelProperty(value = "节次", index = 5)
    private String section;

    @ExcelProperty(value = "上课班级", index = 6)
    private String attendClass;

    @ExcelProperty(value = "上课地点", index = 7)
    private String place;
}
