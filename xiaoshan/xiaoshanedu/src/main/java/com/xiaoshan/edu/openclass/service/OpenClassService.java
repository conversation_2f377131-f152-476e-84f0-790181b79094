package com.xiaoshan.edu.openclass.service;

import com.xiaoshan.basic.vo.FlowNodesListVO;
import com.xiaoshan.edu.ao.openclass.*;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import com.xiaoshan.edu.openclass.entity.OpenClassDO;
import com.xiaoshan.edu.vo.openclass.*;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface OpenClassService extends SqlBaseService<OpenClassDO,Long> {

    /**
     * 发布公开课
     * @param ao
     */
    void publish(OpenClassPublishAO ao);

    /**
     * 管理员端-公开课记录
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> page(OpenClassPageAO ao);

    /**
     * 个人端-公开课广场、听课记录
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> square(OpenClassSquarePageAO ao);

    /**
     * 上课记录
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> launchRecord(OpenClassPageAO ao);

    /**
     * 根据id删除公开课记录
     * @param id
     */
    void removeOpenClass(Long id);

    /**
     * 数据导出
     * @param ao
     * @param response
     */
    void export(OpenClassPageAO ao, HttpServletResponse response);

    /**
     * 根据id确认开课
     * @param id
     */
    void confirm(Long id);

    /**
     * 根据id发布公开课
     * @param id
     */
    void publishById(Long id);

    /**
     * 公开课数量统计
     * @param ao
     * @return
     */
    OpenClassStatisticsVO statistics(OpenClassTimeAO ao);

    /**
     * 公开课统计--教师
     * @param ao
     * @return
     */
    QueryResult<List<TeacherStatisticsVO>> statisticsTeachers(OpenClassTeacherNameAO ao);

    /**
     * 公开课统计--科目
     * @param ao
     * @return
     */
    QueryResult<List<SubjectStatisticsVO>> statisticsSubjects(OpenClassTimePageAO ao);

    /**
     * 我要听课
     * @param openClassId
     */
    void wantListen(Long openClassId);

    /**
     * 取消听课
     * @param openClassId
     */
    void cancelListen(Long openClassId);

    /**
     * 个人端--我的评价
     * @param openClassId
     * @return
     */
    PersonalCommentVO getComment(Long openClassId);

    /**
     * 个人端--提交评价
     * @param ao
     */
    void submitComment(OpenClassCommentAO ao);

    /**
     * 个人端--取消new标签
     * @param openClassId
     */
    void cancelNew(Long openClassId);

    /**
     * 评价列表
     * @param openClassId
     * @return
     */
    List<PersonalCommentVO> commentList(Long openClassId);

    /**
     * 公开课总评价
     * @param openClassId
     * @return
     */
    GeneralCommentVO generalComment(Long openClassId);

    /**
     * 待确认、待发布数量
     * @param ao
     * @return
     */
    ConfirmAndPublishNumVO redNum(OpenClassTimeAO ao);

    /**
     * 公开课统计--教师--数据导出
     * @param ao
     * @param response
     */
    void exportStatisticsTeachers(StatisticsTeacherAO ao, HttpServletResponse response);

    /**
     * 公开课统计--科目--数据导出
     * @param ao
     * @param response
     */
    void exportStatisticsSubject(OpenClassSubjectAO ao, HttpServletResponse response);

    /**
     * 公开课统计--详情--数据导出
     * @param ao
     * @param response
     */
    void exportStatistics(StatisticsDetailAO ao, HttpServletResponse response);

    /**
     * 公开课审批
     * @param approve
     */
    void approve(OpenClassApproveAO approve);

    /**
     * 编辑后保存
     * @param ao
     */
    void edit(OpenClassPublishAO ao);

    /**
     * 公开课统计--教师、科目--详情
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> statisticsTeachersDetails(StaticTeachersDetailAO ao);

    /**
     * 根据id查看公开课详情
     * @param openClassId
     * @return
     */
    OpenClassVO getById(Long openClassId);

    /**
     * 听课教师列表
     * @param openClassId
     * @return
     */
    List<TeacherInfoVO> listenList(Long openClassId);

    /**
     * 公开课开课证明
     * @param openClassId
     * @param response
     */
    void pdf(Long openClassId, HttpServletResponse response);

    /**
     * 模拟获取节点
     * @param ao
     * @return
     */
    FlowNodesListVO simulation(OpenClassSimulationAO ao);

    /**
     * 催办
     * @param ao
     */
    void urging(UrgeAO ao);

    /**
     * 撤销
     * @param ao
     */
    void cancel(CancelAO ao);

    /**
     * 科目列表
     * @return
     */
    List<String> subjectList();

    /**
     * 根据房间类型获取教室树形结构
     * @param types
     * @return
     */
    List<ClassBuildingTreeDTO> getClassRoom(List<Integer> types);

    /**
     * 小程序--公开课记录
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> wxPage(OpeWxPageAO ao);

    /**
     * 电子档案--公开课列表
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> archives(OpeArchivesAO ao);

    /**
     * 电子档案--查询老师的上课记录和听课记录
     * @param ao
     * @return
     */
    QueryResult<List<OpenClassVO>> archivesAll(OpeArchivesAO ao);

    /**
     * 查询老师的上课记录-不分页
     * @param ao
     * @return
     */
    List<OpenClassVO> classRecord(ClassRecordAO ao);
}
