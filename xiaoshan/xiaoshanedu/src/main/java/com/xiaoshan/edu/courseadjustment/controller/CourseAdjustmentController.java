package com.xiaoshan.edu.courseadjustment.controller;



import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.xiaoshan.edu.ao.courseadjustment.*;
import com.xiaoshan.edu.dto.DeleteRelatedDataDTO;
import com.xiaoshan.edu.model.ao.CourseAdjustmentApplyAO;
import com.xiaoshan.edu.vo.courseadjustment.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.topnetwork.ao.IDAO;
import com.xiaoshan.basic.vo.FlowNodesListVO;
import com.xiaoshan.basic.vo.FlowVO;
import com.xiaoshan.edu.courseadjustment.service.CourseAdjustmentService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/courseAdjustment")
@Api(tags = "调代课接口")
@Slf4j
public class CourseAdjustmentController extends BaseController {
	@Autowired
	private CourseAdjustmentService courseAdjustmentService;

	@ApiOperation(value = "调休更新回调调代课")
	@AuthenticationCheck
	@Deprecated
	@PostMapping("/sabbaticalCancelAdjust")
	public BaseResponse sabbaticalCancelAdjust(@RequestBody SabbaticalCancelAdjustAO ao) {
		log.info("调休更新回调调代课入参：" + JSON.toJSONString(ao));
		courseAdjustmentService.sabbaticalCancelAdjust(ao);
		return response();
	}

	@ApiOperation(value = "节假日/调休更新回调调代课")
	@AuthenticationCheck
	@PostMapping("/schoolCalendarCancelAdjust")
	public BaseResponse schoolCalendarCancelAdjust(@RequestBody DateListAO ao) {
		log.info("节假日/调休更新回调调代课入参：" + JSON.toJSONString(ao));
		courseAdjustmentService.schoolCalendarCancelAdjust(ao);
		return response();
	}

	@ApiOperation(value = "（二）查询将要删除的调课/代课涉及的数据【不包括本身】")
	@AuthenticationCheck
	@GetMapping("/getInterrelatedId")
	public ResultResponse<DeleteRelatedDataDTO> getInterrelatedId(
			@RequestParam
			@ApiParam("id")
			@NotNull
			@LongValid Long id,
			@RequestParam
			@ApiParam("数据类型：1：调课，2：代课")
			@IntegerValid(min = 1, max = 2)
			@NotNull Integer dataType) {
		return response(courseAdjustmentService.getInterrelatedId(id, dataType));
	}

	@ApiOperation(value = "（二）删除涉及的调代课数据")
	@AuthenticationCheck
	@DeleteMapping("/deleteInterrelatedData")
	public BaseResponse deleteInterrelatedData(@RequestBody DeleteRelatedDataDTO dto) {
		courseAdjustmentService.deleteInterrelatedData(dto);
		return response();
	}

	@ApiOperation(value = "判断调课时间是否与自己课程时间相冲突",notes = "若存在时间冲突，接口抛出异常")
	@AuthenticationCheck
	@GetMapping("/isAdjustConflict")
	public BaseResponse isAdjustConflict(
			@RequestParam
			@ApiParam("教师id")
			@NotNull
			@LongValid Long teacherId,
			@RequestParam
			@ApiParam("调课时间（教师课表时间）【如：第一周周一第一节】")
			@NotNull String courseTime,
			@RequestParam
			@ApiParam("教师id")
			@NotNull
			@LongValid Long adjustedTeacherId,
			@RequestParam
			@ApiParam("被调课时间（班级课表时间）【如：第一周周一第一节】")
			@NotNull String adjustedCourseTime) {
		courseAdjustmentService.isAdjustConflict(teacherId, courseTime, adjustedTeacherId, adjustedCourseTime);
		return response();
	}

	@ApiOperation(value = "判断该教师在该节次是否已经调课【审批中】",notes = "若存在调课，接口抛出异常")
	@AuthenticationCheck
	@GetMapping("/isAdjusted")
	public BaseResponse isAdjusted(
			@RequestParam
			@ApiParam("教师id")
			@NotNull
			@LongValid Long teacherId,
			@RequestParam
			@ApiParam("课程时间【如：第一周周一第一节】")
			@NotNull String courseTime) {
		courseAdjustmentService.isAdjusted(teacherId, courseTime);
		return response();
	}

	@ApiOperation(value = "判断该教师在该节次是否存在请假公出",notes = "若存在请假公出，接口抛出异常")
	@AuthenticationCheck
	@GetMapping("/isLeavingOrBusiness")
	public BaseResponse isLeavingOrBusiness(
			@RequestParam
			@ApiParam("教师id")
			@NotNull
			@LongValid Long teacherId,
			@RequestParam
			@ApiParam("调课时间【如：第一周周一第一节】")
			@NotNull String courseTime,
			@RequestParam(required = false)
			@ApiParam("课程开始时间【如：8:00】，字符串，可为空")
			String startTime,
			@RequestParam(required = false)
			@ApiParam("课程结束时间【如：8:00】，字符串，可为空")
			String endTime) {
		courseAdjustmentService.isLeavingOrBusiness(teacherId, courseTime, startTime, endTime);
		return response();
	}

	@ApiOperation("获取教师课表")
	@AuthenticationCheck
	@GetMapping("/getTeacherTable")
	public ResultResponse<TeacherTableVO> getTeacherTable(
			@RequestParam
			@ApiParam("教师id")
			@NotNull Long teacherId,
			@RequestParam
			@ApiParam("周次")
			@NotNull Integer number) {
		return response(courseAdjustmentService.getTeacherTable(teacherId, number));
	}

	@ApiOperation("获取班级课表")
	@AuthenticationCheck
	@GetMapping("/getClassTable")
	public ResultResponse<ClassTableVO> getClassTable(
			@RequestParam
			@ApiParam("班级id")
			@NotNull
			@LongValid Long classId,
			@RequestParam
			@ApiParam("周次")
			@NotNull Integer number) {
		return response(courseAdjustmentService.getClassTable(classId, number));
	}

	@ApiOperation("查询上课教师及其所教课程")
	@AuthenticationCheck
	@GetMapping("/allTeacherAndCourse")
	public ResultResponse<List<CourseTeacherDetailVO>> allTeacherAndCourse() {
		return response(courseAdjustmentService.getAllTeacherAndCourse());
	}

	@ApiOperation("课表-调代课详情")
	@AuthenticationCheck
	@PostMapping("/detail/adjustmentOrReplace")
	public ResultResponse<AdjustmentOrReplaceVO> adjustmentOrReplace(@RequestBody CourseTableAdjustmentAO ao) {
		return response(courseAdjustmentService.getAdjustmentOrReplaceDetail(ao));
	}

	@ApiOperation("pc管理-调课记录")
	@AuthenticationCheck
	@PostMapping("/pc/adjustment/page")
	public PageResponse<List<CourseAdjustmentVO>> page(@RequestBody AdjustmentPageAO ao) {
		return response(courseAdjustmentService.queryPage(ao));
	}

	@ApiOperation("pc个人-调课记录")
	@AuthenticationCheck
	@PostMapping("/me/adjustment/page")
	public PageResponse<List<CourseAdjustmentVO>> myAdjustmentPage(@RequestBody AdjustmentPageAO ao) {
		return response(courseAdjustmentService.queryMyPage(ao));
	}

	@ApiOperation("小程序-调代课记录（调代课记录）")
	@AuthenticationCheck
	@PostMapping("/applet/adjustment/page")
	public PageResponse<List<AdjustmentAndReplaceApplyRecordsVO>> myApplyPage(@RequestBody RecordsPageAO ao) {
		return response(courseAdjustmentService.getApplyPage(ao));
	}

	@ApiOperation("调课记录数据导出")
	@AuthenticationCheck
	@PostMapping("/pc/adjustment/page/export")
	public void adjustmentExport(@RequestBody AdjustmentPageAO ao, HttpServletResponse response) {
		courseAdjustmentService.adjustmentExport(ao, response);
	}

	@ApiOperation("根据id删除调课记录")
	@AuthenticationCheck
	@DeleteMapping("/remove/adjustment")
	public BaseResponse remove(@RequestBody IDAO ao) {
		courseAdjustmentService.removeCourseAdjustmentById(ao.getId());
		return response();
	}

	@ApiOperation("调课详情")
	@AuthenticationCheck
	@GetMapping("/detail/adjustment/{id}")
	public ResultResponse<CourseAdjustmentVO> detail(
			@PathVariable
			@ApiParam("调课id")
			@NotNull
			@LongValid Long id) {
		return response(courseAdjustmentService.getAdjustmentById(id));
	}

	@ApiOperation(value = "（二）pc管理/pc个人/小程序-发起调课",notes = "个人端和小程序发起，isAdminApply填false")
	@AuthenticationCheck
	@PostMapping("/pc/adjustment/apply")
	public ResultResponse<ApplyAdjustmentVO> applyAdjustment(@RequestBody CourseAdjustmentApplyAO apply) {
		return response(courseAdjustmentService.applyAdjustment(apply));
	}

	@ApiOperation("pc管理-调代课统计")
	@AuthenticationCheck
	@PostMapping("/pc/statistics")
	public PageResponse<List<AdjustmentAndReplaceStatisticsPageVO>> statisticsPage(@RequestBody AdjustmentAndReplaceStatisticsPageAO ao) {
		return response(courseAdjustmentService.statisticsPage(ao));
	}

	@ApiOperation("调代课统计数据导出")
	@AuthenticationCheck
	@PostMapping("/pc/statistics/export")
	public void statisticsExport(@RequestBody AdjustmentAndReplaceStatisticsPageAO ao, HttpServletResponse response) {
		courseAdjustmentService.statisticsExport(ao, response);
	}

	@ApiOperation("调代课统计-单个教师详情-调课")
	@AuthenticationCheck
	@PostMapping("/pc/statistics/detail/adjustment")
	public PageResponse<List<CourseAdjustmentVO>> singleAdjustmentPage(@RequestBody SinglePageAO ao) {
		return response(courseAdjustmentService.singleAdjustmentPage(ao));
	}

	@ApiOperation("调代课统计-单个教师详情数据导出")
	@AuthenticationCheck
	@PostMapping("/pc/statistics/detail/export")
	public void singleRecordExport(@RequestBody SinglePageAO ao, HttpServletResponse response) {
		courseAdjustmentService.singleRecordExport(ao, response);
	}

	@ApiOperation("获取流程信息")
	@AuthenticationCheck
	@PostMapping("/flows")
	public ResultResponse<FlowVO> flows(@RequestBody RelatedAO ao) {
		return response(courseAdjustmentService.getFlowDetails(ao.getRelatedId(), ao.getRelatedType(), ao.getAdjustmentType()));
	}

	@ApiOperation("调代课审批")
	@AuthenticationCheck
	@PutMapping("/flows/approve")
	public BaseResponse approve(@RequestBody ApproveAO ao){
		courseAdjustmentService.approve(ao);
		return response();
	}

	@ApiOperation("模拟获取节点")
	@AuthenticationCheck
	@PostMapping("/flows/simulation")
	public ResultResponse<FlowNodesListVO> simulation(@RequestBody SimulationAO ao){
		return response(courseAdjustmentService.simulation(ao));
	}

	@ApiOperation("催办")
	@AuthenticationCheck
	@PutMapping("/flows/urging")
	public BaseResponse urging(@RequestBody AdjustmentUrgeAO ao){
		courseAdjustmentService.urging(ao);
		return response();
	}

	@ApiOperation("撤销")
	@AuthenticationCheck
	@PutMapping("/flows/cancel")
	public BaseResponse cancel(@RequestBody AdjustmentCancelAO ao){
		courseAdjustmentService.cancel(ao);
		return response();
	}

}
