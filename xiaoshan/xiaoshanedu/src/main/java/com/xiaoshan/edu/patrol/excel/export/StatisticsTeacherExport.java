package com.xiaoshan.edu.patrol.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;

@Getter@Setter@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class StatisticsTeacherExport {
    @ExcelProperty(value = "序号",index = 0)
    private Integer index;

    @ExcelProperty(value = "教师", index = 1)
    private String objectName;

    @ExcelProperty(value = "部门", index = 2)
    private String departmentName;

    @ExcelProperty(value = "被巡课次数", index = 3)
    private String totalCount;

    @ExcelProperty(value = "表现优秀次数", index = 4)
    private String excellentCount;

    @ExcelProperty(value = "表现一般次数", index = 5)
    private String commonCount;

    @ExcelProperty(value = "表现较差次数", index = 6)
    private String badCount;
}
