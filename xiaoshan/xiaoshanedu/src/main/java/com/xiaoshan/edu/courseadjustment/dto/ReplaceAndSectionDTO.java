package com.xiaoshan.edu.courseadjustment.dto;

import com.xiaoshan.edu.courseadjustment.entity.ReplaceSectionDO;
import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ReplaceAndSectionDTO {
    private Long id;
    private String teacherName;
    private Long teacherId;
    private String replaceTeacherName;
    private Long replaceTeacherId;
    private String courseName;
    private ApprovalState approvalState;
    private List<ReplaceSectionDO> replaceSectionDoList;
}
