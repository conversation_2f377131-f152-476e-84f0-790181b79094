package com.xiaoshan.edu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: klb
 * @createDate: 2021/5/18
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum GenderEnum {

    /**
     * 男生
     */
    MEN(0,"男生"),
    /**
     * 女生
     */
    WOMEN(1,"女生");

    private Integer code;

    private String message;

    public static String codeToMessage(Integer code){
        GenderEnum[] values = GenderEnum.values();
        for (GenderEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getMessage();
            }
        }
        return null;
    }
}
