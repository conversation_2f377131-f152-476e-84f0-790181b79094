package com.xiaoshan.edu.model.vo;

import java.io.Serializable;
import java.sql.Date;

import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class WeekConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 周次
     */
    private Integer number;

    /**
     * 开始日期
     */
    private Date startDay;

    /**
     * 结束日期
     */
    private Date endDay;

    /**
     * 大小周(1大周 2小周)
     */
    private Integer weekSize;

    public String getChineseWeekNumber(){;
        String chineseWeekNum = CourseAdjustmentUtils.number2Chinese(number);
        return  "第" + chineseWeekNum + "周";
    }

}
