package com.xiaoshan.edu.courseadjustment.entity;

import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;

import java.sql.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity("replaceSection")
@Table("cou_replace_section")
@TableDef(comment = "代课节次详情表")
public class ReplaceSectionDO extends BaseV2Ext {

    private static final long serialVersionUID = 1L;

    public ReplaceSectionDO() {
    }

    @ColumnDef(comment = "代课id")
    @Column("replace_id")
    private Long replaceId;

    @ColumnDef(comment = "课程时间：如：第一周周一第一节")
    @Column("course_time")
    private String courseTime;

    @ColumnDef(comment = "课程班级或教室")
    @Column("course_room")
    private String courseRoom;

    @ColumnDef(comment = "课程班级或教室id（教室以ROOM结尾）")
    @Column("course_room_id")
    private String courseRoomId;

    @ColumnDef(comment = "代课日期")
    @Column("replace_date")
    private Date replaceDate;

    @ColumnDef(comment = "代课数据是否失效", isNull = true, defaultValue = "'false'")
    @Column("replace_is_invalid")
    private Boolean replaceIsInvalid;

    @ColumnDef(comment = "代课父Id", isNull = true, defaultValue = "0")
    @Column("replace_father_id")
    private Long replaceFatherId;

    @ColumnDef(comment = "代课父数据类型：ADJUSTMENT/REPLACE，调课/代课", isNull = true)
    @Column("replace_father_type")
    private AdjustmentType replaceFatherType;

    @ColumnDef(comment = "课表ID", isNull = true)
    @Column("curriculum_table_id")
    private Long curriculumTableId;

    @ColumnDef(comment = "代课原课表时间", isNull = true)
    @Column("origin_course_time")
    private String originCourseTime;

    @ColumnDef(comment = "原课程教师id", isNull = true)
    @Column("origin_teacher_id")
    private Long originTeacherId;

    @ColumnDef(comment = "代课老师是否让其他老师代课（连续代课）", isNull = true, defaultValue = "'false'")
    @Column("is_consequent")
    private Boolean isConsequent;

    @ColumnDef(comment = "调休前代课原课表时间", isNull = true)
    @Column("before_origin_course_time")
    private String beforeOriginCourseTime;
}
