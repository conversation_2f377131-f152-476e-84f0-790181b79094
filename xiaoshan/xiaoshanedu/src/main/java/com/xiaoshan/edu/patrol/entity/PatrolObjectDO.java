package com.xiaoshan.edu.patrol.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;

@Getter@Setter@ToString
@Entity("patrolObject")
@Table("pat_patrol_object")
@TableDef(comment = "巡课对象表")
public class PatrolObjectDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;
	
	public PatrolObjectDO(){}

	@ColumnDef(comment = "巡课id")
	@Column("patrol_id")
	private Long patrolId;

	@ColumnDef(comment = "巡课对象id")
	@Column("object_id")
	private Long objectId;

	@ColumnDef(comment = "巡课对象名字")
	@Column("name")
	private String name;

	@ColumnDef(comment = "部门",isNull = true)
	@Column("department_name")
	private String departmentName;

	@ColumnDef(comment = "学号",isNull = true)
	@Column("student_no")
	private String studentNo;

	@ColumnDef(comment = "年级",isNull = true)
	@Column("grade_name")
	private String gradeName;

	@ColumnDef(comment = "班主任id",isNull = true)
	@Column("head_teacher_id")
	private Long headTeacherId;

	@ColumnDef(comment = "家长用户ids",isNull = true)
	@Column("parent_user_ids")
	private String parentUserIds;

	@ColumnDef(comment = "人脸",isNull = true,length = 2048)
	@Column("face_data")
	private String faceData;
}
