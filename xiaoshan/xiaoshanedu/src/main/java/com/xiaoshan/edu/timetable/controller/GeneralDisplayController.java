package com.xiaoshan.edu.timetable.controller;

import java.sql.Time;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/general/display")
@Api(tags = "课表-显示类接口")
public class GeneralDisplayController extends BaseController {

	@Autowired
	private GeneralTableService generalTableService;
	
	@ApiOperation("班级详情(周期)")
	@AuthenticationCheck
	@GetMapping("/class/week/detail/cur/{classId}/{dayWeek}/{week}")
	public ResultResponse<Map<String, List<TeacherAbbDetailVO>>> classTableWeekDetail(
			@PathVariable @ApiParam("班级ID") @NotNull @LongValid Long classId,
			@PathVariable @ApiParam("周") @NotNull @Enum WeekEnum dayWeek,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week) {
		Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> data = generalTableService
				.classTableWeekDetail(classId, week);
		if(data!=null) {
			if (data.containsKey(dayWeek)) {
				Map<String, List<TeacherAbbDetailVO>> dd = data.get(dayWeek);
				for(String section:dd.keySet()) {
					List<TeacherAbbDetailVO> techs=dd.get(section);
					for(TeacherAbbDetailVO v:techs) {
						if(!StringUtils.isEmpty(v.getStartTime())
								&&!StringUtils.isEmpty(v.getEndTime())) {
							Time startTime=Time.valueOf(v.getStartTime()+":00");
							Time endTime=Time.valueOf(v.getEndTime()+":00");
							Time currTime=Time.valueOf(new Time(System.currentTimeMillis()).toString());
							if(currTime.compareTo(startTime)>0&&endTime.compareTo(currTime)>0) {
								Map<String, List<TeacherAbbDetailVO>> map=new LinkedHashMap<>();
								map.put(section, techs);
								return response(map);
							}
						}
					}
				}
			}
		}
		return response(null);
	}

	@ApiOperation("教室详情(周期)")
	@AuthenticationCheck
	@GetMapping("/class/room/week/detail/cur/{classRoomId}/{dayWeek}/{week}")
	public ResultResponse<Map<String, List<TeacherAbbDetailVO>>> classRoomTableWeekDetail(
			@PathVariable @ApiParam("教室ID") @NotNull @LongValid Long classRoomId,
			@PathVariable @ApiParam("周") @NotNull @Enum WeekEnum dayWeek,
			@PathVariable @ApiParam("周次") @NotNull @IntegerValid Integer week) {
		Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> data = generalTableService
				.classRoomTableWeekDetail(classRoomId, week,true);
		if (data.containsKey(dayWeek)) {
			Map<String, List<TeacherAbbDetailVO>> dd = data.get(dayWeek);
			for(String section:dd.keySet()) {
				List<TeacherAbbDetailVO> techs=dd.get(section);
				for(TeacherAbbDetailVO v:techs) {
					if(!StringUtils.isEmpty(v.getStartTime())
							&&!StringUtils.isEmpty(v.getEndTime())) {
						Time startTime=Time.valueOf(v.getStartTime()+":00");
						Time endTime=Time.valueOf(v.getEndTime()+":00");
						Time currTime=Time.valueOf(new Time(System.currentTimeMillis()).toString());
						if(currTime.compareTo(startTime)>0&&endTime.compareTo(currTime)>0) {
							Map<String, List<TeacherAbbDetailVO>> map=new LinkedHashMap<>();
							map.put(section, techs);
							return response(map);
						}
					}
				}
			}
		}
		return response(null);
	}
	
}
