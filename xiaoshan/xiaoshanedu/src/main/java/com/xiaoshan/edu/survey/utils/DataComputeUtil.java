package com.xiaoshan.edu.survey.utils;

import com.xiaoshan.edu.dto.survey.PercentageDTO;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.utils.format.FormatUtil;
import com.xiaoshan.edu.vo.survey.QuestionVO;

import java.util.*;
import java.util.stream.Collectors;

public class DataComputeUtil {


    /**
     * 计算本人数据
     * 人数：完成这道题的人数
     * 比例：选该选项的人数 / 完成这道题的人数
     */
    public static Map<String, PercentageDTO> computeDataMy(List<TaskTemplateStudentTeacherQuestionDO> list, QuestionVO questionVO) {
        // 总人数，该题目回答的人数
        Set<Long> studentIds = new HashSet<>();

        // 先初始化，避免没有人回答这道题导致这个选项没有数据
        Map<String, Set<Long>> itemSelectNumMap = new LinkedHashMap<>();
        for (QuestionOptionDTO item : questionVO.getItems()) {
            itemSelectNumMap.put(item.getOption(), new HashSet<>());
        }

        for (TaskTemplateStudentTeacherQuestionDO tDO : list) {  // 统计每个选项有多少人选
            if (tDO.getIsSubmit()) {
                if (tDO.getAnswer().size() > 0) {
                    studentIds.add(tDO.getStudentId());
                    for (QuestionOptionDTO dto : tDO.getAnswer()) {
                        String key = dto.getOption();
                        Set<Long> set = itemSelectNumMap.get(key);
                        set.add(tDO.getStudentId());
                    }
                }
            }
        }

        Map<String, PercentageDTO> myItemComputeMap = new LinkedHashMap<>();
        int total = studentIds.size();
        itemSelectNumMap.forEach((option, set) -> {
            myItemComputeMap.put(option, new PercentageDTO(set.size(), total == 0 ? "0.00%" : FormatUtil.formatDouble(set.size() * 1.0 / total * 100) + "%"));
        });
        return myItemComputeMap;
    }

    /**
     * 计算科目平均（任课教师），年级平均（班主任）
     * 人数：完成这道题的人数 / 有该科目的班级数量（班主任模板是所有班级数量）
     * 比例：选该选项的人数 / 完成这道题的人数
     */
    public static Map<String, PercentageDTO> computeDataCurrGrade(List<TaskTemplateStudentTeacherQuestionDO> list, QuestionVO questionVO) {
        // 有该科目的班级数量
        int classNum = list.stream().map(TaskTemplateStudentTeacherQuestionDO::getClassName).collect(Collectors.toSet()).size();

        // 总人数，该题目回答的人数
        Set<Long> studentIds = new HashSet<>();

        // 先初始化，避免没有人回答这道题导致这个选项没有数据
        Map<String, Set<Long>> itemSelectNumMap = new LinkedHashMap<>();
        for (QuestionOptionDTO item : questionVO.getItems()) {
            itemSelectNumMap.put(item.getOption(), new HashSet<>());
        }

        for (TaskTemplateStudentTeacherQuestionDO tDO : list) {  // 统计每个选项有多少人选
            if (tDO.getIsSubmit()) {
                if (tDO.getAnswer().size() > 0) {
                    studentIds.add(tDO.getStudentId());
                    for (QuestionOptionDTO dto : tDO.getAnswer()) {
                        String key = dto.getOption();
                        Set<Long> set = itemSelectNumMap.get(key);
                        set.add(tDO.getStudentId());
                    }
                }
            }
        }

        Map<String, PercentageDTO> myItemComputeMap = new LinkedHashMap<>();
        int total = studentIds.size();
        itemSelectNumMap.forEach((option, set) -> {
            myItemComputeMap.put(option, new PercentageDTO(divRound(set.size(), classNum), total == 0 ? "0.00%" : FormatUtil.formatDouble(set.size() * 1.0 / total * 100) + "%"));
        });
        return myItemComputeMap;
    }

    /**
     * 计算班级平均
     * 人数：班级内各科目选择总人数（如果一个学生不同科目选择多次，不去重，其实就是计算这个选项被选的数量）/ 有该题目的科目数量
     * 比例：选择该项人数（人头） / 回答该题人数（人头）
     */
    public static Map<String, PercentageDTO> computeDataClass(List<TaskTemplateStudentTeacherQuestionDO> list, QuestionVO questionVO) {

        // 有该题目的科目数量
        int currNum = list.stream().map(TaskTemplateStudentTeacherQuestionDO::getCurriculum).collect(Collectors.toSet()).size();

        // 总人数，该题目回答的人头
        Set<Long> studentIds = new HashSet<>();

        // 先初始化，避免没有人回答这道题导致这个选项没有数据
        Map<String, List<Long>> itemSelectNumMap = new LinkedHashMap<>();
        for (QuestionOptionDTO item : questionVO.getItems()) {
            itemSelectNumMap.put(item.getOption(), new ArrayList<>());
        }

        for (TaskTemplateStudentTeacherQuestionDO tDO : list) {  // 统计每个选项有多少人选
            if (tDO.getIsSubmit()) {
                if (tDO.getAnswer().size() > 0) {
                    studentIds.add(tDO.getStudentId());
                    for (QuestionOptionDTO dto : tDO.getAnswer()) {
                        String key = dto.getOption();
                        List<Long> itemSelectList = itemSelectNumMap.get(key);
                        itemSelectList.add(tDO.getStudentId());
                    }
                }
            }
        }

        Map<String, PercentageDTO> myItemComputeMap = new LinkedHashMap<>();
        int total = studentIds.size();
        itemSelectNumMap.forEach((option, selectList) -> {
            myItemComputeMap.put(option, new PercentageDTO(divRound(selectList.size(), currNum), total == 0 ? "0.00%" : FormatUtil.formatDouble(new HashSet<>(selectList).size() * 1.0 / total * 100) + "%"));
        });
        return myItemComputeMap;
    }


    /**
     * 两数相除，如果是能除尽，则返回整数，如果不能除尽，保留两位小数
     */
    public static Object divRound(Integer value, int size) {
        Object n = value / size;
        if (value % size >= 1){
            n = FormatUtil.formatDouble(value*1.0 / size);
        }
        return n;
    }
}
