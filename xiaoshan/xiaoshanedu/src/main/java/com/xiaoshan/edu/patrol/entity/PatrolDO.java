package com.xiaoshan.edu.patrol.entity;

import com.xiaoshan.edu.enums.patrol.PatrolAppraise;
import com.xiaoshan.edu.enums.patrol.PatrolType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
@Entity("patrol")
@Table("pat_patrol")
@TableDef(comment = "巡课记录表",charset = "utf8mb4", collate = "utf8mb4_bin")
public class PatrolDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;
	
	public PatrolDO(){}

	@ColumnDef(comment = "巡课类型")
	private PatrolType type;

	@ColumnDef(comment = "巡课班级")
	@Column("class_name")
	private String className;

	@ColumnDef(comment = "巡课时间（课程时间）")
	@Column("course_time")
	private String courseTime;

	@ColumnDef(comment = "巡课科目")
	@Column("course_name")
	private String courseName;

	@ColumnDef(comment = "巡课老师")
	@Column("patrol_teacher_name")
	private String patrolTeacherName;

	@ColumnDef(comment = "巡课评价")
	private PatrolAppraise appraise;

	@ColumnDef(comment = "巡课内容")
	private String content;

	@ColumnDef(comment = "巡课照片urls",isNull = true)
	@Column("pic_urls")
	private JsonArray picUrls;
}
