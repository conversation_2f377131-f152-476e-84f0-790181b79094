package com.xiaoshan.edu.survey.controller;


import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.topnetwork.ao.PageAO;
import com.xiaoshan.edu.ao.survey.TaskAddAO;
import com.xiaoshan.edu.ao.survey.TaskPageAO;
import com.xiaoshan.edu.survey.service.TaskService;
import com.xiaoshan.edu.vo.survey.StudentTaskVO;
import com.xiaoshan.edu.vo.survey.TaskAddVO;
import com.xiaoshan.edu.vo.survey.TaskPageVO;
import com.xiaoshan.edu.vo.survey.TeacherTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

@RestController
@RequestMapping("/v1/survey/task")
@Api(tags = "教学调查接口")
public class TaskController extends BaseController {

	@Autowired
	private TaskService taskService;

	@ApiOperation("调查任务 -- 分页查询")
	@AuthenticationCheck
	@PostMapping("/page")
	public PageResponse<List<TaskPageVO>> page(@RequestBody TaskPageAO ao) {
		return response(taskService.page(ao));
	}

	@ApiOperation("调查任务 -- 删除")
	@AuthenticationCheck
	@DeleteMapping("/{taskId}")
	public BaseResponse remove(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId) {
		taskService.removeById(taskId);
		return response();
	}

	@ApiOperation("调查任务 -- 新增")
	@AuthenticationCheck
	@PostMapping("/add")
	public BaseResponse add(@RequestBody TaskAddAO ao){
		taskService.add(ao);
		return response();
	}

	@ApiOperation("调查任务 -- 编辑")
	@AuthenticationCheck
	@PostMapping("/edit")
	public BaseResponse edit(@RequestBody TaskAddAO ao){
		taskService.edit(ao);
		return response();
	}

	@ApiOperation("调查任务 -- id查询详细信息")
	@AuthenticationCheck
	@GetMapping("/{taskId}")
	public ResultResponse<TaskAddVO> getById(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId){
		return response(taskService.getById(taskId));
	}

	@ApiOperation("公布/取消公布")
	@AuthenticationCheck
	@PutMapping("/{taskId}")
	public BaseResponse publish(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId){
		taskService.publish(taskId);
		return response();
	}

	@ApiOperation("教师端 -- 调查任务列表")
	@AuthenticationCheck
	@GetMapping("/teacher/task")
	public ResultResponse<List<TeacherTaskVO>> teacherTaskList(){
		return response(taskService.teacherTaskList());
	}

	@ApiOperation("小程序 -- 调查任务列表")
	@AuthenticationCheck
	@GetMapping("/student/task/{studentId}")
	public ResultResponse<List<StudentTaskVO>> studentTaskList(@PathVariable @ApiParam("学生id") @NotNull @LongValid Long studentId){
		return response(taskService.studentTaskList(studentId));
	}

	@ApiOperation("电子档案--教师调查任务列表")
	@AuthenticationCheck
	@PostMapping("/archives/page/{teacherId}")
	public PageResponse<List<TaskPageVO>> archives(@PathVariable @ApiParam("教师id") @NotNull @LongValid Long teacherId,
											 @RequestBody PageAO ao){
		return response(taskService.archives(teacherId, ao));
	}
}
