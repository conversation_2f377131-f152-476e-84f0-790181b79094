package com.xiaoshan.edu.openclass.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseExt;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.thirdparty.json.JsonArray;

@Getter@Setter@ToString
@Entity("openClassPerson")
@Table("ope_open_class_person")
@TableDef(comment = "个人参与的公开课与评价", charset = "utf8mb4", collate = "utf8mb4_bin")
public class OpenClassPersonDO extends BaseExt {

	private static final long serialVersionUID = 1L;
	
	public OpenClassPersonDO(){}

	@ColumnDef(comment = "公开课id")
	@Column("open_class_id")
	private Long openClassId;

	@ColumnDef(comment = "教师userId")
	@Column("teacher_user_id")
	private Long teacherUserId;

	@ColumnDef(comment = "教师id")
	@Column("teacher_id")
	private Long teacherId;

	@ColumnDef(comment = "教师姓名")
	@Column("teacher_name")
	private String teacherName;

	@ColumnDef(comment = "教师部门")
	@Column("teacher_department")
	private String teacherDepartment;

	@ColumnDef(comment = "评价完成，true-评价完成，false-未评价", defaultValue = "false")
	private Boolean evaluated;

	@ColumnDef(comment = "语言表达评分", defaultValue = "0")
	private Integer expression;

	@ColumnDef(comment = "学生互动评分", defaultValue = "0")
	private Integer interaction;

	@ColumnDef(comment = "时间分配评分", defaultValue = "0")
	@Column("time_allocation")
	private Integer timeAllocation;

	@ColumnDef(comment = "板书内容评分", defaultValue = "0")
	@Column("blackboard_content")
	private Integer blackboardContent;

	@ColumnDef(comment = "信息化评分", defaultValue = "0")
	private Integer informationize;

	@ColumnDef(comment = "总评分", defaultValue = "0")
	@Column("total_score")
	private Integer totalScore;

	@ColumnDef(comment = "课程评价", isNull = true, length = 500)
	private String comment;

	@ColumnDef(comment = "评价图片url列表", isNull = true)
	@Column("pic_urls")
	private JsonArray picUrls;
}
