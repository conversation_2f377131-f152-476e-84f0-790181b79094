package com.xiaoshan.edu.api.facade.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaoshan.basic.ao.NewWorkFlowAO;
import com.xiaoshan.basic.vo.*;
import com.xiaoshan.edu.api.facade.FlowFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import start.magic.thirdparty.json.JsonObject;

import javax.validation.constraints.Null;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class FlowFacadeImpl implements FlowFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(FlowFacadeImpl.class);

    private final static String WORKFLOW = "https://workflow";

    private final static String FLOWABLE = "http://flowable";


    @Autowired
    @Qualifier("webClient")
    private WebClient webClient;

    @Override
    public FlowNodesListVO getFlowNodes(String departments, String roles, String applicantId, String definitionKey, Map<String, String> conditions) {
        JSONObject json = new JSONObject();
        json.put("applicationDepts", departments);
        json.put("applicationRoles", roles);
        json.put("applicantId", applicantId);
        json.put("processDefinitionKey", definitionKey);
        json.put("conditions", conditions);
        ParameterizedTypeReference<FlowNodesListVO> responseBodyType = new ParameterizedTypeReference<FlowNodesListVO>() {
        };
        return webClient.post().uri(WORKFLOW + "/v2/flows/nodes").body(BodyInserters.fromPublisher(Mono.just(json), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public FlowVO getFlowInformation(Long relatedId, Integer relatedType) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.set("relatedId", relatedId.toString());
        formData.set("relatedType", relatedType.toString());
        ParameterizedTypeReference<String> responseBodyType = new ParameterizedTypeReference<String>() {
        };
        String content = webClient.get().uri(WORKFLOW + "/v2/flows", uriBuilder -> uriBuilder.queryParams(formData).build()).retrieve().bodyToMono(responseBodyType).block();
        return JSONObject.parseObject(content,FlowVO.class);
    }

    @Override
    public List<BatchFlowsVO> batchFlows(Long relatedId, Integer relatedType) {
        JSONObject json = new JSONObject();
        json.put("relatedType", relatedType);
        json.put("relatedIds", relatedId.toString());
        ParameterizedTypeReference<List<BatchFlowsVO>> responseBodyType = new ParameterizedTypeReference<List<BatchFlowsVO>>() {
        };
        return webClient.post().uri(WORKFLOW + "/v2/flows/batchFlows").body(BodyInserters.fromPublisher(Mono.just(json), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public void beginTheWorkFlow(NewWorkFlowAO workFlowAo) {
        LOGGER.info("开始流程的请求JSON为: " + JSONObject.toJSONString(workFlowAo));
        ParameterizedTypeReference<Objects> responseBodyType = new ParameterizedTypeReference<Objects>() {
        };
        webClient.post().uri(WORKFLOW + "/v2/flows").body(BodyInserters.fromPublisher(Mono.just(workFlowAo), NewWorkFlowAO.class)).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public String getFlowsModel(Integer relatedType) {
        return webClient.get().uri(WORKFLOW + "/v2/flows/" + relatedType + "/model").retrieve().bodyToMono(String.class).block();
    }

    @Override
    public String checkModelStarting(String modelId) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.set("id", modelId);
        String content = webClient.get().uri(FLOWABLE + "/flow/modelEnable",uriBuilder -> uriBuilder.queryParams(formData).build()).retrieve().bodyToMono(String.class).block();
        JsonObject jsonResult = new JsonObject(content);
        return jsonResult.get("data").toString();
    }

    @Override
    public void urgeFlow(String messageCode, String content, Long flowId, String extra, String miniProgramUrl, String officeAccountData, String path, String query, Integer sourceId, Long studentId) {
        HashMap<String, Object> params = new HashMap<>(10);
        params.put("flowId",flowId);
        params.put("messageCode", messageCode);
        params.put("content", content);
        params.put("extra",extra);
        params.put("officeAccountData", officeAccountData);
        params.put("miniProgramUrl", miniProgramUrl);
        params.put("path", path);
        params.put("query", query);
        params.put("sourceId", sourceId);
        params.put("studentId", studentId);
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(params));
        LOGGER.info("发送催办json：" + jsonObject.toString());
        ParameterizedTypeReference<Objects> responseBodyType = new ParameterizedTypeReference<Objects>() {
        };
        webClient.post().uri(WORKFLOW + "/v2/flows/urge").body(BodyInserters.fromPublisher(Mono.just(jsonObject), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public void cancelLeaving(String engineFlowId, String message) {
        JSONObject params = new JSONObject();
        params.put("processInstanceId", engineFlowId);
        params.put("cancelReason", message);
        ParameterizedTypeReference<Objects> responseBodyType = new ParameterizedTypeReference<Objects>() {
        };
        webClient.put().uri(WORKFLOW + "/v2/flows/cancel").body(BodyInserters.fromPublisher(Mono.just(params), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
    }
}
