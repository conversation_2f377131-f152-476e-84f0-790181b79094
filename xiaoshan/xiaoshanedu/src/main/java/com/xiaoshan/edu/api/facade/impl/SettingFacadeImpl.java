package com.xiaoshan.edu.api.facade.impl;

import com.xiaoshan.basic.vo.*;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import com.xiaoshan.edu.model.RoomClassVO;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import start.framework.commons.exception.BusinessException;
import start.magic.core.ApplicationException;

import java.sql.Date;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SettingFacadeImpl implements SettingFacade {

    @Autowired
    @Qualifier("webClient")
    public void setWebClient(WebClient webClient) {
        this.webClient = webClient;
    }

    private WebClient webClient;

    private static final String CURRENT_SEMESTER = "https://setting/semesters/current_semester";

    @Override
    public SemesterVO currentSemester() {
        SemesterVO semester = webClient.get().uri(CURRENT_SEMESTER).retrieve().bodyToMono(SemesterVO.class).block();
        if (semester == null) {
            throw new BusinessException("当前学期不在校历内");
        }
        return semester;
    }

    @Override
    public Map<Integer, WeekConfigVO> weekConfigNumberMap(Long id) {
        List<WeekConfigVO> list = weekConfig(id);
        Map<Integer, WeekConfigVO> weekConfigMap = new HashMap<>(5);
        for (WeekConfigVO v : list) {
            weekConfigMap.put(v.getNumber(), v);
        }
        return weekConfigMap;
    }

    @Override
    public List<WeekConfigVO> weekConfig(Long id) {
        ParameterizedTypeReference<List<WeekConfigVO>> responseBodyType = new ParameterizedTypeReference<List<WeekConfigVO>>() {
        };
        String uri = "https://setting/semesters/" + id + "/week_config";
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public List<ItemConfigVO> items(Long id) {
        ParameterizedTypeReference<List<ItemConfigVO>> responseBodyType = new ParameterizedTypeReference<List<ItemConfigVO>>() {
        };
        String uri = "https://setting/semesters/" + id + "/items";
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public List<SemesterVO> semesters() {
        ParameterizedTypeReference<List<SemesterVO>> responseBodyType = new ParameterizedTypeReference<List<SemesterVO>>() {
        };
        String uri = "https://setting/semesters";
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public Map<Long, SemesterVO> semesterMap() {
        List<SemesterVO> list = semesters();
        Map<Long, SemesterVO> semesterMap = new HashMap<>(5);
        for (SemesterVO v : list) {
            semesterMap.put(v.getId(), v);
        }
        return semesterMap;
    }

    @Override
    public PushItemVO pushItems(String itemCode) {
        String uri = "https://setting/push_items/" + itemCode;
        return webClient.get().uri(uri).retrieve().bodyToMono(PushItemVO.class).block();
    }

    @Override
    public List<RestSectionVO> getRestAllBySection() {
        String uri = "https://setting/rest/all/by_section";
        ParameterizedTypeReference<List<RestSectionVO>> responseBodyType = new ParameterizedTypeReference<List<RestSectionVO>>() {
        };
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public Map<String, RestSectionDetailVO> getRestAll(Long sectionId, Integer enrollmentYear) {
        List<RestSectionVO> lists = getRestAllBySection();
        for (RestSectionVO vo : lists) {
            if (vo.getSectionType().equals(sectionId) && vo.getEnrollmentYear().equals(enrollmentYear)) {
                return vo.getDetails().stream().collect(Collectors.toMap(k -> (k.getTimeRange() + "-" + k.getSectionName() + "-" + k.getType()), Function.identity(), (v1, v2) -> v1));
            }
        }
        return Collections.emptyMap();
    }

    @Override
    public RestTimeVO getCurrentRest(String gradeName) {
        String uri = "https://setting/rest/all";
        ParameterizedTypeReference<List<AllRestVO>> responseBodyType = new ParameterizedTypeReference<List<AllRestVO>>() {
        };
        List<AllRestVO> allRestVOList = webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
        assert allRestVOList != null;
        if (allRestVOList.size() > 0) {
            for (AllRestVO allRestVO : allRestVOList) {
                if (allRestVO.getIsEnable() == 0) {
                    // 开启状态
                    List<RestTimeVO> restDetails = allRestVO.getRestDetails();
                    if (restDetails != null && restDetails.size() > 0) {
                        for (RestTimeVO restDetail : restDetails) {
                            if (restDetail.getGradeName().contains(gradeName)) {
                                return restDetail;
                            }
                        }
                    }
                    break;
                }
            }
        }
        return null;
    }

    @Override
    public List<RoomVO> rooms() {
        ParameterizedTypeReference<List<RoomVO>> responseBodyType = new ParameterizedTypeReference<List<RoomVO>>() {
        };
        String uri = "https://setting/rooms/all";
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public List<RoomClassVO> getRoomsTree() {
        ParameterizedTypeReference<List<RoomClassVO>> responseBodyType = new ParameterizedTypeReference<List<RoomClassVO>>() {
        };
        String uri = "https://setting/rooms/tree";
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public List<ClassBuildingTreeDTO> getRoomByRoomType(Integer roomType) {
        ParameterizedTypeReference<List<ClassBuildingTreeDTO>> responseBodyType = new ParameterizedTypeReference<List<ClassBuildingTreeDTO>>() {
        };
        String uri = "https://setting/rooms/" + roomType;
        return webClient.get().uri(uri).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public DayConfigVO dayConfig(Date day) {
        String uri = "https://setting/semesters/day_config/" + day;
        return webClient.get().uri(uri).retrieve().bodyToMono(DayConfigVO.class).block();
    }

    @Override
    public WeekConfigVO getDateWeek(Date date) {
        String uri = "https://setting/semesters/date_week?day=" + date;
        return webClient.get().uri(uri).retrieve().bodyToMono(WeekConfigVO.class).block();
    }

    @Override
    public WeekConfigVO currentWeek() {
        String uri = "https://setting/semesters/current_week";
        return webClient.get().uri(uri).retrieve().bodyToMono(WeekConfigVO.class).block();
    }
}
