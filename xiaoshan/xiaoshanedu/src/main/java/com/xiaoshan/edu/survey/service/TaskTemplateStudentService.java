package com.xiaoshan.edu.survey.service;

import com.xiaoshan.edu.survey.entity.TaskTemplateStudentDO;
import start.framework.service.SqlBaseService;

import java.util.Collection;
import java.util.List;

public interface TaskTemplateStudentService extends SqlBaseService<TaskTemplateStudentDO,Long> {

    /**
     * 小程序 -- 调查详情提交(当所有科目都已完成时调用，否则不需要调)
     * @param templateId
     * @param taskId
     * @param studentId
     */
    void submitCurriculum(Long taskId, Long templateId, Long studentId);

    /**
     * 批量提交题目，往TaskTemplateStudent表中添加一条记录
     * @param taskId
     * @param studentId
     * @param templateId
     */
    void resultSubmit(Long taskId, Long templateId, Collection<Long> studentId);

    /**
     * 重置，结果导入时删除相应数据
     * @param taskId
     * @param templateId
     */
    void reset(Long taskId, Long templateId);

    /**
     * 完成其他
     * @param taskId
     * @param templateId
     * @param studentIds
     * @return
     */
    Collection<Long> findFinishOtherTemplateStudents(Long taskId, Long templateId, Collection<Long> studentIds);
}
