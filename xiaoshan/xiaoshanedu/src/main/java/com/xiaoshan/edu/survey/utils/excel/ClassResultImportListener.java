package com.xiaoshan.edu.survey.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.common.utils.SpringUtils;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.TaskStudentService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentTeacherQuestionService;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import start.framework.commons.exception.BusinessException;
import start.magic.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ClassResultImportListener extends AnalysisEventListener<Map<Integer, String>> {

    private final TaskTemplateDO taskTemplateDO;
    private final Map<String, Long> studentMap;
    private final List<ResultImportEntity> excelEntityList = new ArrayList<>();

    public ClassResultImportListener(TaskTemplateDO taskTemplate) {
        this.taskTemplateDO = taskTemplate;
        TaskStudentService taskStudentService = SpringUtils.getBean(TaskStudentService.class);
        this.studentMap = taskStudentService.getByTaskId(this.taskTemplateDO.getTaskId());
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (context.readRowHolder().getRowIndex() == 1){
            String studentNoStr = "学号";
            String classStr = "班级";
            if (!studentNoStr.equals(headMap.get(0)) || !classStr.equals(headMap.get(1))){
                String message = "班主任结果导入模板不正确";
                throw new BusinessException(message);
            }
            boolean isOk = true;
            Collection<String> values = headMap.values();
            for (String value : values) {
                if (value == null || "".equals(value)){
                    isOk = false;
                    break;
                }
            }
            if (!isOk) {
                String noNullMsg = "选项标题不能为空";
                throw new BusinessException(noNullMsg);
            }
        }
    }

    // 0-学号，1-班级
    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        if (data.get(0) == null || data.get(1) == null) {
            throw new BusinessException("学生班级和学号不能为空,请重新上传");
        }

        Iterator<Map.Entry<Integer, String>> iterator = data.entrySet().iterator();
        String studentNo = iterator.next().getValue();
        String className = iterator.next().getValue();

        ResultImportEntity entity = new ResultImportEntity();
        entity.setClassName(className);
        Long studentId = studentMap.get(studentNo);
        if (studentId == null) {
            String msg1 = "学号为";
            String msg2 = "的学生不在此次调查任务中，请检查excel表";
            throw new BusinessException(msg1 +studentNo+ msg2);
        }
        entity.setStudentId(studentId);
        entity.setStudentNo(studentNo);

        // { questionId -> [{option=A, txt=xxx},{....}] }
        Map<Long, List<QuestionOptionDTO>> questionAnswersMap = new HashMap<>();
        entity.setAnswer(questionAnswersMap);
        excelEntityList.add(entity);

        // 封装模板内每道题目和答案
        for (QuestionVO questionVO : taskTemplateDO.getQuestionList()) {
            if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                continue;
            }

            // {A->xxx, B->xxx}，方便根据选项值构造QuestionOptionDTO
            Map<String, String> itemMap = new HashMap<>();
            for (QuestionOptionDTO item : questionVO.getItems()) {
                itemMap.put(item.getOption(), item.getTxt());
            }

            // 按顺序遍历选项，要与模板一一对应
            String answer = "";
            if (iterator.hasNext()) {
                Map.Entry<Integer, String> next = iterator.next();
                answer = next.getValue() == null ? "" : next.getValue();
            }

            // 构造答案对象
            ArrayList<QuestionOptionDTO> answers = new ArrayList<>();
            String[] items = answer.split("");
            List<String> distinct = Arrays.stream(items).distinct().collect(Collectors.toList());
            if (questionVO.getQuestionType().equals(QuestionType.SINGLE) && distinct.size() >= 2){
                throw new BusinessException(String.format("单选题（%s）：学号为%s的同学选了多个选项，请重新检查excel", questionVO.getTitle(), studentNo));
            }
            for (String item : distinct) {
                if (StringUtils.isEmpty(item)) {
                    break;
                }
                String txt = itemMap.get(item);
                if (txt == null) {
                    throw new BusinessException(String.format("题目%s不存在%s选项", questionVO.getTitle(), item));
                }
                answers.add(new QuestionOptionDTO(item, txt));
            }

            questionAnswersMap.put(questionVO.getQuestionId(), answers);
        }


    }

    // 保存题目到表
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        TaskTemplateStudentTeacherQuestionService service = SpringUtils.getBean(TaskTemplateStudentTeacherQuestionService.class);
        List<TaskTemplateStudentTeacherQuestionDO> questionList = service.getByTaskTemplateId(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId());

        // 先清空数据
        TaskTemplateStudentService taskTemplateStudentService = SpringUtils.getBean(TaskTemplateStudentService.class);
        TaskStudentService taskStudentService = SpringUtils.getBean(TaskStudentService.class);
        taskTemplateStudentService.reset(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId());

        for (TaskTemplateStudentTeacherQuestionDO questionDO : questionList) {
            questionDO.setAnswer(new ArrayList<>());
            questionDO.setIsSubmit(false);
            questionDO.setIsFinish(false);
        }

        // < studentId, <questionId, List<TaskTemplateStudentTeacherQuestionDO> > >
        Map<Long, Map<Long, List<TaskTemplateStudentTeacherQuestionDO>>> dbMap = questionList.stream().collect(
                Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getStudentId,
                        Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getQuestionId)));

        // 填充数据
        Set<Long> studentIds = new HashSet<>();
        for (ResultImportEntity importEntity : excelEntityList) {
            Long studentId = importEntity.getStudentId();
            Map<Long, List<TaskTemplateStudentTeacherQuestionDO>> dbQuestionIdMap = dbMap.get(studentId);
            if (dbQuestionIdMap == null) {
                continue;
            }
            studentIds.add(studentId);

            for (Map.Entry<Long, List<QuestionOptionDTO>> excelItemsEntry : importEntity.getAnswer().entrySet()) {
                if (dbQuestionIdMap.containsKey(excelItemsEntry.getKey())){
                    TaskTemplateStudentTeacherQuestionDO questionDO = dbQuestionIdMap.get(excelItemsEntry.getKey()).get(0);
                    questionDO.setIsSubmit(true);
                    questionDO.setIsFinish(true);
                    questionDO.setAnswer(excelItemsEntry.getValue());
                }
            }
        }

        // 清空这些学生的完成状态
        taskStudentService.reset(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId());

        service.saveBatch(questionList);

        // 保存
        // 往taskTemplateStudent添加一条记录
        taskTemplateStudentService.resultSubmit(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId(), studentIds);
        // 如果完成了所有调查模板，往taskStudent中添加一条记录
        // 查找出所有完成其他调查模板的同学
        Collection<Long> ids = taskTemplateStudentService.findFinishOtherTemplateStudents(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId(), studentIds);

        if (!ids.isEmpty()){
            taskStudentService.submitTemplate(taskTemplateDO.getTaskId(), ids);
        }

    }

}
