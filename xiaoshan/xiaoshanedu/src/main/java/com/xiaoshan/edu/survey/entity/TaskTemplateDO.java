package com.xiaoshan.edu.survey.entity;

import java.util.List;

import com.common.converter.survey.QuestionListConverterEditor;
import com.xiaoshan.edu.enums.survey.SurveyObj;
import com.xiaoshan.edu.vo.survey.QuestionVO;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.IndexesUnion;
import start.magic.persistence.source.jdbc.script.annotations.ScriptConverter;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.UniqueUnion;
import start.magic.persistence.source.jdbc.script.converter.FieldJson;

@Getter@Setter@ToString
@Entity("taskTemplate")
@Table("sur_task_template")
@TableDef(comment = "调查任务-调查模板关联", indexes =
@IndexesUnion(unique = @UniqueUnion(fields = {"taskId","templateId", "deleted"})))
public class TaskTemplateDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;

	public TaskTemplateDO(){}

	@ColumnDef(comment = "调查任务id")
	private Long taskId;

	@ColumnDef(comment = "调查模板id")
	private Long templateId;

	@ColumnDef(comment = "调查模板名称")
	private String templateName;

	@ColumnDef(comment = "调查对象")
	private SurveyObj surveyObj;

	@ColumnDef(comment = "题目列表，保存一份，防止修改调查模板导致的不一致")
	@ScriptConverter(FieldJson.class)
	@PropertyConverter(QuestionListConverterEditor.class)
	private List<QuestionVO> questionList;

}
