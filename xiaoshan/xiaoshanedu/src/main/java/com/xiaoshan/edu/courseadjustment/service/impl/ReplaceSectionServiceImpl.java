package com.xiaoshan.edu.courseadjustment.service.impl;

import com.common.utils.DateUtils;
import com.xiaoshan.edu.courseadjustment.dao.ReplaceSectionDao;
import com.xiaoshan.edu.courseadjustment.entity.CourseReplaceDO;
import com.xiaoshan.edu.courseadjustment.entity.ReplaceSectionDO;
import com.xiaoshan.edu.courseadjustment.service.CourseReplaceService;
import com.xiaoshan.edu.courseadjustment.service.ReplaceSectionService;
import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import com.xiaoshan.edu.vo.courseadjustment.AdjustmentOrReplaceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;

import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("replaceSectionService")
public class ReplaceSectionServiceImpl extends SqlBaseServiceImplV2Ext<ReplaceSectionDO, Long>
        implements ReplaceSectionService {

    @Lazy
    @Autowired
    private CourseReplaceService courseReplaceService;

    private final ReplaceSectionDao replaceSectionDao;

    public static final String MAP_OF_REPLACE_DATE = "replaceDate";

    public ReplaceSectionServiceImpl(@Qualifier("replaceSectionDao") ReplaceSectionDao replaceSectionDao) {
        super(replaceSectionDao);
        this.replaceSectionDao = replaceSectionDao;
    }

    @Override
    public List<ReplaceSectionDO> getListByReplaceId(Long id) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("replace_id", id);
        return replaceSectionDao.queryForList(wrapper);
    }

    @Override
    public ReplaceSectionDO loadById(Long id) {
        return replaceSectionDao.load(id);
    }

    @Override
    public List<ReplaceSectionDO> getByFatherId(Long id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("replace_father_id", id);
        return replaceSectionDao.queryForList(queryWrapper);
    }

    @Override
    public void removeByReplaceId(Long id) {
        DeleteWrapper deleteWrapper = new DeleteWrapper();
        deleteWrapper.eq("replace_id", id);
        replaceSectionDao.executeUpdate(deleteWrapper);
    }

    @Override
    public List<ReplaceSectionDO> getListByReplaceIds(List<Long> ids) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("replace_id",ids);
        return replaceSectionDao.queryForList(queryWrapper);
    }

    @Override
    public Set<Long> getReplaceIdsByCurriculumTableId(Long curriculumTableId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("curriculum_table_id", curriculumTableId);
        List<ReplaceSectionDO> replaceSectionDos = replaceSectionDao.queryForList(queryWrapper);
        if (replaceSectionDos == null || replaceSectionDos.isEmpty()){
            return null;
        }
        return replaceSectionDos.stream().map(ReplaceSectionDO::getReplaceId).collect(Collectors.toSet());
    }

    /**
     * 根据开始日期和结束日期获取代课id集合
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 代课id集合
     */
    @Override
    public Set<Long> getReplaceIdsByReplaceDateScope(Date startDate, Date endDate) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        List<ReplaceSectionDO> replaceSectionDos = replaceSectionDao.queryForListMapper("getReplaceIdsByReplaceDateScope", map);
        if (replaceSectionDos != null && !replaceSectionDos.isEmpty()) {
            return replaceSectionDos.stream().map(ReplaceSectionDO::getReplaceId).collect(Collectors.toSet());
        }
        return null;
    }

    @Override
    public Set<Long> getReplaceIdsByCourseDates(List<Date> dates) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("replace_date", dates);
        List<ReplaceSectionDO> replaceSectionDos = replaceSectionDao.queryForList(queryWrapper);
        if (replaceSectionDos == null || replaceSectionDos.isEmpty()){
            return null;
        }
        return replaceSectionDos.stream().map(ReplaceSectionDO::getReplaceId).collect(Collectors.toSet());
    }

    @Override
    public void setInvalidByReplaceIds(List<Long> ids) {
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.in("replace_id", ids).set("replace_is_invalid", true);
        replaceSectionDao.executeUpdate(updateWrapper);
    }

    /**
     * 根据代课id和课程时间获取代课详情（课表）
     */
    @Override
    public AdjustmentOrReplaceVO getReplaceByIdAndCourseTime(Long sectionId) {
        String courseTimeResult;
        Map<String, Object> map = new HashMap<>(2);
        map.put("id", sectionId);
        List<Map<String, Object>> result = replaceSectionDao.queryForMapMapper("getReplaceByIdAndCourseTime", map);
        if (result == null || result.isEmpty()) {
            return null;
        }
        Map<String, Object> resultMap = result.get(0);
        String courseTime1 = resultMap.get("courseTime").toString();
        int indexOf = courseTime1.indexOf("周周");
        AdjustmentOrReplaceVO vo = new AdjustmentOrReplaceVO();
        vo.setCourseName(resultMap.get("courseName").toString());
        vo.setTeacherName(resultMap.get("teacherName").toString());
        vo.setReplaceTeacherName(resultMap.get("replaceTeacherName").toString());
        vo.setCourseRoom(resultMap.get("courseRoom").toString());
        vo.setCourseRoomId(resultMap.get("courseRoomId").toString());
        vo.setSectionName(courseTime1.substring(indexOf + 3));
        if (resultMap.get(MAP_OF_REPLACE_DATE) != null) {
            java.util.Date replaceDate = DateUtils.stringToDate(resultMap.get("replaceDate").toString() + " 00:00:00");
            String s = DateUtils.formatDateMd(replaceDate);
            courseTimeResult = courseTime1.substring(indexOf + 1, indexOf + 3) + "（" + s + "）";
        } else {
            courseTimeResult = courseTime1.substring(indexOf + 1, indexOf + 3);
        }
        vo.setCourseTime(courseTimeResult);
        return vo;
    }

    @Override
    public Map<Long, Set<Long>> getReplaceOrReplacedCount(Map<String, Object> map, int type) {
        List<Map<String, Object>> getReplaceCount;
        if (type == 1) {
            // 代课次数
            getReplaceCount = replaceSectionDao.queryForMapMapper("getBeReplaceCount", map);
        } else {
            // 被代次数
            getReplaceCount = replaceSectionDao.queryForMapMapper("getToReplaceCount", map);
        }
        if (getReplaceCount != null && !getReplaceCount.isEmpty()) {
            Map<Long, Set<Long>> result = new HashMap<>(2);
            Set<Long> ids;
            for (Map<String, Object> objectMap : getReplaceCount) {
                Long teacherId = Long.valueOf(objectMap.get("teacherId").toString());
                Long replaceId = Long.valueOf(objectMap.get("replaceId").toString());
                if (result.get(teacherId) == null || result.get(teacherId).isEmpty()) {
                    ids = new HashSet<>();
                } else {
                    ids = result.get(teacherId);
                }
                ids.add(replaceId);
                result.put(teacherId, ids);
            }
            return result;
        }
        return null;
    }

    @Override
    public List<ReplaceSectionDO> getReplaceSectionByIsNotInvalid(Long teacherId, String courseTime) {
        List<CourseReplaceDO> replaceDos = courseReplaceService.getByReplaceTeacherAndIdIsNotInvalid(teacherId);
        if (replaceDos == null) {
            return null;
        }
        List<Long> ids = replaceDos.stream().map(CourseReplaceDO::getId).collect(Collectors.toList());
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("course_time", courseTime).eq("replace_is_invalid", "false").in("replace_id",ids);
        return replaceSectionDao.queryForList(queryWrapper);
    }
}
