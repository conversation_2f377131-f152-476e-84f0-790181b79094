package com.xiaoshan.edu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: klb
 * @createDate: 2020/9/11
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum ItemTypeEnum {


    /**
     *
     */
    WORKDAY(1, "日常教学"),

    WEEKEND(2, "周末"),

    HOLIDAY(3, "节假日"),

    TEST(4, "考生"),

    AGENDA(5, "日程"),

    ADJUSTMENT(6, "调休");

    private Integer type;

    private String desc;


}
