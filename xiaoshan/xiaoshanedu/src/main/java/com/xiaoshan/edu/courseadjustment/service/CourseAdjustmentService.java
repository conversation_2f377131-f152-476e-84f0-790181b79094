package com.xiaoshan.edu.courseadjustment.service;

import com.xiaoshan.basic.vo.FlowNodesListVO;
import com.xiaoshan.basic.vo.FlowVO;
import com.xiaoshan.edu.ao.courseadjustment.*;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import com.xiaoshan.edu.courseadjustment.entity.CourseReplaceDO;
import com.xiaoshan.edu.dto.DeleteRelatedDataDTO;
import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import com.xiaoshan.edu.enums.courseadjustment.UpdateType;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.model.ao.CourseAdjustmentApplyAO;
import com.xiaoshan.edu.model.ao.ReplaceCourseDetailAO;
import com.xiaoshan.edu.vo.courseadjustment.*;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CourseAdjustmentService extends SqlBaseService<CourseAdjustmentDO,Long> {

    /**
     * 课表调整撤销调代课
     * @param curriculumTableId 课表id
     * @param updateType 更新类型
     */
    void cancelAdjustment(Long curriculumTableId, UpdateType updateType);

    /**
     * 撤销相关日期的调代课
     * @param ao 日期集合
     */
    void schoolCalendarCancelAdjust(DateListAO ao);

    /**
     * 获取所有教师及其所教课程集合
     * @return 教师信息及所教课程集合
     */
    List<CourseTeacherDetailVO> getAllTeacherAndCourse();

    /**
     * 根据班级id和第几周以及课表id获取调代课记录（审批通过）【班级课表】
     * @param classId 班级id
     * @param number 周次
     * @param curriculumTableId 课表id
     * @return 班级课表
     */
    Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(Long classId, Integer number, Long curriculumTableId);

    /**
     * 根据教师ids和第几周以及课表id获取调代课记录（审批通过）【学生课表】
     * @param stuId
     * @param classId
     * @param teacherIds
     * @param number
     * @param curriculumTableId
     * @return
     */
    Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(Long stuId, Long classId, List<Long> teacherIds, Integer number, Long curriculumTableId);

    /**
     * 根据教师ids和第几周以及课表ids获取调代课记录（审批通过）
     * @param teacherIds
     * @param number
     * @param curriculumTableIds
     * @return
     */
    Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(List<Long> teacherIds, Integer number, List<Long> curriculumTableIds);

    /**
     * 根据教师ids和第几周以及课表ids获取调代课记录（审批通过）
     * @param teacherIds
     * @param numbers
     * @param curriculumTableIds
     * @return
     */
    Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> getRecordsByTeacherIdAndTimeScopeBatch(List<Long> teacherIds, List<Integer> numbers, List<Long> curriculumTableIds);

    /**
     * 根据教师ids和第几周以及课表ids获取代课记录（审批通过）【教室课表】
     * @param roomId
     * @param number
     * @param curriculumTableId
     * @return
     */
    Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(Long roomId, Integer number, List<Long> curriculumTableId);

    /**
     * pc管理-调课记录分页
     * @param ao
     * @return
     */
    QueryResult<List<CourseAdjustmentVO>> queryPage(AdjustmentPageAO ao);

    /**
     * pc个人-调课记录分页
     * @param ao
     * @return
     */
    QueryResult<List<CourseAdjustmentVO>> queryMyPage(AdjustmentPageAO ao);

    /**
     * 调课数据导出
     * @param ao
     * @param response
     */
    void adjustmentExport(AdjustmentPageAO ao, HttpServletResponse response);

    /**
     * 根据id删除调课记录（逻辑删除）
     * @param id
     */
    void removeCourseAdjustmentById(Long id);

    /**
     * 管理端-发起调课
     * @param ao
     * @return
     */
    ApplyAdjustmentVO applyAdjustment(CourseAdjustmentApplyAO ao);

    /**
     * 调代课统计分页（审批通过）
     * @param ao
     * @return
     */
    QueryResult<List<AdjustmentAndReplaceStatisticsPageVO>> statisticsPage(AdjustmentAndReplaceStatisticsPageAO ao);

    /**
     * 调代课统计数据导出
     * @param ao
     * @param response
     */
    void statisticsExport(AdjustmentAndReplaceStatisticsPageAO ao, HttpServletResponse response);

    /**
     * 单个教师在时间范围内调课记录分页（审批通过）
     * @param ao
     * @return
     */
    QueryResult<List<CourseAdjustmentVO>> singleAdjustmentPage(SinglePageAO ao);

    /**
     * 调代课统计单个教师调代课数据导出
     * @param ao
     * @param response
     */
    void singleRecordExport(SinglePageAO ao, HttpServletResponse response);

    /**
     * 调代课审批
     * @param ao
     */
    void approve(ApproveAO ao);

    /**
     * 模拟获取节点
     * @param ao
     * @return
     */
    FlowNodesListVO simulation(SimulationAO ao);

    /**
     * 催办
     * @param ao
     */
    void urging(AdjustmentUrgeAO ao);

    /**
     * 撤销
     * @param ao
     */
    void cancel(AdjustmentCancelAO ao);

    /**
     * 获取流程信息
     * @param relatedId
     * @param relatedType
     * @param adjustmentType
     * @return
     */
    FlowVO getFlowDetails(Long relatedId, Integer relatedType, AdjustmentType adjustmentType);

    /**
     * 根据id获取调课详情
     * @param id
     * @return
     */
    CourseAdjustmentVO getAdjustmentById(Long id);

    /**
     * 单个教师调代课申请记录分页
     * @param ao
     * @return
     */
    QueryResult<List<AdjustmentAndReplaceApplyRecordsVO>> getApplyPage(RecordsPageAO ao);

    /**
     * 根据调代课id和调代课类型获取调代课详情（课表）
     * @param ao
     * @return
     */
    AdjustmentOrReplaceVO getAdjustmentOrReplaceDetail(CourseTableAdjustmentAO ao);

    /**
     * 开始流程
     * @param ao
     * @param courseAdjustmentDO
     * @param courseReplaceDO
     */
    void startFlow(StartFlowAO ao, CourseAdjustmentDO courseAdjustmentDO, CourseReplaceDO courseReplaceDO);

    /**
     * 获取班级课表
     * @param classId 班级id
     * @param number 周次
     * @return 班级课表
     */
    ClassTableVO getClassTable(Long classId, Integer number);

    /**
     * 获取教师课表
     * @param teacherId 教师id
     * @param number 周次
     * @return 教师课表
     */
    TeacherTableVO getTeacherTable(Long teacherId, Integer number);

    /**
     * 判断该教师在该节次是否存在请假公出，若存在，则抛出异常
     * @param teacherId 教师id
     * @param courseStartTime 课程开始时间
     * @param courseEndTime 课程结束时间
     * @param courseTime 课程时间
     */
    void isLeavingOrBusiness(Long teacherId, String courseTime, String courseStartTime, String courseEndTime);

    /**
     * 判断该教师在该节次是否存在请假公出，返回在这些节次存在请假公出的教师
     * @param teacherIds 教师集合
     * @param replaceCourseDetailAos 包含课程时间、课程开始时间、课程结束时间
     * @return 返回在课程时间存在请假或公出的教师id
     */
    Set<Long> getLeavingOrBusinessTeacherIds(List<Long> teacherIds, List<ReplaceCourseDetailAO> replaceCourseDetailAos);

    /**
     * 判断该教师在该节次是否存在调课
     * @param teacherId 教师id
     * @param courseTime 课程时间
     */
    void isAdjusted(Long teacherId, String courseTime);

    /**
     * 判断教师在被调课时间是否存在课程，被调课老师在课程时间是否存在课程
     * @param teacherId
     * @param courseTime
     * @param adjustedTeacherId
     * @param adjustedCourseTime
     */
    void isAdjustConflict(Long teacherId, String courseTime, Long adjustedTeacherId, String adjustedCourseTime);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    CourseAdjustmentDO loadById(Long id);

    /**
     * 获取相关的id
     * @param id
     * @param dataType
     * @return
     */
    DeleteRelatedDataDTO getInterrelatedId(Long id, Integer dataType);

    /**
     * 删除相关联的数据
     * @param dto
     */
    void deleteInterrelatedData(DeleteRelatedDataDTO dto);

    /**
     * 调休更新回调调代课
     * @param ao
     */
    void sabbaticalCancelAdjust(SabbaticalCancelAdjustAO ao);


}
