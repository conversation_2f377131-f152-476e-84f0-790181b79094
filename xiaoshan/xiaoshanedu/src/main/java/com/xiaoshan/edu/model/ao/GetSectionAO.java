package com.xiaoshan.edu.model.ao;

import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.core.valid.annotation.number.LongValid;

import java.sql.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class GetSectionAO {
    @ApiModelProperty("教师id")
    @NotNull
    @LongValid
    private Long teacherId;

    @ApiModelProperty("开始日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date endDate;

    @ApiModelProperty(value = "开始日期节次", example = "第一节")
    @NotNull
    @NotEmpty
    private String startSection;

    @ApiModelProperty(value = "结束日期节次", example = "第二节")
    @NotNull
    @NotEmpty
    private String endSection;

    public Integer getStartSectionNumber() {
        return CourseAdjustmentUtils.chinese2Number(startSection.substring(1, 2));
    }

    public Integer getEndSectionNumber() {
        return CourseAdjustmentUtils.chinese2Number(endSection.substring(1, 2));
    }

}
