package com.xiaoshan.edu.courseadjustment.utils;

import com.common.utils.DateUtils;
import com.xiaoshan.basic.vo.AdjustmentVO;
import com.xiaoshan.basic.vo.ItemConfigVO;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import start.framework.commons.exception.BusinessException;

import java.util.*;

/**
 * 工具类
 *
 * <AUTHOR>
 */
public class CourseAdjustmentUtils {

    public static final String TYPE_ONE = "十零";
    public static final String TYPE_TWO = "一十";
    public static final Integer NUMBER_TWO = 2;
    public static final Integer NUMBER_THREE = 3;

    /**
     * 数字转中文
     */
    public static String number2Chinese(Integer number) {
        String str = number.toString();
        String[] s1 = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] s2 = {"十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"};
        StringBuilder result = new StringBuilder();
        int n = str.length();
        for (int i = 0; i < n; i++) {
            int num = str.charAt(i) - '0';
            if (i != n - 1 && num != 0) {
                result.append(s1[num]).append(s2[n - 2 - i]);
            } else {
                result.append(s1[num]);
            }
        }
        if (result.length() >= NUMBER_THREE && result.substring(0, NUMBER_THREE).contains(TYPE_ONE)) {
            result = new StringBuilder(result.substring(0, 2));
        }
        if (result.length() >= NUMBER_TWO && result.substring(0, NUMBER_TWO).equals(TYPE_TWO)) {
            result = new StringBuilder(result.substring(1));
        }
        return result.toString();
    }

    /**
     * 中文数字转阿拉伯数字
     */
    public static Integer chinese2Number(String chineseNumber) {
        int result = 0;
        int temp = 1;
        int count = 0;
        char[] cnArr = new char[]{'一', '二', '三', '四', '五', '六', '七', '八', '九'};
        char[] chArr = new char[]{'十', '百', '千', '万', '亿'};
        for (int i = 0; i < chineseNumber.length(); i++) {
            boolean b = true;
            char c = chineseNumber.charAt(i);
            for (int j = 0; j < cnArr.length; j++) {
                if (c == cnArr[j]) {
                    if (0 != count) {
                        result += temp;
                        temp = 1;
                        count = 0;
                    }
                    temp = j + 1;
                    b = false;
                    break;
                }
            }
            if (b) {
                for (int j = 0; j < chArr.length; j++) {
                    if (c == chArr[j]) {
                        switch (j) {
                            case 0:
                                temp *= 10;
                                break;
                            case 1:
                                temp *= 100;
                                break;
                            case 2:
                                temp *= 1000;
                                break;
                            case 3:
                                temp *= 10000;
                                break;
                            case 4:
                                temp *= 100000000;
                                break;
                            default:
                                break;
                        }
                        count++;
                    }
                }
            }
            if (i == chineseNumber.length() - 1) {
                result += temp;
            }
        }
        return result;
    }


    /**
     * 获取两段日期之间的日期
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<Date> findDates(Date dBegin, Date dEnd) {
        List<Date> lDate = new ArrayList<>();
        lDate.add(dBegin);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(calBegin.getTime());
        }
        return lDate;
    }

    /**
     * 日期加减天数
     *
     * @param date
     * @param dayNum
     * @return
     */
    public static Date getDatePlus(Date date, Integer dayNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, dayNum);
        return calendar.getTime();
    }

    /**
     * 根据课程时间获取课程日期
     *
     * @param weekConfigs
     * @param courseTime
     * @return
     */
    public static java.sql.Date getDateByCourseTime(Map<Integer, WeekConfigVO> weekConfigs, String courseTime) {
        WeekConfigVO weekConfigVO = weekConfigs.get(CourseTimeUtils.getWeekNumber(courseTime));
        List<java.sql.Date> betweenDate = DateUtils.getBetweenDate(weekConfigVO.getStartDay(), weekConfigVO.getEndDay());
        for (java.sql.Date date : betweenDate) {
            String dayOfWeek = DateUtils.getDayOfWeek(date);
            if (dayOfWeek.equals(CourseTimeUtils.getChineseDayOfWeek(courseTime))) {
                return date;
            }
        }
        return null;
    }

    public static Map<WeekEnum, List<WeekEnum>> getWeekEnumListMap(Map<WeekEnum, String> currentWeekMap, List<ItemConfigVO> items) {
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = new HashMap<>(10);
        for (ItemConfigVO item : items) {
            //3:节假日 6:调休
            if (item.getType() == 3 || item.getType() == 6) {
                for (AdjustmentVO day : item.getAdjustments()) {
                    String dStr = day.getDetailDate().toString();
                    if (currentWeekMap.containsValue(dStr)) {
                        WeekEnum kWeek = WeekEnum.getWeekMap().get(day.getDayOfWeek());
                        List<WeekEnum> weekList = compensatoryMap.computeIfAbsent(kWeek, k -> new ArrayList<>());
                        weekList.add(com.xiaoshan.edu.timetable.utils.DateUtils.getDayOfWeekByDate(dStr, currentWeekMap));
                    }
                }
            }
        }
        return compensatoryMap;
    }

    public static Map<WeekEnum, String> getWeekEnumStringMap(Map<Integer, WeekConfigVO> weekMap, Integer weekNumber) {
        WeekConfigVO weekVo = weekMap.get(weekNumber);
        if (weekVo == null) {
            throw new BusinessException(CodeRes.CODE_1000002);
        }
        //节假日调休课表变更逻辑
        return com.xiaoshan.edu.timetable.utils.DateUtils.currentWeekMap(weekVo.getStartDay(), weekVo.getEndDay());
    }
}
