package com.xiaoshan.edu.timetable.excel;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.xiaoshan.edu.timetable.utils.ExcelUtils;

/**
 * <AUTHOR>
 */
public class StudentMergeStrategy extends AbstractMergeStrategy {

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        ExcelUtils.setCell(sheet, cell);
    }

}
