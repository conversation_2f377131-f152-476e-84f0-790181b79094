package com.xiaoshan.edu.survey.service;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.edu.ao.survey.TaskAddAO;
import com.xiaoshan.edu.ao.survey.TaskPageAO;
import com.xiaoshan.edu.survey.entity.TaskDO;
import com.xiaoshan.edu.vo.survey.StudentTaskVO;
import com.xiaoshan.edu.vo.survey.TaskAddVO;
import com.xiaoshan.edu.vo.survey.TaskPageVO;
import com.xiaoshan.edu.vo.survey.TeacherTaskVO;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import java.util.List;

public interface TaskService extends SqlBaseService<TaskDO,Long> {

    /**
     * 新增
     * @param ao
     */
    void add(TaskAddAO ao);

    /**
     * 调查任务 -- 分页查询
     * @param ao
     * @return
     */
    QueryResult<List<TaskPageVO>> page(TaskPageAO ao);

    /**
     * 调查任务 -- id查询详细信息
     * @param taskId
     * @return
     */
    TaskAddVO getById(Long taskId);

    /**
     * 小程序 -- 调查任务
     * @param studentId
     * @return
     */
    List<StudentTaskVO> studentTaskList(Long studentId);

    /**
     * 编辑
     * @param ao
     */
    void edit(TaskAddAO ao);

    /**
     * 教师端 -- 调查任务列表
     * @return
     */
    List<TeacherTaskVO> teacherTaskList();

    /**
     * （公布/取消公布）结果
     * @param taskId
     */
    void publish(Long taskId);

    /**
     * 删除调查任务
     * @param taskId
     */
    void removeById(Long taskId);

    /**
     * 电子档案--教师调查任务列表
     * @param ao
     * @param teacherId
     * @return
     */
    QueryResult<List<TaskPageVO>> archives(Long teacherId, PageAO ao);
}
