package com.xiaoshan.edu.timetable.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.Closeable;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class StudentExportMergeStrategy extends AbstractMergeStrategy implements Closeable {

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        int row=cell.getRowIndex()%11;
        if(row==0) {
            if(cell.getColumnIndex()==0) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(relativeRowIndex,relativeRowIndex, 0, 7));
            }
        }
        CellStyle style = CellStyleFactory.style(cell);
        cell.setCellStyle(style);
        cell.getRow().setHeight((short) (300 * 2));
        cell.getSheet().setColumnWidth(cell.getColumnIndex(), 2048 * 2);
    }

    @Override
    public void close() throws IOException {
        CellStyleFactory.release();
    }

}
