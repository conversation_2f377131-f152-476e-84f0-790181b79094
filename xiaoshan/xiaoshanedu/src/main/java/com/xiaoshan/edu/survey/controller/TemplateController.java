package com.xiaoshan.edu.survey.controller;


import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.ao.survey.QuestionAO;
import com.xiaoshan.edu.ao.survey.TemplateAO;
import com.xiaoshan.edu.ao.survey.TemplateNameAO;
import com.xiaoshan.edu.survey.service.TemplateQuestionService;
import com.xiaoshan.edu.survey.service.TemplateService;
import com.xiaoshan.edu.vo.survey.QuestionImportVO;
import com.xiaoshan.edu.vo.survey.TemplateAndQuestionVO;
import com.xiaoshan.edu.vo.survey.TemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/v1/survey/template")
@Api(tags = "教学调查接口")
public class TemplateController extends BaseController {

	@Autowired
	private TemplateService templateService;

	@Autowired
	private TemplateQuestionService templateQuestionService;

	@ApiOperation("调查模板 -- 新建/编辑")
	@AuthenticationCheck
	@PostMapping("/addOrUpdate")
	public ResultResponse<Long> addOrUpdate(@RequestBody TemplateAO ao) {
		return response(templateService.saveOrUpdate(ao));
	}

	@ApiOperation("调查模板 -- 分页查询")
	@AuthenticationCheck
	@PostMapping("/page")
	public PageResponse<List<TemplateVO>> page(@RequestBody TemplateNameAO ao) {
		return response(templateService.page(ao));
	}

	@ApiOperation("调查模板 -- id查询详细内容")
	@AuthenticationCheck
	@GetMapping("/{id}")
	public ResultResponse<TemplateAndQuestionVO> getById(@PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long id) {
		return response(templateService.getById(id));
	}

	@ApiOperation("课程统计调查模板 -- id查询详细内容")
	@AuthenticationCheck
	@GetMapping("/curr/{id}/{taskId}")
	public ResultResponse<TemplateAndQuestionVO> getCurrById(@PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long id,
															 @PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId) {
		return response(templateService.getCurrById(id,taskId));
	}

	@ApiOperation("调查模板 -- 删除")
	@AuthenticationCheck
	@DeleteMapping("/{id}")
	public ResultResponse<Integer> remove(@PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long id) {
		return response(templateService.removeById(id));
	}

	@ApiOperation("模板内容 -- 下载模Excel模板")
	@GetMapping("/download")
	public void download(HttpServletResponse response){
		templateService.downloadTemplate(response);
	}

	@ApiOperation("模板内容 -- 导出")
	@GetMapping("/export/{id}")
	public void export(HttpServletResponse response, @PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long id) {
		templateQuestionService.export(response, id);
	}


	@ApiOperation("模板内容 -- 批量添加（编辑）调查题目")
	@AuthenticationCheck
	@PostMapping("/addOrUpdate/{templateId}")
	public BaseResponse addOrUpdate(@PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long templateId,
									@RequestBody List<QuestionAO> ao) {
		templateQuestionService.addOrUpdate(templateId, ao);
		return response();
	}

	@ApiOperation("模板内容 -- 导入模板内容")
	@PostMapping("/import/{templateId}")
	public ResultResponse<List<QuestionImportVO>> importTemplate(@PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long templateId,
																 @PathVariable @ApiParam("模板文件") @NotNull MultipartFile file) {
		return response(templateQuestionService.importTemplate(templateId, file));
	}

}
