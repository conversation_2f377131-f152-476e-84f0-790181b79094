package com.xiaoshan.edu.courseadjustment.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Joiner;
import com.xiaoshan.basic.enums.TaskApprovalStateEnum;
import com.xiaoshan.basic.vo.*;
import com.xiaoshan.edu.ao.courseadjustment.*;
import com.xiaoshan.edu.api.facade.FlowFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.courseadjustment.dao.CourseReplaceDao;
import com.xiaoshan.edu.courseadjustment.mapper.CourseAdjustmentConvert;
import com.xiaoshan.edu.courseadjustment.utils.CourseTimeUtils;
import com.xiaoshan.edu.dto.*;
import com.xiaoshan.edu.enums.courseadjustment.*;
import com.xiaoshan.edu.model.ao.*;
import com.xiaoshan.edu.model.bo.CourseAdjustmentBO;
import com.xiaoshan.edu.model.bo.ToBeProcessCourseInfoBO;
import com.xiaoshan.edu.model.dto.CourseAdjustmentFlowDTO;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.timetable.utils.TeacherTableUtils;
import com.xiaoshan.edu.vo.courseadjustment.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.common.utils.DateUtils;
import com.xiaoshan.basic.ao.NewWorkFlowAO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.courseadjustment.dao.CourseAdjustmentDao;
import com.xiaoshan.edu.courseadjustment.dto.ReplaceAndSectionDTO;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import com.xiaoshan.edu.courseadjustment.entity.CourseReplaceDO;
import com.xiaoshan.edu.courseadjustment.entity.ReplaceSectionDO;
import com.xiaoshan.edu.courseadjustment.excel.CustomCellWriteHandler;
import com.xiaoshan.edu.courseadjustment.excel.ExcelHeadsUtil;
import com.xiaoshan.edu.courseadjustment.excel.export.AdjustmentRecordExport;
import com.xiaoshan.edu.courseadjustment.excel.export.StatisticsAdjustmentAndReplaceExport;
import com.xiaoshan.edu.courseadjustment.excel.export.StatisticsPersonalAdjustmentExport;
import com.xiaoshan.edu.courseadjustment.excel.export.StatisticsPersonalReplaceExport;
import com.xiaoshan.edu.courseadjustment.service.CourseAdjustmentService;
import com.xiaoshan.edu.courseadjustment.service.CourseReplaceService;
import com.xiaoshan.edu.courseadjustment.service.ReplaceSectionService;
import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.service.BasicPushMessage;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.timetable.GeneralTableVO;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherTableVO;
import com.xiaoshan.oa.dto.ClassDTO;
import com.xiaoshan.oa.dto.TeacherDto;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.MapConvert;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("courseAdjustmentService")
public class CourseAdjustmentServiceImpl extends SqlBaseServiceImplV2Ext<CourseAdjustmentDO, Long>
        implements CourseAdjustmentService {

    @Autowired
    private CourseAdjustmentDao courseAdjustmentDao;

    @Autowired
    private StartApprovalAsncy startApprovalAsncy;

    @Autowired
    private FoundationFacade foundationFacade;

    @Autowired
    private SettingFacade settingFacade;

    @Autowired
    private FlowFacade flowFacade;

    @Autowired
    private ReplaceSectionService replaceSectionService;

    @Autowired
    private CurriculumTableService curriculumTableService;

    @Autowired
    private CurriculumService curriculumService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private BasicPushMessage basicPushMessage;

    @Autowired
    private CourseReplaceDao courseReplaceDao;

    @Lazy
    @Autowired
    private CourseReplaceService courseReplaceService;

    @Lazy
    @Autowired
    private GeneralTableService generalTableService;

    public static final String FLOW_IS_START = "1";
    public static final Integer NUMBER_TWO = 2;

    @Value("${xiaoshan.push_item.message.edu_course_adjustment_approval_remind}")
    private String urgePushItemCode;
    @Value("${xiaoshan.course_admin_adjustment.source_id}")
    private Integer adjustmentSourceId;
    @Value("${xiaoshan.course_admin_replace.source_id}")
    private Integer replaceSourceId;
    @Value("${xiaoshan.course_adjustment.related_type}")
    private Integer relatedType;
    @Value("${xiaoshan.miniProgramUrl.edu_course_adjustment.detail.path}")
    private String miniProgramUrl;

    public CourseAdjustmentServiceImpl(@Qualifier("courseAdjustmentDao") CourseAdjustmentDao courseAdjustmentDao) {
        super(courseAdjustmentDao);
        this.courseAdjustmentDao = courseAdjustmentDao;
    }


    /**
     * 课表变更导致调代课变更
     * 如果因课表调整而调代课冲突则对 审批中 或 审批通过未到调代课时间 的调代课进行撤销（若课表删除直接撤销所有涉及的调代课、若课表更新则查询涉及的审批中或审批通过但未开始调代课数据，对比更新后的课表，如不一致则撤销）
     *
     * @param curriculumTableId
     * @param updateType
     */
    private void invalidByTableChanged(Long curriculumTableId, UpdateType updateType) {
        ToBeProcessCourseInfoBO toBeProcessCourseInfoBO = new ToBeProcessCourseInfoBO();
        Integer invalidType = InvalidType.TABLE_CHANGED.getType();
        changeInvalidStateByTable(curriculumTableId, updateType, toBeProcessCourseInfoBO);
        processCancelOrInvalidData(toBeProcessCourseInfoBO, invalidType);
    }

    /**
     * 校历变更导致调代课变更
     * 如果因课表调整而调代课冲突则对 审批中 或 审批通过未到调代课时间 的调代课进行撤销（若课表删除直接撤销所有涉及的调代课、若课表更新则查询涉及的审批中或审批通过但未开始调代课数据，对比更新后的课表，如不一致则撤销）
     *
     * @param changedDates
     */
    private void invalidByCalendarChanged(DateListAO changedDates) {
        ToBeProcessCourseInfoBO toBeProcessCourseInfoBO = new ToBeProcessCourseInfoBO();
        Integer invalidType = InvalidType.CALENDAR_CHANGED.getType();
        changeInvalidStateByCalendar(changedDates, toBeProcessCourseInfoBO);
        processCancelOrInvalidData(toBeProcessCourseInfoBO, invalidType);
    }


    /**
     * 处理取消或者失效的数据
     *
     * @param toBeProcessCourseInfoBO
     * @param invalidType
     */
    private void processCancelOrInvalidData(ToBeProcessCourseInfoBO toBeProcessCourseInfoBO, Integer invalidType) {
        Map<Long, Long> map = getTeacherInfoMap(toBeProcessCourseInfoBO);
        // 更新、异常失效、发送消息
        // 根据调代课id，更新为异常状态
        // 调课
        if (!toBeProcessCourseInfoBO.getNeedCancelCourseAdjustmentDoList().isEmpty()) {
            List<Long> ids = toBeProcessCourseInfoBO.getNeedCancelCourseAdjustmentDoList().stream().map(CourseAdjustmentDO::getId).collect(Collectors.toList());
            // 更新 异常
            for (CourseAdjustmentDO courseAdjustmentDO : toBeProcessCourseInfoBO.getNeedCancelCourseAdjustmentDoList()) {
                // 父数据置为有效
                adjustmentFatherSetIsInvalidFalse(courseAdjustmentDO, false);
            }
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.in("id", ids).set("is_unusual", true).set("is_not_statistics", true).set("is_invalid", true).set("adjusted_is_invalid", true);
            courseAdjustmentDao.executeUpdate(updateWrapper);
            sendAdjustCancelMessage(toBeProcessCourseInfoBO.getNeedCancelCourseAdjustmentDoList(), invalidType, map);
        }

        if (!toBeProcessCourseInfoBO.getNeedInvalidCourseAdjustmentDoList().isEmpty()) {
            List<Long> ids = toBeProcessCourseInfoBO.getNeedInvalidCourseAdjustmentDoList().stream().map(CourseAdjustmentDO::getId).collect(Collectors.toList());
            for (CourseAdjustmentDO courseAdjustmentDO : toBeProcessCourseInfoBO.getNeedInvalidCourseAdjustmentDoList()) {
                // 父数据置为有效
                adjustmentFatherSetIsInvalidFalse(courseAdjustmentDO, false);
            }
            // 更新 失效
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.in("id", ids).set("is_invalid", true).set("adjusted_is_invalid", true);
            courseAdjustmentDao.executeUpdate(updateWrapper);
        }


        // 代课
        if (!toBeProcessCourseInfoBO.getNeedCancelReplaceAndSectionDtoSet().isEmpty()) {
            List<Long> ids = toBeProcessCourseInfoBO.getNeedCancelReplaceAndSectionDtoSet().stream().map(ReplaceAndSectionDTO::getId).collect(Collectors.toList());
            // 更新（失效异常）
            courseReplaceService.updateStartByIds(ids, true);
            sendReplaceCancelMessage(toBeProcessCourseInfoBO, invalidType, map);
        }

        if (!toBeProcessCourseInfoBO.getNeedInvalidReplaceAndSectionDtoSet().isEmpty()) {
            List<Long> ids = toBeProcessCourseInfoBO.getNeedInvalidReplaceAndSectionDtoSet().stream().map(ReplaceAndSectionDTO::getId).collect(Collectors.toList());
            // 更新 （失效）
            courseReplaceService.updateStartByIds(ids, false);
        }
    }

    /**
     * 发送代课取消消息
     *
     * @param toBeProcessCourseInfoBO
     * @param invalidType
     * @param map
     */
    private void sendReplaceCancelMessage(ToBeProcessCourseInfoBO toBeProcessCourseInfoBO, Integer invalidType, Map<Long, Long> map) {
        for (ReplaceAndSectionDTO needCancelReplaceAndSectionDTO : toBeProcessCourseInfoBO.getNeedCancelReplaceAndSectionDtoSet()) {
            Long id = needCancelReplaceAndSectionDTO.getId();
            // 根据调代课id发送撤销消息
            AdjustmentCancelMessageAO adjustmentCancelMessageAo = new AdjustmentCancelMessageAO();
            adjustmentCancelMessageAo.setType(invalidType);
            adjustmentCancelMessageAo.setAdjustmentId(id);
            adjustmentCancelMessageAo.setAdjustmentType(AdjustmentType.REPLACE);
            adjustmentCancelMessageAo.setReplaceCourseTimeList(needCancelReplaceAndSectionDTO.getReplaceSectionDoList().stream().map(ReplaceSectionDO::getCourseTime).collect(Collectors.toList()));
            adjustmentCancelMessageAo.setCourseTeacherUserId(map.get(needCancelReplaceAndSectionDTO.getTeacherId()));
            adjustmentCancelMessageAo.setReverseTeacherUserId(map.get(needCancelReplaceAndSectionDTO.getReplaceTeacherId()));
            basicPushMessage.adjustmentCancelPublish(adjustmentCancelMessageAo);
        }
    }

    /**
     * 撤销审批流 审批中的数据，发送消息
     *
     * @param needCancelCourseAdjustmentDoList
     * @param invalidType
     * @param map
     */
    private void sendAdjustCancelMessage(List<CourseAdjustmentDO> needCancelCourseAdjustmentDoList, Integer invalidType, Map<Long, Long> map) {
        for (CourseAdjustmentDO needCancelCourseAdjustmentDO : needCancelCourseAdjustmentDoList) {
            Long id = needCancelCourseAdjustmentDO.getId();
            // 根据调代课id发送撤销消息
            AdjustmentCancelMessageAO adjustmentCancelMessageAo = new AdjustmentCancelMessageAO();
            adjustmentCancelMessageAo.setType(invalidType);
            adjustmentCancelMessageAo.setAdjustmentId(id);
            adjustmentCancelMessageAo.setAdjustmentType(AdjustmentType.ADJUSTMENT);
            adjustmentCancelMessageAo.setCourseTime(needCancelCourseAdjustmentDO.getCourseTime());
            adjustmentCancelMessageAo.setAdjustedCourseTime(needCancelCourseAdjustmentDO.getAdjustedCourseTime());
            adjustmentCancelMessageAo.setCourseTeacherUserId(map.get(needCancelCourseAdjustmentDO.getTeacherId()));
            adjustmentCancelMessageAo.setReverseTeacherUserId(map.get(needCancelCourseAdjustmentDO.getAdjustedTeacherId()));
            basicPushMessage.adjustmentCancelPublish(adjustmentCancelMessageAo);
        }
    }

    /**
     * 获取教师信息map
     *
     * @param toBeProcessCourseInfoBO
     * @return
     */
    private Map<Long, Long> getTeacherInfoMap(ToBeProcessCourseInfoBO toBeProcessCourseInfoBO) {
        Set<Long> teacherIds = new HashSet<>(10);
        if (!toBeProcessCourseInfoBO.getNeedCancelCourseAdjustmentDoList().isEmpty()) {
            for (CourseAdjustmentDO needCancelCourseAdjustmentDO : toBeProcessCourseInfoBO.getNeedCancelCourseAdjustmentDoList()) {
                teacherIds.add(needCancelCourseAdjustmentDO.getTeacherId());
                if (needCancelCourseAdjustmentDO.getAdjustedTeacherId() != null) {
                    teacherIds.add(needCancelCourseAdjustmentDO.getAdjustedTeacherId());
                }
            }
        }
        if (!toBeProcessCourseInfoBO.getNeedCancelReplaceAndSectionDtoSet().isEmpty()) {
            for (ReplaceAndSectionDTO needCancelReplaceAndSectionDTO : toBeProcessCourseInfoBO.getNeedCancelReplaceAndSectionDtoSet()) {
                teacherIds.add(needCancelReplaceAndSectionDTO.getTeacherId());
                if (needCancelReplaceAndSectionDTO.getReplaceTeacherId() != null) {
                    teacherIds.add(needCancelReplaceAndSectionDTO.getReplaceTeacherId());
                }
            }
        }
        if (!teacherIds.isEmpty()) {
            String s = StringUtils.listToString(teacherIds);
            List<TeacherDto> teacherDtoList = foundationFacade.getAllTeacherByIds(s);
            return teacherDtoList.stream().collect(Collectors.toMap(TeacherDto::getId, TeacherDto::getUserId));
        }
        return Collections.emptyMap();
    }

    /**
     * 课表变更　失效状态处理
     *
     * @param curriculumTableId
     * @param updateType
     * @param toBeProcessCourseInfoBO
     */
    private void changeInvalidStateByTable(Long curriculumTableId, UpdateType updateType, ToBeProcessCourseInfoBO toBeProcessCourseInfoBO) {
        if (curriculumTableId != null && updateType != null) {
            if (updateType.equals(UpdateType.DELETE)) {
                // 课表删除，相关的调代课异常
                QueryWrapper queryWrapper = new QueryWrapper();
                List<CourseAdjustmentDO> courseAdjustmentDoList = getNormalCourseAdjustmentDOList(curriculumTableId, queryWrapper);
                List<ReplaceAndSectionDTO> replaceAndSectionDtoList = courseReplaceService.getReplaceAndSectionByCurriculumTableId(curriculumTableId);
                addNeedCancelAdjustAndReplace(toBeProcessCourseInfoBO, courseAdjustmentDoList, replaceAndSectionDtoList);
            } else {
                // 课表更新，遍历课表，对比异同，数据不同就异常失效
                // 获取更新后的课表
                List<GeneralTableDO> generalTableDOList = generalTableService.queryByCurriculumTableId(curriculumTableId);
                // 获取课程全称和课程简称
                Map<String, String> courseNameMap = curriculumService.aliasMap(true);
                Set<CourseAdjustmentDO> haveChangeAdjust = new HashSet<>();
                Set<ReplaceAndSectionDTO> haveChangeReplace = new HashSet<>();
                List<CourseAdjustmentDO> courseAdjustmentDoList = getNullDeletedCourseAdjustmentDOList(curriculumTableId);

                // 获取代课记录【未删除，审批中或已通过、父ID或者被调父ID为0】
                List<ReplaceAndSectionDTO> replaceData = courseReplaceService.getReplaceAndSectionByCurriculumTableId(curriculumTableId);
                List<ReplaceAndSectionDTO> replaceAndSectionDtoList = getNoParentData(replaceData);

                // 针对代课老师课表同节次新增冲突失效
                List<ReplaceAndSectionDTO> replaceData1 = courseReplaceService.getReplaceAndSectionByReplaceCurriculumTableId(curriculumTableId);
                List<ReplaceAndSectionDTO> validReplaceAndSectionDTOList = getValidDate(replaceData1);

                if (!CollectionUtils.isEmpty(generalTableDOList)) {
                    // 遍历新课表 对比异同
                    for (GeneralTableDO generalTableDO : generalTableDOList) {
                        // 单双周
                        // 过滤单双周（如果分单双周，则有两条数据，过滤，如果不分单双周则有一条数据，不过滤）
                        WeekTypeEnum type = generalTableDO.getType();
                        List<CourseAdjustmentDO> filterWeekAdjust;
                        List<ReplaceAndSectionDTO> filterWeekToBeReplaced;
                        List<ReplaceAndSectionDTO> filterWeekReplace;
                        if (generalTableDOList.size() == 1) {
                            filterWeekAdjust = courseAdjustmentDoList;
                            filterWeekToBeReplaced = replaceAndSectionDtoList;
                            filterWeekReplace = validReplaceAndSectionDTOList;
                        } else {
                            filterWeekAdjust = new ArrayList<>();
                            filterWeekToBeReplaced = new ArrayList<>();
                            filterWeekReplace = new ArrayList<>();
                            // 调课
                            filterAdjust(courseAdjustmentDoList, type, filterWeekAdjust);
                            // 被代课
                            filterToBeReplaced(replaceAndSectionDtoList, type, filterWeekToBeReplaced);
                            // 代课
                            filterReplace(validReplaceAndSectionDTOList, type, filterWeekReplace);
                        }
                        Map<String, List<TeacherTableDTO>> teacherTable = generalTableDO.getTeacherTable();

                        // 遍历调代课数据
                        // 调课
                        getHaveChangeAdjust(courseNameMap, haveChangeAdjust, filterWeekAdjust, teacherTable);
                        // 被代课
                        getHaveChangeToBoReplaced(courseNameMap, haveChangeReplace, filterWeekToBeReplaced, teacherTable);
                        // 代课
                        getHaveChangeReplace(courseNameMap, haveChangeReplace, filterWeekReplace, teacherTable);

                    }
                    List<CourseAdjustmentDO> resultHaveChangeAdjust = new ArrayList<>(haveChangeAdjust);
                    List<ReplaceAndSectionDTO> resultHaveChangeReplace = new ArrayList<>(haveChangeReplace);
                    // 记录符合条件的调代课id
                    addNeedCancelAdjustAndReplace(toBeProcessCourseInfoBO, resultHaveChangeAdjust, resultHaveChangeReplace);
                }
            }

            // 针对教师从课表删除的处理 或 老师从一门课变成教另一门课的处理
            // 获取所有非异常的代课数据
            // 获取代课记录
            List<ReplaceAndSectionDTO> replaceData = courseReplaceService.getReplaceAndSectionDtos(null);
            // 获取所有教师及其所教的课程
            Map<Long, CourseTeacherDetailVO> longCourseTeacherDetailVoMap = getLongCourseTeacherDetailVoMap();
            if (replaceData != null && !replaceData.isEmpty() && longCourseTeacherDetailVoMap != null && !longCourseTeacherDetailVoMap.isEmpty()) {
                Set<ReplaceAndSectionDTO> haveChangeReplace = new HashSet<>();
                for (ReplaceAndSectionDTO replaceDatum : replaceData) {
                    Long replaceTeacherId = replaceDatum.getReplaceTeacherId();
                    String courseName = replaceDatum.getCourseName();
                    CourseTeacherDetailVO courseTeacherDetailVO = longCourseTeacherDetailVoMap.get(replaceTeacherId);
                    if (courseTeacherDetailVO == null) {
                        haveChangeReplace.add(replaceDatum);
                    } else {
                        if (!courseTeacherDetailVO.getCourseName().equals(courseName)) {
                            haveChangeReplace.add(replaceDatum);
                        }
                    }
                }
                List<ReplaceAndSectionDTO> resultHaveChangeReplace = new ArrayList<>(haveChangeReplace);
                // 记录符合条件的调代课id
                addNeedCancelAdjustAndReplace(toBeProcessCourseInfoBO, null, resultHaveChangeReplace);
            }
        }
    }

    /**
     * 校历变更失效状态处理
     *
     * @param dateListAo
     */
    private void changeInvalidStateByCalendar(DateListAO dateListAo, ToBeProcessCourseInfoBO toBeProcessCourseInfoBO) {
        if (dateListAo != null) {
            List<String> changedDates = dateListAo.getDates();
            if (!CollectionUtils.isEmpty(changedDates)) {
                List<java.sql.Date> dates = getFormatDates(changedDates);
                List<java.sql.Date> sabbaticalDate = getSabbaticalDate(dates);
                if (!sabbaticalDate.isEmpty()) {
                    dates.addAll(sabbaticalDate);
                }
                List<CourseAdjustmentDO> courseAdjustmentDOList = getCourseAdjustmentDOList(dates);
                List<ReplaceAndSectionDTO> replaceAndSectionDtoList = courseReplaceService.getReplaceAndSectionByCourseDates(dates);
                addNeedCancelAdjustAndReplace(toBeProcessCourseInfoBO, courseAdjustmentDOList, replaceAndSectionDtoList);
            }
        }
    }

    private void getHaveChangeReplace(Map<String, String> courseNameMap, Set<ReplaceAndSectionDTO> haveChangeReplace, List<ReplaceAndSectionDTO> filterWeekReplace, Map<String, List<TeacherTableDTO>> teacherTable) {
        if (!filterWeekReplace.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : filterWeekReplace) {
                String courseName = replaceAndSectionDTO.getCourseName();
                String abbreviation = courseNameMap.get(courseName);
                Long teacherId = replaceAndSectionDTO.getReplaceTeacherId();
                List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        Boolean replaceIsInvalid = replaceSectionDO.getReplaceIsInvalid();
                        if (!replaceIsInvalid) {
                            String courseTime = replaceSectionDO.getCourseTime();
                            WeekEnum dayOfWeek = CourseTimeUtils.getDayOfWeek(courseTime);
                            String section = CourseTimeUtils.getSection(courseTime);
                            Boolean check = TeacherTableUtils.check(teacherTable, dayOfWeek, section, abbreviation, teacherId);
                            if (check) {
                                // 代课老师此节次新增了课程
                                haveChangeReplace.add(replaceAndSectionDTO);
                            }
                        }
                    }
                }
            }
        }
    }

    private void getHaveChangeToBoReplaced(Map<String, String> courseNameMap, Set<ReplaceAndSectionDTO> haveChangeReplace, List<ReplaceAndSectionDTO> filterWeekToBeReplaced, Map<String, List<TeacherTableDTO>> teacherTable) {
        if (!filterWeekToBeReplaced.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : filterWeekToBeReplaced) {
                String courseName = replaceAndSectionDTO.getCourseName();
                Long teacherId = replaceAndSectionDTO.getTeacherId();
                String abbreviation = courseNameMap.get(courseName);
                List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        Long replaceFatherId = replaceSectionDO.getReplaceFatherId();
                        if (replaceFatherId == 0) {
                            String courseRoomId = replaceSectionDO.getCourseRoomId();
                            String courseTime = replaceSectionDO.getBeforeOriginCourseTime();
                            WeekEnum dayOfWeek = CourseTimeUtils.getDayOfWeek(courseTime);
                            String section = CourseTimeUtils.getSection(courseTime);
                            Boolean check = TeacherTableUtils.check(teacherTable, dayOfWeek, section, abbreviation, teacherId, courseRoomId);
                            if (!check) {
                                haveChangeReplace.add(replaceAndSectionDTO);
                            }
                        }
                    }
                }
            }
        }
    }

    private void getHaveChangeAdjust(Map<String, String> courseNameMap, Set<CourseAdjustmentDO> haveChangeAdjust, List<CourseAdjustmentDO> filterWeekAdjust, Map<String, List<TeacherTableDTO>> teacherTable) {
        if (!filterWeekAdjust.isEmpty()) {
            for (CourseAdjustmentDO courseAdjustmentDO : filterWeekAdjust) {
                Long fatherId = courseAdjustmentDO.getFatherId();
                Long adjustedFatherId = courseAdjustmentDO.getAdjustedFatherId();
                if (fatherId == 0) {
                    Long teacherId = courseAdjustmentDO.getTeacherId();
                    String courseRoomId = courseAdjustmentDO.getCourseRoomId();
                    String courseTime = courseAdjustmentDO.getBeforeOriginCourseTime();
                    String courseName = courseAdjustmentDO.getCourseName();
                    String abbreviation = courseNameMap.get(courseName);
                    WeekEnum dayOfWeek = CourseTimeUtils.getDayOfWeek(courseTime);
                    String section = CourseTimeUtils.getSection(courseTime);
                    Boolean check = TeacherTableUtils.check(teacherTable, dayOfWeek, section, abbreviation, teacherId, courseRoomId);
                    if (!check) {
                        haveChangeAdjust.add(courseAdjustmentDO);
                    }
                }
                if (adjustedFatherId == 0) {
                    String adjustedCourseTime = courseAdjustmentDO.getAdjustedBeforeOriginCourseTime();
                    if (!StringUtils.isEmpty(adjustedCourseTime)) {
                        // 非自修
                        Long teacherId = courseAdjustmentDO.getAdjustedTeacherId();
                        String adjustedCourseRoomId = courseAdjustmentDO.getAdjustedCourseRoomId();
                        String adjustedCourseName = courseAdjustmentDO.getAdjustedCourseName();
                        String adjustedAbbreviation = courseNameMap.get(adjustedCourseName);
                        WeekEnum dayOfWeek = CourseTimeUtils.getDayOfWeek(adjustedCourseTime);
                        String section = CourseTimeUtils.getSection(adjustedCourseTime);
                        Boolean check = TeacherTableUtils.check(teacherTable, dayOfWeek, section, adjustedAbbreviation, teacherId, adjustedCourseRoomId);
                        if (!check) {
                            haveChangeAdjust.add(courseAdjustmentDO);
                        }
                    }
                }
            }
        }
    }

    private void filterReplace(List<ReplaceAndSectionDTO> validReplaceAndSectionDTOList, WeekTypeEnum type, List<ReplaceAndSectionDTO> filterWeekReplace) {
        if (!validReplaceAndSectionDTOList.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : validReplaceAndSectionDTOList) {
                List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        String courseTime = replaceSectionDO.getCourseTime();
                        Integer weekNumber = CourseTimeUtils.getWeekNumber(courseTime);
                        WeekTypeEnum typeEnum = weekNumber % 2 == 0 ? WeekTypeEnum.DOUBLE : WeekTypeEnum.SINGLE;
                        if (type.equals(typeEnum)) {
                            filterWeekReplace.add(replaceAndSectionDTO);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void filterToBeReplaced(List<ReplaceAndSectionDTO> replaceAndSectionDtoList, WeekTypeEnum type, List<ReplaceAndSectionDTO> filterWeekToBeReplaced) {
        if (!replaceAndSectionDtoList.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : replaceAndSectionDtoList) {
                List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        String courseTime = replaceSectionDO.getCourseTime();
                        Integer weekNumber = CourseTimeUtils.getWeekNumber(courseTime);
                        WeekTypeEnum typeEnum = weekNumber % 2 == 0 ? WeekTypeEnum.DOUBLE : WeekTypeEnum.SINGLE;
                        if (type.equals(typeEnum)) {
                            filterWeekToBeReplaced.add(replaceAndSectionDTO);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void filterAdjust(List<CourseAdjustmentDO> courseAdjustmentDoList, WeekTypeEnum type, List<CourseAdjustmentDO> filterWeekAdjust) {
        if (!courseAdjustmentDoList.isEmpty()) {
            for (CourseAdjustmentDO courseAdjustmentDO : courseAdjustmentDoList) {
                String courseTime = courseAdjustmentDO.getCourseTime();
                Integer weekNumber = CourseTimeUtils.getWeekNumber(courseTime);
                WeekTypeEnum typeEnum = weekNumber % 2 == 0 ? WeekTypeEnum.DOUBLE : WeekTypeEnum.SINGLE;
                if (type.equals(typeEnum)) {
                    filterWeekAdjust.add(courseAdjustmentDO);
                    continue;
                }
                String adjustedCourseTime = courseAdjustmentDO.getAdjustedCourseTime();
                if (!StringUtils.isEmpty(adjustedCourseTime)) {
                    Integer adjustedWeekNumber = CourseTimeUtils.getWeekNumber(adjustedCourseTime);
                    WeekTypeEnum adjustedTypeEnum = adjustedWeekNumber % 2 == 0 ? WeekTypeEnum.DOUBLE : WeekTypeEnum.SINGLE;
                    if (type.equals(adjustedTypeEnum)) {
                        filterWeekAdjust.add(courseAdjustmentDO);
                    }
                }
            }
        }
    }

    /**
     * 获取未失效的节次
     *
     * @param replaceData1
     * @return
     */
    private List<ReplaceAndSectionDTO> getValidDate(List<ReplaceAndSectionDTO> replaceData1) {
        List<ReplaceAndSectionDTO> validReplaceAndSectionDTOList = new ArrayList<>();
        // 过滤出代课节次代课未失效的节次
        if (replaceData1 != null && !replaceData1.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : replaceData1) {
                List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        if (!replaceSectionDO.getReplaceIsInvalid()) {
                            validReplaceAndSectionDTOList.add(replaceAndSectionDTO);
                            break;
                        }
                    }
                }
            }
        }
        return validReplaceAndSectionDTOList;
    }

    /**
     * 过滤出代课节次 父ID 为0的数据
     *
     * @param replaceData
     * @return
     */
    private List<ReplaceAndSectionDTO> getNoParentData(List<ReplaceAndSectionDTO> replaceData) {
        List<ReplaceAndSectionDTO> replaceAndSectionDtoList = new ArrayList<>();
        if (replaceData != null && !replaceData.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : replaceData) {
                List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        if (replaceSectionDO.getReplaceFatherId() == 0) {
                            replaceAndSectionDtoList.add(replaceAndSectionDTO);
                            break;
                        }
                    }
                }
            }
        }
        return replaceAndSectionDtoList;
    }

    /**
     * 根据课表ID获取未删除的调代课记录【未删除，审批中或已通过、父ID或者被调父ID为0】
     * 获取调课记录
     *
     * @param curriculumTableId
     * @return
     */
    private List<CourseAdjustmentDO> getNullDeletedCourseAdjustmentDOList(Long curriculumTableId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("is_unusual", "false")
                .and(w -> w.eq("approval_state", ApprovalState.PASS.name()).or().eq("approval_state", ApprovalState.UNDER_APPROVAL.name()))
                .and(w -> w.eq("curriculum_table_id", curriculumTableId).or().eq("adjusted_curriculum_table_id", curriculumTableId))
                .and(w -> w.eq("father_id", 0).or().eq("adjusted_father_id", 0));
        return this.queryForMap(CourseAdjustmentDO.class, queryWrapper);
    }

    /**
     * 过滤已经异常的数据,获取正常数据
     *
     * @param curriculumTableId
     * @param queryWrapper
     * @return
     */
    private List<CourseAdjustmentDO> getNormalCourseAdjustmentDOList(Long curriculumTableId, QueryWrapper queryWrapper) {
        queryWrapper.eq("is_unusual", "false")
                .and(w -> w.eq("curriculum_table_id", curriculumTableId).or().eq("adjusted_curriculum_table_id", curriculumTableId))
                .and(w -> w.eq("approval_state", ApprovalState.PASS.name()).or().eq("approval_state", ApprovalState.UNDER_APPROVAL.name()));
        return courseAdjustmentDao.queryForList(queryWrapper);
    }

    /**
     * 查询相关日期的调课信息
     *
     * @param dates
     * @return
     */
    private List<CourseAdjustmentDO> getCourseAdjustmentDOList(List<java.sql.Date> dates) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("is_unusual", "false")
                .and(w -> w.in("adjust_date", dates).or().in("adjusted_date", dates));
        return courseAdjustmentDao.queryForList(queryWrapper);
    }

    /**
     * 获取格式化时分秒为0的日期
     *
     * @param changedDates
     * @return
     */
    private List<java.sql.Date> getFormatDates(List<String> changedDates) {
        return changedDates.stream().map(day -> new java.sql.Date(DateUtils.stringToDate(day + " 00:00:00").getTime())).collect(Collectors.toList());
    }

    /**
     * 获取休息日列表
     *
     * @param dates
     * @return
     */
    private List<java.sql.Date> getSabbaticalDate(List<java.sql.Date> dates) {
        List<java.sql.Date> sabbaticalDate = new ArrayList<>();
        Map<Integer, WeekEnum> weekMap = WeekEnum.getWeekMap();
        for (java.sql.Date date : dates) {
            DayConfigVO dayConfigVO = settingFacade.dayConfig(date);
            if (dayConfigVO != null) {
                AdjustmentVO adjustmentVO = dayConfigVO.getAdjustment();
                if (adjustmentVO != null) {
                    // 当前日期所在周日期集合
                    List<Date> weekDates = DateUtils.dateToWeek(adjustmentVO.getDetailDate());
                    Integer dayOfWeek = adjustmentVO.getDayOfWeek();
                    if (dayOfWeek != null) {
                        // 调休班次周几
                        WeekEnum weekEnum = weekMap.get(dayOfWeek);
                        for (Date weekDate : weekDates) {
                            WeekEnum weekByIndex = WeekEnum.getWeekByIndex(weekDate);
                            if (weekEnum.equals(weekByIndex)) {
                                // 调休班次日期
                                sabbaticalDate.add(new java.sql.Date(weekDate.getTime()));
                                break;
                            }
                        }
                    }
                }

                List<ItemConfigVO> items = dayConfigVO.getItems();
                if (items != null && !items.isEmpty()) {
                    for (ItemConfigVO item : items) {
                        //3:节假日 6:调休
                        if (item.getType() == 3 || item.getType() == 6) {
                            List<AdjustmentVO> adjustments = item.getAdjustments();
                            if (adjustments != null && !adjustments.isEmpty()) {
                                for (AdjustmentVO adjustment : adjustments) {
                                    if (adjustment.getDetailDate().toString().equals(date.toString())) {
                                        // 当前日期所在周日期集合
                                        List<Date> weekDates = DateUtils.dateToWeek(adjustment.getDetailDate());
                                        if (!weekDates.isEmpty()) {
                                            // 获取调休班次日期
                                            Integer dayOfWeek = adjustment.getDayOfWeek();
                                            WeekEnum weekEnum = weekMap.get(dayOfWeek);
                                            for (Date weekDate : weekDates) {
                                                WeekEnum weekByIndex = WeekEnum.getWeekByIndex(weekDate);
                                                if (weekEnum.equals(weekByIndex)) {
                                                    // 调休班次日期
                                                    sabbaticalDate.add(new java.sql.Date(weekDate.getTime()));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return sabbaticalDate;
    }

    /**
     * 调课父数据置为有效/失效
     *
     * @param courseAdjustmentDO
     * @param b
     */
    private void adjustmentFatherSetIsInvalidFalse(CourseAdjustmentDO courseAdjustmentDO, boolean b) {
        Long fatherId1 = courseAdjustmentDO.getFatherId();
        Long teacherId1 = courseAdjustmentDO.getTeacherId();
        AdjustmentType fatherType = courseAdjustmentDO.getFatherType();
        Long adjustedFatherId1 = courseAdjustmentDO.getAdjustedFatherId();
        Long adjustedTeacherId1 = courseAdjustmentDO.getAdjustedTeacherId();
        AdjustmentType adjustedFatherType = courseAdjustmentDO.getAdjustedFatherType();

        if (fatherType != null && fatherId1 != null && fatherId1 != 0L) {
            if (fatherType.equals(AdjustmentType.ADJUSTMENT)) {
                // 父数据为调课
                CourseAdjustmentDO fatherLoad = courseAdjustmentDao.load(fatherId1);
                if (fatherLoad != null) {
                    if (teacherId1.equals(fatherLoad.getTeacherId())) {
                        // 父数据不参与统计
                        fatherLoad.setIsNotStatistics(b);
                        fatherLoad.setIsInvalid(b);
                        this.save(fatherLoad);
                    } else if (teacherId1.equals(fatherLoad.getAdjustedTeacherId())) {
                        fatherLoad.setAdjustedIsInvalid(b);
                        this.save(fatherLoad);
                    }
                }
            } else {
                // 父数据为代课
                ReplaceSectionDO fatherLoad = replaceSectionService.loadById(fatherId1);
                if (fatherLoad != null) {
                    fatherLoad.setReplaceIsInvalid(b);
                    replaceSectionService.save(fatherLoad);
                }
            }
        }
        if (adjustedFatherType != null && adjustedFatherId1 != null && adjustedFatherId1 != 0L) {
            if (adjustedFatherType.equals(AdjustmentType.ADJUSTMENT)) {
                // 父数据为调课
                CourseAdjustmentDO adjustedFatherLoad = courseAdjustmentDao.load(adjustedFatherId1);
                if (adjustedFatherLoad != null) {
                    if (adjustedTeacherId1.equals(adjustedFatherLoad.getTeacherId())) {
                        // 父数据不参与统计
                        adjustedFatherLoad.setIsNotStatistics(true);
                        adjustedFatherLoad.setIsInvalid(b);
                        this.save(adjustedFatherLoad);
                    } else if (adjustedTeacherId1.equals(adjustedFatherLoad.getAdjustedTeacherId())) {
                        adjustedFatherLoad.setAdjustedIsInvalid(b);
                        this.save(adjustedFatherLoad);
                    }
                }
            } else {
                // 父数据为代课
                ReplaceSectionDO adjustedFatherLoad = replaceSectionService.loadById(adjustedFatherId1);
                if (adjustedFatherLoad != null) {
                    adjustedFatherLoad.setReplaceIsInvalid(b);
                    replaceSectionService.save(adjustedFatherLoad);
                }
            }

        }
    }

    /**
     * 调课父数据置为有效/失效
     * @param courseAdjustmentDO
     * @param b
     */
    private void adjustmentFatherSetIsInvalidFalseApply(CourseAdjustmentDO courseAdjustmentDO, boolean b) {
        Long fatherId1 = courseAdjustmentDO.getFatherId();
        Long teacherId1 = courseAdjustmentDO.getTeacherId();
        AdjustmentType fatherType = courseAdjustmentDO.getFatherType();
        Long adjustedFatherId1 = courseAdjustmentDO.getAdjustedFatherId();
        Long adjustedTeacherId1 = courseAdjustmentDO.getAdjustedTeacherId();
        AdjustmentType adjustedFatherType = courseAdjustmentDO.getAdjustedFatherType();

        if (fatherType != null && fatherId1 != null && fatherId1 != 0L) {
            if (fatherType.equals(AdjustmentType.ADJUSTMENT)) {
                // 父数据为调课
                CourseAdjustmentDO fatherLoad = courseAdjustmentDao.load(fatherId1);
                if (fatherLoad != null) {
                    if (teacherId1.equals(fatherLoad.getTeacherId())) {
                        // 父数据不参与统计
                        fatherLoad.setIsNotStatistics(b);
                        fatherLoad.setIsInvalid(b);
                        this.save(fatherLoad);
                    } else if (teacherId1.equals(fatherLoad.getAdjustedTeacherId())) {
                        fatherLoad.setAdjustedIsInvalid(b);
                        this.save(fatherLoad);
                    }
                }
            }
        }
        if (adjustedFatherType != null && adjustedFatherId1 != null && adjustedFatherId1 != 0L) {
            if (adjustedFatherType.equals(AdjustmentType.ADJUSTMENT)) {
                // 父数据为调课
                CourseAdjustmentDO adjustedFatherLoad = courseAdjustmentDao.load(adjustedFatherId1);
                if (adjustedFatherLoad != null) {
                    if (adjustedTeacherId1.equals(adjustedFatherLoad.getTeacherId())) {
                        // 父数据不参与统计
                        adjustedFatherLoad.setIsNotStatistics(true);
                        adjustedFatherLoad.setIsInvalid(b);
                        this.save(adjustedFatherLoad);
                    } else if (adjustedTeacherId1.equals(adjustedFatherLoad.getAdjustedTeacherId())) {
                        adjustedFatherLoad.setAdjustedIsInvalid(b);
                        this.save(adjustedFatherLoad);
                    }
                }
            }else {
                // 父数据为代课
                ReplaceSectionDO adjustedFatherLoad = replaceSectionService.loadById(adjustedFatherId1);
                if (adjustedFatherLoad != null) {
                    adjustedFatherLoad.setReplaceIsInvalid(b);
                    replaceSectionService.save(adjustedFatherLoad);
                }
            }
        }
    }

    /**
     * 添加需要取消的调代课记录
     *
     * @param toBeProcessCourseInfoBO
     * @param courseAdjustmentDOList
     * @param replaceAndSectionDTOList
     */
    private void addNeedCancelAdjustAndReplace(ToBeProcessCourseInfoBO toBeProcessCourseInfoBO, List<CourseAdjustmentDO> courseAdjustmentDOList, List<ReplaceAndSectionDTO> replaceAndSectionDTOList) {
        java.sql.Date date = new java.sql.Date(System.currentTimeMillis());
        LocalDate nowLocalDate = date.toLocalDate();
        if (courseAdjustmentDOList != null && !courseAdjustmentDOList.isEmpty()) {
            for (CourseAdjustmentDO courseAdjustmentDO : courseAdjustmentDOList) {
                // 审批中直接失效异常
                if (courseAdjustmentDO.getApprovalState().equals(ApprovalState.UNDER_APPROVAL)) {
                    toBeProcessCourseInfoBO.addNeedCancelCourseAdjustment(courseAdjustmentDO);
                } else {
                    if (!courseAdjustmentDO.getIsSelfStudy()) {
                        // 已通过，判断 调课时间与被调时间是否 未开始/未完全结束，失效异常，已结束失效无异常
                        java.sql.Date adjustDate = courseAdjustmentDO.getAdjustDate();
                        java.sql.Date adjustedDate = courseAdjustmentDO.getAdjustedDate();
                        LocalDate adjustLocalDate = adjustDate.toLocalDate();
                        LocalDate adjustedLocalDate = adjustedDate.toLocalDate();
                        // 已经结束
                        if ((adjustLocalDate.isBefore(nowLocalDate) && (adjustedLocalDate.isBefore(nowLocalDate)))) {
                            toBeProcessCourseInfoBO.addNeedInvalidCourseAdjustment(courseAdjustmentDO);
                        }
                        // 未开始 或 未完全结束
                        else {
                            toBeProcessCourseInfoBO.addNeedCancelCourseAdjustment(courseAdjustmentDO);
                        }
                    }
                }
            }
        }
        // 代课
        if (replaceAndSectionDTOList != null && !replaceAndSectionDTOList.isEmpty()) {
            for (ReplaceAndSectionDTO replaceAndSectionDTO : replaceAndSectionDTOList) {
                if (replaceAndSectionDTO.getApprovalState().equals(ApprovalState.UNDER_APPROVAL)) {
                    toBeProcessCourseInfoBO.addNeedCancelReplaceAndSection(replaceAndSectionDTO);
                } else {
                    // 已通过
                    List<ReplaceSectionDO> replaceSectionDoList = replaceAndSectionDTO.getReplaceSectionDoList();
                    if (replaceSectionDoList == null || replaceSectionDoList.isEmpty()) {
                        continue;
                    }
                    for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                        java.sql.Date replaceDate = replaceSectionDO.getReplaceDate();
                        LocalDate replaceLocalDate = replaceDate.toLocalDate();
                        // 已经结束
                        if (replaceLocalDate.isBefore(nowLocalDate)) {
                            toBeProcessCourseInfoBO.addNeedInvalidReplaceAndSection(replaceAndSectionDTO);
                        }
                        // 未开始 或 未完全结束
                        else {
                            toBeProcessCourseInfoBO.addNeedCancelReplaceAndSection(replaceAndSectionDTO);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取所有教师以及其所教课程信息的集合
     *
     * @return 集合
     */
    @Override
    public List<CourseTeacherDetailVO> getAllTeacherAndCourse() {
        Map<Long, CourseTeacherDetailVO> map = getLongCourseTeacherDetailVoMap();
        if (map == null) {
            return null;
        }
        return new ArrayList<>(map.values());
    }

    @Nullable
    private Map<Long, CourseTeacherDetailVO> getLongCourseTeacherDetailVoMap() {
        // 获取当前学期
        SemesterVO currentSemesters = settingFacade.currentSemester();
        Integer orderNo = currentSemesters.getOrderNo();
        Long semesterId = currentSemesters.getId();
        List<CurriculumTableDO> curriculumTableDos = curriculumTableService.getTable(semesterId, orderNo);
        if (curriculumTableDos == null || curriculumTableDos.size() == 0) {
            return null;
        }
        Map<Long, CourseTeacherDetailVO> map = new HashMap<>(10);
        for (CurriculumTableDO table : curriculumTableDos) {
            // 获取总课表ID
            List<TeacherTableVO> teacherTableVos = generalTableService.teacherTable(table.getId());
            for (TeacherTableVO teacherTableVO : teacherTableVos) {
                Map<String, List<TeacherTableDTO>> data = teacherTableVO.getData();
                for (Map.Entry<String, List<TeacherTableDTO>> entry : data.entrySet()) {
                    // 教师
                    String courseName = entry.getKey();
                    List<TeacherTableDTO> value = entry.getValue();
                    for (TeacherTableDTO teacherTableDTO : value) {
                        CourseTeacherDetailVO courseTeacherDetailVO = new CourseTeacherDetailVO();
                        courseTeacherDetailVO.setCourseName(courseName);
                        courseTeacherDetailVO.setTeacherName(teacherTableDTO.getTeacherName());
                        courseTeacherDetailVO.setTeacherId(teacherTableDTO.getTeacherId());
                        map.put(courseTeacherDetailVO.getTeacherId(), courseTeacherDetailVO);
                    }
                }
            }
        }
        return map;
    }


    /**
     * 根据教师id和周获取调代课记录（审批通过）
     */
    @Override
    public Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(Long classId, Integer number, Long curriculumTableId) {
        String week = "第" + CourseAdjustmentUtils.number2Chinese(number) + "周";
        Map<String, Object> map = new HashMap<>(10);
        map.put("week", week);
        map.put("classId", classId);
        map.put("curriculumTableId", curriculumTableId);
        // 获取课程全称和课程简称
        Map<String, String> courseNameMap = curriculumService.aliasMap(true);
        List<TeacherAbbDetailVO> teacherAbbDetailVoList = queryForMapMapper(TeacherAbbDetailVO.class, "getCourseAdjustmentAndReplaceByClassIds", map);
        if (teacherAbbDetailVoList == null || teacherAbbDetailVoList.isEmpty()) {
            return null;
        }
        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVoList) {
            // 课程简称
            for (Map.Entry<String, String> entry : courseNameMap.entrySet()) {
                if (entry.getKey().equals(teacherAbbDetailVO.getCourseName())) {
                    teacherAbbDetailVO.setAbbreviation(entry.getValue());
                }
            }
        }
        Map<Long, List<TeacherAbbDetailVO>> teacherAbbMap = teacherAbbDetailVoList.stream().collect(Collectors.groupingBy(TeacherDetailVO::getTeacherId));
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> result = new HashMap<>(10);
        for (Map.Entry<Long, List<TeacherAbbDetailVO>> entry : teacherAbbMap.entrySet()) {
            Map<WeekEnum, List<TeacherAbbDetailVO>> collect2 = entry.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getDayOfWeek(teacherAbbDetailVO.getCourseTime())));
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> result2 = new HashMap<>(10);
            for (Map.Entry<WeekEnum, List<TeacherAbbDetailVO>> entry1 : collect2.entrySet()) {
                Map<String, List<TeacherAbbDetailVO>> collect3 = entry1.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getSection(teacherAbbDetailVO.getCourseTime())));
                Map<String, TeacherAbbDetailVO> result3 = new HashMap<>(10);
                for (Map.Entry<String, List<TeacherAbbDetailVO>> listEntry : collect3.entrySet()) {
                    result3.put(listEntry.getKey(), listEntry.getValue().get(0));
                }
                result2.put(entry1.getKey(), result3);
            }
            result.put(entry.getKey(), result2);
        }
        return result;
    }

    @Override
    public Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(Long stuId, Long classId, List<Long> teacherIds, Integer number, Long curriculumTableId) {
        String week = "第" + CourseAdjustmentUtils.number2Chinese(number) + "周";
        Map<String, Object> map = new HashMap<>(10);
        map.put("week", week);
        map.put("teacherIds", teacherIds);
        map.put("classId", classId);
        map.put("curriculumTableId", curriculumTableId);
        // 获取课程全称和课程简称
        Map<String, String> courseNameMap = curriculumService.aliasMap(true);
        List<TeacherAbbDetailVO> teacherAbbDetailVoList = queryForMapMapper(TeacherAbbDetailVO.class, "getCourseAdjustmentAndReplaceOfStudentTable", map);
        if (teacherAbbDetailVoList == null || teacherAbbDetailVoList.isEmpty()) {
            return null;
        }
        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVoList) {
            // 课程简称
            for (Map.Entry<String, String> entry : courseNameMap.entrySet()) {
                if (entry.getKey().equals(teacherAbbDetailVO.getCourseName())) {
                    teacherAbbDetailVO.setAbbreviation(entry.getValue());
                }
            }
        }
        // 同一节次有多个数据，去除被调被代
        List<TeacherAbbDetailVO> filterData = new ArrayList<>();
        Map<String, List<TeacherAbbDetailVO>> collect = teacherAbbDetailVoList.stream().collect(Collectors.groupingBy(TeacherAbbDetailVO::getCourseTime));
        for (Map.Entry<String, List<TeacherAbbDetailVO>> stringListEntry : collect.entrySet()) {
            List<TeacherAbbDetailVO> value = stringListEntry.getValue();
            TeacherAbbDetailVO teacherAbbDetailVO = value.get(0);
            if (value.size() > 1) {
                for (TeacherAbbDetailVO vo : value) {
                    // 此情况即存在COVERADJUSTMENT的情况下还有ADJUSTMENT或REPLACE
                    if (!vo.getType().equals(AbbTypeEnum.COVERADJUSTMENT) && !vo.getType().equals(AbbTypeEnum.COVERSUBSTITUTE)) {
                        teacherAbbDetailVO = vo;
                        break;
                    }
                }
            }
            filterData.add(teacherAbbDetailVO);
        }
        teacherAbbDetailVoList = filterData;
        Map<Long, List<TeacherAbbDetailVO>> collect1 = teacherAbbDetailVoList.stream().collect(Collectors.groupingBy(TeacherDetailVO::getTeacherId));
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> result = new HashMap<>(10);
        for (Map.Entry<Long, List<TeacherAbbDetailVO>> entry : collect1.entrySet()) {
            Map<WeekEnum, List<TeacherAbbDetailVO>> collect2 = entry.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getDayOfWeek(teacherAbbDetailVO.getCourseTime())));
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> result2 = new HashMap<>(10);
            for (Map.Entry<WeekEnum, List<TeacherAbbDetailVO>> entry1 : collect2.entrySet()) {
                Map<String, List<TeacherAbbDetailVO>> collect3 = entry1.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getSection(teacherAbbDetailVO.getCourseTime())));
                Map<String, TeacherAbbDetailVO> result3 = new HashMap<>(10);
                for (Map.Entry<String, List<TeacherAbbDetailVO>> listEntry : collect3.entrySet()) {
                    result3.put(listEntry.getKey(), listEntry.getValue().get(0));
                }
                result2.put(entry1.getKey(), result3);
            }
            result.put(entry.getKey(), result2);
        }
        return result;
    }

    @Override
    public Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(List<Long> teacherIds, Integer number, List<Long> curriculumTableIds) {
        String week = "第" + CourseAdjustmentUtils.number2Chinese(number) + "周";
        Map<String, Object> map = new HashMap<>(10);
        map.put("week", week);
        map.put("teacherIds", teacherIds);
        map.put("curriculumTableIds", curriculumTableIds);
        // 获取课程全称和课程简称
        Map<String, String> courseNameMap = curriculumService.aliasMap(true);
        List<TeacherAbbDetailVO> teacherAbbDetailVoList = queryForMapMapper(TeacherAbbDetailVO.class, "getCourseAdjustmentAndReplaceByCurriculumTableIds", map);

        if (teacherAbbDetailVoList == null || teacherAbbDetailVoList.isEmpty()) {
            return null;
        }
        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVoList) {
            // 课程简称
            for (Map.Entry<String, String> entry : courseNameMap.entrySet()) {
                if (entry.getKey().equals(teacherAbbDetailVO.getCourseName())) {
                    teacherAbbDetailVO.setAbbreviation(entry.getValue());
                }
            }
        }
        Map<Long, List<TeacherAbbDetailVO>> collect1 = teacherAbbDetailVoList.stream().collect(Collectors.groupingBy(TeacherDetailVO::getTeacherId));

        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> result = new HashMap<>(10);
        for (Map.Entry<Long, List<TeacherAbbDetailVO>> entry : collect1.entrySet()) {
            Map<WeekEnum, List<TeacherAbbDetailVO>> collect2 = entry.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getDayOfWeek(teacherAbbDetailVO.getCourseTime())));
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> result2 = new HashMap<>(10);
            for (Map.Entry<WeekEnum, List<TeacherAbbDetailVO>> entry1 : collect2.entrySet()) {
                Map<String, List<TeacherAbbDetailVO>> collect3 = entry1.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getSection(teacherAbbDetailVO.getCourseTime())));
                Map<String, TeacherAbbDetailVO> result3 = new HashMap<>(10);
                for (Map.Entry<String, List<TeacherAbbDetailVO>> listEntry : collect3.entrySet()) {
                    List<TeacherAbbDetailVO> value = listEntry.getValue();
                    TeacherAbbDetailVO teacherAbbDetailVO = value.get(0);
                    if (value.size() > 1) {
                        for (TeacherAbbDetailVO vo : value) {
                            // 调课后空的节次被代课
                            // 调课后空的节次被调课
                            // 此情况即存在COVERADJUSTMENT的情况下还有ADJUSTMENT或REPLACE
                            if (!vo.getType().equals(AbbTypeEnum.COVERADJUSTMENT) && !vo.getType().equals(AbbTypeEnum.COVERSUBSTITUTE)) {
                                teacherAbbDetailVO = vo;
                                break;
                            }
                        }
                    }
                    result3.put(listEntry.getKey(), teacherAbbDetailVO);
                }
                result2.put(entry1.getKey(), result3);
            }
            result.put(entry.getKey(), result2);
        }
        return result;
    }

    @Override
    public Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> getRecordsByTeacherIdAndTimeScopeBatch(List<Long> teacherIds, List<Integer> numbers, List<Long> curriculumTableIds) {
        StringBuilder stringBuilder = new StringBuilder();
        numbers.forEach(n ->{
            stringBuilder.append("第" + CourseAdjustmentUtils.number2Chinese(n) + "周").append("|");
        });
        String week = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
        Map<String, Object> map = new HashMap<>(10);
        map.put("week", week);
        map.put("teacherIds", teacherIds);
        map.put("curriculumTableIds", curriculumTableIds);
        // 获取课程全称和课程简称
        Map<String, String> courseNameMap = curriculumService.aliasMap(true);
        List<TeacherAbbDetailVO> teacherAbbDetailVoList = queryForMapMapper(TeacherAbbDetailVO.class, "getCourseAdjustmentAndReplaceByCurriculumTableIdsBatch", map);

        if (teacherAbbDetailVoList == null || teacherAbbDetailVoList.isEmpty()) {
            return null;
        }
        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVoList) {
            // 课程简称
            for (Map.Entry<String, String> entry : courseNameMap.entrySet()) {
                if (entry.getKey().equals(teacherAbbDetailVO.getCourseName())) {
                    teacherAbbDetailVO.setAbbreviation(entry.getValue());
                }
            }
        }
        Map<Long, List<TeacherAbbDetailVO>> collect1 = teacherAbbDetailVoList.stream().collect(Collectors.groupingBy(TeacherDetailVO::getTeacherId));

        //老师第几周调课信息
        Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> weekResult = new HashMap<>(10);
        for (Map.Entry<Long, List<TeacherAbbDetailVO>> entry : collect1.entrySet()) {
            //周次分组  CourseTimeUtils.getWeekNumber(courseTime)
            Map<Integer, List<TeacherAbbDetailVO>> weekCollect = entry.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getWeekNumber(teacherAbbDetailVO.getCourseTime())));
            Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> weekMap = new HashMap<>(10);
            for (Map.Entry<Integer, List<TeacherAbbDetailVO>> weekListEntry : weekCollect.entrySet()) {
                Map<WeekEnum, List<TeacherAbbDetailVO>> collect2 = weekListEntry.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getDayOfWeek(teacherAbbDetailVO.getCourseTime())));
                Map<WeekEnum, Map<String, TeacherAbbDetailVO>> result2 = new HashMap<>(10);
                for (Map.Entry<WeekEnum, List<TeacherAbbDetailVO>> entry1 : collect2.entrySet()) {
                    Map<String, List<TeacherAbbDetailVO>> collect3 = entry1.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getSection(teacherAbbDetailVO.getCourseTime())));
                    Map<String, TeacherAbbDetailVO> result3 = new HashMap<>(10);
                    for (Map.Entry<String, List<TeacherAbbDetailVO>> listEntry : collect3.entrySet()) {
                        List<TeacherAbbDetailVO> value = listEntry.getValue();
                        TeacherAbbDetailVO teacherAbbDetailVO = value.get(0);
                        if (value.size() > 1) {
                            for (TeacherAbbDetailVO vo : value) {
                                // 调课后空的节次被代课
                                // 调课后空的节次被调课
                                // 此情况即存在COVERADJUSTMENT的情况下还有ADJUSTMENT或REPLACE
                                if (!vo.getType().equals(AbbTypeEnum.COVERADJUSTMENT) && !vo.getType().equals(AbbTypeEnum.COVERSUBSTITUTE) && !vo.getType().equals(AbbTypeEnum.SUBSTITUTE)) {
                                    teacherAbbDetailVO = vo;
                                    break;
                                }
                            }
                            result3.put(listEntry.getKey(), teacherAbbDetailVO);
                        }else {
                            if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.SUBSTITUTE)) {
                                QueryWrapper query = new QueryWrapper();
                                query.eq("father_id",teacherAbbDetailVO.getAdjustmentId()).eq("is_invalid","false").eq("approval_state",ApprovalState.PASS.name());
                                List<CourseAdjustmentDO> courseAdjustmentDOS = this.queryForMap(CourseAdjustmentDO.class, query);
                                if (CollectionUtils.isEmpty(courseAdjustmentDOS)) {
                                    result3.put(listEntry.getKey(), teacherAbbDetailVO);
                                }
                            }else {
                                result3.put(listEntry.getKey(), teacherAbbDetailVO);
                            }
                        }
                    }
                    result2.put(entry1.getKey(), result3);
                }
                weekMap.put(weekListEntry.getKey(), result2);
            }
            weekResult.put(entry.getKey(),weekMap);
        }
        return weekResult;
    }

    @Override
    public Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getRecordsByTeacherIdAndTimeScope(Long roomId, Integer number, List<Long> curriculumTableId) {
        String week = "第" + CourseAdjustmentUtils.number2Chinese(number) + "周";
        Map<String, Object> map = new HashMap<>(10);
        map.put("week", week);
        map.put("roomId", roomId + "ROOM");
        map.put("curriculumTableId", curriculumTableId);
        // 获取课程全称和课程简称
        Map<String, String> courseNameMap = curriculumService.aliasMap(true);
        List<TeacherAbbDetailVO> teacherAbbDetailVoList = queryForMapMapper(TeacherAbbDetailVO.class, "getCourseAdjustmentAndReplaceOfRoomTable", map);
        if (teacherAbbDetailVoList == null || teacherAbbDetailVoList.isEmpty()) {
            return null;
        }
        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVoList) {
            // 课程简称
            for (Map.Entry<String, String> entry : courseNameMap.entrySet()) {
                if (entry.getKey().equals(teacherAbbDetailVO.getCourseName())) {
                    teacherAbbDetailVO.setAbbreviation(entry.getValue());
                }
            }
        }
        Map<Long, List<TeacherAbbDetailVO>> collect1 = teacherAbbDetailVoList.stream().collect(Collectors.groupingBy(TeacherDetailVO::getTeacherId));
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> result = new HashMap<>(10);
        for (Map.Entry<Long, List<TeacherAbbDetailVO>> entry : collect1.entrySet()) {
            Map<WeekEnum, List<TeacherAbbDetailVO>> collect2 = entry.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getDayOfWeek(teacherAbbDetailVO.getCourseTime())));
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> result2 = new HashMap<>(10);
            for (Map.Entry<WeekEnum, List<TeacherAbbDetailVO>> entry1 : collect2.entrySet()) {
                Map<String, List<TeacherAbbDetailVO>> collect3 = entry1.getValue().stream().collect(Collectors.groupingBy(teacherAbbDetailVO -> CourseTimeUtils.getSection(teacherAbbDetailVO.getCourseTime())));
                Map<String, TeacherAbbDetailVO> result3 = new HashMap<>(10);
                for (Map.Entry<String, List<TeacherAbbDetailVO>> listEntry : collect3.entrySet()) {
                    result3.put(listEntry.getKey(), listEntry.getValue().get(0));
                }
                result2.put(entry1.getKey(), result3);
            }
            result.put(entry.getKey(), result2);
        }
        return result;
    }

    /**
     * pc管理-调课记录分页
     */
    @Override
    public QueryResult<List<CourseAdjustmentVO>> queryPage(AdjustmentPageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        QueryResult<List<CourseAdjustmentDTO>> queryResult = queryForPage(CourseAdjustmentDTO.class, "adjustmentRecords", map);
        if (queryResult == null) {
            return null;
        }
        return getListQueryResult(queryResult);
    }

    private QueryResult<List<CourseAdjustmentVO>> getListQueryResult(QueryResult<List<CourseAdjustmentDTO>> queryResult) {
        List<CourseAdjustmentDTO> result = queryResult.getResult();
        List<CourseAdjustmentDTO> courseAdjustmentDtoList = new ArrayList<>();
        Map<Long, List<CourseAdjustmentDTO>> mainIdMap = result.stream().filter(courseAdjustmentDto -> "true".equals(courseAdjustmentDto.getAttachment())).collect(Collectors.groupingBy(CourseAdjustmentDTO::getMainId));
        List<CourseAdjustmentDTO> attachmentList = result.stream().filter(courseAdjustmentDto -> "false".equals(courseAdjustmentDto.getAttachment())).collect(Collectors.toList());
        attachmentList.forEach(courseAdjustmentDto -> {
            courseAdjustmentDtoList.add(courseAdjustmentDto);
            List<CourseAdjustmentDTO> courseAdjustmentDtos = mainIdMap.get(courseAdjustmentDto.getId());
            if (!CollectionUtils.isEmpty(courseAdjustmentDtos)) {
                courseAdjustmentDtoList.addAll(courseAdjustmentDtos);
                mainIdMap.remove(courseAdjustmentDto.getId());
            }
        });
        if (!CollectionUtils.isEmpty(mainIdMap)) {
            for (List<CourseAdjustmentDTO> courseAdjustmentDtos : mainIdMap.values()) {
                if (courseAdjustmentDtoList.size() > 0) {
                    CourseAdjustmentDTO courseAdjustment = courseAdjustmentDtos.get(0);
                    List<CourseAdjustmentDTO> filterList = courseAdjustmentDtoList.stream().filter(courseAdjustmentDto -> courseAdjustmentDto.getCreatedDate().getTime() < courseAdjustment.getCreatedDate().getTime()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterList)) {
                        CourseAdjustmentDTO course = filterList.get(0);
                        int index = courseAdjustmentDtoList.indexOf(course);
                        courseAdjustmentDtoList.addAll(index,courseAdjustmentDtos);
                    }else {
                        courseAdjustmentDtoList.addAll(courseAdjustmentDtos);
                    }
                }else {
                    courseAdjustmentDtoList.addAll(courseAdjustmentDtos);
                }
            }
        }
        List<CourseAdjustmentVO> result1 = new ArrayList<>();
        if (courseAdjustmentDtoList.size() > 0) {
            for (CourseAdjustmentDTO courseAdjustmentDTO : courseAdjustmentDtoList) {
                CourseAdjustmentVO courseAdjustmentVO = ClassConverter.aTob(courseAdjustmentDTO, new CourseAdjustmentVO());
                if (courseAdjustmentDTO.getIsSelfStudy()) {
                    courseAdjustmentVO.setAdjustedCourseName("-");
                    courseAdjustmentVO.setAdjustedCourseRoom("-");
                    courseAdjustmentVO.setAdjustedCourseTime("-");
                    courseAdjustmentVO.setAdjustedTeacherName("-");
                }
                if (courseAdjustmentDTO.getIsNotStatistics() != null) {
                    if (courseAdjustmentDTO.getIsNotStatistics()) {
                        courseAdjustmentVO.setStatistics("不参与统计");
                    } else {
                        courseAdjustmentVO.setStatistics("参与统计");
                    }
                }
                Integer weekCycleNum = courseAdjustmentDTO.getWeekCycleNum();
                if (weekCycleNum != null) {
                    if (weekCycleNum == 0) {
                        courseAdjustmentVO.setWeekCycle("/");
                    } else {
                        courseAdjustmentVO.setWeekCycle(weekCycleNum.toString());
                    }
                }
                courseAdjustmentVO.setApprovalState(courseAdjustmentDTO.getApprovalState().getName());
                //组装循环方式
                if (CycleTypeEnum.NOT_CYCLE.getType().equals(courseAdjustmentDTO.getCycleType())) {
                    courseAdjustmentVO.setCycleType(CycleTypeEnum.NOT_CYCLE.getName());
                }else if (CycleTypeEnum.WEEK_CYCLE.getType().equals(courseAdjustmentDTO.getCycleType())) {
                    courseAdjustmentVO.setCycleType(CycleTypeEnum.WEEK_CYCLE.getName() + "(" + courseAdjustmentDTO.getWeekCycleNum() + ")次");
                }else {
                    courseAdjustmentVO.setCycleType(CycleTypeEnum.CUSTOM_CYCLE.getName()+"("+DateUtil.formatDate(courseAdjustmentDTO.getCustomStartTime())+"至"+DateUtil.formatDate(courseAdjustmentDTO.getCustomEndTime())+")");
                }
                result1.add(courseAdjustmentVO);
            }
        }
        QueryResult<List<CourseAdjustmentVO>> queryResult1 = new QueryResult<>();
        queryResult1.setResult(result1);
        queryResult1.setPageInfo(queryResult.getPageInfo());
        return queryResult1;
    }

    /**
     * pc个人-调课记录分页
     */
    @Override
    public QueryResult<List<CourseAdjustmentVO>> queryMyPage(AdjustmentPageAO ao) {
        JwtUser user = getJwtUser();
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("myTeacherName", user.getName());
        QueryResult<List<CourseAdjustmentDTO>> queryResult = queryForPage(CourseAdjustmentDTO.class, "personalAdjustmentRecords", map);
        if (queryResult == null) {
            return null;
        }
        return getListQueryResult(queryResult);
    }

    /**
     * 调课数据导出
     */
    @Override
    public void adjustmentExport(AdjustmentPageAO ao, HttpServletResponse response) {
        ao.setPageSize(-1);
        QueryResult<List<CourseAdjustmentVO>> listQueryResult = queryPage(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<CourseAdjustmentVO> result = listQueryResult.getResult();
        List<AdjustmentRecordExport> list = new ArrayList<>();
        if (result != null && result.size() > 0) {
            list = result.stream().map(courseAdjustmentVO -> ClassConverter.aTob(courseAdjustmentVO, new AdjustmentRecordExport())).collect(Collectors.toList());
        }
        String title = "教师调课记录";
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), AdjustmentRecordExport.class).head(ExcelHeadsUtil.adjustmentRecordHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_400010);
        }
    }

    /**
     * 根据id删除调课记录（逻辑删除）
     */
    @Override
    public void removeCourseAdjustmentById(Long id) {
        CourseAdjustmentDO load = courseAdjustmentDao.load(id);
        if (load != null) {
            // 撤销流程
            if (load.getApprovalState().equals(ApprovalState.UNDER_APPROVAL)) {
                FlowVO flowVO = flowFacade.getFlowInformation(id, relatedType);
                AdjustmentCancelAO ao = new AdjustmentCancelAO();
                ao.setId(id);
                ao.setType(AdjustmentType.ADJUSTMENT.getType());
                ao.setProcessInstanceId(flowVO.getEngineFlowId());
                cancel(ao);
            }
            remove(id);
            adjustmentFatherSetIsInvalidFalse(load, false);
            // 删除衍生数据，通过后衍生数据就是独立数据，不能删除衍生数据
            if (!load.getApprovalState().equals(ApprovalState.PASS)) {
                DeleteWrapper deleteWrapper = new DeleteWrapper();
                deleteWrapper.eq("main_id", id);
                courseAdjustmentDao.executeUpdate(deleteWrapper);
            }
        }
    }

    @Override
    public void isAdjustConflict(Long teacherId, String courseTime, Long adjustedTeacherId, String adjustedCourseTime) {
        // 获取教师名字
        String teacherName = "该";
        String adjustedTeacherName = "该";
        List<TeacherDto> teachers = foundationFacade.getTeachersByIds(teacherId + "," + adjustedTeacherId);
        if (teachers != null && !teachers.isEmpty()) {
            for (TeacherDto teacher : teachers) {
                if (teacher.getId().equals(teacherId)) {
                    teacherName = teacher.getName();
                }
                if (teacher.getId().equals(adjustedTeacherId)) {
                    adjustedTeacherName = teacher.getName();
                }
            }
        }
        // 获取教师每周课表
        // 周次
        Integer courseWeekNumber = CourseTimeUtils.getWeekNumber(courseTime);
        Integer adjustedCourseWeekNumber = CourseTimeUtils.getWeekNumber(adjustedCourseTime);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> courseWeekEnumMapMap = generalTableService.teacherTableWeekDetail(teacherId, adjustedCourseWeekNumber);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> adjustedCourseWeekEnumMapMap = generalTableService.teacherTableWeekDetail(adjustedTeacherId, courseWeekNumber);
        WeekEnum adjustedCourseDayOfWeek = CourseTimeUtils.getDayOfWeek(adjustedCourseTime);
        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> entry : courseWeekEnumMapMap.entrySet()) {
            if (entry.getKey().equals(adjustedCourseDayOfWeek)) {
                Map<String, List<TeacherAbbDetailVO>> value = entry.getValue();
                for (Map.Entry<String, List<TeacherAbbDetailVO>> entry1 : value.entrySet()) {
                    if (entry1.getKey().equals(CourseTimeUtils.getSection(adjustedCourseTime))) {
                        List<TeacherAbbDetailVO> teacherAbbDetailVos = entry1.getValue();
                        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVos) {
                            if (teacherAbbDetailVO.getTeacherId().equals(teacherId)) {
                                throw new BusinessException(CodeRes.CODE_800007, teacherName, adjustedCourseTime);
                            }
                        }
                    }
                }
            }
        }
        WeekEnum courseDayOfWeek = CourseTimeUtils.getDayOfWeek(courseTime);
        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> entry : adjustedCourseWeekEnumMapMap.entrySet()) {
            if (entry.getKey().equals(courseDayOfWeek)) {
                Map<String, List<TeacherAbbDetailVO>> value = entry.getValue();
                for (Map.Entry<String, List<TeacherAbbDetailVO>> entry1 : value.entrySet()) {
                    if (entry1.getKey().equals(CourseTimeUtils.getSection(courseTime))) {
                        List<TeacherAbbDetailVO> teacherAbbDetailVos = entry1.getValue();
                        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVos) {
                            if (teacherAbbDetailVO.getTeacherId().equals(adjustedTeacherId)) {
                                throw new BusinessException(CodeRes.CODE_800007, adjustedTeacherName, courseTime);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public CourseAdjustmentDO loadById(Long id) {
        return courseAdjustmentDao.load(id);
    }

    @Override
    public DeleteRelatedDataDTO getInterrelatedId(Long id, Integer dataType) {
        // 查找关联的数据
        Set<Long> adjustIds = new HashSet<>();
        Set<Long> replaceIds = new HashSet<>();

        if (AdjustmentType.ADJUSTMENT.getType().equals(dataType)) {
            CourseAdjustmentDO load = courseAdjustmentDao.load(id);
            if (load == null) {
                return null;
            }
            // 递归查询出所有子节点
            getAllSon(id, adjustIds, replaceIds);
        } else {
            // 代课
            List<ReplaceSectionDO> listByReplaceId = replaceSectionService.getListByReplaceId(id);
            if (listByReplaceId == null || listByReplaceId.isEmpty()) {
                return null;
            }
            for (ReplaceSectionDO replaceSectionDO : listByReplaceId) {
                getAllSon(replaceSectionDO.getId(), adjustIds, replaceIds);
            }

        }


        if (!adjustIds.isEmpty() || !replaceIds.isEmpty()) {
            DeleteRelatedDataDTO deleteRelatedDataDTO = new DeleteRelatedDataDTO();
            if (!adjustIds.isEmpty()) {
                deleteRelatedDataDTO.setAdjustIds(adjustIds);
            }
            if (!replaceIds.isEmpty()) {
                deleteRelatedDataDTO.setReplaceIds(replaceIds);
            }
            return deleteRelatedDataDTO;
        }
        return null;
    }

    private void getAllSon(Long id, Set<Long> adjustIds, Set<Long> replaceIds) {
        if (id == null) {
            return;
        }
        // 在调课表搜索
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("father_id", id).or().eq("adjusted_father_id", id);
        List<CourseAdjustmentDO> courseAdjustmentDos = courseAdjustmentDao.queryForList(queryWrapper);
        if (!courseAdjustmentDos.isEmpty()) {
            for (CourseAdjustmentDO courseAdjustmentDO : courseAdjustmentDos) {
                adjustIds.add(courseAdjustmentDO.getId());
                getAllSon(courseAdjustmentDO.getId(), adjustIds, replaceIds);
            }
        }
        // 在代课表搜索
        List<ReplaceSectionDO> replaceSectionDos = replaceSectionService.getByFatherId(id);
        if (!replaceSectionDos.isEmpty()) {
            for (ReplaceSectionDO replaceSectionDO : replaceSectionDos) {
                replaceIds.add(replaceSectionDO.getReplaceId());
                getAllSon(replaceSectionDO.getId(), adjustIds, replaceIds);
            }
        }
        // 终止条件
        if (courseAdjustmentDos.isEmpty() && replaceSectionDos.isEmpty()) {
            return;
        }

    }

    @Override
    public void deleteInterrelatedData(DeleteRelatedDataDTO dto) {
        // 删除相关调代课的数据
        // 删除调课
        Set<Long> adjustIds = dto.getAdjustIds();
        Set<Long> replaceIds = dto.getReplaceIds();
        if (adjustIds != null && !adjustIds.isEmpty()) {
            for (Long adjustId : adjustIds) {
                this.removeCourseAdjustmentById(adjustId);
            }
        }
        // 删除代课
        if (replaceIds != null && !replaceIds.isEmpty()) {
            for (Long replaceId : replaceIds) {
                courseReplaceService.removeCourseReplaceById(replaceId);
            }
        }
    }

    @Override
    public void sabbaticalCancelAdjust(SabbaticalCancelAdjustAO ao) {
        List<java.sql.Date> dates = new ArrayList<>();
        java.sql.Date date = ao.getDate();
        dates.add(date);
        Map<Integer, WeekEnum> weekMap = WeekEnum.getWeekMap();
        Integer dayOfWeek = ao.getDayOfWeek();
        WeekEnum weekEnum = weekMap.get(dayOfWeek);
        WeekConfigVO dateWeek = settingFacade.getDateWeek(date);
        java.sql.Date startDay = dateWeek.getStartDay();
        java.sql.Date endDay = dateWeek.getEndDay();
        // 获取日期
        List<java.sql.Date> betweenDate = DateUtils.getBetweenDate(startDay, endDay);
        if (betweenDate != null && !betweenDate.isEmpty()) {
            for (java.sql.Date date1 : betweenDate) {
                WeekEnum weekByIndex = WeekEnum.getWeekByIndex(date1);
                if (weekByIndex.equals(weekEnum)) {
                    dates.add(date1);
                    break;
                }
            }
        }

        DateListAO dateListAo = new DateListAO();
        if (!dates.isEmpty()) {
            List<String> collect = dates.stream().map(java.sql.Date::toString).collect(Collectors.toList());
            dateListAo.setDates(collect);
        }
        schoolCalendarCancelAdjust(dateListAo);
    }


    /**
     * 撤销对应课程日期的调代课，并置为异常，发送通知
     *
     * @param dateListAo 日期集合
     */
    @Override
    public void schoolCalendarCancelAdjust(DateListAO dateListAo) {
        invalidByCalendarChanged(dateListAo);
    }

    @Override
    public void cancelAdjustment(Long curriculumTableId, UpdateType updateType) {
        invalidByTableChanged(curriculumTableId, updateType);
    }

    /**
     * 发起调课
     * 教务发起调代课审批自动通过，直接抄送结果给任课老师和被调课老师
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApplyAdjustmentVO applyAdjustment(CourseAdjustmentApplyAO ao) {
        ApplyAdjustmentVO applyAdjustmentVo = new ApplyAdjustmentVO();
        String result = null;
        SemesterVO semesterVO = settingFacade.currentSemester();
        List<ItemConfigVO> items = settingFacade.items(semesterVO.getId());
        Map<Integer, WeekConfigVO> weekMap = settingFacade.weekConfigNumberMap(semesterVO.getId());
        //获取实际日期
        List<String> courseTimeList = getCourseTimeList(ao);
        Map<String, java.sql.Date> realDateMap = getRealDateMap(courseTimeList, weekMap);
        applyAdjustmentVo = checkWeekIndex(ao,realDateMap);
        if (!CollectionUtils.isEmpty(applyAdjustmentVo.getIndex())) {
            return applyAdjustmentVo;
        }
        //组装数据
        List<CourseAdjustmentDetailAO> courseAdjustmentDetailAoList = ao.getCourseAdjustmentDetailAo();
        CourseAdjustmentBasicAO courseAdjustmentBasicAo = ao.getCourseAdjustmentBasicAo();
        //衍生数据
        List<CourseAdjustmentDO> slaveCourseAdjustmentDos = new ArrayList<>();
        //发起流程列表
        List<CourseAdjustmentFlowDTO> courseAdjustmentFlowDtos = new ArrayList<>();
        //获取所有教师数据
        List<Long> teacherIds = courseAdjustmentDetailAoList.stream().map(CourseAdjustmentDetailAO::getTeacherId).collect(Collectors.toList());
        List<Long> adjustedTeacherIds = courseAdjustmentDetailAoList.stream().map(CourseAdjustmentDetailAO::getAdjustedTeacherId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> allTeacherIds = new ArrayList<>(teacherIds);
        if (!CollectionUtils.isEmpty(adjustedTeacherIds)) {
            allTeacherIds.addAll(adjustedTeacherIds);
        }
        List<TeacherDto> teacherDtoList = getTeacherList(allTeacherIds);
        Map<Long, TeacherDto> teacherDtoMap = teacherDtoList.stream().collect(Collectors.toMap(TeacherDto::getId, t -> t));
        //每条记录的所有调课时间  Integer-列表数据角标
        Map<Integer,List<String>> courseTimeMap = new HashMap<>(6);
        //每条记录的所有被调课时间  Integer-列表数据角标
        Map<Integer,List<String>> adjustedCourseTimeMap = new HashMap<>(6);
        //获取教师周课表
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables = batchTeacherTables(courseTimeMap,adjustedCourseTimeMap,courseAdjustmentDetailAoList,courseAdjustmentBasicAo, allTeacherIds,realDateMap,weekMap);
        //判断有没有重复的调课时间和被调课时间
        applyAdjustmentVo = checkRepeatCourse(allTeacherIds,courseAdjustmentDetailAoList, courseTimeMap,adjustedCourseTimeMap);
        if (!CollectionUtils.isEmpty(applyAdjustmentVo.getIndex())) {
            return applyAdjustmentVo;
        }
        for (CourseAdjustmentDetailAO courseAdjustmentDetailAo : courseAdjustmentDetailAoList) {
            int index = courseAdjustmentDetailAoList.indexOf(courseAdjustmentDetailAo);
            CourseAdjustmentDO courseAdjustmentDo = CourseAdjustmentConvert.INSTANCE.aoToDoDetail(courseAdjustmentDetailAo);
            courseAdjustmentDo.setContent(courseAdjustmentBasicAo.getContent());
            courseAdjustmentDo.setPicUrls(courseAdjustmentBasicAo.getPicUrls());
            courseAdjustmentDo.setWeekCycleNum(courseAdjustmentBasicAo.getWeekCycleNum());
            courseAdjustmentDo.setCycleType(courseAdjustmentBasicAo.getCycleType());
            courseAdjustmentDo.setCustomStartTime(DateUtil.parse(courseAdjustmentBasicAo.getStartTime()));
            courseAdjustmentDo.setCustomEndTime(DateUtil.parse(courseAdjustmentBasicAo.getEndTime()));
            if (courseAdjustmentBasicAo.getIsNotStatistics() == null) {
                courseAdjustmentDo.setIsNotStatistics(false);
            }else {
                courseAdjustmentDo.setIsNotStatistics(courseAdjustmentBasicAo.getIsNotStatistics());
            }
            CourseAdjustmentDO courseInfo = getCourseInfo(courseAdjustmentDetailAo, items, weekMap);
            CourseAdjustmentConvert.INSTANCE.updateDoFromResult(courseInfo, courseAdjustmentDo);

            CourseAdjustmentDO adjustedCourseInfo = getAdjustedCourseInfo(courseAdjustmentDetailAo, items, weekMap);
            CourseAdjustmentConvert.INSTANCE.updateDoFromResult(adjustedCourseInfo, courseAdjustmentDo);

            CourseAdjustmentDO realDateInfo = getRealDateInfo(courseAdjustmentDetailAo, weekMap,realDateMap);
            CourseAdjustmentConvert.INSTANCE.updateDoFromResult(realDateInfo, courseAdjustmentDo);

            CourseAdjustmentDO teacherInfo = getTeacherInfo(courseAdjustmentDetailAo, teacherDtoMap);
            CourseAdjustmentConvert.INSTANCE.updateDoFromResult(teacherInfo, courseAdjustmentDo);
            checkDataWhetherChanged(courseAdjustmentDetailAo,batchTeacherTables);
            checkAdjustedDataWhetherChanged(courseAdjustmentDetailAo,batchTeacherTables);
            if (courseAdjustmentDetailAo.getIsAdminApply()) {
                if (courseAdjustmentBasicAo.getIsNotStatistics() == null) {
                    throw new BusinessException(CodeRes.CODE_800012);
                }
                List<Long> teacherIdList = new ArrayList<>();
                teacherIdList.add(courseAdjustmentDetailAo.getTeacherId());
                if (courseAdjustmentDetailAo.getAdjustedTeacherId() != null) {
                    teacherIdList.add(courseAdjustmentDetailAo.getAdjustedTeacherId());
                }
                List<Long> publishTeacherUserIds = teacherDtoList.stream().filter(teacherDto -> teacherIdList.contains(teacherDto.getId())).map(TeacherDto::getUserId).collect(Collectors.toList());
                courseAdjustmentDo.setApprovalState(ApprovalState.PASS);
                saveAdjust(index,slaveCourseAdjustmentDos,courseAdjustmentDetailAo, items, weekMap, courseAdjustmentDo,courseAdjustmentBasicAo,realDateMap,batchTeacherTables);
                adjustmentFatherSetIsInvalidFalseApply(courseAdjustmentDo, true);
                basicPushMessage.adjustmentPassPublish(courseAdjustmentDo.getId(), AdjustmentType.ADJUSTMENT, publishTeacherUserIds);
            } else {
                String modelId = flowFacade.getFlowsModel(courseAdjustmentDetailAo.getStartFlowAo().getRelatedType());
                result = flowFacade.checkModelStarting(modelId);
                if (!FLOW_IS_START.equals(result)) {
                    courseAdjustmentDo.setApprovalState(ApprovalState.PASS);
                }
                saveAdjust(index,slaveCourseAdjustmentDos,courseAdjustmentDetailAo, items, weekMap, courseAdjustmentDo,courseAdjustmentBasicAo,realDateMap,batchTeacherTables);
            }
            if (result != null) {
                CourseAdjustmentFlowDTO courseAdjustmentFlowDto = new CourseAdjustmentFlowDTO();
                courseAdjustmentFlowDto.setCourseAdjustmentDo(courseAdjustmentDo);
                courseAdjustmentFlowDto.setStartFlowAo(courseAdjustmentDetailAo.getStartFlowAo());
                courseAdjustmentFlowDto.setResult(result);
                courseAdjustmentFlowDtos.add(courseAdjustmentFlowDto);
            }
        }
        this.saveBatch(slaveCourseAdjustmentDos);
        //发起流程
        JwtUser jwtUser = this.getJwtUser();
        courseAdjustmentFlowDtos.forEach(courseAdjustmentFlowDto -> {
            startApprovalAsncy.startApprovalApply(jwtUser,courseAdjustmentFlowDto.getStartFlowAo(),courseAdjustmentFlowDto.getCourseAdjustmentDo(),courseAdjustmentFlowDto.getResult());
        });
        applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.CORRECT.getType());
        return applyAdjustmentVo;
    }

    private ApplyAdjustmentVO checkRepeatCourse(List<Long> allTeacherIds,List<CourseAdjustmentDetailAO> courseAdjustmentDetailAoList,
                                                Map<Integer, List<String>> courseTimeMap,Map<Integer,List<String>> adjustedCourseTimeMap) {
        ApplyAdjustmentVO applyAdjustmentVo = new ApplyAdjustmentVO();
        //获取审批中的调课信息列表
        QueryWrapper courseWrapper = new QueryWrapper();
        courseWrapper.eq("approval_state","UNDER_APPROVAL").in("teacher_id",allTeacherIds);
        List<CourseAdjustmentDO> courseDoList = this.queryForMap(CourseAdjustmentDO.class, courseWrapper);
        //获取审批中的被调课信息列表
        QueryWrapper adjustedWrapper = new QueryWrapper();
        adjustedWrapper.eq("approval_state","UNDER_APPROVAL").in("adjusted_teacher_id",allTeacherIds);
        List<CourseAdjustmentDO> adjustedDoList = this.queryForMap(CourseAdjustmentDO.class, adjustedWrapper);
        //获取审批中的代课信息
        Map<String, Object> map = new HashMap<>();
        map.put("teacherIds", allTeacherIds);
        List<ReplaceRecordDTO> getMoreReplaceRecords = this.queryForMapMapper(ReplaceRecordDTO.class, "getMoreReplaceRecords", map);
        Map<Long, List<ReplaceRecordDTO>> replaceTeacherIdMap = getMoreReplaceRecords.stream().collect(Collectors.groupingBy(ReplaceRecordDTO::getTeacherId));
        Map<Long, List<ReplaceRecordDTO>> replaceTeacherMap = getMoreReplaceRecords.stream().filter(replaceRecordDto -> Objects.nonNull(replaceRecordDto.getReplaceTeacherId())).collect(Collectors.groupingBy(ReplaceRecordDTO::getReplaceTeacherId));
        Map<Long, List<CourseAdjustmentDO>> courseTeacherMap = courseDoList.stream().collect(Collectors.groupingBy(CourseAdjustmentDO::getTeacherId));
        Map<Long, List<CourseAdjustmentDO>> adjustedTeacherMap = adjustedDoList.stream().collect(Collectors.groupingBy(CourseAdjustmentDO::getAdjustedTeacherId));
        for (CourseAdjustmentDetailAO courseAdjustmentDetailAo : courseAdjustmentDetailAoList) {
            int index = courseAdjustmentDetailAoList.indexOf(courseAdjustmentDetailAo);
            //判断是否有相同的调课数据
            for (int i=index+1;i<courseAdjustmentDetailAoList.size();i++) {
                CourseAdjustmentDetailAO courseAdjustment = courseAdjustmentDetailAoList.get(i);
                if (courseAdjustmentDetailAo.equals(courseAdjustment)) {
                    applyAdjustmentVo.setContent(AdjustmentCheckTypeEnum.REPEAT_ERROR.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.REPEAT_ERROR.getType());
                    applyAdjustmentVo.setIndex(Arrays.asList(index, i));
                    return applyAdjustmentVo;
                }
            }
            List<String> courseList = courseTimeMap.get(index);
            //判断有无审批中的调课信息
            List<CourseAdjustmentDO> courseAdjustmentDos = courseTeacherMap.get(courseAdjustmentDetailAo.getTeacherId());
            if (!CollectionUtils.isEmpty(courseAdjustmentDos)) {
                List<String> approvalCourseTime = courseAdjustmentDos.stream().filter(courseAdjustmentDo -> !courseAdjustmentDo.getIsInvalid()).map(CourseAdjustmentDO::getCourseTime).collect(Collectors.toList());
                Optional<String> first = courseList.stream().filter(approvalCourseTime::contains).findFirst();
                if (first.isPresent()) {
                    applyAdjustmentVo.setContent(first.get() + AdjustmentCheckTypeEnum.APPROVAL_ERROR.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.APPROVAL_ERROR.getType());
                    applyAdjustmentVo.setIndex(Collections.singletonList(index));
                    return applyAdjustmentVo;
                }
             }
            //判断有无审批中的被调课信息
            List<CourseAdjustmentDO> courseAdjustment = adjustedTeacherMap.get(courseAdjustmentDetailAo.getTeacherId());
            if (!CollectionUtils.isEmpty(courseAdjustment)) {
                List<String> approvalCourseTime = courseAdjustment.stream().filter(courseAdjustmentDo -> !courseAdjustmentDo.getAdjustedIsInvalid()).map(CourseAdjustmentDO::getAdjustedCourseTime).collect(Collectors.toList());
                Optional<String> first = courseList.stream().filter(approvalCourseTime::contains).findFirst();
                if (first.isPresent()) {
                    applyAdjustmentVo.setContent(first.get() + AdjustmentCheckTypeEnum.APPROVAL_ERROR.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.APPROVAL_ERROR.getType());
                    applyAdjustmentVo.setIndex(Collections.singletonList(index));
                    return applyAdjustmentVo;
                }
            }
            //判断有无审批中的代课信息
            ApplyAdjustmentVO replaceApplyAdjustment = getReplace(applyAdjustmentVo, replaceTeacherIdMap, replaceTeacherMap, courseAdjustmentDetailAo, index, courseList);
            if (replaceApplyAdjustment != null) {
                return replaceApplyAdjustment;
            }
            //判断重复的调课时间
            for (Map.Entry<Integer, List<String>> entry : courseTimeMap.entrySet()) {
                Integer k = entry.getKey();
                List<String> v = entry.getValue();
                if (!k.equals(index)) {
                    Optional<String> first = courseList.stream().filter(v::contains).findFirst();
                    if (first.isPresent()) {
                        applyAdjustmentVo.setContent(AdjustmentCheckTypeEnum.REPETITION_COURSE.getName());
                        applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.REPETITION_COURSE.getType());
                        applyAdjustmentVo.setIndex(Arrays.asList(index, k));
                        return applyAdjustmentVo;
                    }
                }
            }
            //判断重复的被调课时间
            List<String> adjustedCourseList = adjustedCourseTimeMap.get(index);
            //判断被调课是否有审批中的调课
            ApplyAdjustmentVO applyAdjust = getApplyAdjustment(replaceTeacherIdMap,replaceTeacherMap,applyAdjustmentVo, courseTeacherMap, adjustedTeacherMap, courseAdjustmentDetailAo, index, adjustedCourseList);
            if (applyAdjust != null){
                return applyAdjust;
            }
            ApplyAdjustmentVO applyAdjustment = getApplyAdjustmentVo(adjustedCourseTimeMap, applyAdjustmentVo, index, courseList, adjustedCourseList);
            if (applyAdjustment != null) {
                return applyAdjustment;
            }
        }
        return applyAdjustmentVo;
    }

    private ApplyAdjustmentVO getReplace(ApplyAdjustmentVO applyAdjustmentVo, Map<Long, List<ReplaceRecordDTO>> replaceTeacherIdMap, Map<Long, List<ReplaceRecordDTO>> replaceTeacherMap, CourseAdjustmentDetailAO courseAdjustmentDetailAo, int index, List<String> courseList) {
        List<ReplaceRecordDTO> replaceAll = new ArrayList<>();
        List<ReplaceRecordDTO> replaceRecordDtos = replaceTeacherIdMap.get(courseAdjustmentDetailAo.getTeacherId());
        if (!CollectionUtils.isEmpty(replaceRecordDtos)) {
            replaceAll.addAll(replaceRecordDtos);
        }
        List<ReplaceRecordDTO> replaceRecord = replaceTeacherMap.get(courseAdjustmentDetailAo.getTeacherId());
        if (!CollectionUtils.isEmpty(replaceRecord)) {
            replaceAll.addAll(replaceRecord);
        }
        if (!CollectionUtils.isEmpty(replaceAll)) {
            List<String> replaceCourseTime = replaceAll.stream().map(ReplaceRecordDTO::getCourseTime).collect(Collectors.toList());
            Optional<String> first = courseList.stream().filter(replaceCourseTime::contains).findFirst();
            if (first.isPresent()) {
                applyAdjustmentVo.setContent(first.get() + AdjustmentCheckTypeEnum.REPLACE_ERROR.getName());
                applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.REPLACE_ERROR.getType());
                applyAdjustmentVo.setIndex(Collections.singletonList(index));
                return applyAdjustmentVo;
            }
        }
        return null;
    }

    private ApplyAdjustmentVO getApplyAdjustment(Map<Long, List<ReplaceRecordDTO>> replaceTeacherIdMap,Map<Long, List<ReplaceRecordDTO>> replaceTeacherMap,
                                                 ApplyAdjustmentVO applyAdjustmentVo, Map<Long, List<CourseAdjustmentDO>> courseTeacherMap,
                                                 Map<Long, List<CourseAdjustmentDO>> adjustedTeacherMap, CourseAdjustmentDetailAO courseAdjustmentDetailAo,
                                                 int index, List<String> adjustedCourseList) {
        if (Objects.nonNull(courseAdjustmentDetailAo.getAdjustedTeacherId())) {
            List<CourseAdjustmentDO> adjustmentDos = courseTeacherMap.get(courseAdjustmentDetailAo.getAdjustedTeacherId());
            if (!CollectionUtils.isEmpty(adjustmentDos)) {
                List<String> approvalCourseTime = adjustmentDos.stream().filter(courseAdjustmentDo -> !courseAdjustmentDo.getIsInvalid()).map(CourseAdjustmentDO::getCourseTime).collect(Collectors.toList());
                Optional<String> first = adjustedCourseList.stream().filter(approvalCourseTime::contains).findFirst();
                if (first.isPresent()) {
                    applyAdjustmentVo.setContent(first.get() + AdjustmentCheckTypeEnum.APPROVAL_ERROR.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.APPROVAL_ERROR.getType());
                    applyAdjustmentVo.setIndex(Collections.singletonList(index));
                    return applyAdjustmentVo;
                }
            }
            //判断有无审批中的被调课信息
            List<CourseAdjustmentDO> adjustment = adjustedTeacherMap.get(courseAdjustmentDetailAo.getAdjustedTeacherId());
            if (!CollectionUtils.isEmpty(adjustment)) {
                List<String> approvalCourseTime = adjustment.stream().filter(courseAdjustmentDo -> !courseAdjustmentDo.getAdjustedIsInvalid()).map(CourseAdjustmentDO::getAdjustedCourseTime).collect(Collectors.toList());
                Optional<String> first = adjustedCourseList.stream().filter(approvalCourseTime::contains).findFirst();
                if (first.isPresent()) {
                    applyAdjustmentVo.setContent(first.get() + AdjustmentCheckTypeEnum.APPROVAL_ERROR.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.APPROVAL_ERROR.getType());
                    applyAdjustmentVo.setIndex(Collections.singletonList(index));
                    return applyAdjustmentVo;
                }
            }
            //判断有无审批中的代课信息
            List<ReplaceRecordDTO> replaceAll = new ArrayList<>();
            List<ReplaceRecordDTO> replaceRecordDtos = replaceTeacherIdMap.get(courseAdjustmentDetailAo.getAdjustedTeacherId());
            List<ReplaceRecordDTO> replaceRecord = replaceTeacherMap.get(courseAdjustmentDetailAo.getAdjustedTeacherId());
            if (!CollectionUtils.isEmpty(replaceRecordDtos)) {
                replaceAll.addAll(replaceRecordDtos);
            }
            if (!CollectionUtils.isEmpty(replaceRecord)) {
                replaceAll.addAll(replaceRecord);
            }
            if (!CollectionUtils.isEmpty(replaceAll)) {
                List<String> replaceCourse = replaceAll.stream().map(ReplaceRecordDTO::getCourseTime).collect(Collectors.toList());
                Optional<String> first = adjustedCourseList.stream().filter(replaceCourse::contains).findFirst();
                if (first.isPresent()) {
                    applyAdjustmentVo.setContent(first.get() + AdjustmentCheckTypeEnum.REPLACE_ERROR.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.REPLACE_ERROR.getType());
                    applyAdjustmentVo.setIndex(Collections.singletonList(index));
                    return applyAdjustmentVo;
                }
            }
        }
        return null;
    }

    private ApplyAdjustmentVO getApplyAdjustmentVo(Map<Integer, List<String>> adjustedCourseTimeMap, ApplyAdjustmentVO applyAdjustmentVo, int index, List<String> courseList, List<String> adjustedCourseList) {
        for (Map.Entry<Integer, List<String>> entry : adjustedCourseTimeMap.entrySet()) {
            Integer k = entry.getKey();
            List<String> v = entry.getValue();
            if (!k.equals(index)) {
                //判断重复的被调课时间
                Optional<String> first = adjustedCourseList.stream().filter(v::contains).findFirst();
                if (first.isPresent()) {
                    applyAdjustmentVo.setContent(AdjustmentCheckTypeEnum.REPETITION_ADJUSTED_COURSE.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.REPETITION_ADJUSTED_COURSE.getType());
                    applyAdjustmentVo.setIndex(Arrays.asList(index, k));
                    return applyAdjustmentVo;
                }
                //判断调课时间与被调课时间重复
                Optional<String> optional = courseList.stream().filter(v::contains).findFirst();
                if (optional.isPresent()) {
                    applyAdjustmentVo.setContent(AdjustmentCheckTypeEnum.ADJUSTED_COURSE.getName());
                    applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.ADJUSTED_COURSE.getType());
                    applyAdjustmentVo.setIndex(Arrays.asList(index, k));
                    return applyAdjustmentVo;
                }
            }
        }
        return null;
    }

    private Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables(Map<Integer,List<String>> courseTimeMap,Map<Integer,List<String>> adjustedCourseTimeMap,
                                                                                                             List<CourseAdjustmentDetailAO> courseAdjustmentDetailAoList,CourseAdjustmentBasicAO courseAdjustmentBasicAo,
                                                                                                             List<Long> teacherIds,Map<String, java.sql.Date> realDateMap,Map<Integer, WeekConfigVO> weekMap) {

        //获取教师周课表
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> weekDetailBatch = generalTableService.teacherTableWeekDetailBatch(teacherIds, new ArrayList<>(weekMap.keySet()));
        //获取所有周次
        for (CourseAdjustmentDetailAO courseAdjustmentDetailAo : courseAdjustmentDetailAoList) {
            int index = courseAdjustmentDetailAoList.indexOf(courseAdjustmentDetailAo);
            List<String> courseTimeList = new ArrayList<>();
            List<String> adjustedCourseTimeList = new ArrayList<>();
            courseTimeList.add(courseAdjustmentDetailAo.getCourseTime());
            if (!StringUtils.isBlank(courseAdjustmentDetailAo.getAdjustedCourseTime())) {
                adjustedCourseTimeList.add(courseAdjustmentDetailAo.getAdjustedCourseTime());
            }
            //循环周次
            if (!CycleTypeEnum.NOT_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                if (CycleTypeEnum.CUSTOM_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                    java.sql.Date courseDate = realDateMap.get(courseAdjustmentDetailAo.getCourseTime());
                    Date date = new Date(courseDate.getTime());
                    DateTime courseStartTime = DateUtil.parse(DateUtil.formatDate(date) + " " + courseAdjustmentDetailAo.getCourseStartTime());
                    //获取周数
                    long weekNum = DateUtil.betweenWeek(courseStartTime, DateUtil.parse(courseAdjustmentBasicAo.getEndTime()), false);
                    courseAdjustmentBasicAo.setWeekCycleNum(Integer.parseInt(String.valueOf(weekNum)));
                }
                if (courseAdjustmentBasicAo.getWeekCycleNum() > 0) {
                    Integer weekCycleNum = courseAdjustmentBasicAo.getWeekCycleNum();
                    for (int i = 1; i <= weekCycleNum; i++) {
                        //判断调课
                        boolean courseIsSame = false;
                        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekTypeMap = weekDetailBatch.get(courseAdjustmentDetailAo.getTeacherId());
                        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = new HashMap<>(6);
                        if (!CollectionUtils.isEmpty(weekTypeMap)) {
                            weekEnumMapMap = weekTypeMap.get(courseAdjustmentDetailAo.getWeekNumberOfCourseTimeByNumber(i));
                        }
                        if (!CollectionUtils.isEmpty(weekEnumMapMap)) {
                            Map<String, List<TeacherAbbDetailVO>> abbMap = weekEnumMapMap.get(courseAdjustmentDetailAo.getDayOfWeekOfCourseTime());
                            if (abbMap != null && !abbMap.isEmpty()) {
                                courseIsSame = true;
                            }
                        }
                        if (courseIsSame) {
                            String courseTimeByNumber = courseAdjustmentDetailAo.getCourseTimeByNumber(i);
                            courseTimeList.add(courseTimeByNumber);
                            if (!StringUtils.isBlank(courseAdjustmentDetailAo.getAdjustedCourseTime())) {
                                String adjustedCourseTimeByNumber = courseAdjustmentDetailAo.getAdjustedCourseTimeByNumber(i);
                                adjustedCourseTimeList.add(adjustedCourseTimeByNumber);
                            }
                        }else {
                            if (CycleTypeEnum.WEEK_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                                Integer week = courseAdjustmentDetailAo.getWeekNumberOfCourseTimeByNumber(i+1);
                                WeekConfigVO weekConfigVo = weekMap.get(week);
                                if (Objects.nonNull(weekConfigVo)) {
                                    weekCycleNum = weekCycleNum + 1;
                                }
                            }
                        }

                    }
                }
            }
            courseTimeMap.put(index,courseTimeList);
            adjustedCourseTimeMap.put(index,adjustedCourseTimeList);
        }
        return weekDetailBatch;
    }

    /**
     * 获取被调课程相关信息
     *
     * @param apply
     * @param items
     * @param weekMap
     * @return
     */
    private CourseAdjustmentDO getAdjustedCourseInfo(CourseAdjustmentDetailAO apply, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        if (!StringUtils.isEmpty(apply.getAdjustedCourseTime())) {
            CourseAdjustmentDO adjustedCourseInfo = null;
            if (apply.getAdjustedType() == null) {
                throw new BusinessException(CodeRes.CODE_800010, "adjustedType");
            } else {
                if (apply.getAdjustedType().equals(AbbTypeEnum.NORMAL)) {
                    adjustedCourseInfo = getAdjustedCourseIfNormal(apply, items, weekMap);
                } else if (apply.getAdjustedType().equals(AbbTypeEnum.ADJUSTMENT)) {
                    adjustedCourseInfo = getAdjustedCourseIfAdjustment(apply, apply.getAdjustedFatherId());
                } else if (apply.getAdjustedType().equals(AbbTypeEnum.COVERADJUSTMENT)) {
                    throw new BusinessException(CodeRes.CODE_800009, "被调课");
                } else if (apply.getAdjustedType().equals(AbbTypeEnum.SUBSTITUTE)) {
                    adjustedCourseInfo = getAdjustedCourseIfSubstitute(apply.getAdjustedFatherId());
                }
                return adjustedCourseInfo;

            }
        }
        return null;
    }

    /**
     * 获取调课相关信息
     *
     * @param apply   　调课申请
     * @param items   　日历项
     * @param weekMap 　周配置信息
     * @return
     */
    private CourseAdjustmentDO getCourseInfo(CourseAdjustmentDetailAO apply, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        if (apply.getType() != null) {
            CourseAdjustmentDO courseInfo = null;
            if (apply.getType().equals(AbbTypeEnum.NORMAL)) {
                courseInfo = getCourseAdjustmentIfNormal(apply, items, weekMap);
            } else if (apply.getType().equals(AbbTypeEnum.ADJUSTMENT)) {
                courseInfo = getCourseAdjustmentIfAdjust(apply);
            } else if (apply.getType().equals(AbbTypeEnum.COVERADJUSTMENT)) {
                throw new BusinessException(CodeRes.CODE_800009, "调课");
            } else if (apply.getType().equals(AbbTypeEnum.SUBSTITUTE)) {
                courseInfo = getCourseAdjustIfSubstitute(apply.getFatherId());
            }
            return courseInfo;
        }
        return null;
    }

    /**
     * 获取真实的日期
     *
     * @param apply 调课申请
     */
    private CourseAdjustmentDO getRealDateInfo(CourseAdjustmentDetailAO apply, Map<Integer, WeekConfigVO> weekMap,Map<String, java.sql.Date> realDateMap) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        if (!CollectionUtils.isEmpty(realDateMap)) {
            java.sql.Date adjustDate = realDateMap.get(apply.getCourseTime());
            result.setAdjustDate(adjustDate);
            if (apply.getAdjustedCourseTime() != null && !apply.getAdjustedCourseTime().isEmpty()) {
                java.sql.Date adjustedDate = realDateMap.get(apply.getAdjustedCourseTime());
                result.setAdjustedDate(adjustedDate);
            }
        }
        return result;
    }

    /**
     * 获取课程时间列表
     *
     * @param apply
     * @return
     */
    private List<String> getCourseTimeList(CourseAdjustmentApplyAO apply) {
        List<CourseAdjustmentDetailAO> courseAdjustmentDetailAoList = apply.getCourseAdjustmentDetailAo();
        //调课时间
        List<String> courseTimeList = courseAdjustmentDetailAoList.stream().map(CourseAdjustmentDetailAO::getCourseTime).collect(Collectors.toList());
        //被调课时间
        List<String> adjustedCourseTimeList = courseAdjustmentDetailAoList.stream().map(CourseAdjustmentDetailAO::getAdjustedCourseTime).filter(adjustedCourseTime ->!StringUtils.isBlank(adjustedCourseTime)).collect(Collectors.toList());
        courseTimeList.addAll(adjustedCourseTimeList);
        return courseTimeList;
    }


    /**
     * 获取教师列表
     *
     * @param teacherIdList
     * @return
     */
    public List<TeacherDto> getTeacherList(List<Long> teacherIdList) {
        String teacherIds = Joiner.on(",").join(teacherIdList);
        return foundationFacade.getTeachersByIds(teacherIds);
    }


    /**
     * 获取教师相关信息
     *
     * @param apply
     * @param teacherDtoMap
     * @return
     */
    private CourseAdjustmentDO getTeacherInfo(CourseAdjustmentDetailAO apply, Map<Long, TeacherDto> teacherDtoMap) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        if (apply.getTeacherId() != null) {
            TeacherDto teacher = teacherDtoMap.get(apply.getTeacherId());
            if (teacher != null) {
                result.setTeacherName(teacher.getName());
                result.setDepartmentName(teacher.getDepartmentName());
            }
        }
        if (apply.getAdjustedTeacherId() != null) {
            TeacherDto teacherDto = teacherDtoMap.get(apply.getAdjustedTeacherId());
            if (teacherDto != null) {
                result.setAdjustedTeacherName(teacherDto.getName());
            }
        }
        return result;
    }


    /**
     * 调课课程是代课数据
     *
     * @param adjustedFatherId
     */
    private CourseAdjustmentDO getAdjustedCourseIfSubstitute(Long adjustedFatherId) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        if (adjustedFatherId == null) {
            throw new BusinessException(CodeRes.CODE_800008, "被调课", "adjustedFatherId");
        } else {
            if (adjustedFatherId != 0L) {
                result.setAdjustedFatherType(AdjustmentType.REPLACE);
                ReplaceSectionDO fatherData = replaceSectionService.loadById(adjustedFatherId);
                if (fatherData != null) {
                    result.setAdjustedOriginCourseTime(fatherData.getOriginCourseTime());
                    result.setAdjustedOriginTeacherId(fatherData.getOriginTeacherId());
                    result.setAdjustedBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                }
            }
        }
        return result;
    }

    /**
     * 被调课课程是调课数据
     *
     * @param apply
     * @param adjustedFatherId
     */
    private CourseAdjustmentDO getAdjustedCourseIfAdjustment(CourseAdjustmentDetailAO apply, Long adjustedFatherId) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        if (adjustedFatherId == null) {
            throw new BusinessException(CodeRes.CODE_800008, "被调课", "adjustedFatherId");
        } else {
            if (adjustedFatherId != 0L) {
                result.setAdjustedFatherType(AdjustmentType.ADJUSTMENT);
                CourseAdjustmentDO adjustedFatherData = courseAdjustmentDao.load(adjustedFatherId);
                if (adjustedFatherData != null) {
                    if (apply.getAdjustedTeacherId().equals(adjustedFatherData.getTeacherId())) {
                        result.setAdjustedOriginCourseTime(adjustedFatherData.getOriginCourseTime());
                        result.setAdjustedOriginTeacherId(adjustedFatherData.getOriginTeacherId());
                        result.setAdjustedBeforeOriginCourseTime(adjustedFatherData.getBeforeOriginCourseTime());
                    } else if (apply.getAdjustedTeacherId().equals(adjustedFatherData.getAdjustedTeacherId())) {
                        result.setAdjustedOriginCourseTime(adjustedFatherData.getAdjustedOriginCourseTime());
                        result.setAdjustedOriginTeacherId(adjustedFatherData.getAdjustedOriginTeacherId());
                        result.setAdjustedBeforeOriginCourseTime(adjustedFatherData.getAdjustedBeforeOriginCourseTime());
                    }
                }
            }
        }
        return result;
    }

    /**
     * 被调课课程为原课表数据
     *
     * @param apply
     * @param items
     * @param weekMap
     */
    private CourseAdjustmentDO getAdjustedCourseIfNormal(CourseAdjustmentDetailAO apply, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        result.setAdjustedFatherId(0L);
        result.setAdjustedOriginCourseTime(apply.getAdjustedCourseTime());
        result.setAdjustedOriginTeacherId(apply.getAdjustedTeacherId());
        result.setAdjustedBeforeOriginCourseTime(apply.getAdjustedBeforeOriginCourseTime(items, weekMap));
        return result;
    }

    /**
     * 调课课程是代课数据
     *
     * @param fatherId
     */
    private CourseAdjustmentDO getCourseAdjustIfSubstitute(Long fatherId) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        if (fatherId == null) {
            throw new BusinessException(CodeRes.CODE_800008, "调课", "fatherId");
        } else {
            if (fatherId != 0L) {
                // 先代课后调课不纳入统计
                result.setIsNotStatistics(true);
                result.setFatherType(AdjustmentType.REPLACE);
                ReplaceSectionDO fatherData = replaceSectionService.loadById(fatherId);
                if (fatherData != null) {
                    result.setOriginCourseTime(fatherData.getOriginCourseTime());
                    result.setOriginTeacherId(fatherData.getOriginTeacherId());
                    result.setBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                }
            }
        }
        return result;
    }

    /**
     * 调课课程是调课数据
     *
     * @param apply
     */
    private CourseAdjustmentDO getCourseAdjustmentIfAdjust(CourseAdjustmentDetailAO apply) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        if (apply.getFatherId() == null) {
            throw new BusinessException(CodeRes.CODE_800008, "调课", "fatherId");
        } else {
            if (apply.getFatherId() != 0L) {
                result.setFatherType(AdjustmentType.ADJUSTMENT);
                CourseAdjustmentDO fatherData = courseAdjustmentDao.load(apply.getFatherId());
                if (fatherData != null) {
                    if (apply.getTeacherId().equals(fatherData.getTeacherId())) {
                        result.setOriginCourseTime(fatherData.getOriginCourseTime());
                        result.setOriginTeacherId(fatherData.getOriginTeacherId());
                        result.setBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                    } else if (apply.getTeacherId().equals(fatherData.getAdjustedTeacherId())) {
                        result.setOriginCourseTime(fatherData.getAdjustedOriginCourseTime());
                        result.setOriginTeacherId(fatherData.getAdjustedOriginTeacherId());
                        result.setBeforeOriginCourseTime(fatherData.getAdjustedBeforeOriginCourseTime());
                    }
                }
            }
        }
        return result;
    }

    /**
     * 调课课程为原课表数据
     *
     * @param apply
     * @param items
     * @param weekMap
     */
    private CourseAdjustmentDO getCourseAdjustmentIfNormal(CourseAdjustmentDetailAO apply, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        CourseAdjustmentDO result = new CourseAdjustmentDO();
        result.setFatherId(0L);
        result.setOriginCourseTime(apply.getCourseTime());
        result.setOriginTeacherId(apply.getTeacherId());
        result.setBeforeOriginCourseTime(apply.getBeforeOriginCourseTime(items, weekMap));
        return result;
    }

    /**
     * 校验被调课数据是否变化
     *
     * @param apply
     */
    private void checkAdjustedDataWhetherChanged(CourseAdjustmentDetailAO apply,Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables) {
        if (!apply.getIsSelfStudy()) {
            // 非自修
            Integer weekNumber = CourseAdjustmentUtils.chinese2Number(apply.getChineseWeekNumberOfAdjustedCourseTime());
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> nowTeacherTable = new HashMap<>(6);
            Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekMap = batchTeacherTables.get(apply.getAdjustedTeacherId());
            if (!CollectionUtils.isEmpty(weekMap)) {
                nowTeacherTable = weekMap.get(weekNumber);
            }
            if (nowTeacherTable == null || nowTeacherTable.isEmpty()) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
            // 被调周几
            Map<String, List<TeacherAbbDetailVO>> stringListMap = nowTeacherTable.get(apply.getDayOfWeekOfAdjustedCourseTime());
            if (stringListMap == null || stringListMap.isEmpty()) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
            // 被调节次
            assert apply.getAdjustedCourseTime() != null;
            String section = apply.getSectionOfAdjustedCourseTime();
            List<TeacherAbbDetailVO> teacherAbbDetailVos = stringListMap.get(section);
            if (teacherAbbDetailVos == null || teacherAbbDetailVos.isEmpty()) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
            TeacherAbbDetailVO teacherAbbDetailVO = teacherAbbDetailVos.get(0);
            if (!teacherAbbDetailVO.getClassOrRoomId().equals(apply.getAdjustedCourseRoomId())) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
        }
    }

    /**
     * 校验数据是否发生变化
     *
     * @param apply
     */
    private void checkDataWhetherChanged(CourseAdjustmentDetailAO apply,Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables) {
        // 保存前校验课表是否发生改变
        Integer weekNumber = apply.getWeekNumberOfCourseTime();
        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> teacherMap = batchTeacherTables.get(apply.getTeacherId());
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> nowTeacherTable = new HashMap<>(6);
        if (!CollectionUtils.isEmpty(teacherMap)) {
            nowTeacherTable = teacherMap.get(weekNumber);
        }
        if (nowTeacherTable == null || nowTeacherTable.isEmpty()) {
            throw new BusinessException(CodeRes.CODE_800013);
        }
        // 周几
        Map<String, List<TeacherAbbDetailVO>> dayOfWeekTable = nowTeacherTable.get(apply.getDayOfWeekOfCourseTime());
        if (dayOfWeekTable == null || dayOfWeekTable.isEmpty()) {
            throw new BusinessException(CodeRes.CODE_800013);
        }
        // 节次
        String section = apply.getSectionOfCourseTime();
        List<TeacherAbbDetailVO> teacherAbbDetailVos1 = dayOfWeekTable.get(section);
        if (teacherAbbDetailVos1 == null || teacherAbbDetailVos1.isEmpty()) {
            throw new BusinessException(CodeRes.CODE_800013);
        }
        TeacherAbbDetailVO teacherAbbDetailVo1 = teacherAbbDetailVos1.get(0);
        if (!teacherAbbDetailVo1.getClassOrRoomId().equals(apply.getCourseRoomId())) {
            throw new BusinessException(CodeRes.CODE_800013);
        }
    }


    /**
     * 校验申请周次信息
     *
     * @param apply
     */
    private ApplyAdjustmentVO checkWeekIndex(CourseAdjustmentApplyAO apply,Map<String, java.sql.Date> realDateMap) {
        ApplyAdjustmentVO applyAdjustmentVo = new ApplyAdjustmentVO();
        CourseAdjustmentBasicAO courseAdjustmentBasicAo = apply.getCourseAdjustmentBasicAo();
        List<CourseAdjustmentDetailAO> courseAdjustmentDetailAoList = apply.getCourseAdjustmentDetailAo();
        if (CycleTypeEnum.WEEK_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
            if (courseAdjustmentBasicAo.getWeekCycleNum() > 0) {
                courseAdjustmentDetailAoList.forEach(courseAdjustmentDetailAo -> {
                    if (!courseAdjustmentDetailAo.getIsSelfStudy()) {
                        if (courseAdjustmentDetailAo.getChineseWeekNumberOfAdjustedCourseTime() != null && !courseAdjustmentDetailAo.getChineseWeekNumberOfCourseTime().equals(courseAdjustmentDetailAo.getChineseWeekNumberOfAdjustedCourseTime())) {
                            throw new BusinessException(CodeRes.CODE_800011);
                        }
                    }
                });
            }
        }else if (CycleTypeEnum.CUSTOM_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
            //跨周调课不允许周循环
            for (CourseAdjustmentDetailAO courseAdjustmentDetailAo : courseAdjustmentDetailAoList) {
                if (!courseAdjustmentDetailAo.getIsSelfStudy()) {
                    if (courseAdjustmentDetailAo.getChineseWeekNumberOfAdjustedCourseTime() != null && !courseAdjustmentDetailAo.getChineseWeekNumberOfCourseTime().equals(courseAdjustmentDetailAo.getChineseWeekNumberOfAdjustedCourseTime())) {
                        throw new BusinessException(CodeRes.CODE_800011);
                    }
                    //调课时间不在时间段内
                    java.sql.Date courseDate = realDateMap.get(courseAdjustmentDetailAo.getCourseTime());
                    Date date = new Date(courseDate.getTime());
                    DateTime courseStartTime = DateUtil.parse(DateUtil.formatDate(date) + " " + courseAdjustmentDetailAo.getCourseStartTime());
                    DateTime courseEndTime = DateUtil.parse(DateUtil.formatDate(date) + " " + courseAdjustmentDetailAo.getCourseEndTime());
                    if (!(DateUtil.parse(courseAdjustmentBasicAo.getStartTime()).before(courseStartTime) && DateUtil.parse(courseAdjustmentBasicAo.getEndTime()).after(courseEndTime))) {
                        //获取角标
                        int index = courseAdjustmentDetailAoList.indexOf(courseAdjustmentDetailAo);
                        applyAdjustmentVo.setIndex(Collections.singletonList(index));
                        applyAdjustmentVo.setType(AdjustmentCheckTypeEnum.CYCLE.getType());
                        applyAdjustmentVo.setContent(AdjustmentCheckTypeEnum.CYCLE.getName());
                        return applyAdjustmentVo;
                    }
                }
            }
        }
        return applyAdjustmentVo;
    }

    @Transactional(rollbackFor = Exception.class)
    void saveAdjust(Integer index,List<CourseAdjustmentDO> slaveCourseAdjustmentDos,CourseAdjustmentDetailAO apply,
                    List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap, CourseAdjustmentDO courseAdjustmentDO,
                    CourseAdjustmentBasicAO courseAdjustmentBasicAo,Map<String, java.sql.Date> realDateMap,Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables) {
        long id = courseAdjustmentDao.getSnowflakeIdWorkerNextId();
        courseAdjustmentDO.setId(id);
        if (!CycleTypeEnum.NOT_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
            if (CycleTypeEnum.CUSTOM_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                java.sql.Date courseDate = realDateMap.get(apply.getCourseTime());
                Date date = new Date(courseDate.getTime());
                DateTime courseStartTime = DateUtil.parse(DateUtil.formatDate(date) + " " + apply.getCourseStartTime());
                //获取周数
                long weekNum = DateUtil.betweenWeek(courseStartTime, DateUtil.parse(courseAdjustmentBasicAo.getEndTime()), false);
                if (Integer.parseInt(String.valueOf(weekNum)) == 0) {
                    throw new BusinessException(CodeRes.CODE_800015);
                }
                courseAdjustmentBasicAo.setWeekCycleNum(Integer.parseInt(String.valueOf(weekNum)));
            }
            if (courseAdjustmentBasicAo.getWeekCycleNum() > 0) {
                saveWeekCycleData(index,slaveCourseAdjustmentDos,apply, courseAdjustmentDO, weekMap, items,courseAdjustmentBasicAo,batchTeacherTables);
            }
        }
        courseAdjustmentDao.persist(courseAdjustmentDO);
    }

    /**
     * 启动审批流
     *
     * @param startFlowAo
     * @param courseAdjustmentDO
     * @param result
     */
    private void startApproval(StartFlowAO startFlowAo, CourseAdjustmentDO courseAdjustmentDO, String result) {
        String approvalNo;
        if (FLOW_IS_START.equals(result)) {
            startFlowAo.setRelatedId(courseAdjustmentDO.getId());
            this.startFlow(startFlowAo, courseAdjustmentDO, null);
        } else {
            return;
        }
        FlowVO flowVO = this.getFlowDetails(courseAdjustmentDO.getId(), startFlowAo.getRelatedType(), AdjustmentType.ADJUSTMENT);
        approvalNo = flowVO.getSerialNumber();
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.nested(w -> w.eq("id", courseAdjustmentDO.getId()).or().eq("main_id", courseAdjustmentDO.getId()));
        updateWrapper.set("approval_no", approvalNo);
        courseAdjustmentDao.executeUpdate(updateWrapper);
    }

    /**
     * 保存周循环数据
     *
     * @param apply
     * @param courseAdjustmentDO
     * @param items
     */
    private void saveWeekCycleData(Integer index,List<CourseAdjustmentDO> slaveCourseAdjustmentDos,CourseAdjustmentDetailAO apply,
                                   CourseAdjustmentDO courseAdjustmentDO,
                                   Map<Integer, WeekConfigVO> weekMap,
                                   List<ItemConfigVO> items,CourseAdjustmentBasicAO courseAdjustmentBasicAo,Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables) {
        List<CourseAdjustmentDO> attachmentData = new ArrayList<>();
        List<CourseTimeAO> courseTimeList = null;
        List<CourseTimeAO> adjustedCourseTimeList = null;
        if (!apply.getIsSelfStudy()) {
            courseTimeList = new ArrayList<>();
            adjustedCourseTimeList = new ArrayList<>();
        }
        Integer weekCycleNum = courseAdjustmentBasicAo.getWeekCycleNum();
        for (int i = 1; i <= weekCycleNum; i++) {
            boolean isSame;
            boolean courseIsSame;
            boolean adjustedIsSame;
            CourseAdjustmentBO courseAdjustmentBO = new CourseAdjustmentBO();
            courseIsSame = checkCourseWhetherSame(apply, items, weekMap, i, courseAdjustmentBO,batchTeacherTables);
            adjustedIsSame = checkAdjustedCourseWhetherSame(apply, items, weekMap, i, courseAdjustmentBO,batchTeacherTables);
            if (apply.getIsSelfStudy()) {
                isSame = courseIsSame;
            } else {
                isSame = courseIsSame && adjustedIsSame;
            }
            if (isSame) {
                CourseAdjustmentDO newCourseAdjustmentDO = CourseAdjustmentConvert.INSTANCE.doToDo(courseAdjustmentDO);
                newCourseAdjustmentDO.setIsAttachment(true);
                newCourseAdjustmentDO.setMainId(courseAdjustmentDO.getId());
                newCourseAdjustmentDO.setId(null);
                newCourseAdjustmentDO.setIsUnusual(false);
                newCourseAdjustmentDO.setIsInvalid(false);
                newCourseAdjustmentDO.setAdjustedIsInvalid(false);
                newCourseAdjustmentDO.setWeekCycleNum(0);
                CourseAdjustmentConvert.INSTANCE.updateCourseAdjustment(courseAdjustmentBO, newCourseAdjustmentDO);
                java.sql.Date newCourseTimeDate = CourseAdjustmentUtils.getDateByCourseTime(weekMap, courseAdjustmentBO.getCourseTime());
                newCourseAdjustmentDO.setAdjustDate(newCourseTimeDate);
                if (!apply.getIsSelfStudy()) {
                    java.sql.Date newAdjustedCourseTimeDate = CourseAdjustmentUtils.getDateByCourseTime(weekMap, courseAdjustmentBO.getAdjustedCourseTime());
                    newCourseAdjustmentDO.setAdjustedDate(newAdjustedCourseTimeDate);
                    CourseTimeAO courseTimeAo = new CourseTimeAO();
                    courseTimeAo.setCourseTime(courseAdjustmentBO.getCourseTime());
                    courseTimeAo.setCourseStartTime(courseAdjustmentBO.getCourseTimeStartTime());
                    courseTimeAo.setCourseEndTime(courseAdjustmentBO.getCourseTimeEndTime());
                    courseTimeList.add(courseTimeAo);

                    CourseTimeAO adjustedCourseAo = new CourseTimeAO();
                    adjustedCourseAo.setCourseTime(courseAdjustmentBO.getAdjustedCourseTime());
                    adjustedCourseAo.setCourseStartTime(courseAdjustmentBO.getAdjustedCourseTimeStartTime());
                    adjustedCourseAo.setCourseEndTime(courseAdjustmentBO.getAdjustedCourseTimeEndTime());
                    adjustedCourseTimeList.add(adjustedCourseAo);
                }
                if (!apply.getIsAdminApply()) {
                    newCourseAdjustmentDO.setApprovalState(ApprovalState.UNDER_APPROVAL);
                }
                attachmentData.add(newCourseAdjustmentDO);
            }else {
                if (CycleTypeEnum.WEEK_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                    Integer week = apply.getWeekNumberOfCourseTimeByNumber(i+1);
                    WeekConfigVO weekConfigVo = weekMap.get(week);
                    if (Objects.nonNull(weekConfigVo)) {
                        weekCycleNum = weekCycleNum + 1;
                    }
                }
            }
        }

        // 请假公出校验
        if (!CollectionUtils.isEmpty(courseTimeList) && !CollectionUtils.isEmpty(adjustedCourseTimeList)) {
            // 调课老师在被调时间是否请假公出的校验
            checkTeacherLeavingOrBusinessStateBatch(apply.getTeacherId(), adjustedCourseTimeList,weekMap);
            // 被调老师在调课时间是否请假公出的校验
            checkTeacherLeavingOrBusinessStateBatch(apply.getAdjustedTeacherId(), courseTimeList,weekMap);
        }

        if (!apply.getIsAdminApply()) {
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.eq("id", courseAdjustmentDO.getId()).set("week_cycle_num", courseAdjustmentBasicAo.getWeekCycleNum());
            courseAdjustmentDao.executeUpdate(updateWrapper);
        }else {
            //更新失效数据
            attachmentData.forEach(courseAdjustment -> {
                adjustmentFatherSetIsInvalidFalseApply(courseAdjustment, true);
            });
        }
        if (!attachmentData.isEmpty()) {
            slaveCourseAdjustmentDos.addAll(attachmentData);
        }else {
            if (CycleTypeEnum.CUSTOM_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                throw new BusinessException(CodeRes.CODE_800015);
            }else if (CycleTypeEnum.WEEK_CYCLE.getType().equals(courseAdjustmentBasicAo.getCycleType())) {
                throw new BusinessException(CodeRes.CODE_800019);
            }

        }
    }

    private boolean checkAdjustedCourseWhetherSame(CourseAdjustmentDetailAO apply, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap,
                                                   int i, CourseAdjustmentBO courseAdjustmentBO,Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables) {
        boolean adjustedIsSame = false;
        if (!apply.getIsSelfStudy()) {
            Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekTypeMap = batchTeacherTables.get(apply.getAdjustedTeacherId());
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> adjustedTeacherWeekMap = new HashMap<>(6);
            if (!CollectionUtils.isEmpty(weekTypeMap)) {
                adjustedTeacherWeekMap = weekTypeMap.get(apply.getWeekNumberOfCourseTimeByNumber(i));
            }
            if (!CollectionUtils.isEmpty(adjustedTeacherWeekMap)) {
                Map<String, List<TeacherAbbDetailVO>> adjustedWeekTable = adjustedTeacherWeekMap.get(apply.getDayOfWeekOfAdjustedCourseTime());
                if (adjustedWeekTable != null && !adjustedWeekTable.isEmpty()) {
                    List<TeacherAbbDetailVO> teacherAbbDetailVOList = adjustedWeekTable.get(apply.getSectionOfAdjustedCourseTime());
                    if (teacherAbbDetailVOList != null && !teacherAbbDetailVOList.isEmpty()) {
                        for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVOList) {
                            if (teacherAbbDetailVO.getTeacherId().equals(apply.getAdjustedTeacherId()) && !"自修".equals(teacherAbbDetailVO.getCourseName())) {
                                courseAdjustmentBO.setAdjustedCourseTime(apply.getAdjustedCourseTimeByNumber(i));
                                courseAdjustmentBO.setAdjustedCourseTimeStartTime(teacherAbbDetailVO.getStartTime());
                                courseAdjustmentBO.setAdjustedCourseTimeEndTime(teacherAbbDetailVO.getEndTime());
                                if (teacherAbbDetailVO.getAdjustmentId() != null) {
                                    courseAdjustmentBO.setAdjustedFatherId(teacherAbbDetailVO.getAdjustmentId());
                                } else {
                                    courseAdjustmentBO.setAdjustedOriginCourseTime(courseAdjustmentBO.getAdjustedCourseTime());
                                    courseAdjustmentBO.setAdjustedOriginTeacherId(teacherAbbDetailVO.getTeacherId());
                                }
                                courseAdjustmentBO.setAdjustedBeforeOriginCourseTime(apply.getAdjustedBeforeOriginCourseTime(i, items, weekMap));
                                if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.ADJUSTMENT)) {
                                    if (teacherAbbDetailVO.getAdjustmentId() != null) {
                                        courseAdjustmentBO.setAdjustedFatherType(AdjustmentType.ADJUSTMENT);
                                        CourseAdjustmentDO fatherData = courseAdjustmentDao.load(courseAdjustmentBO.getAdjustedFatherId());
                                        if (fatherData != null) {
                                            if (apply.getAdjustedTeacherId().equals(fatherData.getTeacherId())) {
                                                courseAdjustmentBO.setAdjustedOriginCourseTime(fatherData.getOriginCourseTime());
                                                courseAdjustmentBO.setAdjustedOriginTeacherId(fatherData.getOriginTeacherId());
                                                courseAdjustmentBO.setAdjustedBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                                            } else if (apply.getAdjustedTeacherId().equals(fatherData.getAdjustedTeacherId())) {
                                                courseAdjustmentBO.setAdjustedOriginCourseTime(fatherData.getAdjustedOriginCourseTime());
                                                courseAdjustmentBO.setAdjustedOriginTeacherId(fatherData.getAdjustedOriginTeacherId());
                                                courseAdjustmentBO.setAdjustedBeforeOriginCourseTime(fatherData.getAdjustedBeforeOriginCourseTime());
                                            }
                                        }
                                    }
                                } else if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.SUBSTITUTE)) {
                                    if (teacherAbbDetailVO.getAdjustmentId() != null) {
                                        courseAdjustmentBO.setAdjustedFatherType(AdjustmentType.REPLACE);
                                        ReplaceSectionDO fatherData = replaceSectionService.loadById(courseAdjustmentBO.getAdjustedFatherId());
                                        if (fatherData != null) {
                                            courseAdjustmentBO.setAdjustedOriginCourseTime(fatherData.getCourseTime());
                                            courseAdjustmentBO.setAdjustedOriginTeacherId(fatherData.getOriginTeacherId());
                                            courseAdjustmentBO.setAdjustedOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                                        }
                                    }
                                }
                                adjustedIsSame = true;
                            }
                        }
                    }
                }
            }
        }
        return adjustedIsSame;
    }

    /**
     * 校验课程是否相同
     *
     * @param apply
     * @param items
     * @param weekMap
     * @param i
     * @param courseAdjustmentBO
     * @return
     */
    private boolean checkCourseWhetherSame(CourseAdjustmentDetailAO apply, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap,
                                           int i, CourseAdjustmentBO courseAdjustmentBO,Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> batchTeacherTables) {
        boolean courseIsSame = false;
        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekTypeMap = batchTeacherTables.get(apply.getTeacherId());
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = new HashMap<>(6);
        if (!CollectionUtils.isEmpty(weekTypeMap)) {
            weekEnumMapMap = weekTypeMap.get(apply.getWeekNumberOfCourseTimeByNumber(i));
        }
        if (!CollectionUtils.isEmpty(weekEnumMapMap)) {
            Map<String, List<TeacherAbbDetailVO>> abbMap = weekEnumMapMap.get(apply.getDayOfWeekOfCourseTime());
            if (abbMap != null && !abbMap.isEmpty()) {
                List<TeacherAbbDetailVO> teacherAbbDetailVOList = abbMap.get(apply.getSectionOfCourseTime());
                if (teacherAbbDetailVOList != null && !teacherAbbDetailVOList.isEmpty()) {
                    for (TeacherAbbDetailVO teacherAbbDetailVO : teacherAbbDetailVOList) {
                        if (teacherAbbDetailVO.getTeacherId().equals(apply.getTeacherId()) && !"自修".equals(teacherAbbDetailVO.getCourseName())) {
                            courseAdjustmentBO.setCourseTime(apply.getCourseTimeByNumber(i));
                            courseAdjustmentBO.setCourseTimeStartTime(teacherAbbDetailVO.getStartTime());
                            courseAdjustmentBO.setCourseTimeEndTime(teacherAbbDetailVO.getEndTime());
                            if (teacherAbbDetailVO.getAdjustmentId() != null) {
                                courseAdjustmentBO.setFatherId(teacherAbbDetailVO.getAdjustmentId());
                            } else {
                                courseAdjustmentBO.setOriginCourseTime(courseAdjustmentBO.getCourseTime());
                                courseAdjustmentBO.setOriginTeacherId(teacherAbbDetailVO.getTeacherId());
                            }
                            courseAdjustmentBO.setBeforeOriginCourseTime(apply.getBeforeOriginCourseTimeByNumber(i, items, weekMap));
                            if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.ADJUSTMENT)) {
                                if (teacherAbbDetailVO.getAdjustmentId() != null) {
                                    courseAdjustmentBO.setFatherType(AdjustmentType.ADJUSTMENT);
                                    CourseAdjustmentDO fatherData = courseAdjustmentDao.load(courseAdjustmentBO.getFatherId());
                                    if (fatherData != null) {
                                        if (apply.getTeacherId().equals(fatherData.getTeacherId())) {
                                            courseAdjustmentBO.setOriginCourseTime(fatherData.getOriginCourseTime());
                                            courseAdjustmentBO.setOriginTeacherId(fatherData.getOriginTeacherId());
                                            courseAdjustmentBO.setBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                                        } else if (apply.getTeacherId().equals(fatherData.getAdjustedTeacherId())) {
                                            courseAdjustmentBO.setOriginCourseTime(fatherData.getAdjustedOriginCourseTime());
                                            courseAdjustmentBO.setOriginTeacherId(fatherData.getAdjustedOriginTeacherId());
                                            courseAdjustmentBO.setBeforeOriginCourseTime(fatherData.getAdjustedBeforeOriginCourseTime());
                                        }
                                    }
                                }
                            } else if (teacherAbbDetailVO.getType().equals(AbbTypeEnum.SUBSTITUTE)) {
                                if (teacherAbbDetailVO.getAdjustmentId() != null) {
                                    courseAdjustmentBO.setFatherType(AdjustmentType.REPLACE);
                                    ReplaceSectionDO fatherData = replaceSectionService.loadById(courseAdjustmentBO.getFatherId());
                                    if (fatherData != null) {
                                        courseAdjustmentBO.setOriginCourseTime(fatherData.getCourseTime());
                                        courseAdjustmentBO.setOriginTeacherId(fatherData.getOriginTeacherId());
                                        courseAdjustmentBO.setBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                                    }
                                }
                            }
                            courseIsSame = true;
                        }
                    }
                }
            }
        }
        return courseIsSame;
    }

    /**
     * 根据第几周周几获取日期
     *
     * @param courseTimeList 如：第一周周一第一节
     * @return 返回对应的日期
     */
    private Map<String, java.sql.Date> getRealDateMap(List<String> courseTimeList, Map<Integer, WeekConfigVO> weekConfigMap) {
        Map<String, java.sql.Date> resultMap = new HashMap<>(10);
        if (!CollectionUtils.isEmpty(courseTimeList)) {
            for (String courseTime : courseTimeList) {
                WeekConfigVO weekConfigVO = weekConfigMap.get(CourseTimeUtils.getWeekNumber(courseTime));
                if (weekConfigVO != null) {
                    List<Date> dates = CourseAdjustmentUtils.findDates(weekConfigVO.getStartDay(), weekConfigVO.getEndDay());
                    for (Date date : dates) {
                        if (DateUtils.getDayOfWeek(date).equals(CourseTimeUtils.getChineseDayOfWeek(courseTime))) {
                            java.sql.Date realDate = new java.sql.Date(date.getTime());
                            resultMap.put(courseTime, realDate);
                            break;
                        }
                    }
                }
            }
        }
        return resultMap;
    }

    /**
     * 调代课统计分页
     */
    @Override
    public QueryResult<List<AdjustmentAndReplaceStatisticsPageVO>> statisticsPage(AdjustmentAndReplaceStatisticsPageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        QueryResult<List<AdjustmentAndReplaceStatisticsPageDTO>> queryPage = queryForPage(AdjustmentAndReplaceStatisticsPageDTO.class, "adjustmentStatistics", map);
        if (queryPage == null) {
            return null;
        }
        // 代课次数与被代次数查询
        Map<Long, Set<Long>> beReplaceCount = replaceSectionService.getReplaceOrReplacedCount(map, 1);
        Map<Long, Set<Long>> toReplaceCount = replaceSectionService.getReplaceOrReplacedCount(map, 2);

        List<AdjustmentAndReplaceStatisticsPageVO> result1 = new ArrayList<>();
        for (AdjustmentAndReplaceStatisticsPageDTO dto : queryPage.getResult()) {
            if (!"待安排".equals(dto.getTeacherName()) && !StringUtils.isEmpty(dto.getTeacherName())) {
                AdjustmentAndReplaceStatisticsPageVO vo = ClassConverter.aTob(dto, new AdjustmentAndReplaceStatisticsPageVO());
                Set<Long> beReplaceIds = null;
                Set<Long> toReplaceIds = null;

                if (beReplaceCount != null && !beReplaceCount.isEmpty()) {
                    beReplaceIds = beReplaceCount.get(dto.getTeacherId());
                }

                if (toReplaceCount != null && !toReplaceCount.isEmpty()) {
                    toReplaceIds = toReplaceCount.get(dto.getTeacherId());
                }

                vo.setAdjustmentCount(dto.getAdjustmentCount() + "次");
                if (beReplaceIds != null && !beReplaceCount.isEmpty()) {
                    vo.setBeReplacedCount(beReplaceIds.size() + "次");
                } else {
                    vo.setBeReplacedCount("0次");
                }
                if (toReplaceIds != null && !toReplaceCount.isEmpty()) {
                    vo.setToReplaceCount(toReplaceIds.size() + "次");
                } else {
                    vo.setToReplaceCount("0次");
                }
                if (dto.getToReplaceSection().intValue() == 0) {
                    vo.setToReplaceCount("0次");
                }
                if (dto.getBeReplacedSection().intValue() == 0) {
                    vo.setBeReplacedCount("0次");
                }
                vo.setToReplaceSection(dto.getToReplaceSection().intValue() + "节");
                vo.setBeReplacedSection(dto.getBeReplacedSection().intValue() + "节");
                result1.add(vo);
            }
        }
        QueryResult<List<AdjustmentAndReplaceStatisticsPageVO>> queryPage1 = new QueryResult<>();
        PageResponse.PageInfo pageInfo = queryPage.getPageInfo();
        pageInfo.setTotalCount(result1.size());
        queryPage1.setPageInfo(pageInfo);
        queryPage1.setResult(result1);
        return queryPage1;
    }

    /**
     * 调代课统计数据导出
     */
    @Override
    public void statisticsExport(AdjustmentAndReplaceStatisticsPageAO ao, HttpServletResponse response) {
        ao.setPageSize(-1);
        QueryResult<List<AdjustmentAndReplaceStatisticsPageVO>> listQueryResult = statisticsPage(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<AdjustmentAndReplaceStatisticsPageVO> result = listQueryResult.getResult();
        List<StatisticsAdjustmentAndReplaceExport> list = new ArrayList<>();
        if (result != null && result.size() > 0) {
            AtomicInteger index = new AtomicInteger(0);
            for (AdjustmentAndReplaceStatisticsPageVO courseAdjustmentVO : result) {
                StatisticsAdjustmentAndReplaceExport export = ClassConverter.aTob(courseAdjustmentVO, new StatisticsAdjustmentAndReplaceExport());
                export.setIndex(index.incrementAndGet());
                list.add(export);
            }
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd");
        String title = format.format(ao.getStartDate()) + " - " + format.format(ao.getEndDate()) + " 调代课统计";
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), StatisticsAdjustmentAndReplaceExport.class).head(ExcelHeadsUtil.statisticsAdjustmentAndReplace(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_400010);
        }
    }

    /**
     * 单个教师在时间范围内调课记录分页（审批通过）
     */
    @Override
    public QueryResult<List<CourseAdjustmentVO>> singleAdjustmentPage(SinglePageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("approvalState", ApprovalState.PASS.name());
        QueryResult<List<CourseAdjustmentDTO>> queryResult = queryForPage(CourseAdjustmentDTO.class, "singleAdjustmentRecords", map);
        if (queryResult == null) {
            return null;
        }
        return getListQueryResult(queryResult);
    }

    /**
     * 调代课统计单个教师调代课数据导出
     */
    @Override
    public void singleRecordExport(SinglePageAO ao, HttpServletResponse response) {
        ao.setPageSize(-1);
        Integer type = ao.getType();
        boolean b = !(type >= 1 && type <= NUMBER_TWO);
        if (b) {
            throw new BusinessException("调代课类型有误！");
        }
        if (type == 1) {
            // 调课
            QueryResult<List<CourseAdjustmentVO>> listQueryResult = singleAdjustmentPage(ao);
            if (listQueryResult == null) {
                listQueryResult = new QueryResult<>();
                listQueryResult.setResult(new ArrayList<>());
            }
            List<CourseAdjustmentVO> result = listQueryResult.getResult();
            List<StatisticsPersonalAdjustmentExport> list = new ArrayList<>();
            if (result != null && result.size() > 0) {
                list = result.stream().map(courseAdjustmentVO -> ClassConverter.aTob(courseAdjustmentVO, new StatisticsPersonalAdjustmentExport())).collect(Collectors.toList());
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd");
            String title = ao.getTeacherName() + "教师 " + format.format(ao.getStartDate()) + " - " + format.format(ao.getEndDate()) + " 调代课统计";
            try {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), StatisticsPersonalAdjustmentExport.class).head(ExcelHeadsUtil.statisticsPersonalAdjustment(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
            } catch (IOException e) {
                throw new BusinessException(CodeRes.CODE_400010);
            }
        } else {
            // 代课
            QueryResult<List<CourseReplaceVO>> listQueryResult = courseReplaceService.singleReplacePage(ao);
            if (listQueryResult == null) {
                listQueryResult = new QueryResult<>();
                listQueryResult.setResult(new ArrayList<>());
            }
            List<CourseReplaceVO> result = listQueryResult.getResult();
            List<StatisticsPersonalReplaceExport> list = new ArrayList<>();
            if (result != null && result.size() > 0) {
                list = result.stream().map(courseReplaceVO -> ClassConverter.aTob(courseReplaceVO, new StatisticsPersonalReplaceExport())).collect(Collectors.toList());

            }
            SimpleDateFormat format2 = new SimpleDateFormat("yyyy.MM.dd");
            String title = ao.getTeacherName() + "教师 " + format2.format(ao.getStartDate()) + " - " + format2.format(ao.getEndDate()) + " 调代课统计";
            try {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), StatisticsPersonalReplaceExport.class).head(ExcelHeadsUtil.statisticsPersonalReplace(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
            } catch (IOException e) {
                throw new BusinessException(CodeRes.CODE_400010);
            }
        }
    }

    /**
     * 开始流程
     */
    @Override
    public void startFlow(StartFlowAO ao, CourseAdjustmentDO courseAdjustmentDO, CourseReplaceDO courseReplaceDO) {
        String type = "";
        Integer typeNum = null;
        String teacherName = "";
        JwtUser user = getJwtUser();
        List<TeacherDto> list = foundationFacade.getTeachersByIds(String.valueOf(user.getId()));
        TeacherDto teacherDto = list.get(0);
        String deptIds = teacherDto.getDepartmentIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        String roleIds = StringUtils.listToString(user.getRoleIds(), ",");
        NewWorkFlowAO flowAo = new NewWorkFlowAO();
        flowAo.setApplicantId(user.getId());
        flowAo.setApplicantName(user.getName());
        flowAo.setApplicantType(1);
        flowAo.setApprover(ao.getApprover());
        flowAo.setApplicationDepts(deptIds);
        flowAo.setApplicationRoles(roleIds);
        flowAo.setAssigneeIds(String.valueOf(user.getId()));
        flowAo.setConditions(ao.getConditions());
        flowAo.setNodeInfo(ao.getNodeInfo());
        flowAo.setProcessDefinitionKey(ao.getProcessDefinitionKey());
        flowAo.setRelatedType(relatedType);
        JsonArray j = new JsonArray();
        if (courseAdjustmentDO != null) {
            type = AdjustmentType.ADJUSTMENT.getName();
            typeNum = AdjustmentType.ADJUSTMENT.getType();
            teacherName = courseAdjustmentDO.getTeacherName();
            flowAo.setBusinessKey(String.valueOf(courseAdjustmentDO.getId()));
            flowAo.setToDoContent(teacherName + "教师的调课申请");
            flowAo.setRelatedId(String.valueOf(courseAdjustmentDO.getId()));
            j.put(type);
            j.put("调课时间：" + courseAdjustmentDO.getCourseTime());
            if (courseAdjustmentDO.getIsSelfStudy() && StringUtils.isEmpty(courseAdjustmentDO.getAdjustedCourseTime())) {
                j.put("课程情况：" + "自修");

            } else {
                j.put("被调时间：" + courseAdjustmentDO.getAdjustedCourseTime());
            }
        }
        if (courseReplaceDO != null) {
            type = AdjustmentType.REPLACE.getName();
            typeNum = AdjustmentType.REPLACE.getType();
            teacherName = courseReplaceDO.getTeacherName();
            flowAo.setBusinessKey(String.valueOf(courseReplaceDO.getId()));
            flowAo.setToDoContent(teacherName + "教师的代课申请");
            flowAo.setRelatedId(String.valueOf(courseReplaceDO.getId()));
            j.put(type);
            j.put("开始时间：" + courseReplaceDO.getReplaceStartDate() + " " + courseReplaceDO.getStartSection());
            j.put("结束时间：" + courseReplaceDO.getReplaceEndDate() + " " + courseReplaceDO.getEndSection());
        }
        flowAo.setExtra(j.toString());
        JsonObject jsonObject = new JsonObject();
        jsonObject.put("adjustmentType", typeNum);
        jsonObject.put("first", "老师您好，" + teacherName + "老师的" + type + "申请正等待您的审批，请及时处理！");
        jsonObject.put("taskName", type + "申请");
        jsonObject.put("date", DateUtils.formatDateTime(new Date()));
        jsonObject.put("remark", "请尽快处理");
        flowAo.setOfficeAccountData(jsonObject.toString());

        flowFacade.beginTheWorkFlow(flowAo);
    }

    /**
     * 获取班级课表
     * 根据课表里的教师id获取教师是否请假公出
     * 走班课、教师请假/公出置灰
     *
     * @param classId 班级ID
     * @param number  周次
     * @return 班级课表
     */
    @Override
    public ClassTableVO getClassTable(Long classId, Integer number) {
        // 获取每周班级课表
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = generalTableService.classTableWeekDetailBatch(classId, number);
        if (weekEnumMapMap == null || weekEnumMapMap.isEmpty()) {
            return null;
        }

        List<ClassDTO> allClass = foundationFacade.getAllClass(classId.toString());
        ClassDTO classDto = get(allClass);

        // 处理走班课
        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapEntry : weekEnumMapMap.entrySet()) {
            Map<String, List<TeacherAbbDetailVO>> value = weekEnumMapEntry.getValue();
            if (value != null && !value.isEmpty()) {
                for (Map.Entry<String, List<TeacherAbbDetailVO>> stringListEntry : value.entrySet()) {
                    if (stringListEntry.getKey() != null) {
                        List<TeacherAbbDetailVO> value1 = stringListEntry.getValue();
                        TeacherAbbDetailVO newTeacherAbbDetailVO = null;
                        boolean isMove = false;
                        if (value1 != null && value1.size() == 1) {
                            TeacherAbbDetailVO teacherAbbDetailVO = value1.get(0);
                            if (!teacherAbbDetailVO.getClassOrRoomId().equals(classId.toString())) {
                                // 走班课
                                newTeacherAbbDetailVO = ClassConverter.aTob(teacherAbbDetailVO, new TeacherAbbDetailVO());
                                isMove = true;
                            }
                        }
                        if (isMove) {
                            value1.add(newTeacherAbbDetailVO);
                        }
                    }
                }
            }
        }

        WeekTypeEnum weekTypeEnum;
        if (number % NUMBER_TWO == 0) {
            weekTypeEnum = WeekTypeEnum.DOUBLE;
        } else {
            weekTypeEnum = WeekTypeEnum.SINGLE;
        }
        ClassTableVO classTableVO = new ClassTableVO();
        GeneralTableVO generalTableVO = new GeneralTableVO();
        DataTableDTO dataTableDTO = new DataTableDTO();
        dataTableDTO.setClassOrRoomId(classId.toString());
        dataTableDTO.setClassOrRoomName(classDto.getName());
        dataTableDTO.setDetailv(weekEnumMapMap);
        generalTableVO.setData(Collections.singletonList(dataTableDTO));
        generalTableVO.setType(weekTypeEnum);
        classTableVO.setClassTableVoList(Collections.singletonList(generalTableVO));

        return classTableVO;
    }

    /**
     * 获取走班课课程时间
     *
     * @param curriculumTableId 课表id
     * @param number            周次
     * @return 课程时间
     */
    public List<String> getRoomCourseTime(Long curriculumTableId, Integer number, Long teacherId) {
        WeekTypeEnum weekTypeEnum;
        if (number % NUMBER_TWO == 0) {
            weekTypeEnum = WeekTypeEnum.DOUBLE;
        } else {
            weekTypeEnum = WeekTypeEnum.SINGLE;
        }
        // 走班课时间
        List<String> courseTimeList = new ArrayList<>();
        // 获取班级课表
        List<GeneralTableVO> classesTable = generalTableService.classesTable(curriculumTableId);
        if (classesTable != null && !classesTable.isEmpty()) {
            // 遍历课表
            for (GeneralTableVO generalTableVO : classesTable) {
                if (classesTable.size() == 1 || generalTableVO.getType().equals(weekTypeEnum)) {
                    GeneralTableVO resultGeneralTableVO = new GeneralTableVO();
                    resultGeneralTableVO.setType(weekTypeEnum);
                    List<DataTableDTO> data = generalTableVO.getData();
                    if (data == null || data.size() == 0) {
                        continue;
                    }
                    for (DataTableDTO datum : data) {
                        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> entry : datum.getDetailv().entrySet()) {
                            String week = WeekEnum.getDayOfWeek(entry.getKey());
                            for (Map.Entry<String, List<TeacherAbbDetailVO>> stringListEntry : entry.getValue().entrySet()) {
                                String sectionStr = "第" + stringListEntry.getKey() + "节";
                                List<TeacherAbbDetailVO> value = stringListEntry.getValue();
                                if (!value.isEmpty()) {
                                    for (TeacherAbbDetailVO vo : value) {
                                        boolean b = vo.getTeacherId().equals(teacherId) && (value.size() > 1 || vo.getClassOrRoomId().contains("ROOM"));
                                        if (b) {
                                            // 同一节次多节课程为走班课，或者为非行政班课
                                            String courseTime = week + sectionStr;
                                            courseTimeList.add(courseTime);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!courseTimeList.isEmpty()) {
            return courseTimeList;
        }
        return null;
    }

    /**
     * 获取教师课表
     *
     * @param teacherId 教师id
     * @param number    周次
     * @return 教师课表
     */
    @Override
    public com.xiaoshan.edu.vo.courseadjustment.TeacherTableVO getTeacherTable(Long teacherId, Integer number) {
        // 获取每周教师课表
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = generalTableService.teacherTableWeekDetail(teacherId, number);
        if (weekEnumMapMap == null || weekEnumMapMap.isEmpty()) {
            return null;
        }
        // 教师涉及到多个年级上课
        List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumIds(null, teacherId);
        if (curriculumTableIds == null) {
            return null;
        }
        List<String> roomCourseTime = new ArrayList<>();
        // 获取走班课课程时间
        for (Long curriculumTableId : curriculumTableIds) {
            List<String> roomCourseTime1 = getRoomCourseTime(curriculumTableId, number, teacherId);
            if (roomCourseTime1 != null && !roomCourseTime1.isEmpty()) {
                roomCourseTime.addAll(roomCourseTime1);
            }
        }
        com.xiaoshan.edu.vo.courseadjustment.TeacherTableVO teacherTableVo1 = new com.xiaoshan.edu.vo.courseadjustment.TeacherTableVO();
        WeekTypeEnum weekTypeEnum;
        if (number % NUMBER_TWO == 0) {
            weekTypeEnum = WeekTypeEnum.DOUBLE;
        } else {
            weekTypeEnum = WeekTypeEnum.SINGLE;
        }
        List<TeacherTableVO> teacherTableVoList = new ArrayList<>();
        TeacherTableVO resultTeacherTableVO = new TeacherTableVO();
        resultTeacherTableVO.setType(weekTypeEnum);
        // 构造教师课表数据
        Map<String, List<TeacherTableDTO>> resultData = new HashMap<>(10);
        TeacherTableDTO resultTeacherTableDTO = new TeacherTableDTO();
        String courseName = "";
        int i = 0;
        // 遍历课表数据
        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> entry1 : weekEnumMapMap.entrySet()) {
            WeekEnum key = entry1.getKey();
            String week = WeekEnum.getDayOfWeek(key);
            Map<String, List<TeacherAbbDetailVO>> stringListMap = entry1.getValue();
            if (stringListMap != null && !stringListMap.isEmpty()) {
                for (Map.Entry<String, List<TeacherAbbDetailVO>> entry2 : stringListMap.entrySet()) {
                    String section = entry2.getKey();
                    String sectionStr = "第" + section + "节";
                    String courseTime = week + sectionStr;
                    List<TeacherAbbDetailVO> value1 = entry2.getValue();
                    TeacherAbbDetailVO teacherAbbDetailVO = null;
                    for (TeacherAbbDetailVO vo : value1) {
                        i++;
                        if (i == 1) {
                            courseName = vo.getCourseName();
                        }
                        boolean b = (!roomCourseTime.isEmpty() && roomCourseTime.contains(courseTime)) || vo.getClassOrRoomId().contains("ROOM");
                        if (b) {
                            // 走班课
                            teacherAbbDetailVO = new TeacherAbbDetailVO();
                            BeanUtils.copyProperties(vo, new TeacherAbbDetailVO());
                        }
                    }
                    if (teacherAbbDetailVO != null) {
                        // 构造成两节一样的课，前端用于区分走班
                        value1.add(teacherAbbDetailVO);
                    }
                }
            }
        }
        resultTeacherTableDTO.setDetailv(weekEnumMapMap);
        resultData.put(courseName, Collections.singletonList(resultTeacherTableDTO));
        resultTeacherTableVO.setData(resultData);
        teacherTableVoList.add(resultTeacherTableVO);
        teacherTableVo1.setTeacherTableVoList(teacherTableVoList);
        return teacherTableVo1;
    }

    @Override
    public void isLeavingOrBusiness(Long teacherId, String courseTime, String courseStartTime, String courseEndTime) {
        CourseTimeAO ao = new CourseTimeAO();
        ao.setCourseTime(courseTime);
        ao.setCourseStartTime(courseStartTime);
        ao.setCourseEndTime(courseEndTime);
        checkTeacherLeavingOrBusinessState(teacherId, Collections.singletonList(ao));
    }

    /**
     * 课程时间是否存在请假公出校验
     *
     * @param teacherId
     * @param timeList
     */
    private void checkTeacherLeavingOrBusinessState(Long teacherId, List<CourseTimeAO> timeList) {
        if (!CollectionUtils.isEmpty(timeList)) {
            String teacherName = getTeacherNameById(teacherId);
            SemesterVO currentSemesters = settingFacade.currentSemester();
            List<WeekConfigVO> weekConfigVoList = settingFacade.weekConfig(currentSemesters.getId());
            List<Integer> numbers = timeList.stream().map(CourseTimeAO::getWeekNumber).collect(Collectors.toList());
            List<LeavingAndBusinessVO> teacherLeavingOrBusiness = foundationFacade.getTeacherLeavingOrBusiness(Collections.singletonList(teacherId), numbers);
            for (CourseTimeAO courseTimeAo : timeList) {
                String courseTime = courseTimeAo.getCourseTime();
                String courseStartTime = courseTimeAo.getCourseStartTime();
                String courseEndTime = courseTimeAo.getCourseEndTime();
                WeekConfigVO weekConfig = getWeekConfigVO(weekConfigVoList, courseTimeAo.getWeekNumber());
                Date queryDate = getQueryDate(weekConfig, courseTimeAo.getWeek());
                Date queryStartTime = null;
                Date queryEndTime = null;
                if (queryDate != null) {
                    java.sql.Date date = new java.sql.Date(queryDate.getTime());
                    if (!StringUtils.isEmpty(courseStartTime)) {
                        queryStartTime = DateUtils.stringToDate(date + " " + courseStartTime + ":00");
                    }
                    if (!StringUtils.isEmpty(courseEndTime)) {
                        queryEndTime = DateUtils.stringToDate(date + " " + courseEndTime + ":00");
                    }
                }
                checkLeavingOrBusinessDetail(teacherName, teacherLeavingOrBusiness, courseTimeAo, courseTime, DateUtil.parseDate(DateUtil.formatDate(queryDate)), queryStartTime, queryEndTime);
            }
        }
    }

    /**
     * 课程时间是否存在请假公出校验
     *
     * @param teacherId
     * @param timeList
     */
    private void checkTeacherLeavingOrBusinessStateBatch(Long teacherId, List<CourseTimeAO> timeList,Map<Integer, WeekConfigVO> weekMap) {
        if (!CollectionUtils.isEmpty(timeList)) {
            String teacherName = getTeacherNameById(teacherId);
            List<Integer> numbers = timeList.stream().map(CourseTimeAO::getWeekNumber).collect(Collectors.toList());
            List<LeavingAndBusinessVO> teacherLeavingOrBusiness = foundationFacade.getTeacherLeavingOrBusiness(Collections.singletonList(teacherId), numbers);
            for (CourseTimeAO courseTimeAo : timeList) {
                String courseTime = courseTimeAo.getCourseTime();
                String courseStartTime = courseTimeAo.getCourseStartTime();
                String courseEndTime = courseTimeAo.getCourseEndTime();
                WeekConfigVO weekConfig = weekMap.get(courseTimeAo.getWeekNumber());
                Date queryDate = getQueryDate(weekConfig, courseTimeAo.getWeek());
                Date queryStartTime = null;
                Date queryEndTime = null;
                if (queryDate != null) {
                    java.sql.Date date = new java.sql.Date(queryDate.getTime());
                    if (!StringUtils.isEmpty(courseStartTime)) {
                        queryStartTime = DateUtils.stringToDate(date + " " + courseStartTime + ":00");
                    }
                    if (!StringUtils.isEmpty(courseEndTime)) {
                        queryEndTime = DateUtils.stringToDate(date + " " + courseEndTime + ":00");
                    }
                }
                checkLeavingOrBusinessDetail(teacherName, teacherLeavingOrBusiness, courseTimeAo, courseTime, DateUtil.parseDate(DateUtil.formatDate(queryDate)), queryStartTime, queryEndTime);
            }
        }
    }

    private Date getQueryDate(WeekConfigVO weekConfig, String week) {
        Date queryDate = null;
        if (weekConfig != null) {
            List<Date> dates = CourseAdjustmentUtils.findDates(weekConfig.getStartDay(), weekConfig.getEndDay());
            for (Date date : dates) {
                String dayOfWeek = DateUtils.getDayOfWeek(date);
                if (week.equals(dayOfWeek)) {
                    queryDate = date;
                    break;
                }
            }
        }
        return queryDate;
    }

    private WeekConfigVO getWeekConfigVO(List<WeekConfigVO> weekConfigVoList, Integer weekNumber) {
        WeekConfigVO weekConfig = null;
        for (WeekConfigVO weekConfigVO : weekConfigVoList) {
            if (weekNumber.equals(weekConfigVO.getNumber().intValue())) {
                weekConfig = weekConfigVO;
                break;
            }
        }
        return weekConfig;
    }

    /**
     * 校验是否存在冲突
     *
     * @param teacherName
     * @param teacherLeavingOrBusiness
     * @param courseTimeAo
     * @param courseTime
     * @param queryDate
     * @param queryStartTime
     * @param queryEndTime
     */
    private void checkLeavingOrBusinessDetail(String teacherName, List<LeavingAndBusinessVO> teacherLeavingOrBusiness, CourseTimeAO courseTimeAo, String courseTime, Date queryDate, Date queryStartTime, Date queryEndTime) {
        if (!CollectionUtils.isEmpty(teacherLeavingOrBusiness)) {
            List<String> morningTimeScopeList = new ArrayList<>();
            Collections.addAll(morningTimeScopeList, "第一节", "第二节", "第三节", "第四节", "第五节");
            List<String> afternoonTimeScopeList = new ArrayList<>();
            Collections.addAll(afternoonTimeScopeList, "第六节", "第七节", "第八节", "第九节");
            for (LeavingAndBusinessVO leavingOrBusiness : teacherLeavingOrBusiness) {
                if (!leavingOrBusiness.temporaryLeft()) {
                    if (leavingOrBusiness.getStartTime().equals(leavingOrBusiness.getActualEndTime())) {
                        if (leavingOrBusiness.getStartTime().equals(queryDate)) {
                            if (leavingOrBusiness.getStartTimeScope().equals(leavingOrBusiness.getActualEndTimeScope())) {
                                boolean inTimeScope = checkWhetherInTimeScope(morningTimeScopeList, afternoonTimeScopeList, leavingOrBusiness.getStartTimeScope(), courseTimeAo.getSection());
                                if (inTimeScope) {
                                    throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                                }
                            } else {
                                throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                            }
                        }
                    } else {
                        if (CourseAdjustmentUtils.getDatePlus(leavingOrBusiness.getStartTime(), 1).equals(leavingOrBusiness.getActualEndTime())) {
                            if (leavingOrBusiness.getStartTime().equals(queryDate) || leavingOrBusiness.getActualEndTime().equals(queryDate)) {
                                if (leavingOrBusiness.getStartTime().equals(queryDate)) {
                                    if (checkWhetherInStartDateTimeScope(afternoonTimeScopeList, leavingOrBusiness.getStartTimeScope(), courseTimeAo.getSection())) {
                                        throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                                    }
                                } else if (leavingOrBusiness.getActualEndTime().equals(queryDate)) {
                                    if (checkWhetherInEndDateTimeScope(morningTimeScopeList, leavingOrBusiness.getActualEndTimeScope(), courseTimeAo.getSection())) {
                                        throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                                    }
                                }
                            }
                        } else {
                            if (checkWhetherInLeavingDate(queryDate, leavingOrBusiness.getStartTime(), leavingOrBusiness.getActualEndTime())) {
                                if (queryDate.after(leavingOrBusiness.getStartTime()) && queryDate.before(leavingOrBusiness.getActualEndTime())) {
                                    throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                                } else if (queryDate.equals(leavingOrBusiness.getStartTime())) {
                                    if (checkWhetherInStartDateTimeScope(afternoonTimeScopeList, leavingOrBusiness.getStartTimeScope(), courseTimeAo.getSection())) {
                                        throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                                    }
                                } else if (queryDate.equals(leavingOrBusiness.getActualEndTime())) {
                                    if (checkWhetherInEndDateTimeScope(morningTimeScopeList, leavingOrBusiness.getActualEndTimeScope(), courseTimeAo.getSection())) {
                                        throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if (queryStartTime != null && queryEndTime != null) {
                        if (checkTempLeavingInTimeScope(queryStartTime, queryEndTime, leavingOrBusiness.getStartTime(), leavingOrBusiness.getActualEndTime())) {
                            throw new BusinessException(CodeRes.CODE_800004, teacherName, courseTime, leavingOrBusiness.getLeavingTypeDesc());
                        }
                    }
                }
            }
        }
    }

    /**
     * 是否在临时假时间范围内
     *
     * @param queryStartTime
     * @param queryEndTime
     * @param startTime
     * @param actualEndTime
     * @return
     */
    private boolean checkTempLeavingInTimeScope(Date queryStartTime, Date queryEndTime, Date startTime, Date actualEndTime) {
        return startTime.getTime() < queryEndTime.getTime() && actualEndTime.getTime() > queryStartTime.getTime();
    }

    /**
     * 是否在请假范围内
     *
     * @param queryDate
     * @param startTime
     * @param actualEndTime
     * @return
     */
    private boolean checkWhetherInLeavingDate(Date queryDate, Date startTime, Date actualEndTime) {
        return queryDate.after(startTime) && queryDate.before(actualEndTime) || queryDate.equals(startTime) || queryDate.equals(actualEndTime);
    }

    /**
     * 是否在结束时间时间范围内
     *
     * @param morningTimeScopeList
     * @param actualEndTimeScope
     * @param section
     * @return
     */
    private boolean checkWhetherInEndDateTimeScope(List<String> morningTimeScopeList, String actualEndTimeScope, String section) {
        return ("上午".equals(actualEndTimeScope) && morningTimeScopeList.contains(section)) || "下午".equals(actualEndTimeScope);
    }

    /**
     * 校验是否在开始日期的范围内
     *
     * @param afternoonTimeScopeList
     * @param startTimeScope
     * @param section
     * @return
     */
    private boolean checkWhetherInStartDateTimeScope(List<String> afternoonTimeScopeList, String startTimeScope, String section) {
        return "上午".equals(startTimeScope) || ("下午".equals(startTimeScope) && afternoonTimeScopeList.contains(section));
    }

    /**
     * 校验课程是否在请假时间范围内
     *
     * @param morningTimeScopeList
     * @param afternoonTimeScopeList
     * @param startTimeScope
     * @param section
     * @return
     */
    private boolean checkWhetherInTimeScope(List<String> morningTimeScopeList, List<String> afternoonTimeScopeList, String startTimeScope, String section) {
        return ("上午".equals(startTimeScope) && morningTimeScopeList.contains(section)) || ("下午".equals(startTimeScope) && afternoonTimeScopeList.contains(section));
    }

    /**
     * 获取教师名字
     *
     * @param teacherId 教师id
     * @return
     */
    private String getTeacherNameById(Long teacherId) {

        String teacherName = "该";
        List<TeacherDto> teachers = foundationFacade.getTeachersByIds(String.valueOf(teacherId));
        if (teachers != null && !teachers.isEmpty()) {
            teacherName = teachers.get(0).getName();
        }
        return teacherName;
    }

    @Override
    public Set<Long> getLeavingOrBusinessTeacherIds(List<Long> teacherIds, List<ReplaceCourseDetailAO> replaceCourseDetailAos) {
        List<Integer> weekNumbers = replaceCourseDetailAos.stream().map(ReplaceCourseDetailAO::getWeekNumber).distinct().collect(Collectors.toList());
        List<LeavingAndBusinessVO> teacherLeavingOrBusiness = foundationFacade.getTeacherLeavingOrBusiness(teacherIds, weekNumbers);
        SemesterVO currentSemesters = settingFacade.currentSemester();
        List<WeekConfigVO> weekConfigVoList = settingFacade.weekConfig(currentSemesters.getId());
        Set<Long> removeTeacherIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(teacherLeavingOrBusiness)) {
            List<String> morningTimeScopeList = new ArrayList<>();
            Collections.addAll(morningTimeScopeList, "第一节", "第二节", "第三节", "第四节", "第五节");
            List<String> afternoonTimeScopeList = new ArrayList<>();
            Collections.addAll(afternoonTimeScopeList, "第六节", "第七节", "第八节", "第九节");
            for (LeavingAndBusinessVO leavingOrBusiness : teacherLeavingOrBusiness) {
                String endTimeScope = leavingOrBusiness.getActualEndTimeScope();
                for (ReplaceCourseDetailAO replaceCourseDetailAo : replaceCourseDetailAos) {
                    WeekConfigVO weekConfig = getWeekConfigVO(weekConfigVoList, replaceCourseDetailAo.getWeekNumber());
                    Date queryDate = getQueryDate(weekConfig, replaceCourseDetailAo.getFullChineseDayOfWeek());
                    Date queryStartTime = null;
                    Date queryEndTime = null;
                    if (queryDate != null) {
                        java.sql.Date date = new java.sql.Date(queryDate.getTime());
                        if (!StringUtils.isEmpty(replaceCourseDetailAo.getStartTime())) {
                            queryStartTime = DateUtils.stringToDate(date + " " + replaceCourseDetailAo.getStartTime() + ":00");
                        }
                        if (!StringUtils.isEmpty(replaceCourseDetailAo.getEndTime())) {
                            queryEndTime = DateUtils.stringToDate(date + " " + replaceCourseDetailAo.getEndTime() + ":00");
                        }
                    }
                    if (haveCollision(morningTimeScopeList, afternoonTimeScopeList, leavingOrBusiness, endTimeScope, replaceCourseDetailAo, DateUtil.parseDate(DateUtil.formatDate(queryDate)), queryStartTime, queryEndTime)) {
                        removeTeacherIds.add(leavingOrBusiness.getTeacherId());
                    }
                }
            }
        }
        return removeTeacherIds;
    }

    /**
     * 是否存在冲突
     *
     * @param morningTimeScopeList
     * @param afternoonTimeScopeList
     * @param leavingOrBusiness
     * @param endTimeScope
     * @param replaceCourseDetailAo
     * @param queryDate
     * @param queryStartTime
     * @param queryEndTime
     * @return
     */
    private boolean haveCollision(List<String> morningTimeScopeList, List<String> afternoonTimeScopeList, LeavingAndBusinessVO leavingOrBusiness, String endTimeScope, ReplaceCourseDetailAO replaceCourseDetailAo, Date queryDate, Date queryStartTime, Date queryEndTime) {
        if (!leavingOrBusiness.temporaryLeft()) {
            if (leavingOrBusiness.getStartTime().equals(leavingOrBusiness.getActualEndTime())) {
                if (leavingOrBusiness.getStartTime().equals(queryDate)) {
                    if (leavingOrBusiness.getStartTimeScope().equals(endTimeScope)) {
                        return checkWhetherInTimeScope(morningTimeScopeList, afternoonTimeScopeList, leavingOrBusiness.getStartTimeScope(), replaceCourseDetailAo.getFullSection());
                    } else {
                        return true;
                    }
                }
            } else {
                if (CourseAdjustmentUtils.getDatePlus(leavingOrBusiness.getStartTime(), 1).equals(leavingOrBusiness.getActualEndTime())) {
                    if (leavingOrBusiness.getStartTime().equals(queryDate) || leavingOrBusiness.getActualEndTime().equals(queryDate)) {
                        if (leavingOrBusiness.getStartTime().equals(queryDate)) {
                            return checkWhetherInStartDateTimeScope(afternoonTimeScopeList, leavingOrBusiness.getStartTimeScope(), replaceCourseDetailAo.getFullSection());
                        } else if (leavingOrBusiness.getActualEndTime().equals(queryDate)) {
                            return checkWhetherInEndDateTimeScope(afternoonTimeScopeList, endTimeScope, replaceCourseDetailAo.getFullSection());
                        }
                    }
                } else {
                    assert queryDate != null;
                    if (checkWhetherInLeavingDate(queryDate, leavingOrBusiness.getStartTime(), leavingOrBusiness.getActualEndTime())) {
                        if (queryDate.after(leavingOrBusiness.getStartTime()) && queryDate.before(leavingOrBusiness.getActualEndTime())) {
                            return true;
                        } else if (queryDate.equals(leavingOrBusiness.getStartTime())) {
                            return checkWhetherInStartDateTimeScope(afternoonTimeScopeList, leavingOrBusiness.getStartTimeScope(), replaceCourseDetailAo.getFullSection());
                        } else if (queryDate.equals(leavingOrBusiness.getActualEndTime())) {
                            return checkWhetherInEndDateTimeScope(afternoonTimeScopeList, endTimeScope, replaceCourseDetailAo.getFullSection());
                        }
                    }
                }
            }
        } else {
            if (queryStartTime != null && queryEndTime != null) {
                return checkTempLeavingInTimeScope(queryStartTime, queryEndTime, leavingOrBusiness.getStartTime(), leavingOrBusiness.getActualEndTime());
            }
        }
        return false;
    }

    @Override
    public void isAdjusted(Long teacherId, String courseTime) {
        // 查询同节次未失效的代课
        List<ReplaceSectionDO> replaceSectionDos = replaceSectionService.getReplaceSectionByIsNotInvalid(teacherId, courseTime);
        //判断有没有审批中代课记录
        Map<String, Object> map = new HashMap<>(3);
        map.put("courseTimeList", Collections.singletonList(courseTime));
        map.put("approvalState", ApprovalState.UNDER_APPROVAL.name());
        map.put("teacherIds", Collections.singletonList(teacherId));
        List<CourseReplaceDO> courseReplaceDos = courseReplaceDao.queryForListMapper("getReplaceRecords",map);
        if (!CollectionUtils.isEmpty(courseReplaceDos)) {
            throw new BusinessException(CodeRes.CODE_800016);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.nested(w -> w.nested(w1 -> w1.eq("teacher_id", teacherId).eq("course_time", courseTime))
                        .or(w2 -> w2.eq("adjusted_teacher_id", teacherId).eq("adjusted_course_time", courseTime)))
                .and(w -> w.eq("approval_state", ApprovalState.UNDER_APPROVAL.name()).or(w1 -> w1.eq("approval_state", ApprovalState.PASS.name()).eq("is_invalid", "false")))
                .eq("is_unusual", "false");

        List<CourseAdjustmentDO> courseAdjustmentDoList = courseAdjustmentDao.queryForList(queryWrapper);
        if (courseAdjustmentDoList != null && !courseAdjustmentDoList.isEmpty()) {
            for (CourseAdjustmentDO courseAdjustmentDO : courseAdjustmentDoList) {
                if (courseAdjustmentDO.getApprovalState().equals(ApprovalState.PASS)) {
                    // 同节次不存在未失效的代课
                    if (replaceSectionDos == null || replaceSectionDos.isEmpty()) {
                        throw new BusinessException(CodeRes.CODE_800006);
                    }
                } else {
                    throw new BusinessException(CodeRes.CODE_800006);
                }
            }
        }
    }


    /**
     * 调代课审批
     */
    @Async
    @Override
    public void approve(ApproveAO ao) {
        Long id = ao.getId();
        Integer type = ao.getType();
        ApprovalState approvalState = ao.getApprovalState();
        if (type == 1) {
            // 调课
            approveAdjust(id, approvalState);
        } else {
            // 代课
            approveReplace(id, approvalState);
        }

    }

    /**
     * 代课审批
     *
     * @param id
     * @param approvalState
     */
    private void approveReplace(Long id, ApprovalState approvalState) {
        courseReplaceService.updateReplaceApprovalState(id, approvalState);
        List<ReplaceSectionDO> replaceSectionDoList = replaceSectionService.getListByReplaceId(id);
        if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
            for (ReplaceSectionDO sectionDO : replaceSectionDoList) {
                // 父数据标为失效
                if (sectionDO.getReplaceFatherType() != null && sectionDO.getReplaceFatherId() != null && sectionDO.getReplaceFatherId() != 0L) {
                    if (sectionDO.getReplaceFatherType().equals(AdjustmentType.REPLACE)) {
                        // 父数据为代课
                        ReplaceSectionDO fatherLoad = replaceSectionService.loadById(sectionDO.getReplaceFatherId());
                        if (fatherLoad != null) {
                            fatherLoad.setReplaceIsInvalid(true);
                            replaceSectionService.save(fatherLoad);
                        }
                    } else {
                        // 父数据为调课
                        CourseAdjustmentDO fatherLoad = courseAdjustmentDao.load(sectionDO.getReplaceFatherId());
                        if (fatherLoad != null) {
                            if (sectionDO.getCourseTime().equals(fatherLoad.getCourseTime())) {
                                // 父数据不参与统计
                                fatherLoad.setIsNotStatistics(true);
                                fatherLoad.setIsInvalid(true);
                                this.save(fatherLoad);
                            } else if (sectionDO.getCourseTime().equals(fatherLoad.getAdjustedCourseTime())) {
                                fatherLoad.setAdjustedIsInvalid(true);
                                this.save(fatherLoad);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 调课审批
     *
     * @param id
     * @param approvalState
     */
    private void approveAdjust(Long id, ApprovalState approvalState) {
        CourseAdjustmentDO load = courseAdjustmentDao.load(id);
        if (load != null) {
            load.setApprovalState(approvalState);
            if (approvalState.equals(ApprovalState.PASS)) {
                // 已通过，父数据标记为失效
                if (load.getFatherType() != null && load.getFatherId() != null && load.getFatherId() != 0L) {
                    if (load.getFatherType().equals(AdjustmentType.ADJUSTMENT)) {
                        // 父数据为调课
                        CourseAdjustmentDO fatherLoad = courseAdjustmentDao.load(load.getFatherId());
                        if (fatherLoad != null) {
                            if (load.getTeacherId().equals(fatherLoad.getTeacherId())) {
                                // 父数据不参与统计
                                fatherLoad.setIsNotStatistics(true);
                                fatherLoad.setIsInvalid(true);
                                this.save(fatherLoad);
                            } else if (load.getTeacherId().equals(fatherLoad.getAdjustedTeacherId())) {
                                fatherLoad.setAdjustedIsInvalid(true);
                                this.save(fatherLoad);
                            }
                        }
                    } else {
                        // 父数据为代课
                        ReplaceSectionDO fatherLoad = replaceSectionService.loadById(load.getFatherId());
                        if (fatherLoad != null) {
                            fatherLoad.setReplaceIsInvalid(true);
                            replaceSectionService.save(fatherLoad);
                        }
                    }
                }
                if (load.getAdjustedFatherType() != null && load.getAdjustedFatherId() != null && load.getAdjustedFatherId() != 0L) {
                    if (load.getAdjustedFatherType().equals(AdjustmentType.ADJUSTMENT)) {
                        // 父数据为调课
                        CourseAdjustmentDO adjustedFatherLoad = courseAdjustmentDao.load(load.getAdjustedFatherId());
                        if (adjustedFatherLoad != null) {
                            if (load.getAdjustedTeacherId().equals(adjustedFatherLoad.getTeacherId())) {
                                // 父数据不参与统计
                                adjustedFatherLoad.setIsInvalid(true);
                                this.save(adjustedFatherLoad);
                            } else if (load.getAdjustedTeacherId().equals(adjustedFatherLoad.getAdjustedTeacherId())) {
                                adjustedFatherLoad.setAdjustedIsInvalid(true);
                                this.save(adjustedFatherLoad);
                            }
                        }
                    } else {
                        // 父数据为代课
                        ReplaceSectionDO adjustedFatherLoad = replaceSectionService.loadById(load.getAdjustedFatherId());
                        if (adjustedFatherLoad != null) {
                            adjustedFatherLoad.setReplaceIsInvalid(true);
                            replaceSectionService.save(adjustedFatherLoad);
                        }
                    }
                }
            } else {
                // 已驳回，此数据标记为失效
                load.setIsInvalid(true);
                load.setAdjustedIsInvalid(true);
            }
            this.save(load);

            // 更新衍生数据（周循环）的审批状态
            updateData(approvalState, load);
        }
    }

    private void updateData(ApprovalState approvalState, CourseAdjustmentDO load) {
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("main_id", load.getId()).set("approval_state", approvalState.name());
        courseAdjustmentDao.executeUpdate(updateWrapper);
        //更新衍生数据的失效状态
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("main_id", load.getId());
        List<CourseAdjustmentDO> courseAdjustmentDos = courseAdjustmentDao.queryForList(wrapper);
        courseAdjustmentDos.forEach(courseAdjustmentDo -> {
            if (approvalState.equals(ApprovalState.PASS)) {
                // 已通过，父数据标记为失效
                if (courseAdjustmentDo.getFatherType() != null && courseAdjustmentDo.getFatherId() != null && courseAdjustmentDo.getFatherId() != 0L) {
                    if (courseAdjustmentDo.getFatherType().equals(AdjustmentType.ADJUSTMENT)) {
                        // 父数据为调课
                        CourseAdjustmentDO fatherLoad = courseAdjustmentDao.load(courseAdjustmentDo.getFatherId());
                        if (fatherLoad != null) {
                            if (courseAdjustmentDo.getTeacherId().equals(fatherLoad.getTeacherId())) {
                                // 父数据不参与统计
                                fatherLoad.setIsNotStatistics(true);
                                fatherLoad.setIsInvalid(true);
                                this.save(fatherLoad);
                            } else if (courseAdjustmentDo.getTeacherId().equals(fatherLoad.getAdjustedTeacherId())) {
                                fatherLoad.setAdjustedIsInvalid(true);
                                this.save(fatherLoad);
                            }
                        }
                    } else {
                        // 父数据为代课
                        ReplaceSectionDO fatherLoad = replaceSectionService.loadById(courseAdjustmentDo.getFatherId());
                        if (fatherLoad != null) {
                            fatherLoad.setReplaceIsInvalid(true);
                            replaceSectionService.save(fatherLoad);
                        }
                    }
                }
                if (courseAdjustmentDo.getAdjustedFatherType() != null && courseAdjustmentDo.getAdjustedFatherId() != null && courseAdjustmentDo.getAdjustedFatherId() != 0L) {
                    if (courseAdjustmentDo.getAdjustedFatherType().equals(AdjustmentType.ADJUSTMENT)) {
                        // 父数据为调课
                        CourseAdjustmentDO adjustedFatherLoad = courseAdjustmentDao.load(courseAdjustmentDo.getAdjustedFatherId());
                        if (adjustedFatherLoad != null) {
                            if (courseAdjustmentDo.getAdjustedTeacherId().equals(adjustedFatherLoad.getTeacherId())) {
                                // 父数据不参与统计
                                adjustedFatherLoad.setIsInvalid(true);
                                this.save(adjustedFatherLoad);
                            } else if (courseAdjustmentDo.getAdjustedTeacherId().equals(adjustedFatherLoad.getAdjustedTeacherId())) {
                                adjustedFatherLoad.setAdjustedIsInvalid(true);
                                this.save(adjustedFatherLoad);
                            }
                        }
                    } else {
                        // 父数据为代课
                        ReplaceSectionDO adjustedFatherLoad = replaceSectionService.loadById(courseAdjustmentDo.getAdjustedFatherId());
                        if (adjustedFatherLoad != null) {
                            adjustedFatherLoad.setReplaceIsInvalid(true);
                            replaceSectionService.save(adjustedFatherLoad);
                        }
                    }
                }
            } else {
                // 已驳回，此数据标记为失效
                courseAdjustmentDo.setIsInvalid(true);
                courseAdjustmentDo.setAdjustedIsInvalid(true);
            }
        });
    }

    /**
     * 模拟获取节点
     */
    @Override
    public FlowNodesListVO simulation(SimulationAO ao) {
        JwtUser user = getJwtUser();
        Long applicantId = user.getId();
        List<TeacherDto> list = foundationFacade.getTeachersByIds(String.valueOf(applicantId));
        TeacherDto teacherDto = list.get(0);
        String deptIds = StringUtils.listToString(teacherDto.getDepartmentIds(), ",");
        String roleIds = StringUtils.listToString(user.getRoleIds(), ",");
        return flowFacade.getFlowNodes(deptIds, roleIds, applicantId.toString(), ao.getProcessDefinitionKey(), ao.getConditions());
    }

    /**
     * 催办
     */
    @Override
    public void urging(AdjustmentUrgeAO ao) {
        Long id = ao.getId();
        String key = "URGE_SOMEBODY_" + id;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            throw new BusinessException(CodeRes.CODE_600011);
        }
        AdjustmentType adjustmentType;
        String teacherName;
        Integer sourceId;
        if (ao.getType() == 1) {
            adjustmentType = AdjustmentType.ADJUSTMENT;
            CourseAdjustmentDO load = this.load(id);
            teacherName = load.getTeacherName();
            sourceId = adjustmentSourceId;
        } else {
            adjustmentType = AdjustmentType.REPLACE;
            CourseReplaceDO load = courseReplaceService.load(id);
            teacherName = load.getTeacherName();
            sourceId = replaceSourceId;
        }
        String query = String.format("id=%s&type=%s&role=approve", id, adjustmentType.getType());
        String content = teacherName + "教师的" + adjustmentType.getName() + "申请正等待您的审批，请及时处理。";
        String officialAccountModel = basicPushMessage.getOfficialAccountModel(content, teacherName, adjustmentType.getName(), miniProgramUrl, id, urgePushItemCode);
        JsonObject json = new JsonObject();
        json.put("adjustmentType", adjustmentType.getType());
        json.put("type", adjustmentType.getName());
        json.put("relatedId", id);
        json.put("relatedType", relatedType);
        flowFacade.urgeFlow(urgePushItemCode, adjustmentType.getName(), ao.getFlowId(),
                json.toString(), String.format("%s?id=%s&type=%s&role=approve", miniProgramUrl, id, adjustmentType.getType()), officialAccountModel, miniProgramUrl, query, sourceId, null);
        redisTemplate.opsForValue().set(key, "1", 30 * 60, TimeUnit.SECONDS);
    }

    /**
     * 撤销
     */
    @Override
    public void cancel(AdjustmentCancelAO ao) {
        Long id = ao.getId();
        Integer type = ao.getType();
        String processInstanceId = ao.getProcessInstanceId();
        String message;
        // 判断所有节点是否有人审批过
        // 获取流程信息
        FlowVO flowVO = flowFacade.getFlowInformation(id, relatedType);
        List<FlowVO.Nodes> nodes = flowVO.getNodes();
        if (!CollectionUtils.isEmpty(nodes)) {
            List<FlowVO.Approvers> allApprovers = new ArrayList<>();
            nodes.forEach(nodeVO -> {
                allApprovers.addAll(nodeVO.getApprovers());
            });
            if (!CollectionUtils.isEmpty(allApprovers)) {
                Optional<FlowVO.Approvers> first = allApprovers.stream().filter(p -> TaskApprovalStateEnum.PASSED.getCode().equals(p.getResultState())
                        || TaskApprovalStateEnum.REJECTED.getCode().equals(p.getResultState())
                        || TaskApprovalStateEnum.APPROVER_INVALID_HAS_PASS.getCode().equals(p.getResultState())
                        || TaskApprovalStateEnum.ROLE_INVALID_HAS_PASS.getCode().equals(p.getResultState())
                        || TaskApprovalStateEnum.STARTER_IS_APPROVER.getCode().equals(p.getResultState())
                ).findFirst();
                if (!first.isPresent()) {
                    if (type == 1) {
                        message = "调课撤销";
                        UpdateWrapper wrapper = new UpdateWrapper();
                        wrapper.eq("id", id).or().eq("main_id",id).set("approval_state", ApprovalState.REVOKED.name());
                        courseAdjustmentDao.executeUpdate(wrapper);
                    } else {
                        message = "代课撤销";
                        CourseReplaceDO load = courseReplaceService.load(id);
                        load.setApprovalState(ApprovalState.REVOKED);
                        courseReplaceService.saveForUpdate(id, load);
                    }
                    flowFacade.cancelLeaving(processInstanceId, message);
                }else {
                    throw new BusinessException(CodeRes.CODE_600015);
                }
            }
        }
    }

    /**
     * 获取流程信息
     */
    @Override
    public FlowVO getFlowDetails(Long relatedId, Integer relatedType, AdjustmentType adjustmentType) {
        FlowVO flowVO = flowFacade.getFlowInformation(relatedId, relatedType);
        // 发起时间从自己数据库获取
        Date createdDate;
        if (adjustmentType.equals(AdjustmentType.ADJUSTMENT)) {
            // 调课
            CourseAdjustmentDO load = this.load(relatedId);
            createdDate = load.getCreatedDate();
        } else {
            // 代课
            CourseReplaceDO load = courseReplaceService.load(relatedId);
            createdDate = load.getCreatedDate();
        }
        if (flowVO != null && createdDate != null) {
            flowVO.setApplyTime(createdDate);
        }
        return flowVO;
    }

    /**
     * 根据id获取调课详情
     */
    @Override
    public CourseAdjustmentVO getAdjustmentById(Long id) {
        CourseAdjustmentDO load = courseAdjustmentDao.load(id);
        if (load != null) {
            List<CourseAdjustmentDO> attachments = queryForList("main_id", id);
            CourseAdjustmentVO courseAdjustmentVO = ClassConverter.aTob(load, new CourseAdjustmentVO());
            courseAdjustmentVO.setCycleTypeInt(load.getCycleType());
            if (CycleTypeEnum.CUSTOM_CYCLE.getType().equals(load.getCycleType())) {
                courseAdjustmentVO.setCustomStartTime(DateUtil.format(load.getCustomStartTime(),"yyyy-MM-dd HH:mm"));
                courseAdjustmentVO.setCustomEndTime(DateUtil.format(load.getCustomEndTime(),"yyyy-MM-dd HH:mm"));
            }
            courseAdjustmentVO.setWeekCycle(load.getWeekCycleNum() + "次");
            if (load.getIsNotStatistics()) {
                courseAdjustmentVO.setStatistics("不参与统计");
            } else {
                courseAdjustmentVO.setStatistics("参与统计");
            }
            if (attachments != null && !attachments.isEmpty()) {
                List<String> strings = new ArrayList<>();
                for (CourseAdjustmentDO attachment : attachments) {
                    String s;
                    if (attachment.getIsSelfStudy()) {
                        s = attachment.getCourseTime() + " 设为自修";
                    } else {
                        s = attachment.getCourseTime() + " 与 " + attachment.getAdjustedCourseTime() + " 调课";
                    }
                    strings.add(s);
                }
                courseAdjustmentVO.setWeekCycleList(strings);
            }
            courseAdjustmentVO.setApprovalState(load.getApprovalState().getName());
            return courseAdjustmentVO;
        }
        return null;
    }

    /**
     * 单个教师调代课申请记录分页
     */
    @Override
    public QueryResult<List<AdjustmentAndReplaceApplyRecordsVO>> getApplyPage(RecordsPageAO ao) {
        JwtUser user = getJwtUser();
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("teacherName", user.getName());
        QueryResult<List<AdjustmentAndReplaceApplyRecordsDTO>> queryResult = queryForPage(AdjustmentAndReplaceApplyRecordsDTO.class, "personalApplyRecords", map);
        if (queryResult == null) {
            return null;
        }
        String typeStr;
        List<AdjustmentAndReplaceApplyRecordsVO> result1 = new ArrayList<>();
        for (AdjustmentAndReplaceApplyRecordsDTO dto : queryResult.getResult()) {
            AdjustmentAndReplaceApplyRecordsVO vo = ClassConverter.aTob(dto, new AdjustmentAndReplaceApplyRecordsVO());
            vo.setApprovalState(dto.getApprovalState().getName());
            if ("1".equals(dto.getType())) {
                typeStr = "调课";
            } else {
                typeStr = "代课";
            }
            vo.setType(typeStr);
            if (dto.getWeekCycleNum() != null && dto.getWeekCycleNum() != 0) {
                vo.setWeekCycle(dto.getWeekCycleNum() + "次");
            }
            //组装循环方式
            if (Objects.nonNull(dto.getCycleType())) {
                if (CycleTypeEnum.NOT_CYCLE.getType().equals(dto.getCycleType())) {
                    vo.setCycleType(CycleTypeEnum.NOT_CYCLE.getName());
                }else if (CycleTypeEnum.WEEK_CYCLE.getType().equals(dto.getCycleType())) {
                    vo.setCycleType(CycleTypeEnum.WEEK_CYCLE.getName() + "(" + dto.getWeekCycleNum() + ")次");
                }else {
                    vo.setCycleType(CycleTypeEnum.CUSTOM_CYCLE.getName()+"("+DateUtil.formatDate(dto.getCustomStartTime())+"至"+DateUtil.formatDate(dto.getCustomEndTime())+")");
                }
            }
            result1.add(vo);
        }
        QueryResult<List<AdjustmentAndReplaceApplyRecordsVO>> queryResult1 = new QueryResult<>();
        queryResult1.setPageInfo(queryResult.getPageInfo());
        queryResult1.setResult(result1);
        return queryResult1;
    }

    /**
     * 根据调代课id和调代课类型获取调代课详情（课表）
     */
    @Override
    public AdjustmentOrReplaceVO getAdjustmentOrReplaceDetail(CourseTableAdjustmentAO courseTableAdjustmentAo) {
        AbbTypeEnum type = courseTableAdjustmentAo.getType();
        Long adjustmentId = courseTableAdjustmentAo.getAdjustmentId();
        switch (type) {
            case ADJUSTMENT:
            case COVERADJUSTMENT:
                CourseAdjustmentDO courseAdjustmentDO = courseAdjustmentDao.load(adjustmentId);
                if (courseAdjustmentDO == null) {
                    return null;
                }
                AdjustmentOrReplaceVO adjustmentDetail = CourseAdjustmentConvert.INSTANCE.doToVo(courseAdjustmentDO);
                adjustmentDetail.setSectionName(CourseTimeUtils.getFullSection(adjustmentDetail.getCourseTime()));
                String courseTimeResult;
                String adjustedCourseTimeResult;
                if (courseAdjustmentDO.getAdjustDate() != null) {
                    String monthAndDayDate = DateUtils.formatDateMd(courseAdjustmentDO.getAdjustDate());
                    courseTimeResult = CourseTimeUtils.getFullChineseDayOfWeek(adjustmentDetail.getCourseTime()) + "（" + monthAndDayDate + "）";
                } else {
                    courseTimeResult = CourseTimeUtils.getFullChineseDayOfWeek(adjustmentDetail.getCourseTime());
                }
                adjustmentDetail.setCourseTime(courseTimeResult);
                if (!courseAdjustmentDO.getIsSelfStudy()) {
                    adjustmentDetail.setAdjustedSectionName(CourseTimeUtils.getFullSection(adjustmentDetail.getAdjustedCourseTime()));
                    if (courseAdjustmentDO.getAdjustedDate() != null) {
                        String monthAndDayDate = DateUtils.formatDateMd(courseAdjustmentDO.getAdjustedDate());
                        adjustedCourseTimeResult = CourseTimeUtils.getFullChineseDayOfWeek(adjustmentDetail.getAdjustedCourseTime()) + "（" + monthAndDayDate + "）";
                    } else {
                        adjustedCourseTimeResult = CourseTimeUtils.getFullChineseDayOfWeek(adjustmentDetail.getAdjustedCourseTime());
                    }
                    adjustmentDetail.setAdjustedCourseTime(adjustedCourseTimeResult);
                } else {
                    adjustmentDetail.setAdjustedCourseName("自修");
                    adjustmentDetail.setAdjustedSectionName(CourseTimeUtils.getFullSection(courseAdjustmentDO.getCourseTime()));
                    adjustmentDetail.setAdjustedTeacherName(courseAdjustmentDO.getTeacherName());
                    adjustmentDetail.setAdjustedCourseTime(courseTimeResult);
                    adjustmentDetail.setAdjustedCourseRoom(courseAdjustmentDO.getCourseRoom());
                    adjustmentDetail.setAdjustedCourseRoomId(courseAdjustmentDO.getCourseRoomId());
                }
                return adjustmentDetail;
            case SUBSTITUTE:
            case COVERSUBSTITUTE:
                return replaceSectionService.getReplaceByIdAndCourseTime(courseTableAdjustmentAo.getAdjustmentId());
            default:
                return null;
        }
    }

    /**
     * jwt 用户校验
     *
     * @return
     */
    private JwtUser getJwtUser() {
        JwtUser user = UserContextHolder.getUser();
        if (user == null) {
            throw new BusinessException(CodeRes.CODE_400006);
        }
        return user;
    }
}
