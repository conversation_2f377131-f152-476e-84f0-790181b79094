package com.xiaoshan.edu.service;

import java.util.Collections;
import java.util.List;

import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xiaoshan.basic.vo.SemesterVO;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.oa.dto.ClassDTO;

import start.framework.commons.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Service
public class GetCurriculumTable {
    @Autowired
    private SettingFacade settingFacade;
    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private CurriculumTableService curriculumTableService;

    // 获取课表
    public List<CurriculumTableDO> getCurriculumTableDO(Long classId, Integer type) {
        // 获取当前学期
        SemesterVO currentSemesters = settingFacade.currentSemester();
        Integer orderNo = currentSemesters.getOrderNo();
        Long semesterId = currentSemesters.getId();

        if (type == 1 && classId != null) {
            // 通过班级id获取学段年级，获取课表（行政班）
            List<ClassDTO> classDTOList = foundationFacade.getAllClass(String.valueOf(classId));
            if (classDTOList == null || classDTOList.size() == 0) {
                throw new BusinessException("班级数据不存在！");
            }
            ClassDTO classDto = classDTOList.get(0);
            // 获取年级id
            Integer enrollmentYear = classDto.getEnrollmentYear();
            Long sectionId = classDto.getSectionId();
            // 获取总课表id
            return Collections.singletonList(curriculumTableService.getTable(sectionId, enrollmentYear, semesterId, orderNo));
        } else {
            // 不通过年级id获取课表（走班课、教师）
            return curriculumTableService.getTable(semesterId, orderNo);
        }
    }
}
