package com.xiaoshan.edu.timetable.utils;

import com.alibaba.fastjson.JSON;
import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.basic.vo.RoomVO;
import com.xiaoshan.basic.vo.StudentVO;
import com.xiaoshan.basic.vo.TeacherVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.ao.timetable.CheckAO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.dto.TempCurriculumTableDTO;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import start.framework.commons.exception.BusinessException;
import start.magic.utils.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class CurriculumTableCheck {

    private CheckAO table;

    private List<StudentVO> students;
    private Map<String, String> curriculumAliasMap;

    private Map<String, TeacherVO> teacherMap;
    private Map<String, ClassVO> classesMap;
    private Map<String, RoomVO> roomMap;

    private Map<String, TempCurriculumTableDTO> cache;
    private WeekTypeEnum weekType;

    private Map<String, List<List<String>>> checkMainTable;
    private Map<String, List<List<String>>> checkTeacherTable;
    private Map<String, List<List<String>>> checkClassTable;
    private Map<String, List<List<String>>> checkStudentTable;

    private Map<String, List<TeacherTableDTO>> teacherTable = new LinkedHashMap<>();

    public CurriculumTableCheck(CheckAO ao, Map<String, TempCurriculumTableDTO> cache,
                                WeekTypeEnum weekType, List<StudentVO> students, List<TeacherVO> teachers,
                                List<ClassVO> classes, List<RoomVO> rooms, Map<String, String> curriculumAliasMap) {
        this.table = ao;
        this.cache = cache;
        this.weekType = weekType;
        this.students = students;
        this.curriculumAliasMap = curriculumAliasMap;

        classesMap = classes.stream().collect(Collectors.toMap(ClassVO::getName, Function.identity(), (t1, t2) -> t2));
        roomMap = rooms.stream().collect(Collectors.toMap(RoomVO::getName, Function.identity(), (t1, t2) -> t2));
        teacherMap = teachers.stream()
                .collect(Collectors.toMap(TeacherVO::getName, Function.identity(), (t1, t2) -> t2));
    }

    public Set<String> checkMainTable() {
        String mainTableKey = CurriculumTableTypeEnum.GENERAL + ":" + weekType;
        if (!cache.containsKey(mainTableKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.GENERAL.getDescription(),
                    WeekTypeEnum.getWeekType(weekType));
        }
        // 格式校验出错直接异常
        TempCurriculumTableDTO mainTableTemp = cache.get(mainTableKey);
        mainTableFormatCheck(mainTableTemp);
        Set<String> messages = new HashSet<>();
        List<String> existsClassList = new ArrayList<>();
        List<String> existsRoomList = new ArrayList<>();
        Map<Integer, String> abbMap = new LinkedHashMap<>();
        checkMainTable = new LinkedHashMap<>();
        for (String key : mainTableTemp.getData().keySet()) {
            List<List<String>> result = new ArrayList<>();
            List<Map<Integer, String>> items = mainTableTemp.getData().get(key);
            int headCol = 0;
            for (int i = 0; i < items.size(); i++) {
                Map<Integer, String> data = items.get(i);
                if (i == 0) {
                    headCol = data.size() + 1;
                    if (headCol == 2) {
                        throw new BusinessException(CodeRes.CODE_700014);
                    }
                    for (Integer k : data.keySet()) {
                        if (k > 1) {
                            String classOrRoomName = data.get(k);
                            if (StringUtils.isEmpty(classOrRoomName)) {
                                data.put(k, "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10032, classOrRoomName));
                            } else {
                                if (ClassTableUtils.isClass(classOrRoomName)) {
                                    // 班级是否存在
                                    if (!classesMap.containsKey(classOrRoomName)) {
                                        data.put(k, classOrRoomName + "&");
                                        messages.add(RuleUtils.messageFormat(TableMessage.C10005, classOrRoomName));
                                    } else {
                                        if (!abbMap.containsKey(k)) {
                                            abbMap.put(k, classOrRoomName);
                                        }
                                        ClassVO classes = classesMap.get(classOrRoomName);
                                        if (!CheckUtils.isTableSectionIdAndEnrollmentYear(table.getEnrollmentYear(), table.getSectionId(), classes)) {
                                            data.put(k, classOrRoomName + "&");
                                            long gyear = table.getEnrollmentYear() + 3;
                                            messages.add(RuleUtils.messageFormat(TableMessage.C10018, gyear));
                                        }
                                    }
                                    // 班级是否重复
                                    if (existsClassList.contains(classOrRoomName)) {
                                        data.put(k, classOrRoomName + "&");
                                        messages.add(TableMessage.C10002);
                                        log.info("班级列表{}", JSON.toJSONString(existsClassList));
                                        log.info("重复名称{}", JSON.toJSONString(classOrRoomName));
                                    }
                                    existsClassList.add(classOrRoomName);
                                } else {
                                    // 教室是否存在
                                    if (!roomMap.containsKey(classOrRoomName)) {
                                        data.put(k, classOrRoomName + "&");
                                        messages.add(RuleUtils.messageFormat(TableMessage.C10003, classOrRoomName));
                                    } else {
                                        if (!abbMap.containsKey(k)) {
                                            abbMap.put(k, classOrRoomName);
                                        }
                                    }
                                    // 教室是否重复
                                    if (existsRoomList.contains(classOrRoomName)) {
                                        data.put(k, classOrRoomName + "&");
                                        messages.add(TableMessage.C10001);
                                    }
                                    existsRoomList.add(classOrRoomName);
                                }
                            }
                        }
                    }
                    data.put(headCol, null);
                } else {
                    for (Integer k : data.keySet()) {
                        if (k > 1) {
                            String abbreviation = data.get(k);
                            if (!StringUtils.isEmpty(abbreviation)) {
                                if (!curriculumAliasMap.containsValue(abbreviation)) {
                                    data.put(k, abbreviation + "&");
                                    messages.add(TableMessage.C10004);
                                }
                                if (abbMap.containsKey(k)) {
                                    abbMap.remove(k);
                                }
                            }
                        }
                    }
                    for (int x = data.size() + 1; x <= headCol; x++) {
                        data.put(x, null);
                    }
                }
                List<String> cells = new ArrayList<>();
                for (int j = 0; j < data.size(); j++) {
                    cells.add(data.get(j));
                }
                result.add(cells);
            }
            for (String cName : abbMap.values()) {
                if (ClassTableUtils.isClass(cName)) {
                    messages.add(cName + "班请至少维护一门课程");
                } else {
                    messages.add(cName + "教室请至少维护一门课程");
                }
            }
            checkMainTable.put(key, result);
        }
        return messages;
    }

    public Set<String> checkTeacherTable() {
        String teacherKey = CurriculumTableTypeEnum.TEACHER + ":" + weekType;
        if (!cache.containsKey(teacherKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.TEACHER.getDescription(),
                    WeekTypeEnum.getWeekType(weekType));
        }
        TempCurriculumTableDTO teacherTableTemp = cache.get(teacherKey);
        teacherTableFormatCheck(teacherTableTemp);
        Set<String> messages = new HashSet<>();
        checkTeacherTable = new LinkedHashMap<>();
        Map<String, String> existsTeacherMap = new HashMap<>(10);
        for (String key : teacherTableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = teacherTableTemp.getData().get(key);
            List<List<String>> result = new ArrayList<>();
            List<String> chcells = new ArrayList<>();
            for (int i = 0; i < items.get(0).size(); i++) {
                chcells.add(items.get(0).get(i));
            }
            result.add(chcells);
            Map<Integer, String> courseData = items.get(1);
            List<String> cousesList = new ArrayList<>();
            for (int x = 1; x < courseData.size(); x++) {
                String courseName = courseData.get(x);
                if (StringUtils.isEmpty(courseName)) {
                    courseName = "";
                }
                if (RuleUtils.ignoreCourse(courseName)) {
                    courseData.put(x, courseName + "&");
                    messages.add(RuleUtils.messageFormat(TableMessage.C10033, courseName));
                }
                if (!curriculumAliasMap.containsKey(courseName)) {
                    courseData.put(x, courseName + "&");
                    messages.add(RuleUtils.messageFormat(TableMessage.C10014));
                }
                if (cousesList.contains(courseName)) {
                    courseData.put(x, courseName + "&");
                    messages.add(RuleUtils.messageFormat(TableMessage.C10044, courseName));
                } else {
                    cousesList.add(courseName);
                }
            }
            List<String> ccells = new ArrayList<>();
            for (int i = 0; i < courseData.size(); i++) {
                ccells.add(courseData.get(i));
            }
            result.add(ccells);
            List<String> classOrRoomList = new ArrayList<>();
            for (int x = 2; x < items.size(); x++) {
                Map<Integer, String> data = items.get(x);
                String classOrRoomName = data.get(0);
                if (StringUtils.isEmpty(classOrRoomName)) {
                    for (int i = 1; i < data.size(); i++) {
                        String teacherName = data.get(i);
                        if (!StringUtils.isEmpty(teacherName)) {
                            data.put(0, "&");
                            messages.add(RuleUtils.messageFormat(TableMessage.C10043, teacherName));
                            break;
                        }
                    }
                } else {
                    String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                    if (classOrRoomId == null) {
                        data.put(0, classOrRoomName + "&");
                        messages.add(RuleUtils.messageFormat(TableMessage.C10003, classOrRoomName));
                    } else {
                        if (ClassTableUtils.isClass(classOrRoomId)) {
                            ClassVO classes = classesMap.get(classOrRoomName);
                            if (!CheckUtils.isTableSectionIdAndEnrollmentYear(table.getEnrollmentYear(), table.getSectionId(), classes)) {
                                data.put(0, classOrRoomName + "&");
                                int gyear = table.getEnrollmentYear() + 3;
                                messages.add(RuleUtils.messageFormat(TableMessage.C10018, gyear));
                            }
                        }
                        if (classOrRoomList.contains(classOrRoomId)) {
                            data.put(0, classOrRoomName + "&");
                            messages.add(RuleUtils.messageFormat(TableMessage.C10046, classOrRoomName));
                        } else {
                            classOrRoomList.add(classOrRoomId);
                        }
                    }
                    Boolean flag = false;
                    for (int i = 1; i < data.size(); i++) {
                        String courseName = courseData.get(i);
                        String teacherName = data.get(i);
                        if (!StringUtils.isEmpty(teacherName)) {
                            flag = true;
                            if (!teacherMap.containsKey(teacherName)) {
                                data.put(i, teacherName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10007, teacherName));
                            }
                            if (existsTeacherMap.containsKey(teacherName)) {
                                if (!existsTeacherMap.get(teacherName).equals(courseName)) {
                                    data.put(i, teacherName + "&");
                                    messages.add(RuleUtils.messageFormat(TableMessage.C10022, teacherName));
                                }
                            }
                            existsTeacherMap.put(teacherName, courseName);
                        }
                    }
                    if (!flag) {
                        messages.add(RuleUtils.messageFormat(TableMessage.C10045, classOrRoomName));
                    }
                }
                List<String> cells = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    cells.add(data.get(i));
                }
                result.add(cells);
            }
            checkTeacherTable.put(key, result);
        }
        return messages;
    }

    @Deprecated
    public Set<String> checkTeacherTable1() {
        String teacherKey = CurriculumTableTypeEnum.TEACHER + ":" + weekType;
        if (!cache.containsKey(teacherKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.TEACHER.getDescription(),
                    WeekTypeEnum.getWeekType(weekType));
        }
        Map<Integer, WeekEnum> weekMap = WeekEnum.getWeekMap();
        TempCurriculumTableDTO teacherTableTemp = cache.get(teacherKey);
        teacherAndClassFormatCheck(teacherTableTemp);
        /////
        for (String courseName : teacherTableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = teacherTableTemp.getData().get(courseName);
            String abbreviation = curriculumAliasMap.get(courseName);
            TeacherTableDTO dt = null;
            for (int row = 1; row <= items.size(); row++) {
                Map<Integer, String> data = items.get(row - 1);
                if (row % 11 == 1) {
                    dt = new TeacherTableDTO();
                    String teacherName1 = data.get(0).trim();
                    String teacherName = teacherName1;
                    if (teacherName1.length() > 4) {
                        teacherName = teacherName1.substring(0, teacherName1.length() - 4);
                    } else {
                        dt.setTeacherId(0L);
                    }
                    // 教师是否存在
                    if (teacherMap.containsKey(teacherName)) {
                        dt.setTeacherId(teacherMap.get(teacherName).getId());
                    } else {
                        dt.setTeacherId(0L);
                    }
                    dt.setTeacherName(teacherName);
                    dt.setDetail(new LinkedHashMap<>());
                } else if (row % 11 == 2) {
                    List<TeacherTableDTO> list = teacherTable.get(abbreviation);
                    if (list == null) {
                        list = new ArrayList<>();
                        teacherTable.put(abbreviation, list);
                    }
                    list.add(dt);
                } else {
                    String section = data.get(0);
                    int s = 8;
                    for (int i = 1; i < s; i++) {
                        String classOrRoomName = data.get(i);
                        if (StringUtils.isEmpty(classOrRoomName)) {
                            continue;
                        }
                        WeekEnum week = weekMap.get(i);
                        //同一时间，不同老师，同一个地点上课
                        String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                        if (classOrRoomId != null) {
                            Map<WeekEnum, Map<String, String>> detail = dt.getDetail();
                            Map<String, String> detailMap = detail.get(week);
                            if (detailMap == null) {
                                detailMap = new LinkedHashMap<>();
                                detail.put(week, detailMap);
                            }
                            detailMap.put(section, classOrRoomId);
                        }
                    }
                }
            }
        }


        Set<String> messages = new HashSet<>();
        checkTeacherTable = new LinkedHashMap<>();
        List<String> existsTeacherList = new ArrayList<>();
        for (String courseName : teacherTableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = teacherTableTemp.getData().get(courseName);
            List<List<String>> result = new ArrayList<>();
            if (RuleUtils.ignoreCourse(courseName)) {
                messages.add(RuleUtils.messageFormat(TableMessage.C10033, courseName));
            }
            if (!curriculumAliasMap.containsKey(courseName)) {
                messages.add(RuleUtils.messageFormat(TableMessage.C10021));
            }
            TeacherTableDTO dt = null;
            for (int row = 1; row <= items.size(); row++) {
                Map<Integer, String> data = items.get(row - 1);
                if (row % 11 == 1) {
                    dt = new TeacherTableDTO();
                    String teacherName1 = data.get(0).trim();
                    String teacherName = teacherName1;
                    if (teacherName1.length() < 4) {
                        data.put(0, teacherName1 + "&");
                        messages.add(RuleUtils.messageFormat(TableMessage.C10041, courseName, teacherName1));
                    } else {
                        teacherName = teacherName1.substring(0, teacherName1.length() - 4);
                    }
                    // 教师是否存在
                    if (!teacherMap.containsKey(teacherName)) {
                        data.put(0, teacherName1 + "&");
                        messages.add(RuleUtils.messageFormat(TableMessage.C10007, teacherName1));
                    } else {
                        dt.setTeacherId(teacherMap.get(teacherName).getId());
                    }
                    // 教师是否重复(姓名)
                    if (existsTeacherList.contains(teacherName)) {
                        data.put(0, teacherName1 + "&");
                        messages.add(RuleUtils.messageFormat(TableMessage.C10022, teacherName));
                    }
                    dt.setTeacherName(teacherName);
                    dt.setDetail(new LinkedHashMap<>());
                    existsTeacherList.add(teacherName);
                } else if (row % 11 == 2) {

                } else {
                    String section = data.get(0);
                    int s = 8;
                    for (int i = 1; i < s; i++) {
                        String classOrRoomName = data.get(i);
                        if (StringUtils.isEmpty(classOrRoomName)) {
                            continue;
                        }
                        WeekEnum week = weekMap.get(i);
                        if (ClassTableUtils.isClass(classOrRoomName)) {
                            // 班级是否存在
                            if (!classesMap.containsKey(classOrRoomName)) {
                                data.put(i, classOrRoomName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10005, classOrRoomName));
                            } else {
                                ClassVO classes = classesMap.get(classOrRoomName);
                                if (!CheckUtils.isTableSectionIdAndEnrollmentYear(table.getEnrollmentYear(), table.getSectionId(), classes)) {
                                    data.put(i, classOrRoomName + "&");
                                    int gyear = table.getEnrollmentYear() + 3;
                                    messages.add(RuleUtils.messageFormat(TableMessage.C10018, gyear));
                                }
                            }
                        } else {
                            // 教室是否存在
                            if (!roomMap.containsKey(classOrRoomName)) {
                                data.put(i, classOrRoomName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10003, classOrRoomName));
                            }
                        }
                        //同一时间，不同老师，同一个地点上课
                        String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                        if (classOrRoomId != null) {
                            if (dt.getTeacherId() != null) {
                                if (dt.getTeacherId() != null && TeacherTableUtils.checkEquals(dt.getTeacherId(), teacherTable, week, section, classOrRoomId)) {
                                    data.put(i, classOrRoomName + "&");
                                    Set<String> teacherNames = TeacherTableUtils.getCheckEqualsTeacherName(dt.getTeacherId(), teacherTable, week, section, classOrRoomId);
                                    String message = "科目:%s " + dt.getTeacherName() + "老师与(" + StringUtils.listToString(teacherNames) + ")老师%s第%s节课同一时间共同在%s上课";
                                    messages.add(String.format(message, courseName, WeekEnum.getDayOfWeek(week), section, classOrRoomName));
                                }
                                Map<WeekEnum, Map<String, String>> detail = dt.getDetail();
                                Map<String, String> detailMap = detail.get(week);
                                if (detailMap == null) {
                                    detailMap = new LinkedHashMap<>();
                                    detail.put(week, detailMap);
                                }
                                detailMap.put(section, classOrRoomId);
                            }
                        }
                    }
                }
                List<String> cells = new ArrayList<>();
                int s = 8;
                for (int i = 0; i < s; i++) {
                    cells.add(data.get(i));
                }
                result.add(cells);
            }
            if (dt.getTeacherId() != null) {
                if (CollectionUtils.isEmpty(dt.getDetail())) {
                    messages.add(String.format(TableMessage.C10034, dt.getTeacherName()));
                }
            }
            checkTeacherTable.put(courseName, result);
        }
        return messages;
    }

    public Set<String> checkClassTable() {
        String classesKey = CurriculumTableTypeEnum.CLASSES + ":" + weekType;
        if (!cache.containsKey(classesKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.CLASSES.getDescription(),
                    WeekTypeEnum.getWeekType(weekType));
        }
        TempCurriculumTableDTO classTableTemp = cache.get(classesKey);
        teacherAndClassFormatCheck(classTableTemp);
        Set<String> messages = new HashSet<>();
        checkClassTable = new LinkedHashMap<>();
        List<String> existsClassList = new ArrayList<>();
        for (String key : classTableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = classTableTemp.getData().get(key);
            List<List<String>> result = new ArrayList<>();
            String className = "";
            for (int row = 1; row <= items.size(); row++) {
                Map<Integer, String> data = items.get(row - 1);
                if (row % 11 == 1) {
                    String v = data.get(0);
                    // 2021班转化为2021
                    className = v.substring(0, v.length() - 1);
                    // 班级是否存在
                    if (!classesMap.containsKey(className)) {
                        data.put(0, v + "&");
                        messages.add(RuleUtils.messageFormat(TableMessage.C10005, className));
                    } else {
                        ClassVO classes = classesMap.get(className);
                        if (!CheckUtils.isTableSectionIdAndEnrollmentYear(table.getEnrollmentYear(), table.getSectionId(), classes)) {
                            data.put(0, v + "&");
                            int gyear = table.getEnrollmentYear() + 3;
                            messages.add(RuleUtils.messageFormat(TableMessage.C10018, gyear));
                        }
                    }
                    // 班级是否重复
                    if (existsClassList.contains(className)) {
                        data.put(0, v + "&");
                        messages.add(TableMessage.C10020);
                    }
                    existsClassList.add(className);
                } else if (row % 11 == 2) {
                } else {
                    int s = 8;
                    for (int i = 1; i < s; i++) {
                        String courseName = data.get(i);
                        if (StringUtils.isEmpty(courseName)) {
                            continue;
                        }
                        String[] courses = courseName.split(Constant.SPLITSTRING);
                        for (String name : courses) {
                            if (RuleUtils.ignoreCourse(name)) {
                                data.put(i, courseName + "&");
                                messages.add(String.format(TableMessage.C10035, name));
                            }
                            if (!curriculumAliasMap.containsKey(name)) {
                                data.put(i, courseName + "&");
                                messages.add(TableMessage.C10014);
                            }
                        }
                    }
                }
                List<String> cells = new ArrayList<>();
                int s = 8;
                for (int i = 0; i < s; i++) {
                    cells.add(data.get(i));
                }
                result.add(cells);
            }
            checkClassTable.put(key, result);
        }
        return messages;
    }

    public Set<String> checkStudentTable() {
        String studentKey = CurriculumTableTypeEnum.STUDENT + ":" + weekType;
        if (!cache.containsKey(studentKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.STUDENT.getDescription(),
                    WeekTypeEnum.getWeekType(weekType));
        }
        TempCurriculumTableDTO studentTableTemp = cache.get(studentKey);
        studentTableFormatCheck(studentTableTemp);
        Map<String, StudentVO> studentMap = students.stream()
                .collect(Collectors.toMap(StudentVO::getStudentNo, Function.identity(), (t, t1) -> t1));
        Set<String> messages = new HashSet<>();
        List<String> existsCourseList = new ArrayList<>();
        checkStudentTable = new LinkedHashMap<>();
        Map<Integer, List<String>> teachersMap = new LinkedHashMap<>();
        List<String> existsStudentNoList = new ArrayList<>();
        for (String key : studentTableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = studentTableTemp.getData().get(key);
            List<List<String>> result = new ArrayList<>();
            List<String> chcells = new ArrayList<>();
            for (int i = 0; i < items.get(0).size(); i++) {
                chcells.add(items.get(0).get(i));
            }
            result.add(chcells);
            Map<Integer, String> headerMap = new LinkedHashMap<>();
            int s = 2;
            for (int i = 4; i < items.get(1).size(); i = i + s) {
                String courseName = items.get(1).get(i);
                headerMap.put(i, courseName);
            }
            for (int x = 1; x < items.size(); x++) {
                Map<Integer, String> data = items.get(x);
                if (x == 1) {
                    // 课程
                    int se = 4;
                    int ss = 2;
                    for (int i = se; i < data.size(); i = i + ss) {
                        String name = data.get(i - 1);
                        if (!"走班地点".equals(name)) {
                            data.put(i - 1, name + "&");
                            messages.add(TableMessage.C10039);
                        }
                        String courseName = data.get(i);
                        if (StringUtils.isEmpty(courseName)) {
                            continue;
                        }
                        if (!curriculumAliasMap.containsKey(courseName)) {
                            data.put(i, courseName + "&");
                            messages.add(TableMessage.C10014);
                        }
                        // 课程名称是否重复
                        if (existsCourseList.contains(courseName)) {
                            data.put(i, courseName + "&");
                            messages.add(RuleUtils.messageFormat(TableMessage.C10016, courseName));
                        }
                        existsCourseList.add(courseName);
                    }
                } else {
                    // 学生信息
                    String studentNo = data.get(1);
                    String studentName = data.get(2);
                    StudentVO vo = studentMap.get(studentNo);
                    if (vo != null) {
                        // 学生是否存在
                        if (!vo.getName().equals(studentName)) {
                            data.put(2, studentName + "&");
                            messages.add(RuleUtils.messageFormat(TableMessage.C10008, studentName));
                        }
                        String className = data.get(0);
                        if (!vo.getClassName().equals(className)) {
                            data.put(0, className + "&");
                            messages.add(RuleUtils.messageFormat(TableMessage.C10011, studentName, className));
                        }
                        if (!CheckUtils.isTableSectionIdAndEnrollmentYear(table.getEnrollmentYear(), table.getSectionId(), vo)) {
                            data.put(2, studentName + "&");
                            int gyear = table.getEnrollmentYear() + 3;
                            messages.add(RuleUtils.messageFormat(TableMessage.C10018, gyear));
                        }
                        if (existsStudentNoList.contains(studentNo)) {
                            data.put(2, studentName + "&");
                            messages.add(RuleUtils.messageFormat(TableMessage.C10015));
                        }
                        existsStudentNoList.add(studentNo);
                    } else {
                        data.put(1, studentNo + "&");
                        messages.add(RuleUtils.messageFormat(TableMessage.C10008, studentName));
                    }
                    // 教师
                    List<String> existsTeacherList = new ArrayList<>();
                    int se = 3;
                    int step = 2;
                    for (int i = se; i < data.size(); i = i + step) {
                        int classOrRoomIndex = i;
                        int teacherId = i + 1;
                        String classOrRoomName = data.get(i);
                        String teacherName = null;
                        if (data.size() > i) {
                            teacherName = data.get(teacherId);
                        }
                        if (StringUtils.isEmpty(teacherName)) {
                            if (!StringUtils.isEmpty(classOrRoomName)) {
                                data.put(classOrRoomIndex, classOrRoomName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10036, studentName, classOrRoomName));
                            }
                            continue;
                        }
                        if ("1".equals(teacherName)) {
                            if (!StringUtils.isEmpty(classOrRoomName)) {
                                data.put(classOrRoomIndex, classOrRoomName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10038, studentName));
                            }
                        } else {
                            if (StringUtils.isEmpty(classOrRoomName)) {
                                data.put(classOrRoomIndex, classOrRoomName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10037, studentName));
                            } else {
                                String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                                if (StringUtils.isEmpty(classOrRoomId)) {
                                    data.put(classOrRoomIndex, classOrRoomName + "&");
                                    messages.add(RuleUtils.messageFormat(TableMessage.C10040, studentName, classOrRoomName));
                                } else {
                                    if (vo != null) {
                                        if (classOrRoomName.equals(vo.getClassName())) {
                                            String courseName = headerMap.get(teacherId);
                                            data.put(classOrRoomIndex, classOrRoomName + "&");
                                            messages.add(RuleUtils.messageFormat(TableMessage.C10042, studentName, courseName));
                                        }
                                    }
                                }
                            }
                            // 教师是否存在
                            if (!teacherMap.containsKey(teacherName)) {
                                data.put(teacherId, teacherName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10007, teacherName));
                            }
                            //TODO:同一老师教多门课处理代码
                            // 一个教师只能教一门课
                            if (existsTeacherList.contains(teacherName)) {
                                data.put(teacherId, teacherName + "&");
                                messages.add(RuleUtils.messageFormat(TableMessage.C10017));
                            }
                            existsTeacherList.add(teacherName);
                            //
                            for (Integer k : teachersMap.keySet()) {
                                if (k != teacherId) {
                                    List<String> teachers = teachersMap.get(k);
                                    if (teachers != null) {
                                        if (teachers.contains(teacherName)) {
                                            data.put(teacherId, teacherName + "&");
                                            messages.add(RuleUtils.messageFormat(TableMessage.C10028));
                                        }
                                    }
                                }
                            }
                        }
                        List<String> teachers = teachersMap.get(teacherId);
                        if (teachers == null) {
                            teachers = new ArrayList<>();
                            teachersMap.put(teacherId, teachers);
                        }
                        teachers.add(teacherName);
                    }
                }
                List<String> cells = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    cells.add(data.get(i));
                }
                result.add(cells);
            }
            checkStudentTable.put(key, result);
        }
        return messages;
    }

    /**
     * 总课表表格校验
     *
     * @param tableTemp
     */
    public void mainTableFormatCheck(TempCurriculumTableDTO tableTemp) {
        for (String key : tableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = tableTemp.getData().get(key);
            for (int i = 0; i < items.size(); i++) {
                Map<Integer, String> item = items.get(i);
                for (Integer sectionIndex : item.keySet()) {
                    if (i > 0) {
                        int v = i % 9;
                        if (v == 0) {
                            v = 9;
                        }
                        if (v == 1 && sectionIndex == 0) {
                            // Week
                            String week = item.get(sectionIndex);
                            int sv = RuleUtils.getWeekIndex(week);
                            if (i / 9 != sv) {
                                throw new BusinessException(CodeRes.CODE_700010);
                            }
                        } else if (sectionIndex == 1) {
                            String section = item.get(sectionIndex);
                            int sv = RuleUtils.getSectionIndex(section) + 1;
                            if (sv != v) {
                                throw new BusinessException(CodeRes.CODE_700010);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 教师班级表格校验
     *
     * @param tableTemp
     */
    public void teacherAndClassFormatCheck(TempCurriculumTableDTO tableTemp) {
        for (String key : tableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = tableTemp.getData().get(key);
            if (items.size() % 11 == 0) {
                int z = 0;
                int s = 11;
                for (int j = z; j < items.size() / s; j++) {
                    List<Map<Integer, String>> vals = items.subList(j * 11, j * 11 + 11);
                    Map<Integer, String> header = vals.get(1);
                    if (header.size() >= 8) {
                        if (!"节次".equals(header.get(0))) {
                            throw new BusinessException(CodeRes.CODE_700010);
                        }
                        int se = 1;
                        int ss = 8;
                        for (int i = se; i < ss; i++) {
                            String section = header.get(i);
                            if (StringUtils.isEmpty(section)) {
                                throw new BusinessException(CodeRes.CODE_700010);
                            }
                            int index = RuleUtils.getWeekIndex(section, false) + 1;
                            if (i != index) {
                                throw new BusinessException(CodeRes.CODE_700010);
                            }
                        }
                    }
                    int sj = 2;
                    for (int i = sj; i < vals.size(); i++) {
                        String section = vals.get(i).get(0);
                        int sectionIndex = RuleUtils.getSectionIndex(section) + 2;
                        if (sectionIndex != i) {
                            throw new BusinessException(CodeRes.CODE_700010);
                        }
                    }
                }
            } else {
                throw new BusinessException(CodeRes.CODE_700010);
            }
        }
    }

    public void teacherTableFormatCheck(TempCurriculumTableDTO tableTemp) {
        for (String key : tableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = tableTemp.getData().get(key);
            if (items.size() > 1) {
                Map<Integer, String> header = items.get(1);
                if (header.size() > 1) {
                    if (!"上课地点".equals(header.get(0))) {
                        throw new BusinessException(CodeRes.CODE_700010);
                    }
                } else {
                    throw new BusinessException(CodeRes.CODE_700010);
                }
            } else {
                throw new BusinessException(CodeRes.CODE_700010);
            }
        }
    }

    /**
     * 学生表格校验
     *
     * @param tableTemp
     */
    public void studentTableFormatCheck(TempCurriculumTableDTO tableTemp) {
        for (String key : tableTemp.getData().keySet()) {
            List<Map<Integer, String>> items = tableTemp.getData().get(key);
            if (items.size() > 1) {
                Map<Integer, String> header = items.get(1);
                if (header.size() > 3) {
                    if (!"班级".equals(header.get(0))) {
                        throw new BusinessException(CodeRes.CODE_700010);
                    } else if (!"学号".equals(header.get(1))) {
                        throw new BusinessException(CodeRes.CODE_700010);
                    } else if (!"姓名".equals(header.get(2))) {
                        throw new BusinessException(CodeRes.CODE_700010);
                    }
                } else {
                    throw new BusinessException(CodeRes.CODE_700010);
                }
            } else {
                throw new BusinessException(CodeRes.CODE_700010);
            }
        }
    }

    public Map<String, List<List<String>>> getCheckMainTable() {
        return checkMainTable;
    }

    public Map<String, List<List<String>>> getCheckTeacherTable() {
        return checkTeacherTable;
    }

    public Map<String, List<List<String>>> getCheckClassTable() {
        return checkClassTable;
    }

    public Map<String, List<List<String>>> getCheckStudentTable() {
        return checkStudentTable;
    }

}
