package com.xiaoshan.edu.patrol.excel;

import java.util.ArrayList;
import java.util.List;

public class ExcelHeadsUtil {
    // 教师巡课列表 excel表头
    public static List<List<String>> patrolRecordHead(String title) {
        String[] head = {"序号", "巡课对象", "巡课类型", "巡课班级", "部门", "巡课时间", "巡课科目", "巡课老师", "巡课评价", "记录时间", "巡课内容"};
        return getLists(title, head);
    }

    // 单个对象巡课列表 excel表头
    public static List<List<String>> singleRecordHead(String title) {
        String[] head = {"序号", "巡课对象", "巡课类型", "巡课班级", "巡课时间", "巡课科目", "巡课老师", "巡课评价", "记录时间", "巡课内容"};
        return getLists(title, head);
    }

    // 巡课统计班级 excel表头
    public static List<List<String>> statisticsClassHead(String title){
        String[] head = {"序号", "班级", "被巡课次数", "表现优秀次数", "表现一般次数", "表现较差次数"};
        return getLists(title, head);
    }

    // 巡课统计教师 excel表头
    public static List<List<String>> statisticsTeacherHead(String title){
        String[] head = {"序号", "教师", "部门", "被巡课次数", "表现优秀次数", "表现一般次数", "表现较差次数"};
        return getLists(title, head);
    }

    // 巡课统计学生 excel表头
    public static List<List<String>> statisticsStudentHead(String title){
        String[] head = {"序号", "学号", "学生", "年级", "班级", "被巡课次数", "表现优秀次数", "表现一般次数", "表现较差次数"};
        return getLists(title, head);
    }

    public static List<List<String>> getLists(String title, String[] head) {
        List<List<String>> list = new ArrayList<>();
        for (String s : head) {
            ArrayList<String> l = new ArrayList<>();
            l.add(title);
            l.add(s);
            list.add(l);
        }
        return list;
    }
}
