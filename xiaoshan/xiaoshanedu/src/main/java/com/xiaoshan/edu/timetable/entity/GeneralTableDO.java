package com.xiaoshan.edu.timetable.entity;

import java.util.List;
import java.util.Map;

import com.common.converter.DataTableConverterEditor;
import com.common.converter.MapDataTableConverterEditor;
import com.common.converter.StudentDataTableConverterEditor;
import com.xiaoshan.edu.converter.MapStringListStringConverterEditor;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.rest.converter.list.ListLongConverterEditor;
import start.framework.commons.rest.converter.map.MapLongLongConverterEditor;
import start.framework.commons.rest.converter.map.MapStringStringConverterEditor;
import start.framework.entity.BaseExt;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.Indexes;
import start.magic.persistence.source.jdbc.script.annotations.IndexesUnion;
import start.magic.persistence.source.jdbc.script.annotations.ScriptConverter;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.Normal;
import start.magic.persistence.source.jdbc.script.annotations.indexes.UniqueUnion;
import start.magic.persistence.source.jdbc.script.converter.FieldJson;
import start.magic.persistence.source.jdbc.script.converter.FieldMediumText;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity("generalTable")
@Table("tim_general_table")
@TableDef(comment = "课表数据", indexes =
@IndexesUnion(unique = @UniqueUnion(fields = {"curriculumTableId", "type"})))
public class GeneralTableDO extends BaseExt {

    private static final long serialVersionUID = 1L;

    public GeneralTableDO() {
    }

    @ColumnDef(indexes = @Indexes(normal = @Normal), comment = "课表ID")
    private Long curriculumTableId;

    @ColumnDef(comment = "周类型")
    private WeekTypeEnum type;

    @ColumnDef(comment = "总课表,List<班级教室ID:Map<周:Map<节次:课程>>>")
    @ScriptConverter(FieldMediumText.class)
    @PropertyConverter(DataTableConverterEditor.class)
    private List<DataTableDTO> mainTable;

    @ColumnDef(comment = "班级课表,List<班级教室ID:Map<周:Map<节次:课程>>>")
    @ScriptConverter(FieldMediumText.class)
    @PropertyConverter(DataTableConverterEditor.class)
    private List<DataTableDTO> classTable;

    @ColumnDef(comment = "学生课表,Map<班级ID:List<Map<学生ID,Map<老师ID,教室ID>>>>")
    @ScriptConverter(FieldMediumText.class)
    @PropertyConverter(StudentDataTableConverterEditor.class)
    private List<StudentTableDTO> studentTable;

    @ColumnDef(comment = "教师课表,课程->List<教师ID:Map<周:Map<节次:班级教室ID>>>")
    @ScriptConverter(FieldMediumText.class)
    @PropertyConverter(MapDataTableConverterEditor.class)
    private Map<String, List<TeacherTableDTO>> teacherTable;

    @ColumnDef(comment = "班主任,Map<教师ID,班级ID>")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(MapLongLongConverterEditor.class)
    private Map<Long, Long> classTeacher;

    @ColumnDef(comment = "课程映射 Map<语,语文>")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(MapStringStringConverterEditor.class)
    private Map<String, String> curriculumAlias;

    @ColumnDef(comment = "学生列表")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(ListLongConverterEditor.class)
    private List<Long> stuIds;

    @ColumnDef(comment = "任课教师")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(ListLongConverterEditor.class)
    private List<Long> teacherIds;

    @ColumnDef(comment = "班主任")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(ListLongConverterEditor.class)
    private List<Long> classTeacherIds;

    @ColumnDef(comment = "班级ID")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(ListLongConverterEditor.class)
    private List<Long> classIds;

    @ColumnDef(comment = "班级,Map<班级ID,房间ID>")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(MapLongLongConverterEditor.class)
    private Map<Long, Long> classRoomMap;

    @ColumnDef(comment = "走班课程,Map<班级ID,List<周-次节-课程>>")
    @ScriptConverter(FieldJson.class)
    @PropertyConverter(MapStringListStringConverterEditor.class)
    private Map<String, List<String>> classLeaveMap;

}
