package com.xiaoshan.edu.api.facade;

import com.xiaoshan.basic.ao.NewWorkFlowAO;
import com.xiaoshan.basic.vo.BatchFlowsVO;
import com.xiaoshan.basic.vo.FlowNodesListVO;
import com.xiaoshan.basic.vo.FlowVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FlowFacade {

    /**
     * 教师公出/请假 - 模拟获取节点（发起流程前获取节点信息，地址：/v2/flows/nodes）
     * @param departments 部门ids
     * @param roles 角色ids
     * @param applicantId 人员ids
     * @param definitionKey 获取的流程引擎模板返回值中的templateKey字段
     * @param conditions 流程条件
     * @return
     */
    FlowNodesListVO getFlowNodes(String departments, String roles, String applicantId, String definitionKey, Map<String,String> conditions);

    /**
     * 获取流程信息（发起流程后获取节点信息，地址：/v2/flows ）
     * @param relatedId 关联业务ID
     * @param relatedType 关联类型
     * @return 流程信息
     */
    FlowVO getFlowInformation(Long relatedId, Integer relatedType);

    /**
     *批量获取流程信息
     * @param relatedId
     * @param relatedType
     * @return
     */
    List<BatchFlowsVO> batchFlows(Long relatedId, Integer relatedType);

    /**
     * 开始流程（提交表单时，地址：/v2/flows ）
     * @param workFlowAo 流程对象
     */
    void beginTheWorkFlow(NewWorkFlowAO workFlowAo);

    /**
     * 获取流程模板ID
     * @param relatedType 关联类型
     * @return 流程模板ID
     */
    String getFlowsModel(Integer relatedType);

    /**
     * 根据流程模板ID，判断流程是否开启
     * @param modelId 流程模板ID
     * @return 流程是否开启
     */
    String checkModelStarting(String modelId);

    /**
     * 催办（/v2/flows/urge ）
     * @param messageCode
     * @param content
     * @param flowId
     * @param extra
     * @param miniProgramUrl
     * @param officeAccountData
     * @param path
     * @param query
     * @param sourceId
     * @param studentId
     */
    void urgeFlow(String messageCode, String content, Long flowId, String extra, String miniProgramUrl,
                  String officeAccountData, String path, String query, Integer sourceId, Long studentId);


    /**
     * 撤销
     * @param engineFlowId
     * @param message
     */
    void cancelLeaving(String engineFlowId, String message);

}
