package com.xiaoshan.edu.courseadjustment.utils;

import com.xiaoshan.edu.enums.timetable.WeekEnum;

/**
 * <AUTHOR>
 */
public class CourseTimeUtils {

    public static Integer getIndexOfWeek(String courseTime) {
        return courseTime.indexOf("周周");
    }

    public static String getChineseWeekNumber(String courseTime) {
        return courseTime.substring(1, getIndexOfWeek(courseTime));
    }

    public static Integer getWeekNumber(String courseTime) {
        return CourseAdjustmentUtils.chinese2Number(getChineseWeekNumber(courseTime));
    }

    public static String getSection(String courseTime) {
        return courseTime.substring(getIndexOfWeek(courseTime) + 4, getIndexOfWeek(courseTime) + 5);
    }

    public static String getFullSection(String courseTime) {
        return courseTime.substring(getIndexOfWeek(courseTime) + 3, getIndexOfWeek(courseTime) + 6);
    }

    public static WeekEnum getDayOfWeek(String courseTime) {
        return WeekEnum.getDayOfWeek(courseTime.substring(getIndexOfWeek(courseTime) + 2, getIndexOfWeek(courseTime) + 3));
    }

    public static String getChineseDayOfWeek(String courseTime) {
        return courseTime.substring(getIndexOfWeek(courseTime) + 1, getIndexOfWeek(courseTime) + 3);
    }

    public static String getFullChineseDayOfWeek(String courseTime) {
        return courseTime.substring(getIndexOfWeek(courseTime) + 1, getIndexOfWeek(courseTime) + 3);
    }

    public static String getNoWeekCourseTime(String courseTime) {
        return courseTime.substring(getIndexOfWeek(courseTime) + 1);
    }

}
