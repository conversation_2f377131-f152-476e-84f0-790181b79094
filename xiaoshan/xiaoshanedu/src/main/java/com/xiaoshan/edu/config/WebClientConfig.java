package com.xiaoshan.edu.config;

import com.alibaba.druid.util.StringUtils;
import com.common.model.UserContextHolder;
import com.xiaoshan.basic.vo.TokenVO;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.client.loadbalancer.reactive.ReactorLoadBalancerExchangeFilterFunction;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.tcp.TcpClient;
import start.magic.thirdparty.codec.Base64;

import javax.net.ssl.SSLException;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Configuration
public class WebClientConfig {
    public static final String BEARER_TOKEN = "BearerToken";
    @Value("${xiaoshan.token.client_id}")
    private String clientId;

    @Value("${xiaoshan.token.client_secret}")
    private String clientSecret;

    @Value("${xiaoshan.token.auth}")
    private String basicAuth;

    @Autowired
    private ReactorLoadBalancerExchangeFilterFunction reactorLoadBalancerExchangeFilterFunction;

    @Autowired
    @Qualifier("authClient")
    private WebClient authClient;

    @Autowired
    @Qualifier("okRedis")
    private RedisTemplate<String, Object> redisTemplate;


    @Autowired
    DiscoveryClient discoveryClient;

    @Bean("webClient")
    public WebClient webClient() throws SSLException {
        SslContext sslContext = SslContextBuilder
                .forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();
        TcpClient tcpClient = TcpClient.create().secure(sslContextSpec -> sslContextSpec.sslContext(sslContext));
        HttpClient httpClient = HttpClient.from(tcpClient);
        return WebClient.builder().clientConnector(new ReactorClientHttpConnector(httpClient)).filter(reactorLoadBalancerExchangeFilterFunction)
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(configurer -> {
                            configurer.defaultCodecs()
                                    .maxInMemorySize(16 * 1024 * 1024) ; }
                        )
                        .build())
                .filter((request, next) -> {
                    TokenVO tokenVO = getToken();
                    ClientRequest filtered = ClientRequest.from(request)
                            .header("Authorization", tokenVO.getTokenType() + " " + tokenVO.getAccessToken())
                            .build();
                    return next.exchange(filtered);
                }).build();
    }
    public TokenVO getToken() {
        String t = UserContextHolder.getToken();
        if (!StringUtils.isEmpty(t)) {
            String[] tokens = t.split(" ");
            TokenVO tokenVO = new TokenVO();
            tokenVO.setTokenType(tokens[0]);
            tokenVO.setAccessToken(tokens[1]);
            return tokenVO;
        }
        if (Boolean.TRUE.equals(redisTemplate.hasKey(BEARER_TOKEN))) {
            return (TokenVO) redisTemplate.opsForValue().get(BEARER_TOKEN);
        } else {
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.set("grant_type", "client_credentials");
            formData.set("client_id", clientId);
            formData.set("client_secret", clientSecret);
            List<ServiceInstance> instances = discoveryClient.getInstances("sso");
            if (instances.size() > 0) {
                ServiceInstance serviceInstance = instances.get(0);
                String uri = "https://" + serviceInstance.getHost() + ":" + serviceInstance.getPort() + "/oauth/token";
                TokenVO tokenVO = authClient.post().uri(uri).
                        header("Authorization", "Basic " + Base64.encode(basicAuth.getBytes()))
                        .bodyValue(formData).retrieve().bodyToMono(TokenVO.class).block();
                redisTemplate.opsForValue().set(BEARER_TOKEN, tokenVO, 7000, TimeUnit.SECONDS);
                return tokenVO;
            }
        }

        return null;
    }


}
