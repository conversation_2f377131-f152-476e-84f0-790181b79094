package com.xiaoshan.edu.timetable.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.xiaoshan.edu.api.facade.FoundationFacade;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.xiaoshan.basic.vo.DictionaryVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.ao.timetable.CurriculumAO;
import com.xiaoshan.edu.ao.timetable.CurriculumPageAO;
import com.xiaoshan.edu.enums.timetable.CurriculumTypeEnum;
import com.xiaoshan.edu.timetable.dao.CurriculumDao;
import com.xiaoshan.edu.timetable.entity.CurriculumDO;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.vo.timetable.CurriculumVO;

import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.MapConvert;
import start.framework.service.impl.SqlBaseServiceImplV1Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
@Service("curriculumService")
public class CurriculumServiceImpl extends SqlBaseServiceImplV1Ext<CurriculumDO, Long>
        implements CurriculumService {

    private CurriculumDao curriculumDao;

    @Autowired
    private FoundationFacade foundationFacade;

    public CurriculumServiceImpl(@Qualifier("curriculumDao") CurriculumDao curriculumDao) {
        super(curriculumDao);
        this.curriculumDao = curriculumDao;
    }

    @Override
    public void saveForUpdate(CurriculumAO ao) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("code", ao.getCode()).ne(ao.getCurriculumId() != null, "id", ao.getCurriculumId());
        if (!curriculumDao.queryForList(wrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_700000);
        }
        wrapper = new QueryWrapper();
        wrapper.eq("name", ao.getName()).ne(ao.getCurriculumId() != null, "id", ao.getCurriculumId());
        if (!curriculumDao.queryForList(wrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_700001);
        }
        wrapper = new QueryWrapper();
        wrapper.eq("abbreviation", ao.getAbbreviation()).ne(ao.getCurriculumId() != null, "id", ao.getCurriculumId());
        if (!curriculumDao.queryForList(wrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_700005);
        }
        CurriculumDO curriculum = null;
        if (ao.getCurriculumId() != null) {
            curriculum = curriculumDao.load(ao.getCurriculumId());
        } else {
            curriculum = new CurriculumDO();
            curriculum.setSys(false);
        }
        BeanUtils.copyProperties(ao, curriculum);
        if (curriculum.getSys()) {
            curriculum.setName(null);
        }
        save(curriculum);
    }

    @Override
    public Map<String, String> aliasMap(Boolean flag) {
        List<CurriculumDO> list = queryForAll();
        Map<String, String> maps = new LinkedHashMap<>();
        for (CurriculumDO c : list) {
            if (flag) {
                maps.put(c.getName(), c.getAbbreviation());
            } else {
                maps.put(c.getAbbreviation(), c.getName());
            }
        }
        return maps;
    }

    @Override
    public QueryResult<List<CurriculumVO>> page(CurriculumPageAO ao) {
        Map<String, Object> params = MapConvert.convert(ao);
        params.put("code", null);
        params.put("name", null);
        QueryWrapper wrapper = buildQueryWrapper(CurriculumVO.class, params);
        if (wrapper.isEmptyOfWhere()) {
            wrapper.eq("1", "1");
        }
        wrapper.like(!StringUtils.isEmpty(ao.getCode()), "code", ao.getCode());
        wrapper.like(!StringUtils.isEmpty(ao.getName()), "name", ao.getName());
        wrapper.orderByDesc("id");
        wrapper.last("limit " + ao.index() + "," + ao.getPageSize());
        return queryForPage(CurriculumVO.class, wrapper);
    }

    @Override
    public int remove(List<Long> ids) {
        if (ids.size() == 0) {
            return 0;
        }
        DeleteWrapper wrapper = new DeleteWrapper();
        wrapper.in("id", ids).eq("sys", String.valueOf(false));
        return curriculumDao.executeUpdate(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sync() {
        List<DictionaryVO> dics1 = foundationFacade.getDictionaryValues("subject1");
        Map<String, DictionaryVO> dic1Map = dics1.stream().collect(Collectors.toMap(DictionaryVO::getName, Function.identity()));
        List<DictionaryVO> dics2 = foundationFacade.getDictionaryValues("subject2");
        Map<String, DictionaryVO> dic2Map = dics2.stream().collect(Collectors.toMap(DictionaryVO::getName, Function.identity()));
        Map<String, DictionaryVO> dicData = new HashMap<>(dic1Map);
        for (String name : dic2Map.keySet()) {
            if (!dicData.containsKey(name)) {
                dicData.put(name, dic2Map.get(name));
            }
        }
        UpdateWrapper sysWrapper = new UpdateWrapper();
        sysWrapper.set("sys", String.valueOf(false));
        curriculumDao.executeUpdate(sysWrapper);
        List<CurriculumDO> curriculumList = queryForAll();
        List<String> abbs = new ArrayList<>();
        for (CurriculumDO c : curriculumList) {
            abbs.add(c.getAbbreviation());
        }
        Map<String, CurriculumDO> currMap = curriculumList.stream().collect(Collectors.toMap(CurriculumDO::getName, Function.identity()));
        List<CurriculumDO> list = new ArrayList<>();
        for (String name : dicData.keySet()) {
            String abb = name.substring(0, 1);
            DictionaryVO v = dicData.get(name);
            CurriculumDO curriculum = currMap.get(name);
            if (curriculum == null) {
                curriculum = new CurriculumDO();
                curriculum.setCode(v.getId());
                if (abbs.contains(abb)) {
                    curriculum.setAbbreviation(abb + v.getId());
                } else {
                    curriculum.setAbbreviation(abb);
                }
                curriculum.setName(name);
                curriculum.setType(CurriculumTypeEnum.OTHER);
                curriculum.setRemark("");
            }
            curriculum.setSys(true);
            list.add(curriculum);
            abbs.add(abb);
        }
        saveBatch(list);
    }

}
