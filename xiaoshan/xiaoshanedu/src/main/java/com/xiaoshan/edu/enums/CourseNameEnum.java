package com.xiaoshan.edu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum CourseNameEnum {

    /**
     * 课程名称
     */
    CHINESE("语","语文"),
    MATH("数","数学"),
    ENGLISH("外","英语"),
    PHYSICAL("物","物理"),
    CHEMICAL("化","化学"),
    BIOLOGICAL("生","生物"),
    POLITICAL("政","政治"),
    HISTORY("历","历史"),
    GEOGRAPHIC("地","地理"),
    INFORMATION_TECH("信","信息"),
    GENERAL_TECH("通","通用"),
    ;


    private String code;

    private String message;
}
