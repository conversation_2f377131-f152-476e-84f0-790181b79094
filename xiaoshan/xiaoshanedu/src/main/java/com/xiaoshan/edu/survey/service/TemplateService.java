package com.xiaoshan.edu.survey.service;

import com.xiaoshan.edu.ao.survey.TemplateAO;
import com.xiaoshan.edu.ao.survey.TemplateNameAO;
import com.xiaoshan.edu.survey.entity.TemplateDO;
import com.xiaoshan.edu.vo.survey.TemplateAndQuestionVO;
import com.xiaoshan.edu.vo.survey.TemplateVO;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface TemplateService extends SqlBaseService<TemplateDO,Long> {

    /**
     * 分页查询
     * @param ao 查询条件
     * @return 模板列表
     */
    QueryResult<List<TemplateVO>> page(TemplateNameAO ao);

    /**
     * id查询详细内容
     * @param id 模板id
     * @return 详细内容
     */
    TemplateAndQuestionVO getById(Long id);

    /**
     * 保存或编辑
     * @param ao
     * @return
     */
    Long saveOrUpdate(TemplateAO ao);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    Integer removeById(Long id);

    /**
     * 下载模板
     * @param response
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 新的模板信息
     * @param id
     * @param taskId
     * @return
     */
    TemplateAndQuestionVO getCurrById(Long id, Long taskId);
}
