package com.xiaoshan.edu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaoshan.basic.vo.RoomClassTreeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.persistence.annotation.Column;

import java.util.List;

@Data
public class RoomClassFloorTreeVO {

	@ApiModelProperty("楼层id")
	private Integer floorId;

	@ApiModelProperty("楼层名称")
	private String floorName;

	@ApiModelProperty("楼层")
	private Integer level;

	@ApiModelProperty("房间列表")
	@Column("roomTreeDTOS")
	@JsonProperty("roomTreeDTOS")
	private List<RoomClassTreeDTO> roomClassTreeDTOList;

}