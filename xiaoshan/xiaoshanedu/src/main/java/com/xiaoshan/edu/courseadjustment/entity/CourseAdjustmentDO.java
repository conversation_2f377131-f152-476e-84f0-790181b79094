package com.xiaoshan.edu.courseadjustment.entity;

import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.thirdparty.json.JsonArray;

import java.sql.Date;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@Entity("courseAdjustment")
@Table("cou_course_adjustment")
@TableDef(comment = "调课记录表")
public class CourseAdjustmentDO extends BaseV2Ext {

    private static final long serialVersionUID = 1L;

    public CourseAdjustmentDO() {
    }

    @ColumnDef(comment = "调课课程")
    @Column("course_name")
    private String courseName;

    @ColumnDef(comment = "调课时间（课程时间：如：第一周周一第一节）")
    @Column("course_time")
    private String courseTime;

    @ColumnDef(comment = "调课班级或教室（教室ROOM结尾）")
    @Column("course_room")
    private String courseRoom;

    @ColumnDef(comment = "调课班级或教室id")
    @Column("course_room_id")
    private String courseRoomId;

    @ColumnDef(comment = "调课教师")
    @Column("teacher_name")
    private String teacherName;

    @ColumnDef(comment = "教师id")
    @Column("teacher_id")
    private Long teacherId;

    @ColumnDef(comment = "部门")
    @Column("department_name")
    private String departmentName;

    @ColumnDef(comment = "被调时间（被调课程时间：如：第一周周一第一节）", isNull = true)
    @Column("adjusted_course_time")
    private String adjustedCourseTime;

    @ColumnDef(comment = "被调课程", isNull = true)
    @Column("adjusted_course_name")
    private String adjustedCourseName;

    @ColumnDef(comment = "被调班级或教室", isNull = true)
    @Column("adjusted_course_room")
    private String adjustedCourseRoom;

    @ColumnDef(comment = "被调班级或教室id（教室以ROOM结尾）", isNull = true)
    @Column("adjusted_course_room_id")
    private String adjustedCourseRoomId;

    @ColumnDef(comment = "被调教师", isNull = true)
    @Column("adjusted_teacher_name")
    private String adjustedTeacherName;

    @ColumnDef(comment = "被调教师id", isNull = true)
    @Column("adjusted_teacher_id")
    private Long adjustedTeacherId;

    @ColumnDef(comment = "调课事由")
    private String content;

    @ColumnDef(comment = "图片url列表", isNull = true)
    @Column("pic_urls")
    private JsonArray picUrls;

    @ColumnDef(comment = "审批编号", isNull = true)
    @Column("approval_no")
    private String approvalNo;

    @ColumnDef(comment = "审批状态", defaultValue = "'UNDER_APPROVAL'")
    @Column("approval_state")
    private ApprovalState approvalState;

    @ColumnDef(comment = "是否自修", defaultValue = "'false'")
    @Column("is_self_study")
    private Boolean isSelfStudy;

    @ColumnDef(comment = "是否异常【因课表调整导致调代课冲突失效】", defaultValue = "'false'")
    @Column("is_unusual")
    private Boolean isUnusual;

    @ColumnDef(comment = "调课课表id【用于标识学期、年级】", isNull = true)
    @Column("curriculum_table_id")
    private Long curriculumTableId;

    @ColumnDef(comment = "被调课课表id【用于标识学期、年级】", isNull = true)
    @Column("adjusted_curriculum_table_id")
    private Long adjustedCurriculumTableId;

    @ColumnDef(comment = "调课日期", isNull = true)
    @Column("adjust_date")
    private Date adjustDate;

    @ColumnDef(comment = "被调课日期", isNull = true)
    @Column("adjusted_date")
    private Date adjustedDate;

    @ColumnDef(comment = "调课原课表时间", isNull = true)
    @Column("origin_course_time")
    private String originCourseTime;

    @ColumnDef(comment = "被调课原课表时间", isNull = true)
    @Column("adjusted_origin_course_time")
    private String adjustedOriginCourseTime;

    @ColumnDef(comment = "调课原课程教师id", isNull = true)
    @Column("origin_teacher_id")
    private Long originTeacherId;

    @ColumnDef(comment = "被调课原课程教师id", isNull = true)
    @Column("adjusted_origin_teacher_id")
    private Long adjustedOriginTeacherId;

    @ColumnDef(comment = "调课数据是否失效", isNull = true, defaultValue = "'false'")
    @Column("is_invalid")
    private Boolean isInvalid;

    @ColumnDef(comment = "被调课数据是否失效", isNull = true, defaultValue = "'false'")
    @Column("adjusted_is_invalid")
    private Boolean adjustedIsInvalid;

    @ColumnDef(comment = "调课父Id", isNull = true, defaultValue = "0")
    @Column("father_id")
    private Long fatherId;

    @ColumnDef(comment = "被调课父Id", isNull = true, defaultValue = "0")
    @Column("adjusted_father_id")
    private Long adjustedFatherId;

    @ColumnDef(comment = "调课父数据类型：ADJUSTMENT/REPLACE，调课/代课", isNull = true)
    @Column("father_type")
    private AdjustmentType fatherType;

    @ColumnDef(comment = "被调课父数据类型：ADJUSTMENT/REPLACE，调课/代课", isNull = true)
    @Column("adjusted_father_type")
    private AdjustmentType adjustedFatherType;

    @ColumnDef(comment = "周循环调课", defaultValue = "0")
    @Column("week_cycle_num")
    private Integer weekCycleNum;

    @ColumnDef(comment = "不纳入统计", defaultValue = "false")
    @Column("is_not_statistics")
    private Boolean isNotStatistics;

    @ColumnDef(comment = "是否是衍生数据", defaultValue = "false")
    @Column("is_attachment")
    private Boolean isAttachment;

    @ColumnDef(comment = "衍生数据父Id", isNull = true)
    @Column("main_id")
    private Long mainId;

    @ColumnDef(comment = "调休前调课原课表时间", isNull = true)
    @Column("before_origin_course_time")
    private String beforeOriginCourseTime;

    @ColumnDef(comment = "调休前被调课原课表时间", isNull = true)
    @Column("adjusted_before_origin_course_time")
    private String adjustedBeforeOriginCourseTime;

    @ColumnDef(comment = "循环方式(1-不循环，2-周循环，3-自定义时间段)")
    @Column("cycle_type")
    private Integer cycleType;

    @ColumnDef(comment = "自定义开始时间")
    @Column("custom_start_time")
    private java.util.Date customStartTime;

    @ColumnDef(comment = "自定义结束时间")
    @Column("custom_end_time")
    private java.util.Date customEndTime;
}
