package com.xiaoshan.edu.survey.utils.excel;

import com.common.utils.ClassConverter;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.TaskStudentService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentTeacherQuestionService;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import start.framework.commons.exception.BusinessException;
import start.magic.persistence.source.jdbc.sqlplus.toolkit.EmptyUtils;
import start.magic.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class CurriculumResultImportHandler {

    private Logger logger = LoggerFactory.getLogger(CurriculumResultImportHandler.class);

    @Autowired
    private TaskStudentService taskStudentService;
    @Autowired
    private TaskTemplateStudentTeacherQuestionService service;
    @Autowired
    private TaskTemplateStudentService taskTemplateStudentService;

    // 结果导入，解析任课教师excel
    @Transactional(rollbackFor = Exception.class)
    public void doAnalyze(TaskTemplateDO taskTemplate, MultipartFile file) throws Exception {

        // {学号：学生id}
        Map<String, Long> studentMap = taskStudentService.getByTaskId(taskTemplate.getTaskId());
        List<ResultImportEntity> excelEntityList = new ArrayList<>();

        // {语文: [{QuestionVO},{QuestionVO}]}
        Map<String, List<QuestionVO>> currQuestionListMap = new HashMap<>();
        for (QuestionVO questionVO : taskTemplate.getQuestionList()) {
            for (String curr : questionVO.getCurriculumList()) {
                List<QuestionVO> list = currQuestionListMap.computeIfAbsent(curr, key -> new ArrayList<>());
                if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                    continue;
                }
                list.add(questionVO);
            }
        }

        try (XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream())) {
            XSSFSheet sheet = workbook.getSheetAt(0);

            // {语文: 2, 生物: 2}   课程->单选多选题目数量
            Map<String, Integer> currRangeMap = getCurrRangeMap(sheet);

            for (int i = 3; i <= sheet.getLastRowNum(); i++) {  // 遍历excel数据记录
                if (isRowEmpty(sheet.getRow(i))) {
                    break;
                }
                XSSFRow row = sheet.getRow(i);
                row.getCell(0).setCellType(CellType.STRING);
                row.getCell(1).setCellType(CellType.STRING);
                String studentNo = row.getCell(0).getStringCellValue();
                String className = row.getCell(1).getStringCellValue();
                if ("".equals(studentNo) || "".equals(className)) {
                    throw new BusinessException("excel表" + (i + 1) + "行学号或班级不能为空");
                }

                ResultImportEntity entity = new ResultImportEntity();
                entity.setClassName(className);
                Long studentId = studentMap.get(studentNo);
                if (studentId == null) {
                    throw new BusinessException("学号为"+studentNo+"的学生不在此次调查任务中，请检查excel表");
                }
                entity.setStudentId(studentId);
                entity.setStudentNo(studentNo);

                int colIndex = 2;  // 答案从第三列开始

                for (Map.Entry<String, Integer> currQuestionNumEntry : currRangeMap.entrySet()) {  // 遍历每个科目
                    ResultImportEntity resultImportEntity = ClassConverter.aTob(entity, new ResultImportEntity());
                    resultImportEntity.setCurriculum(currQuestionNumEntry.getKey());
                    List<QuestionVO> questions = currQuestionListMap.get(currQuestionNumEntry.getKey());
                    if (questions == null) {
                        throw new BusinessException("模板中未维护有关" + currQuestionNumEntry.getKey() + "的题目");
                    }

                    Map<Long, List<QuestionOptionDTO>> questionIdAnswersMap = new HashMap<>();
                    resultImportEntity.setAnswer(questionIdAnswersMap);
                    excelEntityList.add(resultImportEntity);

                    for (int j = 0; j < currQuestionNumEntry.getValue(); j++) {
                        if (j >= questions.size()) {
                            throw new BusinessException("exccel表中维护的" + currQuestionNumEntry.getKey() + "科目题目数量与模板不符");
                        }
                        QuestionVO questionVO = questions.get(j);

                        // {A->问题1, B->问题2}
                        Map<String, String> itemMap = getOptionTxtMap(questionVO);

                        XSSFCell cell = row.getCell(colIndex++);
                        if (cell != null) {
                            String[] items = cell.getStringCellValue().split("");
                            List<String> distinct = Arrays.stream(items).distinct().collect(Collectors.toList());
                            if (questionVO.getQuestionType().equals(QuestionType.SINGLE) && distinct.size() >= 2){
                                throw new BusinessException(String.format("单选题（%s）：学号为%s的同学选了多个选项，请重新检查excel", questionVO.getTitle(), studentNo));
                            }

                            List<QuestionOptionDTO> answers = new ArrayList<>();

                            for (String item : distinct) {
                                if (StringUtils.isEmpty(item)) {
                                    break;
                                }
                                String txt = itemMap.get(item);
                                if (txt == null) {
                                    throw new BusinessException(String.format("题目%s不存在%s选项", questionVO.getTitle(), item));
                                }
                                answers.add(new QuestionOptionDTO(item, txt));
                            }
                            questionIdAnswersMap.put(questionVO.getQuestionId(), answers);
                        }
                    }
                }

            }
        }

        // 操作数据库更新数据
        doAfterAllAnalysed(excelEntityList, taskTemplate);
    }

    // 操作数据库更新数据
    private void doAfterAllAnalysed(List<ResultImportEntity> excelEntityList, TaskTemplateDO taskTemplateDO) {

        List<TaskTemplateStudentTeacherQuestionDO> questionList = service.getByTaskTemplateId(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId());

        // 先清空数据
        taskTemplateStudentService.reset(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId());

        for (TaskTemplateStudentTeacherQuestionDO questionDO : questionList) {
            questionDO.setAnswer(new ArrayList<>());
            questionDO.setIsSubmit(false);
            questionDO.setIsFinish(false);
        }

        // 组织数据库中的数据
        // < studentId, < curriculum, < questionId, List<TaskTemplateStudentTeacherQuestionDO> > >
        Map<Long, Map<String, Map<Long, List<TaskTemplateStudentTeacherQuestionDO>>>> dbDataMap = questionList.stream().collect(
                Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getStudentId,
                        Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getCurriculum,
                                Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getQuestionId))));

        Set<Long> studentIds = new HashSet<>();

        // 填充数据
        for (ResultImportEntity resultImportEntity : excelEntityList) {  // 遍历学生和课程
            for (Map.Entry<Long, List<QuestionOptionDTO>> questionIdAnswerEntry : resultImportEntity.getAnswer().entrySet()) {  // 遍历所选的题目和答案
                Map<String, Map<Long, List<TaskTemplateStudentTeacherQuestionDO>>> currQuestionIdAnswerMap = dbDataMap.get(resultImportEntity.getStudentId());
                if (currQuestionIdAnswerMap == null){
                    throw new BusinessException("本调查任务中无学号为"+resultImportEntity.getStudentId()+"的学生");
                }
                Map<Long, List<TaskTemplateStudentTeacherQuestionDO>> questionIdAnswerMap = currQuestionIdAnswerMap.get(resultImportEntity.getCurriculum());
                if (questionIdAnswerMap == null){
                    if (logger.isDebugEnabled()){
                        logger.debug("本调查任务中学号为{}的学生不需要对{}老师进行调查", resultImportEntity.getStudentNo(), resultImportEntity.getCurriculum());
                    }
                    continue;
                }

                List<TaskTemplateStudentTeacherQuestionDO> tmpList = questionIdAnswerMap.get(questionIdAnswerEntry.getKey());
                if (EmptyUtils.isEmpty(tmpList)){
                    throw new BusinessException(String.format("学生id：%s 课程：%s questionId：%s 数据无法找到", resultImportEntity.getStudentId(), resultImportEntity.getCurriculum(), questionIdAnswerEntry.getKey()));
                }

                studentIds.add(resultImportEntity.getStudentId());

                // 最终数据库中的数据
                TaskTemplateStudentTeacherQuestionDO data = tmpList.get(0);
                data.setIsFinish(true);
                data.setIsSubmit(true);
                data.setAnswer(questionIdAnswerEntry.getValue());

            }
        }

        // 清空这些学生的完成状态
        taskStudentService.reset(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId());
        service.saveBatch(questionList);

        // 保存
        // 往taskTemplateStudent添加记录，代表这个学生完成了这个调查模板
        taskTemplateStudentService.resultSubmit(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId(), studentIds);

        Collection<Long> ids = taskTemplateStudentService.findFinishOtherTemplateStudents(taskTemplateDO.getTaskId(), taskTemplateDO.getTemplateId(), studentIds);
        if (!ids.isEmpty()){
            taskStudentService.submitTemplate(taskTemplateDO.getTaskId(), ids);
        }


    }

    // {A->问题1, B->问题2}，方便根据选项值构造QuestionOptionDTO
    private Map<String, String> getOptionTxtMap(QuestionVO questionVO) {
        Map<String, String> itemMap = new HashMap<>();
        for (QuestionOptionDTO item : questionVO.getItems()) {
            itemMap.put(item.getOption(), item.getTxt());
        }
        return itemMap;
    }

    // 从任课教师excel中获取元数据  {语文=2, 生物=2}
    private Map<String, Integer> getCurrRangeMap(XSSFSheet sheet) {

        int two = 2;
        XSSFRow questionRow = sheet.getRow(two);

        // 模板校验
        Cell stuNoCell = questionRow.getCell(0);
        Cell classNameCell = questionRow.getCell(1);
        String schoolNoStr = "学号";
        String classStr = "班级";
        if (stuNoCell == null || classNameCell == null || !schoolNoStr.equals(stuNoCell.toString()) || !classStr.equals(classNameCell.toString())){
            throw new BusinessException("任课教师结果导入模板不正确");
        }

        int index = two;
        while(questionRow.getCell(index) != null && !"".equals(questionRow.getCell(index).toString())){
            index++;
        }

        String preCurr = "";
        XSSFRow curRow = sheet.getRow(1);
        if (curRow.getCell(two) == null || "".equals(curRow.getCell(two).toString())){
            throw new BusinessException("任课教师模板第2行第3列不能为空");
        }
        Map<String, Integer> map = new LinkedHashMap<>();
        for(int j = two; j < index; j++){
            XSSFCell cell = curRow.getCell(j);
            if (!"".equals(cell.getStringCellValue())){
                preCurr = cell.getStringCellValue();
                map.put(preCurr, 1);
            }else {
                map.put(preCurr, map.get(preCurr)+1);
            }
        }
        return map;
    }

    // 判断是否是空行
    public boolean isRowEmpty(XSSFRow row) {

        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && !cell.getCellTypeEnum().equals(CellType.BLANK)) {
                return false;
            }
        }
        return true;
    }
}
