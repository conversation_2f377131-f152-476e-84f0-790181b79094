package com.xiaoshan.edu.timetable.excel;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.xiaoshan.edu.timetable.utils.ExcelUtils;

/**
 * <AUTHOR>
 */
public class ClassesMergeStrategy extends AbstractMergeStrategy {

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        int row = cell.getRowIndex() % 11;
        if (row == 0) {
            if (cell.getColumnIndex() == 0) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(relativeRowIndex, relativeRowIndex, 0, 7));
            }
        }
        ExcelUtils.setCell(sheet, cell);
    }

}
