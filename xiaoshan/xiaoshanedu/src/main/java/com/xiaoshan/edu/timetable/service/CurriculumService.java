package com.xiaoshan.edu.timetable.service;

import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.ao.timetable.CurriculumAO;
import com.xiaoshan.edu.ao.timetable.CurriculumPageAO;
import com.xiaoshan.edu.timetable.entity.CurriculumDO;
import com.xiaoshan.edu.vo.timetable.CurriculumVO;

import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

/**
 * <AUTHOR>
 */
public interface CurriculumService extends SqlBaseService<CurriculumDO, Long> {

    /**
     * 保存或更新
     *
     * @param ao
     */
    void saveForUpdate(CurriculumAO ao);

    /**
     * 别名映射 Map<语文,语>
     *
     * @param flag
     * @return
     */
    Map<String, String> aliasMap(Boolean flag);

    /**
     * 分页
     *
     * @param ao
     * @return
     */
    QueryResult<List<CurriculumVO>> page(CurriculumPageAO ao);

    /**
     * 同步数据字典课程
     */
    void sync();

}
