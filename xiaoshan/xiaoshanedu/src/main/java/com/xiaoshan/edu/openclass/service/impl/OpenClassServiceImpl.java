package com.xiaoshan.edu.openclass.service.impl;

import com.alibaba.excel.EasyExcel;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.common.utils.DateUtils;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import com.xiaoshan.basic.ao.NewWorkFlowAO;
import com.xiaoshan.basic.dto.ClassRoomTreeDTO;
import com.xiaoshan.basic.vo.FlowNodesListVO;
import com.xiaoshan.basic.vo.FlowVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.ao.openclass.*;
import com.xiaoshan.edu.api.facade.FlowFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.enums.openclass.OpenClassApprovalState;
import com.xiaoshan.edu.enums.openclass.OpenClassConfirmState;
import com.xiaoshan.edu.enums.openclass.OpenClassType;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import com.xiaoshan.edu.model.ClassFloorTreeDTO;
import com.xiaoshan.edu.openclass.dao.OpenClassDao;
import com.xiaoshan.edu.openclass.entity.GeneralCommentDO;
import com.xiaoshan.edu.openclass.entity.OpenClassDO;
import com.xiaoshan.edu.openclass.entity.OpenClassPersonDO;
import com.xiaoshan.edu.openclass.excel.CustomCellWriteHandler;
import com.xiaoshan.edu.openclass.excel.ExcelHeadsUtils;
import com.xiaoshan.edu.openclass.excel.PageUtil;
import com.xiaoshan.edu.openclass.excel.export.*;
import com.xiaoshan.edu.openclass.service.CloudRoomService;
import com.xiaoshan.edu.openclass.service.GeneralCommentService;
import com.xiaoshan.edu.openclass.service.OpenClassPersonService;
import com.xiaoshan.edu.openclass.service.OpenClassService;
import com.xiaoshan.edu.service.BasicPushMessage;
import com.xiaoshan.edu.vo.openclass.*;
import com.xiaoshan.oa.dto.TeacherDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.MapConvert;
import start.framework.service.impl.SqlBaseServiceImpl;
import start.magic.core.ApplicationException;
import start.magic.persistence.source.jdbc.mapper.SqlTemplate;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;
import start.magic.utils.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service("openClassService")
public class OpenClassServiceImpl extends SqlBaseServiceImpl<OpenClassDO, Long>
        implements OpenClassService {

    private OpenClassDao openClassDao;
    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private SettingFacade settingFacade;
    @Autowired
    private BasicPushMessage basicPushMessage;
    @Autowired
    private GeneralCommentService generalCommentService;
    @Autowired
    private OpenClassPersonService openClassPersonService;
    @Autowired
    private CloudRoomService cloudRoomService;
    @Autowired
    private FlowFacade flowFacade;
    @Value("${xiaoshan.push_item.message.edu_open_class_urging}")
    private String eduOpenClassUrging;
    @Value("${xiaoshan.open_class.manage.source_id}")
    private Integer manageSourceId;
    @Value("${xiaoshan.open_class.approval.path}")
    private String approvalPath;

    public OpenClassServiceImpl(@Qualifier("openClassDao") OpenClassDao openClassDao) {
        super(openClassDao);
        this.openClassDao = openClassDao;
    }

    // 发布公开课
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish(OpenClassPublishAO ao) {
        // 地点校验
        QueryWrapper placeWrapper = new QueryWrapper();
        placeWrapper.eq("date", ao.getDate())
                .eq("start_time", ao.getStartTime())
                .eq("place", ao.getPlace())
                .ne("approval_state", OpenClassApprovalState.REVOKED.name())
                .ne("approval_state", OpenClassApprovalState.REJECT.name())
                .eq("deleted", 0);
        if (!openClassDao.queryForList(placeWrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_500007);
        }
        // 教师冲突校验
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("teacher_id", ao.getTeacherId())
                .eq("date", ao.getDate())
                .eq("start_time", ao.getStartTime())
                .ne("approval_state", OpenClassApprovalState.REVOKED.name())
                .ne("approval_state", OpenClassApprovalState.REJECT.name())
                .eq("deleted", 0);
        if (!openClassDao.queryForList(queryWrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_500008);
        }
        // 公开课班级时间冲突校验
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("attend_class", ao.getAttendClass())
                .eq("date", ao.getDate())
                .eq("start_time", ao.getStartTime())
                .ne("approval_state", OpenClassApprovalState.REVOKED.name())
                .ne("approval_state", OpenClassApprovalState.REJECT.name())
                .eq("deleted", 0);
        if (!openClassDao.queryForList(wrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_500006);
        }
        List<TeacherDto> teachers = checkOpenClass(ao);
        OpenClassDO o = ClassConverter.aTob(ao, new OpenClassDO());
        o.setTeacherId(ao.getTeacherId());
        o.setTeacherUserId(teachers.get(0).getUserId());
        o.setTeacherDepartment(teachers.get(0).getDepartmentName());
        save(o);
        if (ao.getType().equals(OpenClassType.SCHOOL_OPEN_CLASS) || ao.getStartFlowAo() == null) {
            Date date = new Date();
            o.setConfirmState(OpenClassConfirmState.CONFIRMING);
            o.setPublishTime(date);
            o.setApprovalPassDate(date);
            o.setIsNew(true);
            o.setApprovalState(OpenClassApprovalState.PASS);
            o.setScore("暂无评价");
            basicPushMessage.openClassPublish(o);
            save(o);
        }
        GeneralCommentDO g = new GeneralCommentDO();
        g.setOpenClassId(o.getId());
        generalCommentService.save(g);
        if (!ao.getType().equals(OpenClassType.SCHOOL_OPEN_CLASS) && ao.getStartFlowAo() != null) {
            startFlow(ao.getStartFlowAo(), o);
            FlowVO flowVO = flowFacade.getFlowInformation(o.getId(), 7);
            if (flowVO != null && flowVO.getSerialNumber() != null && !"".equals(flowVO.getSerialNumber())) {
                UpdateWrapper updateWrapper = new UpdateWrapper().set("approval_code", flowVO.getSerialNumber())
                        .eq("id", o.getId());
                openClassDao.executeUpdate(updateWrapper);
            }
        }
    }

    // 编辑后保存
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(OpenClassPublishAO ao) {
        // 地点校验
        QueryWrapper placeWrapper = new QueryWrapper();
        placeWrapper.eq("date", ao.getDate())
                .eq("start_time", ao.getStartTime())
                .eq("place", ao.getPlace())
                .ne("approval_state", OpenClassApprovalState.REVOKED.name())
                .ne("approval_state", OpenClassApprovalState.REJECT.name())
                .ne("id", ao.getOpenClassId())
                .eq("deleted", 0);
        if (!openClassDao.queryForList(placeWrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_500007);
        }
        // 教师冲突校验
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("teacher_id", ao.getTeacherId())
                .eq("date", ao.getDate())
                .eq("start_time", ao.getStartTime())
                .ne("approval_state", OpenClassApprovalState.REVOKED.name())
                .ne("approval_state", OpenClassApprovalState.REJECT.name())
                .ne("id", ao.getOpenClassId())
                .eq("deleted", 0);
        if (!openClassDao.queryForList(queryWrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_500008);
        }
        // 公开课班级时间冲突校验
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("attend_class", ao.getAttendClass())
                .eq("date", ao.getDate())
                .eq("start_time", ao.getStartTime())
                .ne("approval_state", OpenClassApprovalState.REVOKED.name())
                .ne("approval_state", OpenClassApprovalState.REJECT.name())
                .ne("id", ao.getOpenClassId())
                .eq("deleted", 0);
        if (!openClassDao.queryForList(wrapper).isEmpty()) {
            throw new BusinessException(CodeRes.CODE_500006);
        }
        checkOpenClass(ao);
        ao.setTeacherId(null);
        ao.setTeacherName(null);
        ao.setIsNew(true);
        saveForUpdate(ao.getOpenClassId(), ao);
        OpenClassDO load = load(ao.getOpenClassId());
        // 发起人
        basicPushMessage.openClassUpdate(load, Collections.singletonList(load.getTeacherUserId()), false);
        // 听课教师
        List<TeacherInfoVO> teacherInfoVos = openClassPersonService.getListenList(ao.getOpenClassId());
        List<Long> collect = teacherInfoVos.stream().map(TeacherInfoVO::getTeacherUserId).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            basicPushMessage.openClassUpdate(load, collect, true);
        }
    }

    // 公开课统计--教师、科目--详情
    @Override
    public QueryResult<List<OpenClassVO>> statisticsTeachersDetails(StaticTeachersDetailAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        return queryForPage(OpenClassVO.class, "statisticsTeachersDetails", map);
    }

    // 根据id获取公开课详情
    @Override
    public OpenClassVO getById(Long openClassId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", openClassId).eq("deleted", 0);
        OpenClassDO load = get(queryForMap(OpenClassDO.class, wrapper));
        if (load == null) {
            return null;
        }
        OpenClassPersonDO personDO = openClassPersonService.getByOpenClassIdAndUserId(openClassId, getJwtUser().getUserId());
        OpenClassVO vo = ClassConverter.aTob(load, new OpenClassVO());
        LocalDate nowDate = LocalDate.now();
        LocalTime nowTime = LocalTime.now();
        LocalDate startDate = LocalDate.parse(load.getDate().toString());
        LocalTime startTime = LocalTime.parse(load.getStartTime().toString());
        vo.setDate(String.format("%s 第%s周 %s", load.getDate(), load.getWeekNum(), load.getWeekName()));
        vo.setListen(personDO != null);
        vo.setEvaluated(personDO != null && personDO.getEvaluated());
        vo.setCourseState(startDate.isBefore(nowDate) || (startDate.equals(nowDate) && startTime.isBefore(nowTime)));
        vo.setOpenClassId(openClassId);
        return vo;
    }


    // 听课教师列表
    @Override
    public List<TeacherInfoVO> listenList(Long openClassId) {
        return openClassPersonService.getListenList(openClassId);
    }

    // 公开课开课证明
    @Override
    public void pdf(Long openClassId, HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("公开课证明", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".pdf");
            response.setContentType("application/pdf");
            OpenClassDO load = load(openClassId);

            // 页面大小
            Rectangle pageSize = new Rectangle(773.955f, 557.928f);
            Document document = new Document(pageSize, 119.07f, 119.07f, 150f, 0);
            PdfWriter.getInstance(document, response.getOutputStream());
            document.open();

            BaseFont baseFont = BaseFont.createFont("resource/SIMLI.TTF", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, BaseFont.NOT_CACHED);
            BaseFont titleAndNameFont = BaseFont.createFont("resource/simkai.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, BaseFont.NOT_CACHED);

            Font blodFont = new Font(titleAndNameFont);
            blodFont.setSize(32);
            blodFont.setStyle(Font.BOLD);

            Font nomalFont = new Font(baseFont);
            nomalFont.setSize(26);
            nomalFont.setStyle(Font.NORMAL);

            Font nomalMidFont = new Font(baseFont);
            nomalMidFont.setSize(28);
            nomalMidFont.setStyle(Font.NORMAL);

            Font blodMidFont = new Font(titleAndNameFont);
            blodMidFont.setSize(28);
            blodMidFont.setStyle(Font.BOLD);

            // 第一段内容
            Paragraph one = new Paragraph();
            Chunk teacherName = new Chunk(load.getTeacherName(), blodFont);

            String head = String.format("老师在萧山中学组织的%s活动中开设题为《", load.getType().getDescription());
            Chunk headChunk = new Chunk(head, nomalFont);
            Chunk courseName = new Chunk(load.getCourseName(), blodMidFont);
            Chunk tailChunk = new Chunk("》的公开课", nomalFont);

            one.add(teacherName);
            one.add(headChunk);
            one.add(courseName);
            one.add(tailChunk);

            one.setLeading(50f);
            one.setFirstLineIndent(26);
            document.add(one);

            // 特此证明
            Paragraph two = new Paragraph("特此证明！", nomalFont);
            two.setLeading(70);
            two.setFirstLineIndent(52);
            document.add(two);

            // 特此证明
            Paragraph three = new Paragraph("浙江省萧山中学", nomalMidFont);
            three.setLeading(70);
            three.setFirstLineIndent(280);
            document.add(three);

            // 时间
            String[] dateSplit = load.getDate().toString().split("-");
            Paragraph four = new Paragraph(String.format("%s年%s月%s日", dateSplit[0], dateSplit[1], dateSplit[2]), nomalMidFont);
            four.setLeading(40);
            four.setFirstLineIndent(280);
            document.add(four);

            document.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("pdf导出失败");
        }
    }

    // 模拟获取节点
    @Override
    public FlowNodesListVO simulation(OpenClassSimulationAO ao) {
        JwtUser user = getJwtUser();
        List<TeacherDto> list = foundationFacade.getTeachersByIds(String.valueOf(user.getId()));
        TeacherDto teacherDto = list.get(0);
        String deptIds = teacherDto.getDepartmentIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        String roleIds = "2,167,3,4";
        if (user.getRoleIds() != null) {
            roleIds = user.getRoleIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        }
        return flowFacade.getFlowNodes(deptIds, roleIds, String.valueOf(user.getId()), ao.getProcessDefinitionKey(), ao.getConditions());
    }

    // 催办
    @Override
    public void urging(UrgeAO ao) {
        OpenClassDO load = load(ao.getOpenClassId());
        JsonObject json = new JsonObject();
        json.put("relatedId", ao.getOpenClassId());
        json.put("relatedType", "7");
        String query = String.format("id=%s&from=msg", ao.getOpenClassId());
        String content = String.format("老师您好，%s的%s申请正等待您的审批，请及时处理",
                load.getTeacherName(), load.getType().getDescription());
        String officialAccountData = basicPushMessage.getTemplateForOpenClass(content, load.getTeacherName(), "公开课", load.getId(), eduOpenClassUrging);
        try {
            flowFacade.urgeFlow(eduOpenClassUrging,
                    "公开课",
                    ao.getFlowId(),
                    json.toString(),
                    approvalPath + "?" + query,
                    officialAccountData,
                    approvalPath,
                    query,
                    manageSourceId,
                    null);
        } catch (ApplicationException e) {
            e.printStackTrace();
            throw new BusinessException("已提醒对方审批，请耐心等候");
        }
    }

    // 撤销
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancel(CancelAO ao) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("id", ao.getOpenClassId()).set("approval_state", OpenClassApprovalState.REVOKED.name());
        openClassDao.executeUpdate(wrapper);
        flowFacade.cancelLeaving(ao.getProcessInstanceId(), "");
    }

    // 科目列表
    @Override
    public List<String> subjectList() {
        List<OpenClassDO> openClassDoList = openClassDao.queryForList(new QueryWrapper().select("distinct subject").eq("deleted", 0));
        return openClassDoList.stream().map(OpenClassDO::getSubject).collect(Collectors.toList());
    }

    /**
     * 根据房间类型获取教室树形结构
     */
    @Override
    public List<ClassBuildingTreeDTO> getClassRoom(List<Integer> types) {
        if (types.size() == 1 && types.get(0) == -1) {
            // 云课堂教室
            return cloudRoomService.getCloudRoomTree();
        }

        if (types.size() > 1 && types.contains(-1)) {
            throw new BusinessException("云课堂教室和其他教室不能同时获取");
        }

        Map<Long, ClassBuildingTreeDTO> buildingMap = new HashMap<>();  // 建筑id -> 建筑
        for (Integer type : types) {
            // 绿城不愿意提供对应接口，只能循环请求绿城的接口
            List<ClassBuildingTreeDTO> buildingTreeDtos = settingFacade.getRoomByRoomType(type);

            for (ClassBuildingTreeDTO buildNow : buildingTreeDtos) {
                ClassBuildingTreeDTO buildingPrev = buildingMap.get(buildNow.getBuildingId());
                if (buildingPrev != null) {
                    // 如果之前数据中，建筑已经存在，需要进行合并，假设建筑1存在
                    // 先把之前建筑1中的楼层存入map中
                    HashMap<Long, ClassFloorTreeDTO> floorMap = new HashMap<>(); // 楼层id -> 楼层
                    for (ClassFloorTreeDTO floorPrev : buildingPrev.getClassFloorTreeDTOList()) {
                        floorMap.put(floorPrev.getFloorId(), floorPrev);
                    }
                    // 再把现在建筑1中的楼层一个一个拿出来，从前面建筑1楼层的map里找
                    for (ClassFloorTreeDTO floorNow : buildNow.getClassFloorTreeDTOList()) {
                        ClassFloorTreeDTO floorPrev = floorMap.get(floorNow.getFloorId());
                        if (floorPrev == null) {
                            // 如果之前的楼层里不存在现在的楼层，直接加入
                            buildingPrev.getClassFloorTreeDTOList().add(floorNow);
                        } else {
                            // 如果存在的话，需要合并房间，把之前房间放入map
                            HashMap<Long, ClassRoomTreeDTO> roomMap = new HashMap<>();
                            for (ClassRoomTreeDTO roomPrev : floorPrev.getClassRoomTreeDTOList()) {
                                roomMap.put(roomPrev.getRoomId(), roomPrev);
                            }
                            // 从map中寻找是否存在现在的房间
                            for (ClassRoomTreeDTO roomTreeNow : floorNow.getClassRoomTreeDTOList()) {
                                ClassRoomTreeDTO classRoomTreeDTO = roomMap.get(roomTreeNow.getRoomId());
                                if (classRoomTreeDTO == null) {
                                    // 如果不存在，加入到之前的数据，如果存在就不管
                                    floorPrev.getClassRoomTreeDTOList().add(roomTreeNow);
                                }
                            }
                        }
                    }

                } else {
                    buildingMap.put(buildNow.getBuildingId(), buildNow);
                }
            }
        }
        return new ArrayList<>(buildingMap.values());
    }

    /**
     * 小程序--公开课记录
     */
    @Override
    public QueryResult<List<OpenClassVO>> wxPage(OpeWxPageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("nowDate", LocalDate.now());
        map.put("nowTime", LocalTime.now());
        map.put("isPublish",ao.getIsPublish());
        map.put("isFinish",ao.getIsFinish());
        QueryResult<List<OpenClassVO>> page = queryForPage(OpenClassVO.class,"wxRecord", map);
        if (page == null || page.getResult() == null || page.getResult().isEmpty()) {
            return null;
        }
        List<OpenClassVO> result = page.getResult();
        for (OpenClassVO openClassVO : result) {
            int index = openClassVO.getAttendClass().indexOf("届");
            if (index != -1 && index + 1 < openClassVO.getAttendClass().length()) {
                openClassVO.setAttendClass(openClassVO.getAttendClass().substring(index + 1));
            }
        }
        return page;
    }

    /**
     * 电子档案--公开课列表
     */
    @Override
    public QueryResult<List<OpenClassVO>> archives(OpeArchivesAO ao) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select(
                "id AS openClassId",
                "course_name AS courseName",
                "subject",
                "type",
                "concat(date, ' ', '第', week_num, '周', ' ', week_name) AS date",
                "attend_class AS attendClass",
                "score"
        );
        wrapper.eq("deleted", 0).eq("confirm_state", String.valueOf(OpenClassConfirmState.CONFIRMED));
        wrapper.ge(ao.getStartDate() != null, "date", Optional.ofNullable(ao.getStartDate()).orElse(new java.sql.Date(1)).toString());
        wrapper.le(ao.getEndDate() != null, "date", Optional.ofNullable(ao.getEndDate()).orElse(new java.sql.Date(1)).toString());
        wrapper.eq(ao.getTeacherId() != null, "teacher_id", ao.getTeacherId());
        wrapper.orderByAsc("date");
        wrapper.last(String.format("limit %s,%s", ao.index(), ao.getPageSize()));
        return queryForPage(OpenClassVO.class, wrapper);
    }

    @Override
    public QueryResult<List<OpenClassVO>> archivesAll(OpeArchivesAO ao) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select(
                "id AS openClassId",
                "course_name AS courseName",
                "subject",
                "type",
                "concat(date, ' ', '第', week_num, '周', ' ', week_name) AS date",
                "attend_class AS attendClass",
                "score",
                "confirm_state AS confirmState",
                "'false' AS listen"
        );
        wrapper.eq("deleted", 0).eq("confirm_state", String.valueOf(OpenClassConfirmState.CONFIRMED));
        wrapper.ge(ao.getStartDate() != null, "date", Optional.ofNullable(ao.getStartDate()).orElse(new java.sql.Date(1)).toString());
        wrapper.le(ao.getEndDate() != null, "date", Optional.ofNullable(ao.getEndDate()).orElse(new java.sql.Date(1)).toString());
        wrapper.eq(ao.getTeacherId() != null, "teacher_id", ao.getTeacherId());
        List<OpenClassVO> openClassVos = queryForMap(OpenClassVO.class, wrapper);

        Map<String, Object> map = new HashMap<>();
        map.put("teacherId", ao.getTeacherId());
        if (ao.getStartDate() != null) {
            map.put("startDate", ao.getStartDate());
        }
        if (ao.getEndDate() != null) {
            map.put("endDate", ao.getEndDate());
        }
        List<OpenClassVO> listenList = queryForMapMapper(OpenClassVO.class, "archivesAll", map);

        openClassVos.addAll(listenList);
        openClassVos.sort(Comparator.comparing(OpenClassVO::getDate));

        return PageUtil.getQueryResult(openClassVos, ao.getPageIndex(), ao.getPageSize());

    }

    /**
     * 查询老师的上课记录-不分页
     *
     * @param ao
     * @return
     */
    @Override
    public List<OpenClassVO> classRecord(ClassRecordAO ao) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select(
                "id AS openClassId",
                "course_name AS courseName",
                "subject",
                "type",
                "concat(date, ' ', '第', week_num, '周', ' ', week_name) AS date",
                "attend_class AS attendClass",
                "score",
                "confirm_state AS confirmState",
                "'false' AS listen"
        );
        wrapper.eq("deleted", 0).eq("confirm_state", String.valueOf(OpenClassConfirmState.CONFIRMED));
        wrapper.ge(ao.getStartDate() != null, "date", Optional.ofNullable(ao.getStartDate()).orElse(new java.sql.Date(1)).toString());
        wrapper.le(ao.getEndDate() != null, "date", Optional.ofNullable(ao.getEndDate()).orElse(new java.sql.Date(1)).toString());
        wrapper.eq(ao.getTeacherId() != null, "teacher_id", ao.getTeacherId());
        List<OpenClassVO> openClassVos = queryForMap(OpenClassVO.class, wrapper);
        openClassVos.sort(Comparator.comparing(OpenClassVO::getDate));

        return openClassVos;
    }


    public void startFlow(OpenClassStartFlowAO ao, OpenClassDO openClassDO) {
        JwtUser user = getJwtUser();
        List<TeacherDto> list = foundationFacade.getTeachersByIds(String.valueOf(user.getId()));
        TeacherDto teacherDto = list.get(0);
        String deptIds = teacherDto.getDepartmentIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        String roleIds = StringUtils.listToString(user.getRoleIds(), ",");
        NewWorkFlowAO flowAo = new NewWorkFlowAO();
        flowAo.setApplicantId(user.getId());
        flowAo.setApplicantName(user.getName());
        flowAo.setApplicantType(1);
        flowAo.setApprover(ao.getApprover());
        flowAo.setApplicationDepts(deptIds);
        flowAo.setApplicationRoles(roleIds);
        flowAo.setAssigneeIds(String.valueOf(user.getId()));
        flowAo.setBusinessKey(String.valueOf(openClassDO.getId()));
        flowAo.setConditions(ao.getConditions());
        flowAo.setNodeInfo(ao.getNodeInfo());
        flowAo.setProcessDefinitionKey(ao.getProcessDefinitionKey());
        flowAo.setRelatedType(7);
        flowAo.setToDoContent(openClassDO.getTeacherName() + "的公开课申请");
        flowAo.setRelatedId(String.valueOf(openClassDO.getId()));
        JsonArray j = new JsonArray();
        j.put("课题名称：" + openClassDO.getCourseName());
        j.put("所属科目：" + openClassDO.getSubject());
        j.put("公开课类型：" + openClassDO.getType().getDescription());
        flowAo.setExtra(j.toString());
        JsonObject jsonObject = new JsonObject();
        jsonObject.put("first", "老师您好，" + openClassDO.getTeacherName() + "的公开课申请正等待您的审批，请及时处理！");
        jsonObject.put("taskName", openClassDO.getCourseName());
        jsonObject.put("date", DateUtils.formatDateTime(new Date()));
        jsonObject.put("remark", "请尽快处理");
        flowAo.setOfficeAccountData(jsonObject.toString());
        flowFacade.beginTheWorkFlow(flowAo);
    }

    // 公开课记录
    @Override
    public QueryResult<List<OpenClassVO>> page(OpenClassPageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("nowDate", LocalDate.now());
        map.put("nowTime", LocalTime.now());
        map.put("courseState", ao.getCourseState());
        QueryResult<List<OpenClassVO>> page = queryForPage(OpenClassVO.class, "openClassRecords", map);
        if (page == null || page.getResult() == null || page.getResult().isEmpty()) {
            return null;
        }
        List<OpenClassVO> result = page.getResult();
        for (OpenClassVO openClassVO : result) {
            int index = openClassVO.getAttendClass().indexOf("届");
            if (index != -1 && index + 1 < openClassVO.getAttendClass().length()) {
                openClassVO.setAttendClass(openClassVO.getAttendClass().substring(index + 1));
            }
        }
        return page;

    }

    // 个人端--公开课广场、听课记录
    @Override
    public QueryResult<List<OpenClassVO>> square(OpenClassSquarePageAO ao) {
        JwtUser user = getJwtUser();
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("teacherUserId", user.getUserId());
        map.put("nowDate", LocalDate.now());
        map.put("nowTime", LocalTime.now());
        // boolean类型需要单独添加
        map.put("courseState", ao.getCourseState());
        map.put("listenRecord", ao.getListenRecord());
        if (ao.getTypeMult() != null) {
            List<String> collect = ao.getTypeMult().stream().map(Enum::name).collect(Collectors.toList());
            map.put("typeMult", collect);
        } else {
            map.put("typeMult", null);
        }

        QueryResult<List<OpenClassVO>> page = queryForPage(OpenClassVO.class, "square", map);
        if (page == null || page.getResult() == null || page.getResult().isEmpty()) {
            return null;
        }
        List<OpenClassVO> result = page.getResult();
        for (OpenClassVO openClassVO : result) {
            int index = openClassVO.getAttendClass().indexOf("届");
            if (index != -1 && index + 1 < openClassVO.getAttendClass().length()) {
                openClassVO.setAttendClass(openClassVO.getAttendClass().substring(index + 1));
            }
        }
        return page;
    }


    // 上课记录
    @Override
    public QueryResult<List<OpenClassVO>> launchRecord(OpenClassPageAO ao) {
        JwtUser user = getJwtUser();
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() < 1) {
            map.put("pageSize", 8);
        }
        map.put("pageIndex", ao.index());
        map.put("teacherUserId", user.getUserId());
        map.put("courseState", ao.getCourseState());
        if (ao.getTypeMult() != null) {
            List<String> collect = ao.getTypeMult().stream().map(Enum::name).collect(Collectors.toList());
            map.put("typeMult", collect);
        } else {
            map.put("typeMult", null);
        }
        QueryResult<List<OpenClassVO>> page = queryForPage(OpenClassVO.class, "launchRecord", map);
        if (page == null || page.getResult() == null || page.getResult().isEmpty()) {
            return null;
        }
        List<OpenClassVO> result = page.getResult();
        for (OpenClassVO openClassVO : result) {
            int index = openClassVO.getAttendClass().indexOf("届");
            if (index != -1 && index + 1 < openClassVO.getAttendClass().length()) {
                openClassVO.setAttendClass(openClassVO.getAttendClass().substring(index + 1));
            }
        }
        return page;
    }

    // 根据id删除公开课记录
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeOpenClass(Long id) {
        OpenClassDO load = load(id);
        // 撤销流程
        if (load.getApprovalState().equals(OpenClassApprovalState.UNDER_APPROVAL)) {
            FlowVO flowVO = flowFacade.getFlowInformation(id, 7);
            CancelAO ao = new CancelAO();
            ao.setOpenClassId(id);
            ao.setProcessInstanceId(flowVO.getEngineFlowId());
            cancel(ao);
        }
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.set("deleted", 1).eq("id", id);
        openClassDao.executeUpdate(updateWrapper);
    }

    // 根据id确认开课
    @Override
    public void confirm(Long id) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("confirm_state", OpenClassConfirmState.CONFIRMED.name())
                .set("confirm_date", new Date())
                .set("is_new", true)
                .eq("id", id);
        openClassDao.executeUpdate(wrapper);
    }

    // 根据id发布公开课
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publishById(Long id) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("confirm_state", OpenClassConfirmState.CONFIRMING.name())
                .set("publish_date", new Date())
                .set("is_new", true)
                .set("score", "暂无评价")
                .eq("id", id);
        openClassDao.executeUpdate(wrapper);
        basicPushMessage.openClassPublish(load(id));
    }

    // 公开课数量统计
    @Override
    public OpenClassStatisticsVO statistics(OpenClassTimeAO ao) {
        OpenClassStatisticsVO vo = new OpenClassStatisticsVO();
        Map<String, Object> params = MapConvert.convert(ao);
        List<Map<String, Object>> statistics = openClassDao.queryForMapMapper("statistics", params);
        if (statistics.isEmpty()) {
            return vo;
        }
        for (Map<String, Object> statistic : statistics) {
            String type = (String) statistic.get("type");
            Long count = (Long) statistic.get("count");
            if (type.equals(OpenClassType.SCHOOL_OPEN_CLASS.name())) {
                vo.setSchoolOpenClassNum(count);
                continue;
            }
            if (type.equals(OpenClassType.INNER_OPEN_CLASS.name())) {
                vo.setInnerOpenClassNum(count);
                continue;
            }
            if (type.equals(OpenClassType.INNER_LECTURE.name())) {
                vo.setInnerLectureNum(count);
            }
            if (type.equals(OpenClassType.CLOUD_CLASS.name())) {
                vo.setCloudClassNum(count);
            }
        }
        return vo;
    }

    // 公开课统计--教师
    @Override
    public QueryResult<List<TeacherStatisticsVO>> statisticsTeachers(OpenClassTeacherNameAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        String mapperSql = openClassDao.mapperSql("statisticsTeachers", map);
        List<Map<String, Object>> results = openClassDao.queryForMap(
                SqlTemplate.getExecuteSql(mapperSql),
                SqlTemplate.getExecuteParameter(mapperSql, map));
        if (results.isEmpty()) {
            return null;
        }
        int count = openClassDao.queryForIntMapper("statisticsTeachersCount", map);
        return queryResult(TeacherStatisticsVO.class, results, count);
    }

    // 公开课统计--科目
    @Override
    public QueryResult<List<SubjectStatisticsVO>> statisticsSubjects(OpenClassTimePageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        String mapperSql = openClassDao.mapperSql("statisticsSubjects", map);
        List<Map<String, Object>> results = openClassDao.queryForMap(
                SqlTemplate.getExecuteSql(mapperSql),
                SqlTemplate.getExecuteParameter(mapperSql, map));
        if (results.isEmpty()) {
            return null;
        }
        int count = openClassDao.queryForIntMapper("statisticsSubjectsCount", map);
        return queryResult(SubjectStatisticsVO.class, results, count);
    }

    // 我要听课
    @Override
    public void wantListen(Long openClassId) {
        JwtUser user = getJwtUser();
        Boolean exist = openClassPersonService.exist(openClassId, user.getUserId());
        if (exist) {
            throw new BusinessException(CodeRes.CODE_500005);
        }
        List<TeacherDto> list = foundationFacade.getTeachersByIds(String.valueOf(user.getId()));
        OpenClassPersonDO op = new OpenClassPersonDO();
        op.setOpenClassId(openClassId);
        op.setTeacherId(user.getId());
        op.setTeacherName(user.getName());
        op.setTeacherUserId(user.getUserId());
        op.setTeacherDepartment(list.get(0).getDepartmentName());
        openClassPersonService.save(op);
    }

    // 取消听课
    @Override
    public void cancelListen(Long openClassId) {
        JwtUser user = getJwtUser();
        openClassPersonService.removeByUserIdAndOpenClassId(user.getUserId(), openClassId);
    }

    // 个人端--我的评价
    @Override
    public PersonalCommentVO getComment(Long openClassId) {
        JwtUser user = getJwtUser();
        HashMap<String, Object> map = new HashMap<>();
        map.put("teacher_user_id", user.getUserId());
        map.put("open_class_id", openClassId);
        return get(openClassPersonService.queryForMap(PersonalCommentVO.class, map));
    }

    // 个人端--提交评价
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitComment(OpenClassCommentAO ao) {
        JwtUser user = getJwtUser();
        OpenClassPersonDO p = openClassPersonService.getByOpenClassIdAndUserId(ao.getOpenClassId(), user.getUserId());
        if (p == null) {
            throw new BusinessException("异常：您没有听过该课");
        }
        ClassConverter.aTob(ao, p);
        p.setEvaluated(true);
        p.setTotalScore(ao.getExpression() + ao.getBlackboardContent() + ao.getInformationize() + ao.getInteraction() + ao.getTimeAllocation());
        p.setModifiedDate(null);  // 防止自动更新时间失效
        openClassPersonService.save(p);
        Double score = generalCommentService.updateScore(ao.getOpenClassId());
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.set("score", score).eq("id", ao.getOpenClassId());
        openClassDao.executeUpdate(updateWrapper);
    }

    // 个人端--取消new标签
    @Override
    public void cancelNew(Long openClassId) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("id", openClassId).set("is_new", "false");
        openClassDao.executeUpdate(wrapper);
    }

    // 评价列表
    @Override
    public List<PersonalCommentVO> commentList(Long openClassId) {
        List<OpenClassPersonDO> personList = openClassPersonService.getByOpenClassId(openClassId);
        Map<Long, TeacherDto> avatarMap = foundationFacade.getAvatarMap();
        return personList.stream().map(item -> {
            PersonalCommentVO p = ClassConverter.aTob(item, new PersonalCommentVO());
            p.setTotalScore(Float.valueOf(item.getTotalScore()));
            TeacherDto teacherDto = avatarMap.get(item.getTeacherId());
            if (teacherDto != null) {
                p.setFaceUrl(teacherDto.getUrl() == null ? "" : teacherDto.getUrl());
            }
            return p;
        }).collect(Collectors.toList());
    }

    // 公开课总评价
    @Override
    public GeneralCommentVO generalComment(Long openClassId) {
        GeneralCommentVO vo = new GeneralCommentVO();
        OpenClassDO load = load(openClassId);
        GeneralCommentDO gcd = generalCommentService.getByOpenClassId(openClassId);
        ClassConverter.aTob(load, vo);
        ClassConverter.aTob(gcd, vo);
        vo.setSubject(load.getAbbreviation());
        vo.setDate(String.format("%s 第%s周 %s", load.getDate(), load.getWeekNum(), load.getWeekName()));
        return vo;
    }

    // 待确认、待发布数量
    @Override
    public ConfirmAndPublishNumVO redNum(OpenClassTimeAO ao) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.select("count(1)").eq("deleted", 0).eq("confirm_state", OpenClassConfirmState.CONFIRMING.name())
                .and(w1 -> w1.lt("date", LocalDate.now()).or(w2 -> w2.eq("date", LocalDate.now()).le("start_time", LocalTime.now()))).between("date", ao.getStartDate(), ao.getEndDate());
        Integer waitConfirmNum = openClassDao.queryForInt(wrapper);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("count(1)").eq("deleted", 0).eq("confirm_state", OpenClassConfirmState.TO_BE_RELEASED.name())
                .eq("approval_state", OpenClassApprovalState.PASS.name()).between("date", ao.getStartDate(), ao.getEndDate());
        Integer toBeReleaseNum = openClassDao.queryForInt(queryWrapper);
        ConfirmAndPublishNumVO vo = new ConfirmAndPublishNumVO();
        vo.setConfirmingNum(waitConfirmNum);
        vo.setToBePublishNum(toBeReleaseNum);
        return vo;
    }

    // 数据导出
    @Override
    public void export(OpenClassPageAO ao, HttpServletResponse response) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        QueryResult<List<OpenClassVO>> page = page(ao);
        if (page == null) {
            page = new QueryResult<>();
            page.setResult(new ArrayList<>());
        }
        List<OpenClassVO> result = page.getResult();
        try {
            if (ao.getConfirmState().equals(OpenClassConfirmState.TO_BE_RELEASED)) {
                List<ToBeReleaseExport> list = result.stream().map(item -> {
                    ToBeReleaseExport export = ClassConverter.aTob(item, new ToBeReleaseExport());
                    export.setApprovalState(item.getApprovalState().getDescription());
                    export.setType(item.getType().getDescription());
                    return export;
                }).collect(Collectors.toList());
                String fileName = URLEncoder.encode("公开课待发布数据", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ToBeReleaseExport.class).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
            } else if (ao.getConfirmState().equals(OpenClassConfirmState.CONFIRMING)) {
                List<ConfirmingExport> list = result.stream().map(item -> {
                    ConfirmingExport export = ClassConverter.aTob(item, new ConfirmingExport());
                    export.setCourseState(item.getCourseState() ? "已结束" : "待开课");
                    export.setType(item.getType().getDescription());
                    return export;
                }).collect(Collectors.toList());
                String fileName = URLEncoder.encode("公开课待确认数据", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ConfirmingExport.class).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
            } else if (ao.getConfirmState().equals(OpenClassConfirmState.CONFIRMED)) {
                List<ConfirmedExport> list = result.stream().map(item -> {
                    ConfirmedExport export = ClassConverter.aTob(item, new ConfirmedExport());
                    export.setType(item.getType().getDescription());
                    return export;
                }).collect(Collectors.toList());
                String fileName = URLEncoder.encode("公开课已确认数据", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ConfirmedExport.class).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
            }
        } catch (Exception e) {
            log.error("导出错误", e);
            throw new BusinessException(CodeRes.CODE_400010);
        }
    }

    // 公开课统计--教师--数据导出
    @Override
    public void exportStatisticsTeachers(StatisticsTeacherAO ao, HttpServletResponse response) {
        QueryResult<List<TeacherStatisticsVO>> listQueryResult = statisticsTeachers(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<TeacherStatisticsVO> result = listQueryResult.getResult();
        AtomicInteger index = new AtomicInteger(0);
        List<StatisticsTeacherExport> list = result.stream().map(item -> {
            StatisticsTeacherExport export = ClassConverter.aTob(item, new StatisticsTeacherExport());
            export.setIndex(index.incrementAndGet());
            return export;
        }).collect(Collectors.toList());
        String title;
        if (ao.getIsCurrentSemester()) {
            title = String.format("%s-%s学年第%s学期教师公开课统计", ao.getStartYear(), ao.getEndYear(), ao.getOrderNo() == 1 ? "一" : "二");
        } else {
            title = String.format("%s-%s教师公开课统计", DateUtils.formatDate(ao.getStartDate()), DateUtils.formatDate(ao.getEndDate()));
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), StatisticsTeacherExport.class).head(ExcelHeadsUtils.statisticTeacherHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (IOException e) {

            throw new BusinessException(CodeRes.CODE_400010);
        }
    }

    // 公开课统计--科目--数据导出
    @Override
    public void exportStatisticsSubject(OpenClassSubjectAO ao, HttpServletResponse response) {
        QueryResult<List<SubjectStatisticsVO>> listQueryResult = statisticsSubjects(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<SubjectStatisticsVO> result = listQueryResult.getResult();
        AtomicInteger index = new AtomicInteger(0);
        List<StatisticsSubjectExport> list = result.stream().map(item -> {
            StatisticsSubjectExport export = ClassConverter.aTob(item, new StatisticsSubjectExport());
            export.setIndex(index.incrementAndGet());
            return export;
        }).collect(Collectors.toList());
        String title;
        if (ao.getIsCurrentSemester()) {
            title = String.format("%s-%s学年第%s学期科目公开课统计", ao.getStartYear(), ao.getEndYear(), ao.getOrderNo() == 1 ? "一" : "二");
        } else {
            title = String.format("%s-%s科目公开课统计", DateUtils.formatDate(ao.getStartDate()), DateUtils.formatDate(ao.getEndDate()));
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), StatisticsSubjectExport.class).head(ExcelHeadsUtils.statisticSubjectHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_400010);
        }
    }

    // 公开课统计--教师、科目详情--数据导出
    @Override
    public void exportStatistics(StatisticsDetailAO ao, HttpServletResponse response) {
        QueryResult<List<OpenClassVO>> listQueryResult = statisticsTeachersDetails(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<OpenClassVO> result = listQueryResult.getResult();
        AtomicInteger count = new AtomicInteger();
        List<StatisticsDetailsExport> collect = result.stream().map(item -> {
            StatisticsDetailsExport export = ClassConverter.aTob(item, new StatisticsDetailsExport());
            export.setIndex(count.incrementAndGet());
            export.setType(item.getType().getDescription());
            return export;
        }).collect(Collectors.toList());
        String title;
        if (ao.getTeacherUserId() != null) {
            if (ao.getTeacherName() == null || "".equals(ao.getTeacherName())) {
                throw new BusinessException("teacherName参数需要和teacherUserId一起带上");
            }
            title = ao.getTeacherName();
        } else {
            if (ao.getSubject() == null || "".equals(ao.getSubject())) {
                throw new BusinessException("teacherUserId或subject参数必须传入其中的一个");
            }
            title = ao.getSubject();
        }
        if (ao.getIsCurrentSemester()) {
            title = String.format("%s%s-%s学年第%s学期公开课统计", title, ao.getStartYear(), ao.getEndYear(), ao.getOrderNo() == 1 ? "一" : "二");
        } else {
            title = String.format("%s%s-%s公开课统计", title, DateUtils.formatDate(ao.getStartDate()), DateUtils.formatDate(ao.getEndDate()));
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), StatisticsDetailsExport.class)
                    .head(ExcelHeadsUtils.statisticDetailHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(collect);
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_400010);
        }

    }

    // 公开课审批
    @Async
    @Override
    public void approve(OpenClassApproveAO approve) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("id", approve.getOpenClassId()).set("approval_state", approve.getApprovalState().name());
        if (approve.getApprovalState().equals(OpenClassApprovalState.PASS)) {
            wrapper.set("approval_pass_date", new Date());
        }
        openClassDao.executeUpdate(wrapper);
    }

    // jwt 用户校验
    private JwtUser getJwtUser() {
        JwtUser user = UserContextHolder.getUser();
        if (user == null) {
            throw new BusinessException(CodeRes.CODE_400006);
        }
        return user;
    }

    // 公开课发布、编辑校验
    private List<TeacherDto> checkOpenClass(OpenClassPublishAO ao) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("teacherIds", ao.getTeacherId());
        List<TeacherDto> teachers = foundationFacade.getTeachers(map);
        if (teachers == null || teachers.size() != 1) {
            throw new BusinessException(CodeRes.CODE_500003);
        }
        String empty = " ";
        int three = 3;
        if (ao.getPlace().split(empty).length != three) {
            throw new BusinessException("上课地点格式错误");
        }
        return teachers;
    }
}
