package com.xiaoshan.edu.model.ao;

import com.xiaoshan.edu.ao.courseadjustment.StartFlowAO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.utils.TimeUtils;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.TimeFormat;
import start.magic.persistence.annotation.Column;
import start.magic.thirdparty.json.JsonArray;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CourseReplaceApplyAO {

    @ApiModelProperty("课程教师id")
    @NotNull
    @NotEmpty
    private Long teacherId;

    @ApiModelProperty("课程名称")
    @NotNull
    @NotEmpty
    private String courseName;

    @ApiModelProperty("代课开始日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date replaceStartDate;

    @ApiModelProperty("代课结束日期")
    @NotNull
    @TimeFormat(format = TimeUtils.YYYYMMDD_F)
    private Date replaceEndDate;

    @ApiModelProperty("开始日期节次")
    @NotNull
    @NotEmpty
    private String startSection;

    @ApiModelProperty("开始日期节次")
    @NotNull
    @NotEmpty
    private String endSection;

    @ApiModelProperty("（二）代课节次详情列表")
    @NotNull
    private List<ReplaceCourseDetailAO> replaceCourseDetailList;

    @ApiModelProperty("代课节次")
    @NotNull
    private Integer section;

    @ApiModelProperty("代课教师id【非教务处安排老师时必填】")
    private Long replaceTeacherId;

    @ApiModelProperty("代课事由")
    @NotNull
    @NotEmpty
    private String content;

    @ApiModelProperty("图片url列表")
    private JsonArray picUrls;

    @ApiModelProperty("流程，若流程未开启，则传null或不传")
    @Column("startFlowAO")
    private StartFlowAO startFlowAo;

    @ApiModelProperty("是否由教务处安排老师【非管理端不为空】")
    @BooleanValid
    private Boolean isAcademicAffairsSectionArrange;

    @ApiModelProperty("是否由管理端发起")
    @BooleanValid
    @NotNull
    private Boolean isAdminApply;

}
