package com.xiaoshan.edu.patrol.controller;


import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.xiaoshan.edu.ao.patrol.*;
import com.xiaoshan.edu.vo.patrol.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.common.annotation.AuthenticationCheck;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.mvc.BaseController;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.patrol.service.PatrolService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

@RestController
@RequestMapping("/v1/patrol")
@Api(tags = "教师巡课接口")
public class PatrolController extends BaseController {

    @Autowired
    private PatrolService patrolService;

    @ApiOperation("教师电子档案-根据教师id查询巡课记录")
    @AuthenticationCheck
    @PostMapping("/getPatrolElectronicRecords")
    public ResultResponse<PatrolElectronicArchivesVO> getPatrolElectronicRecords(@RequestBody PatrolElectronicArchivesAO ao) {
        return response(patrolService.getPatrolElectronicRecords(ao));
    }

    @ApiOperation("教师电子档案-根据教师id查询巡课记录(我的档案)")
    @AuthenticationCheck
    @PostMapping("/getPatrolElectronicRecords/mine")
    public ResultResponse<PatrolElectronicArchivesVO> getMyPatrolElectronicRecords(@RequestBody PatrolElectronicArchivesAO ao) {
        ao.setTeacherId(UserContextHolder.getUser().getId());
        return response(patrolService.getPatrolElectronicRecords(ao));
    }

    @ApiOperation("获取当前节次")
    @AuthenticationCheck
    @GetMapping("/getCurrentSection/{classId}")
    public ResultResponse<String> getCurrentSection(
            @PathVariable
            @ApiParam("classId")
            @NotNull
            @LongValid Long classId
    ) {
        return response(patrolService.getCurrentSection(classId));
    }

    @ApiOperation("获取所有班级和教室")
    @AuthenticationCheck
    @GetMapping("/applet/getRoom")
    public ResultResponse<ClassAndRoomVO> getRoom() {
        return response(patrolService.getRoom());
    }

    @ApiOperation("根据班级和时间获取课表数据")
    @AuthenticationCheck
    @PostMapping("/applet/getCurriculum")
    public ResultResponse<SubjectAndObjectVO> getCurriculum(@RequestBody PatrolTimeAndClassAO ao) {
        return response(patrolService.getCurriculum(ao));
    }

    @ApiOperation("小程序 发起巡课")
    @AuthenticationCheck
    @PostMapping("/applet/save")
    public BaseResponse save(@RequestBody PatrolSaveAO ao) {
        patrolService.savePatrol(ao);
        return response();
    }

    @ApiOperation("小程序 巡课记录")
    @AuthenticationCheck
    @PostMapping("/applet/record")
    public PageResponse<List<PatrolVO>> page(@RequestBody PatrolPageAO ao) {
        JwtUser user = UserContextHolder.getUser();
        if (user == null){
            throw new BusinessException(CodeRes.CODE_400006);
        }
        return response(patrolService.getAppletRecord(ao, user.getName()));
    }

    @ApiOperation("批量删除")
    @AuthenticationCheck
    @DeleteMapping
    public BaseResponse remove(@RequestBody Long[] ids) {
        patrolService.remove(ids);
        return response();
    }

    @ApiOperation("pc-管理 巡课记录")
    @AuthenticationCheck
    @PostMapping("/pc/record")
    public PageResponse<List<PatrolVO>> page(@RequestBody PatrolRecordPageAO ao) {
        return response(patrolService.queryPage(ao));
    }

    @ApiOperation("巡课记录数据导出")
    @AuthenticationCheck
    @PostMapping("/pc/record/excelExport")
    public void recodeExcelExport(@RequestBody PatrolRecordPageAO ao, HttpServletResponse response) {
        patrolService.recodeExcelExport(ao, response);
    }

    @ApiOperation("pc-管理 巡课统计")
    @AuthenticationCheck
    @PostMapping("/pc/statistics")
    public PageResponse<List<PatrolStatisticsVO>> statisticsPage(@RequestBody PatrolStatisticsPageAO ao) {
        return response(patrolService.getStatisticsPage(ao));
    }

    @ApiOperation("巡课统计数据导出")
    @AuthenticationCheck
    @PostMapping("/pc/statistics/excelExport")
    public void statisticsPageExcelExport(@RequestBody PatrolStatisticsPageAO ao, HttpServletResponse response) {
        patrolService.statisticsExcelExport(ao, response);
    }

    @ApiOperation("pc-管理 单个对象统计详情")
    @AuthenticationCheck
    @PostMapping("/pc/statistics/singleDetail")
    public PageResponse<List<PatrolVO>> page(@RequestBody PatrolSinglePageAO ao) {
        return response(patrolService.queryPage(ao));
    }

    @ApiOperation("单个对象统计详情数据导出")
    @AuthenticationCheck
    @PostMapping("/pc/statistics/singleDetail/excelExport")
    public void recodeExcelExport(@RequestBody PatrolSinglePageAO ao, HttpServletResponse response) {
        patrolService.singleRecodeExcelExport(ao, response);
    }

    @ApiOperation("巡课详情-小程序")
    @AuthenticationCheck
    @GetMapping("/detail/{id}")
    public ResultResponse<PatrolVO> detail(
            @PathVariable
            @ApiParam("Id")
            @NotNull
            @LongValid Long id,
            @RequestParam(required = false)
            @ApiParam("是否从家长端进入")
            Boolean isFromParent,
            @RequestParam(required = false)
            @ApiParam("学生Id（从家长端进入时，消息的extra能获取到stuId）")
            Long stuId) {
        PatrolVO vo = patrolService.loadPatrolAndObjects(id, isFromParent, stuId);
        return response(vo);
    }

    @ApiOperation("巡课详情-web")
    @AuthenticationCheck
    @GetMapping("/patrolDetail")
    public ResultResponse<PatrolVO> patrolDetail(
            @RequestParam
            @ApiParam("Id")
            @NotNull
            @LongValid Long id,
            @RequestParam
            @ApiParam("巡课对象id")
            @NotNull
            @LongValid Long objectId) {
        PatrolVO vo = patrolService.loadPatrolAndObjects(id, objectId);
        return response(vo);
    }

}
