package com.xiaoshan.edu.survey.controller;


import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.ao.survey.TaskStudentAO;
import com.xiaoshan.edu.survey.service.TaskStudentService;
import com.xiaoshan.edu.vo.survey.StatisticNumVO;
import com.xiaoshan.edu.vo.survey.StudentQuestionVO;
import com.xiaoshan.edu.vo.survey.TaskStudentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/v1/survey/taskStudent")
@Api(tags = "教学调查接口")
public class TaskStudentController extends BaseController {

	@Autowired
	private TaskStudentService taskStudentService;

    @ApiOperation("完成情况 -- 分页查询")
    @AuthenticationCheck
    @PostMapping("/page")
    public PageResponse<List<TaskStudentVO>> page(@RequestBody TaskStudentAO ao){
        return response(taskStudentService.page(ao));
    }

    @ApiOperation("完成情况 -- 总人数、已完成、未完成人数")
    @AuthenticationCheck
    @GetMapping("/getNum/{taskId}")
    public ResultResponse<StatisticNumVO> getNum(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId){
        return response(taskStudentService.getNum(taskId));
    }

    @ApiOperation("完成情况 -- 导出")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody TaskStudentAO ao) {
        taskStudentService.export(response, ao);
    }

    @ApiOperation("完成情况 -- 一键通知未完成学生")
    @AuthenticationCheck
    @GetMapping("/notify/{taskId}")
    public BaseResponse notify(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId){
        taskStudentService.notify(taskId);
        return response();
    }

    @ApiOperation("完成情况 -- 查看详情")
    @AuthenticationCheck
    @GetMapping("/look/{taskId}/{templateId}/{studentId}")
    public ResultResponse<List<StudentQuestionVO>> look(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId,
                                                        @PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long templateId,
                                                        @PathVariable @ApiParam("学生id") @NotNull @LongValid Long studentId){
        return response(taskStudentService.look(taskId, templateId, studentId));
    }

}
