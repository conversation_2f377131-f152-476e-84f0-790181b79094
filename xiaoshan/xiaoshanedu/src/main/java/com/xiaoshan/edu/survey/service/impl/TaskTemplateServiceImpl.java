package com.xiaoshan.edu.survey.service.impl;

import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.xiaoshan.edu.ao.survey.TaskTemplateAO;
import com.xiaoshan.edu.survey.dao.TaskTemplateDao;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.TaskTemplateService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentTeacherQuestionService;
import com.xiaoshan.edu.survey.service.TemplateService;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import com.xiaoshan.edu.vo.survey.StudentTaskTemplateVO;
import com.xiaoshan.edu.vo.survey.TaskTemplateVO;
import com.xiaoshan.edu.vo.survey.TemplateAndQuestionVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.commons.exception.BusinessException;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service("taskTemplateService")
public class TaskTemplateServiceImpl extends SqlBaseServiceImplV2Ext<TaskTemplateDO,Long>
implements TaskTemplateService {

	@SuppressWarnings("unused")
	private TaskTemplateDao taskTemplateDao;
	@Autowired
	private TaskTemplateStudentTeacherQuestionService dataService;
	@Autowired
	private TemplateService templateService;


	public TaskTemplateServiceImpl(@Qualifier("taskTemplateDao")TaskTemplateDao taskTemplateDao) {
		super(taskTemplateDao);
		this.taskTemplateDao=taskTemplateDao;
	}

	/**
	 * 调查模板 -- 根据调查任务id查询调查模板列表
	 */
    @Override
    public List<TaskTemplateVO> getByTaskId(Long taskId) {
		List<TaskTemplateDO> list = queryForList("taskId", taskId);
		return ClassConverter.aTobList(list, TaskTemplateVO.class);
	}

	/**
	 * 删除调查任务关联的调查模板
	 */
    @Override
    public void removeByTaskId(Long taskId) {
        remove("taskId", taskId);
    }

	/**
	 * 调查任务添加调查模板
	 */
    @Override
    public void add(Long taskId, List<TaskTemplateAO> templateList) {
		// 查询每个模板的题目
		HashMap<Long, CompletableFuture<List<QuestionVO>>> map = new HashMap<>();
		for (TaskTemplateAO templateAo : templateList) {
			CompletableFuture<List<QuestionVO>> future = CompletableFuture.supplyAsync(() -> {
				TemplateAndQuestionVO vo = templateService.getById(templateAo.getTemplateId());
				return vo.getQuestionList();
			});
			map.put(templateAo.getTemplateId(), future);
		}

		// 保存模板
		List<TaskTemplateDO> taskTemplateDoList = templateList.stream().map(ao -> {
			TaskTemplateDO taskTemplateDO = new TaskTemplateDO();
			BeanUtils.copyProperties(ao, taskTemplateDO);
			taskTemplateDO.setTaskId(taskId);
			try {
				taskTemplateDO.setQuestionList(map.get(ao.getTemplateId()).get());
			} catch (InterruptedException | ExecutionException e) {
				throw new BusinessException("保存题目列表异常");
			}
			return taskTemplateDO;
		}).collect(Collectors.toList());
		saveBatch(taskTemplateDoList);
	}

	/**
	 * 小程序 -- 根据调查任务id查询调查模板列表
	 */
    @Override
    public List<StudentTaskTemplateVO> studentGetByTaskId(Long taskId, Long studentId) {
		HashMap<String, Object> map = new HashMap<>();
		map.put("taskId", taskId);
		map.put("studentId", studentId);
		return queryForMapMapper(StudentTaskTemplateVO.class, "getTemplate", map);
    }

	/**
	 * 教师端 -- 调查模板列表
	 */
    @Override
    public List<TaskTemplateVO> templateList(Long taskId) {
		Long teacherId = UserContextHolder.getUser().getId();

		HashMap<String, Object> map = new HashMap<>();
		map.put("taskId", taskId);
		map.put("teacherId", teacherId);
		List<TaskTemplateVO> voList = queryForMapMapper(TaskTemplateVO.class, "getTeacherTemplate", map);

		// 每个模板学生人数
		List<TaskTemplateStudentTeacherQuestionDO> data = dataService.getDistinctStudentByTaskAndTeacherId(taskId, teacherId);
		Map<Long, List<TaskTemplateStudentTeacherQuestionDO>> templateIdMap = data.stream()
				.collect(Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getTemplateId));
		for (TaskTemplateVO vo : voList) {
			List<TaskTemplateStudentTeacherQuestionDO> questionDoList = templateIdMap.get(vo.getTemplateId());
			vo.setTotal(questionDoList==null?0:questionDoList.size());
		}
		return voList;
	}

	/**
	 * 根据模板id查询
	 */
    @Override
    public List<TaskTemplateDO> getByTemplate(Long id) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("templateId", id);
		return taskTemplateDao.queryForList(wrapper);
    }

	/**
	 * 根据调查任务id和模板id查询
	 */
	@Override
	public TaskTemplateDO getByTaskTemplateId(Long taskId, Long templateId) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("templateId", templateId);
		wrapper.eq("taskId", taskId);
		return get(taskTemplateDao.queryForList(wrapper));
	}
}
