package com.xiaoshan.edu.timetable.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.xiaoshan.edu.api.facade.FoundationFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;

/**
 * <AUTHOR>
 */
@Service
public class CurriculumAuthService {

    @Autowired
    private CurriculumTableService curriculumTableService;

    @Autowired
    private GeneralTableService generalTableService;

    @Autowired
    private FoundationFacade foundationFacade;

    public List<StudentTableDTO> getAllStudent(Long userId, Long id) {
        CurriculumTableDO table = curriculumTableService.load(id);
        List<GeneralTableDO> generals = generalTableService.queryByCurriculumTableId(table.getId());
        List<String> auths = curriculumTableService.getAuth(table.getSemesterId(), table.getOrderNo(), userId);
        Map<Long, StudentTableDTO> students = new LinkedHashMap<>();
        for (String a : auths) {
            if ("3".equals(a)) {
                //校领导
                for (GeneralTableDO g : generals) {
                    for (StudentTableDTO d : g.getStudentTable()) {
                        students.put(d.getStuId(), d);
                    }
                }
            } else if ("1".equals(a)) {
                List<ClassVO> classes = foundationFacade.allClass();
                Map<Long, ClassVO> classesMap = classes.stream().collect(Collectors.toMap(ClassVO::getHeadTeacherId, Function.identity(), (t, t1) -> t1));
                if (classesMap.containsKey(userId)) {
                    ClassVO v = classesMap.get(userId);
                    //班主任
                    for (GeneralTableDO g : generals) {
                        for (StudentTableDTO d : g.getStudentTable()) {
                            if (d.getClassId().equals(v.getId())) {
                                students.put(d.getStuId(), d);
                            }
                        }
                    }
                }
            }
        }
        return new ArrayList<>(students.values());
    }

}
