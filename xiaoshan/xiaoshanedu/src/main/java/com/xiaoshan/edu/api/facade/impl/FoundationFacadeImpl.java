package com.xiaoshan.edu.api.facade.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaoshan.attendance.ao.report.BasicStudentPageAO;
import com.xiaoshan.attendance.ao.report.ClassesAO;
import com.xiaoshan.basic.vo.*;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.oa.dto.ClassDTO;
import com.xiaoshan.oa.dto.StudentDto;
import com.xiaoshan.oa.dto.TeacherDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.rest.ConverterEditorUtils;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;
import start.magic.utils.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class FoundationFacadeImpl implements FoundationFacade {

    private final static String FOUNDATION = "https://foundation";

    private final static String OA = "http://oa";


    @Autowired
    @Qualifier("webClient")
    private WebClient webClient;


    @Override
    public List<TeacherVO> getTeachers() {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.set("current", "-1");
        formData.set("size", "-1");
        ParameterizedTypeReference<PageVO<List<TeacherVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<TeacherVO>>>() {
        };
        PageVO<List<TeacherVO>> page = webClient.get().uri(FOUNDATION + "/teachers",uriBuilder -> uriBuilder.queryParams(formData).build()).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<DictionaryVO> getDictionaryValues(String dicCode) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.set("dicCode", dicCode);
        formData.set("defaultStatus", "0");
        formData.set("current", "-1");
        formData.set("size", "-1");
        ParameterizedTypeReference<PageVO<List<DictionaryVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<DictionaryVO>>>() {
        };
        PageVO<List<DictionaryVO>> page = webClient.get().uri(FOUNDATION + "/dictionary_values",uriBuilder -> uriBuilder.queryParams(formData).build()).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    public PageVO<List<StudentVO>> studentPages(BasicStudentPageAO param) {
        return studentPages(param, true);
    }

    public PageVO<List<StudentVO>> studentPages(BasicStudentPageAO param, Boolean isAll) {
        JSONObject json = new JSONObject();
        json.put("sectionId", param.getSectionId());
        json.put("enrollmentYear", param.getEnrollmentYear());
        json.put("name", param.getStudentName());
        if (param.getClassId() != null) {
            json.put("classId", param.getClassId());
        }
        if (param.getClassIdStr() != null) {
            json.put("classIdStr", String.valueOf(param.getClassIdStr()));
        }
        if (param.getLivingInSchool() != null) {
            json.put("livingSchoolState", String.valueOf(param.getLivingInSchool()));
        }
        if (param.getEveningStudyState() != null) {
            json.put("eveningStudyState", String.valueOf(param.getEveningStudyState()));
        }
        if (param.getSaturdayState() != null) {
            json.put("saturdayState", String.valueOf(param.getSaturdayState()));
        }
        if (param.getSundayState() != null) {
            json.put("sundayState", String.valueOf(param.getSundayState()));
        }
        if (param.getStuId() != null) {
            json.put("stuIds", param.getStuId());
        }
        if (param.getStuIds() != null) {
            json.put("stuIds", StringUtils.listToString(param.getStuIds(), ","));
        }
        if (param.getPersonIds() != null) {
            json.put("personIds", StringUtils.listToString(param.getPersonIds(), ","));
        }
        json.put("full", String.valueOf(true));
        json.put("scope", param.getScope());
        json.put("attendanceSort", "true");
        if (isAll) {
            json.put("current", "-1");
            json.put("size", "-1");
        } else {
            json.put("current", String.valueOf(param.getPageIndex()));
            json.put("size", String.valueOf(param.getPageSize()));
        }
        ParameterizedTypeReference<PageVO<List<StudentVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<StudentVO>>>() {
        };
        PageVO<List<StudentVO>> page = webClient.post().uri(FOUNDATION + "/students/all").body(BodyInserters.fromPublisher(Mono.just(json), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
        return page;
    }

    @Override
    public List<StudentVO> findBasicByStuIds(List<Long> stuIds) {
        BasicStudentPageAO pageParam = new BasicStudentPageAO();
        pageParam.setStuIds(stuIds);
        PageVO<List<StudentVO>> pageVO = studentPages(pageParam);
        return pageVO.getList();
    }

    @Override
    public List<ClassVO> allClass() {
        ClassesAO param = new ClassesAO();
        PageVO<List<ClassVO>> classes = classes(param, -1, -1);
        return classes.getList();
    }

    public PageVO<List<ClassVO>> classes(ClassesAO param, Integer pageIndex, Integer pageSize) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        if (param.getSectionId() != null) {
            formData.set("sectionId", String.valueOf(param.getSectionId()));
        }
        if (param.getEnrollmentYear() != null) {
            formData.set("enrollmentYear", String.valueOf(param.getEnrollmentYear()));
        }
        if (param.getClassId() != null) {
            formData.set("classId", String.valueOf(param.getClassId()));
        }
        if (param.getClassIdStr() != null) {
            formData.set("classIdStr", String.valueOf(param.getClassIdStr()));
        }
        formData.set("fullClass", String.valueOf(true));
        formData.set("current", String.valueOf(pageIndex));
        formData.set("size", String.valueOf(pageSize));
        ParameterizedTypeReference<PageVO<List<ClassVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<ClassVO>>>() {
        };
        return webClient.get().uri(FOUNDATION + "/classes", uriBuilder -> uriBuilder.queryParams(formData).build()).retrieve().bodyToMono(responseBodyType).block();
    }

    @Override
    public List<TeacherDto> getTeachers(Map<String, Object> param) {
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(param));
        ParameterizedTypeReference<PageVO<List<TeacherDto>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<TeacherDto>>>() {
        };
        PageVO<List<TeacherDto>> page = webClient.post().uri(FOUNDATION + "/teachers/post").body(BodyInserters.fromPublisher(Mono.just(jsonObject), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public Map<Long, TeacherDto> getAvatarMap() {
        HashMap<Long, TeacherDto> map = new HashMap<>();
        HashMap<String, Object> params = new HashMap<>();
        params.put("dataScope", "1");
        for (TeacherDto teacherDto : getTeachers(params)) {
            map.put(teacherDto.getId(), teacherDto);
        }
        return map;
    }

    @Override
    public List<TeacherDto> getTeachersByIds(String ids) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("teacherIds", ids);
        List<TeacherDto> teachers = getTeachers(map);
        if (teachers == null || teachers.isEmpty()) {
            throw new BusinessException("教师id不存在");
        }
        return teachers;
    }

    @Override
    public List<TeacherDto> getAllTeacherByIds(String teacherIds) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        if (!StringUtils.isEmpty(teacherIds)) {
            map.add("teacherIds", teacherIds);
        }
        map.add("current", "1");
        map.add("size", "-1");
        return teachers(map);
    }

    @Override
    public List<TeacherDto> teachers(MultiValueMap<String, String> map) {
        ParameterizedTypeReference<PageVO<List<TeacherDto>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<TeacherDto>>>() {
        };
        PageVO<List<TeacherDto>> page = webClient.get().uri(FOUNDATION + "/teachers",uriBuilder -> uriBuilder.queryParams(map).build()).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<StudentDto> getAllStudent(String stuIds) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        if (!StringUtils.isEmpty(stuIds)) {
            map.add("stuIds", stuIds);
        }
        map.add("all", "true");
        map.add("current", "1");
        map.add("size", "-1");
        map.add("scope", "2");
        return students(map);
    }

    @Override
    public List<StudentDto> students(MultiValueMap<String, String> map) {
        ParameterizedTypeReference<PageVO<List<StudentDto>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<StudentDto>>>() {
        };
        PageVO<List<StudentDto>> page = webClient.get().uri(FOUNDATION + "/students",uriBuilder -> uriBuilder.queryParams(map).build()).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<StudentDto> studentsPost(Map<String, String> map) {
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(map));
        ParameterizedTypeReference<PageVO<List<StudentDto>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<StudentDto>>>() {
        };
        PageVO<List<StudentDto>> page = webClient.post().uri(FOUNDATION + "/students/all").body(BodyInserters.fromPublisher(Mono.just(jsonObject), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<StudentDto> studentsOfPost(String stuIds) {
        JSONObject jsonObject = new JSONObject();
        if (!StringUtils.isEmpty(stuIds)) {
            jsonObject.put("stuIds", stuIds);
        }
        jsonObject.put("all", "true");
        jsonObject.put("current", "1");
        jsonObject.put("size", "-1");
        jsonObject.put("scope", "2");
        ParameterizedTypeReference<PageVO<List<StudentDto>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<StudentDto>>>() {
        };
        PageVO<List<StudentDto>> page = webClient.post().uri(FOUNDATION + "/students/all").body(BodyInserters.fromPublisher(Mono.just(jsonObject), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<ClassDTO> getAllClass(String classIdStr) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        if (!StringUtils.isEmpty(classIdStr)) {
            map.add("classIdStr", classIdStr);
        }
        map.add("current", "1");
        map.add("size", "-1");
        return classes(map);
    }

    @Override
    public List<ClassDTO> classes(MultiValueMap<String, String> map) {
        ParameterizedTypeReference<PageVO<List<ClassDTO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<ClassDTO>>>() {
        };
        PageVO<List<ClassDTO>> page = webClient.get().uri(FOUNDATION + "/classes",uriBuilder -> uriBuilder.queryParams(map).build()).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<StudentVO> getStudent(Map<String, String> params) {
        params.putIfAbsent("current", "1");
        params.putIfAbsent("size", "-1");
        params.putIfAbsent("scope", "1");
        params.putIfAbsent("full", "true");
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(params));
        ParameterizedTypeReference<PageVO<List<StudentVO>>> responseBodyType = new ParameterizedTypeReference<PageVO<List<StudentVO>>>() {
        };
        PageVO<List<StudentVO>> page = webClient.post().uri(FOUNDATION + "/students/all").body(BodyInserters.fromPublisher(Mono.just(jsonObject), JSONObject.class)).retrieve().bodyToMono(responseBodyType).block();
        return page.getList();
    }

    @Override
    public List<LeavingAndBusinessVO> getTeacherLeavingOrBusiness(List<Long> teacherIds, List<Integer> weekNumbers) {
        String emptyJson = "{}";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("teacherIds", teacherIds);
        jsonObject.put("weekNumbers", weekNumbers);
        String content = webClient.post().uri(OA + "/v1/teacher/leaving/isExistenceLeaving").body(BodyInserters.fromPublisher(Mono.just(jsonObject), JSONObject.class)).retrieve().bodyToMono(String.class).block();
        if (!StringUtils.isEmpty(content) && !emptyJson.equals(content)) {
            JsonObject jsonObject1 = new JsonObject(content);
            JsonArray json = new JsonArray(jsonObject1.get("result").toString());
            return ConverterEditorUtils.restoreList(LeavingAndBusinessVO.class, json);
        }
        return null;
    }


}
