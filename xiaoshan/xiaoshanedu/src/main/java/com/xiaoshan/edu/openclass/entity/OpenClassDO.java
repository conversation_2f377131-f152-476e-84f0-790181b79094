package com.xiaoshan.edu.openclass.entity;

import com.xiaoshan.edu.enums.openclass.OpenClassApprovalState;
import com.xiaoshan.edu.enums.openclass.OpenClassConfirmState;
import com.xiaoshan.edu.enums.openclass.OpenClassType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseExt;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;

import java.util.Date;

@Getter@Setter@ToString
@Entity("openClass")
@Table("ope_open_class")
@TableDef(comment = "公开课", charset = "utf8mb4", collate = "utf8mb4_bin")
public class OpenClassDO extends BaseExt {

	private static final long serialVersionUID = 1L;
	
	public OpenClassDO(){}

	@ColumnDef(comment = "课题")
	@Column("course_name")
	private String courseName;

	@ColumnDef(comment = "科目")
	private String subject;

	@ColumnDef(comment = "科目简称")
	private String abbreviation;

	@ColumnDef(comment = "教师姓名")
	@Column("teacher_name")
	private String teacherName;

	@ColumnDef(comment = "教师部门")
	@Column("teacher_department")
	private String teacherDepartment;

	@ColumnDef(comment = "教师userId")
	@Column("teacher_user_id")
	private Long teacherUserId;

	@ColumnDef(comment = "教师id")
	@Column("teacher_id")
	private Long teacherId;

	@ColumnDef(comment = "公开课类型")
	private OpenClassType type;

	@ColumnDef(comment = "上课日期")
	private java.sql.Date date;

	@ColumnDef(comment = "上课班级")
	@Column("attend_class")
	private String attendClass;

	@ColumnDef(comment = "周次")
	@Column("week_num")
	private Integer weekNum;

	@ColumnDef(comment = "本周星期几，如星期四、星期三")
	@Column("week_name")
	private String weekName;

	@ColumnDef(comment = "节次")
	private String section;

	@ColumnDef(comment = "节次对应的开始时间")
	@Column("start_time")
	private java.sql.Time startTime;

	@ColumnDef(comment = "地点")
	@Column("place")
	private String place;

	@ColumnDef(comment = "课题简介",isNull = true)
	private String introduction;

	@ColumnDef(comment = "总评分数", defaultValue = "'-'")
	private String score;

	@ColumnDef(comment = "确认状态",defaultValue = "'TO_BE_RELEASED'")
	@Column("confirm_state")
	private OpenClassConfirmState confirmState;

	@ColumnDef(comment = "发布时间",isNull = true)
	@Column("publish_date")
	private Date publishTime;

	@ColumnDef(comment = "确认时间",isNull = true)
	@Column("confirm_date")
	private Date confirmDate;

	@ColumnDef(comment = "审批状态", defaultValue = "'UNDER_APPROVAL'")
	@Column("approval_state")
	private OpenClassApprovalState approvalState;

	@ColumnDef(comment = "审批通过时间",isNull = true)
	@Column("approval_pass_date")
	private Date approvalPassDate;

	@ColumnDef(comment = "当管理员更新公开课信息后为new",defaultValue = "'false'")
	@Column("is_new")
	private Boolean isNew;

	@ColumnDef(comment = "逻辑删除，0-未删除，1-已删除",defaultValue = "0")
	private Integer deleted;

	@ColumnDef(comment = "审批编号", isNull = true)
	@Column("approval_code")
	private String approvalCode;

}
