package com.xiaoshan.edu.survey.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.common.utils.ClassConverter;
import com.xiaoshan.edu.ao.survey.QuestionAO;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.survey.dao.TemplateQuestionDao;
import com.xiaoshan.edu.survey.entity.TemplateDO;
import com.xiaoshan.edu.survey.entity.TemplateQuestionDO;
import com.xiaoshan.edu.survey.service.TemplateQuestionService;
import com.xiaoshan.edu.survey.service.TemplateService;
import com.xiaoshan.edu.survey.utils.excel.SurveyExcelUtils;
import com.xiaoshan.edu.vo.survey.QuestionImportVO;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import com.xiaoshan.edu.vo.survey.TemplateAndQuestionVO;
import com.xiaoshan.edu.vo.survey.check.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import start.framework.commons.exception.BusinessException;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.utils.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Service("questionService")
public class TemplateQuestionServiceImpl extends SqlBaseServiceImplV2Ext<TemplateQuestionDO,Long>
implements TemplateQuestionService {

	@SuppressWarnings("unused")
	private TemplateQuestionDao templateQuestionDao;
	@Autowired
	private TemplateService templateService;

	public TemplateQuestionServiceImpl(@Qualifier("questionDao") TemplateQuestionDao templateQuestionDao) {
		super(templateQuestionDao);
		this.templateQuestionDao = templateQuestionDao;
	}

	/**
	 * 获取模板题目
	 * @param id 模板id
	 * @return 模板题目
	 */
    @Override
    public List<QuestionVO> getByTemplateId(Long id) {
		List<TemplateQuestionDO> list = queryForList("templateId", id);
		return list.stream().map(item -> {
			QuestionVO vo = new QuestionVO();
			BeanUtils.copyProperties(item, vo);
			vo.setQuestionId(item.getId());
			return vo;
		}).collect(Collectors.toList());
    }

	/**
	 * 批量添加（编辑）调查题目
	 * @param templateId 模板id
	 * @param ao 题目列表
	 */
	@Transactional(rollbackFor = Exception.class)
    @Override
    public void addOrUpdate(Long templateId, List<QuestionAO> ao) {
		remove("templateId", templateId);
		List<TemplateQuestionDO> doList = ao.stream().map(item -> {
			item.setTemplateId(templateId);
			return ClassConverter.aTob(item, new TemplateQuestionDO());
		}).collect(Collectors.toList());
		saveBatch(doList);

	}

	/**
	 * 导入模板内容
	 */
    @Override
    public List<QuestionImportVO> importTemplate(Long templateId, MultipartFile file) {
		TemplateDO load = templateService.load(templateId);
		List<QuestionImportVO> res = new ArrayList<>();
		String message = "导入模板格式有误，请检查";
		try {
			EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {

				private int zero = 0;
				private int one = 1;
				private int twentyNine = 29;
				private int twentyEight = 28;
				private String answerStr = "问答题";
				private final Set<String> TYPE_SET = new HashSet<>(Arrays.asList("单选题", "多选题", "问答题"));

				@Override
				public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
					int seven = 7;
					if (context.readRowHolder().getRowIndex() == seven){
						String questionType = "题目类型";
						String limitStr = "题目名称（限制100个字）";
						String isRequire = "是否必填";
						String subjCurrStr = "应用科目";
						if (!questionType.equals(headMap.get(zero)) || !limitStr.equals(headMap.get(one)) || !isRequire.equals(headMap.get(twentyEight)) || !subjCurrStr.equals(headMap.get(twentyNine))){
							String message = "导入excel与模板不匹配，请检查";
							throw new BusinessException(message);
						}
					}
				}

				@Override
				public void invoke(Map<Integer, String> data, AnalysisContext context) {
					QuestionImportVO vo = new QuestionImportVO();
					vo.setQuestionTypeCheck(new QuestionTypeCheckVO(data.get(zero),!TYPE_SET.contains(data.get(zero))));
					int limit = 100;
					vo.setTitleCheck(new TitleCheckVO(data.get(one), StringUtils.isEmpty(data.get(one))||data.get(one).length()> limit));

					if (StringUtils.isEmpty(data.get(twentyEight))){
						data.put(twentyEight, "是");
					}
					vo.setRequiredCheck(new RequiredCheckVO(data.get(twentyEight), StringUtils.isEmpty(data.get(twentyEight))|| !"是否".contains(data.get(twentyEight))));

					List<String> curList;
					if (StringUtils.isEmpty(data.get(twentyNine))){
						String curriculumStrs = StringUtils.listToString(load.getCurriculumList());
						data.put(twentyNine, curriculumStrs);
						curList = load.getCurriculumList();
					}else {
						curList = StringUtils.stringToList(data.get(twentyNine));
					}

					vo.setCurriculumListCheck(new CurriculumListCheckVO(data.get(twentyNine), !load.getCurriculumList().containsAll(curList)));

					handleItem(data, vo);
					res.add(vo);
				}

				// 处理选项，单选、多选不足两个给提示，足两个但不连续给提示
				private void handleItem(Map<Integer, String> data, QuestionImportVO vo) {
					String[] options = {"A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"};
					List<OptionCheckVO> checkOptionList = new ArrayList<>();
					Map<Integer, String> map = new LinkedHashMap<>();
					int two = 2;
					int twentySeven = 27;
					for(int i = two; i <= twentySeven; i++){
						if (answerStr.equals(data.get(zero))){
							break;
						}
						if (!StringUtils.isEmpty(data.get(i))){
							map.put(i, data.get(i));
						}
					}

					if (!answerStr.equals(data.get(zero))){
						int index = zero;
						String itemStr = "选项";
						for (Map.Entry<Integer, String> entry : map.entrySet()) {
							QuestionOptionDTO dto = new QuestionOptionDTO();
							dto.setOption(itemStr +options[index++]);
							dto.setTxt(entry.getValue());
							OptionCheckVO checkVO = new OptionCheckVO(dto,false);
							checkOptionList.add(checkVO);
						}
						// 不足两个往后补错误数据
						for(int i = index; i < two; i++){
							QuestionOptionDTO dto = new QuestionOptionDTO();
							dto.setOption(itemStr +options[i]);
							dto.setTxt("");
							OptionCheckVO checkVO = new OptionCheckVO(dto,true);
							checkOptionList.add(checkVO);
						}
					}

					vo.setItemsCheck(checkOptionList);
				}

				@Override
				public void doAfterAllAnalysed(AnalysisContext context) {

				}
			}).sheet().headRowNumber(8).doRead();
		} catch (ExcelAnalysisException e){
			if (e.getCause() instanceof BusinessException){
				throw (BusinessException) e.getCause();
			}else {
				throw new BusinessException(message);
			}
		} catch (Exception e) {
			throw new BusinessException(message);
		}

		if (res.isEmpty()){
			String msg = "模板有误或模板内容为空，请重新上传";
			throw new BusinessException(msg);
		}
		return res;
    }


	/**
	 * excel导出
	 */
	@Override
	public void export(HttpServletResponse response, Long id) {
		List<List<String>> data = new ArrayList<>();
		List<String> head = Arrays.asList("题目类型", "题目名称（限制100个字）", "选项A", "选项B", "选项C", "选项D", "选项E", "选项F", "选项G", "选项H", "选项I", "选项J", "选项K", "选项L", "选项M", "选项N", "选项O", "选项P", "选项Q", "选项R", "选项S","选项T", "选项U", "选项V", "选项W", "选项X", "选项Y", "选项Z", "是否必填", "应用科目");
		data.add(head);

		TemplateAndQuestionVO vo = templateService.getById(id);
		for (QuestionVO questionVO : vo.getQuestionList()) {
			ArrayList<String> l = new ArrayList<>();
			l.add(questionVO.getQuestionType().getDesc());
			l.add(questionVO.getTitle());
			HashMap<String, String> itemMap = new HashMap<>();
			for (QuestionOptionDTO item : questionVO.getItems()) {
				itemMap.put("选项"+item.getOption(), item.getTxt());
			}
			int two = 2;
			int twentySeven = 27;
			for (int i = two; i <= twentySeven; i++) {
				l.add(itemMap.getOrDefault(head.get(i),""));
			}
			l.add(questionVO.getIsRequired() ? "是" : "否");
			l.add(StringUtils.listToString(Optional.ofNullable(questionVO.getCurriculumList()).orElse(new ArrayList<>())));
			data.add(l);
		}

		SurveyExcelUtils.writeExcelData(response, data, "模板内容");
	}
}
