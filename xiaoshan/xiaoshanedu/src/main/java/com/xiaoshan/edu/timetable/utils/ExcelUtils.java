package com.xiaoshan.edu.timetable.utils;

import org.apache.poi.ss.usermodel.*;

/**
 * <AUTHOR>
 */
public class ExcelUtils {

    public static void setCell(Sheet sheet, Cell cell) {
        cell.getRow().setHeight((short) (300 * 2));
        cell.getSheet().setColumnWidth(cell.getColumnIndex(), 2048 * 2);
        Workbook workbook = sheet.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        if (cell.getCellTypeEnum() == CellType.STRING) {
            String value = cell.getStringCellValue();
            String s="&";
            if (value.contains(s)) {
                cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cell.setCellValue(value.substring(0, value.length() - 1));
            }
        }
        cell.setCellStyle(cellStyle);
    }

}
