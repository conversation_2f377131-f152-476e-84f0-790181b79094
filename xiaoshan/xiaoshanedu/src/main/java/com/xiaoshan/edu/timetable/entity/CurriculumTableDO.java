package com.xiaoshan.edu.timetable.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.Indexes;
import start.magic.persistence.source.jdbc.script.annotations.IndexesUnion;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.Normal;
import start.magic.persistence.source.jdbc.script.annotations.indexes.UniqueUnion;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity("curriculumTable")
@Table("tim_curriculum_table")
@TableDef(comment = "课程表", indexes =
@IndexesUnion(unique = @UniqueUnion(fields = {"semesterId", "enrollmentYear", "sectionId", "deleted"})))
public class CurriculumTableDO extends BaseV2Ext {

    private static final long serialVersionUID = 1L;

    public CurriculumTableDO() {
    }

    @ColumnDef(indexes = @Indexes(normal = @Normal), comment = "学段ID")
    private Long sectionId;

    @ColumnDef(comment = "入学年份")
    private Integer enrollmentYear;

    @ColumnDef(indexes = @Indexes(normal = @Normal), comment = "学期ID")
    private Long semesterId;

    @ColumnDef(comment = "学期")
    private Integer orderNo;

    @ColumnDef(comment = "是否单双周")
    private Boolean singleWeek;

    @ColumnDef(comment = "上传ID")
    private Long uploadId;

    @ColumnDef(comment = "是否发布")
    private Boolean published;

    @ColumnDef(comment = "是否为首次发布")
    private Boolean firstPublished;

}
