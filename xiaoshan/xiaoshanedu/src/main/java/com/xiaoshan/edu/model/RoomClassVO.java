package com.xiaoshan.edu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.persistence.annotation.Column;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RoomClassVO {

    @ApiModelProperty("楼宇id")
    private Integer buildingId;

    @ApiModelProperty("楼宇名称")
    private String buildingName;

    @ApiModelProperty("楼层列表")
    @JsonProperty("floorTreeDTOS")
    @Column("floorTreeDTOS")
    private List<RoomClassFloorTreeVO> floorTreeDTOList;

}