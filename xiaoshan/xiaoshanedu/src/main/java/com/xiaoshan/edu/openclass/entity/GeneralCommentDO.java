package com.xiaoshan.edu.openclass.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseExt;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;

@Getter@Setter@ToString
@Entity("generalComment")
@Table("ope_general_comment")
@TableDef(comment = "公开课总评价")
public class GeneralCommentDO extends BaseExt {

	private static final long serialVersionUID = 1L;
	
	public GeneralCommentDO(){}

	@ColumnDef(comment = "公开课id")
	@Column("open_class_id")
	private Long openClassId;

	@ColumnDef(comment = "语言表达评分", isNull = true)
	private Float expression;

	@ColumnDef(comment = "学生互动评分", isNull = true)
	private Float interaction;

	@ColumnDef(comment = "时间分配评分", isNull = true)
	@Column("time_allocation")
	private Float timeAllocation;

	@ColumnDef(comment = "板书内容评分",isNull = true)
	@Column("blackboard_content")
	private Float blackboardContent;

	@ColumnDef(comment = "信息化评分", isNull = true)
	private Float informationize;

	@ColumnDef(comment = "总评分", isNull = true)
	@Column("total_score")
	private Float totalScore;

	@ColumnDef(comment = "评分人数", defaultValue = "0")
	@Column("total_people")
	private Integer totalPeople;


}
