package com.xiaoshan.edu.model.dto;

import com.xiaoshan.edu.ao.courseadjustment.StartFlowAO;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CourseAdjustmentFlowDTO {

    @ApiModelProperty("流程，若流程未开启，则传null或不传")
    private StartFlowAO startFlowAo;

    @ApiModelProperty("调课信息")
    private CourseAdjustmentDO courseAdjustmentDo;

    @ApiModelProperty("模板是否启用")
    private String result;

}
