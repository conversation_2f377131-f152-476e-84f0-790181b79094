package com.xiaoshan.edu.timetable.utils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.enums.timetable.WeekEnum;

import start.framework.commons.utils.TimeUtils;

/**
 * <AUTHOR>
 */
public class DateUtils {

    public static void main(String[] args) {
        System.out.println(currentWeekMap(new Date(), new Date()));
    }

    /**
     * 根据日期获取对应的周
     */
    public static WeekEnum getDayOfWeekByDate(String date, Map<WeekEnum, String> currentWeekMap) {
        for (WeekEnum week : currentWeekMap.keySet()) {
            if (currentWeekMap.get(week).equals(date)) {
                return week;
            }
        }
        return null;
    }

    /**
     * 当前周的映射
     *
     * @return
     */
    public static Map<WeekEnum, String> currentWeekMap(Date d, Date e) {
        Map<WeekEnum, String> mapDate = new HashMap<>(10);
        List<Date> days = dateToWeek(d);
        for (Date date : days) {
            if (date.compareTo(d) >= 0 && e.compareTo(date) >= 0) {
                mapDate.put(WeekEnum.getWeekByIndex(date), TimeUtils.format(date, TimeUtils.YYYYMMDD_F));
            }
        }
        return mapDate;
    }

    /**
     * 根据日期获得所在周的日期
     */
    @SuppressWarnings("deprecation")
    public static List<Date> dateToWeek(Date mdate) {
        int seven = 7;
        int b = mdate.getDay();
        Date fdate;
        List<Date> list = new ArrayList<>();
        long fTime = mdate.getTime() - b * 24 * 3600000;
        for (int a = 1; a <= seven; a++) {
            fdate = new Date();
            fdate.setTime(fTime + ((long) a * 24 * 3600000));
            list.add(a - 1, fdate);
        }
        return list;
    }

}
