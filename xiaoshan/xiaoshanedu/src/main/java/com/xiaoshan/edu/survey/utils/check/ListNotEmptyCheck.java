package com.xiaoshan.edu.survey.utils.check;

import start.magic.core.valid.ValidCheckCustom;
import start.magic.thirdparty.json.JsonArray;

import java.util.List;

/**
 * list元素不能为空
 */
public class ListNotEmptyCheck implements ValidCheckCustom {

    @Override
    public boolean check(Object value) {
        if (value instanceof List){
            return !((List<?>) value).isEmpty();
        }else if (value instanceof JsonArray){
            return ((JsonArray) value).length()!=0;
        }
        return true;
    }
}
