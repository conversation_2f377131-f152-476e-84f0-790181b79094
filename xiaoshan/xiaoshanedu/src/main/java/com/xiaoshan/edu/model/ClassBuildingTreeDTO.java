package com.xiaoshan.edu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.persistence.annotation.Column;

import java.util.List;

/**
 * 房间、楼宇
 */
@Data
public class ClassBuildingTreeDTO {

    @ApiModelProperty("楼宇id")
    private Long buildingId;

    @ApiModelProperty("楼宇名称")
    private String buildingName;

    @ApiModelProperty("楼层树形结构")
    @Column("floorTreeDTOS")
    @JsonProperty("floorTreeDTOS")
    private List<ClassFloorTreeDTO> classFloorTreeDTOList;
}
