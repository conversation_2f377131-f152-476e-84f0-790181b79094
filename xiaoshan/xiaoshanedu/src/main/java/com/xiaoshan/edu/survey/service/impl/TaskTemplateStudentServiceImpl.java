package com.xiaoshan.edu.survey.service.impl;

import com.xiaoshan.edu.survey.dao.TaskTemplateStudentDao;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.TaskTemplateService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentTeacherQuestionService;
import com.xiaoshan.edu.vo.survey.TaskTemplateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;

import java.util.*;
import java.util.stream.Collectors;

@Service("taskTemplateStudentService")
public class TaskTemplateStudentServiceImpl extends SqlBaseServiceImplV2Ext<TaskTemplateStudentDO,Long>
implements TaskTemplateStudentService {

	@SuppressWarnings("unused")
	private TaskTemplateStudentDao taskTemplateStudentDao;

	@Autowired
	private TaskTemplateService taskTemplateService;
	@Autowired
	private TaskTemplateStudentTeacherQuestionService service;

	public TaskTemplateStudentServiceImpl(@Qualifier("taskTemplateStudentDao")TaskTemplateStudentDao taskTemplateStudentDao) {
		super(taskTemplateStudentDao);
		this.taskTemplateStudentDao=taskTemplateStudentDao;
	}


	/**
	 * 小程序 -- 调查详情提交(当所有科目都已完成时调用，否则不需要调)
	 */
	@Override
	public void submitCurriculum(Long taskId, Long templateId, Long studentId) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("taskId", taskId).eq("templateId", templateId).eq("studentId", studentId);
		TaskTemplateStudentDO taskTemplateStudentDO = get(taskTemplateStudentDao.queryForList(wrapper));
		if (taskTemplateStudentDO == null){
			save(new TaskTemplateStudentDO(taskId, templateId, studentId));
		}
	}

	@Override
	public void resultSubmit(Long taskId, Long templateId, Collection<Long> studentId) {
		List<TaskTemplateStudentDO> list = new ArrayList<>();
		for (Long id : studentId) {
			list.add(new TaskTemplateStudentDO(taskId, templateId, id));
		}
		saveBatch(list);
	}

	/**
	 * 重置，结果导入时删除相应数据
	 */
	@Override
	public void reset(Long taskId, Long templateId) {
		DeleteWrapper wrapper = new DeleteWrapper();
		wrapper.eq("taskId", taskId).eq("templateId", templateId);
		taskTemplateStudentDao.executeUpdate(wrapper);
	}

	@Override
	public Collection<Long> findFinishOtherTemplateStudents(Long taskId, Long templateId, Collection<Long> studentIds) {
		List<Long> res = new ArrayList<>();

		List<TaskTemplateVO> templates = taskTemplateService.getByTaskId(taskId);

		// 如果调查任务中只有一个模板，且导入结果导入的是这个模板，那么这些学生都是已完成的
		if (templates.size() == 1 && templates.get(0).getTemplateId().equals(templateId)){
			return studentIds;
		}

		// 如果调查任务中有多个模板，查找其他模板都已完成的学生
		// 如果其他模板都已完成的学生中有上面传入的学生，则这些有交集的学生就是完成调查任务的学生

		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("taskId", taskId).ne("templateId", templateId);
		List<TaskTemplateStudentDO> taskTemplateStudentList = taskTemplateStudentDao.queryForList(wrapper);

		// 特殊情况处理，如果调查任务两个模板，但是有的学生只有一个模板，需要加入到res中
		List<TaskTemplateStudentTeacherQuestionDO> list = service.getByTaskStudentIds(taskId, studentIds);
		Map<Long, List<TaskTemplateStudentTeacherQuestionDO>> map = list.stream().collect(Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getStudentId));
		for (Map.Entry<Long, List<TaskTemplateStudentTeacherQuestionDO>> entry : map.entrySet()) {
			Set<Long> set = entry.getValue().stream().map(TaskTemplateStudentTeacherQuestionDO::getTemplateId).collect(Collectors.toSet());
			if (set.size()==1 && set.contains(templateId) && studentIds.contains(entry.getKey())){
				res.add(entry.getKey());
			}
		}

		// 如果没有学生完成其他调查模板，则直接返回
		if (taskTemplateStudentList.size() == 0) {
			return res;
		}

		Map<Long, List<TaskTemplateStudentDO>> collect = taskTemplateStudentList.stream().collect(Collectors.groupingBy(TaskTemplateStudentDO::getTemplateId));

		// 获取其他模板中，每个模板完成的学生
		HashMap<Long, Set<Long>> mixMap = new HashMap<>();
		for (Map.Entry<Long, List<TaskTemplateStudentDO>> entry : collect.entrySet()) {
			mixMap.putIfAbsent(entry.getKey(), entry.getValue().stream().map(TaskTemplateStudentDO::getStudentId).collect(Collectors.toSet()));
		}

		Collection<Set<Long>> sets = mixMap.values();
		if (sets.size() == 0) {
			return res;
		}

		for (Long studentId : studentIds) {
			boolean flag = true;
			for (Set<Long> set : sets) {
				if (!set.contains(studentId)) {
					flag = false;
					break;
				}
			}
			if (flag){
				res.add(studentId);
			}
		}

		return res;

	}
}
