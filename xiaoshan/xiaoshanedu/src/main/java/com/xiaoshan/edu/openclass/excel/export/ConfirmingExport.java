package com.xiaoshan.edu.openclass.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;

@Getter@Setter@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class ConfirmingExport extends BaseExport {

    @ExcelProperty(value = "上课状态", index = 8)
    private String courseState;

    @ExcelProperty(value = "听课教师", index = 9)
    private Integer listenTeacherNum;

}
