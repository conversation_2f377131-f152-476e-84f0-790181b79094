package com.xiaoshan.edu.timetable.utils.check;

import java.util.List;
import java.util.Map;

import com.xiaoshan.edu.ao.timetable.CurriculumTableAO;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.dto.TempCurriculumTableDTO;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class CheckData {

    private CurriculumTableAO table;
    private TempCurriculumTableDTO mainTableTemp;
    private TempCurriculumTableDTO teacherTableTemp;
    private TempCurriculumTableDTO classTableTemp;
    private TempCurriculumTableDTO studentTableTemp;
    private List<DataTableDTO> mainTable;
    private Map<String, List<TeacherTableDTO>> teacherTable;
    private List<DataTableDTO> classTable;
    private List<StudentTableDTO> studentList;
    private Map<Long, Long> classTeacher;
    private Map<Long, Long> classRoomMap;
    private Map<String, List<String>> classLeaveMap;
    private Map<String, String> curriculumAliasMap;

}
