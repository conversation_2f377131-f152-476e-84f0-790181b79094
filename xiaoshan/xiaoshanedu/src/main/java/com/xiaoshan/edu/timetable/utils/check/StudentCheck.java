package com.xiaoshan.edu.timetable.utils.check;

import java.util.List;
import java.util.Map;

import com.xiaoshan.basic.vo.TeacherVO;
import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.timetable.utils.RuleUtils;
import com.xiaoshan.edu.timetable.utils.TeacherTableUtils;

/**
 * <AUTHOR>
 */
public class StudentCheck {

    public static void check1(CheckData cddata, StudentTableDTO item, TeacherVO teacher, String courseName, String abbreviation) {
        boolean flag = true;
        for (DataTableDTO classDto : cddata.getClassTable()) {
            if (classDto.getClassOrRoomId().equals(String.valueOf(item.getClassId()))) {
                List<String> leaveClassList = cddata.getClassLeaveMap().get(classDto.getClassOrRoomId());
                for (WeekEnum week : classDto.getDetail().keySet()) {
                    Map<String, String> detaila = classDto.getDetail().get(week);
                    for (String section : detaila.keySet()) {
                        if (leaveClassList.contains(week + section + abbreviation)) {
                            String[] abbs = detaila.get(section).split(Constant.SPLITSTRING);
                            for (String abb : abbs) {
                                if (abbreviation.equals(abb)) {
                                    if (TeacherTableUtils.checkWeekSectionTeacherId(cddata.getTeacherTable(), week, section, teacher.getId())) {
                                        flag = false;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (flag) {
            String message = "%s学生在%s班级下的%s课程与教师课表%s教师上课节次不匹配";
            RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(),
                    message, item.getName(), item.getClassName(), courseName, teacher.getName());
        }
    }

}
