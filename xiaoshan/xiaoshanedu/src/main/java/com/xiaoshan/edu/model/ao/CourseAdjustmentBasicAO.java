package com.xiaoshan.edu.model.ao;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.thirdparty.json.JsonArray;


/**
 * <AUTHOR>
 */
@Data
public class CourseAdjustmentBasicAO {

    @ApiModelProperty("调课事由")
    @NotNull
    @NotEmpty
    private String content;

    @ApiModelProperty("图片url列表")
    private JsonArray picUrls;

    @NotNull
    @ApiModelProperty("循环方式(1-不循环，2-周循环，3-自定义时间段)")
    private Integer cycleType;

    @ApiModelProperty("周循环次数")
    @IntegerValid
    private Integer weekCycleNum;

    @ApiModelProperty("自定义开始时间")
    private String startTime;

    @ApiModelProperty("自定义结束时间")
    private String endTime;

    @ApiModelProperty("（二）不纳入统计，管理端必传")
    @BooleanValid
    private Boolean isNotStatistics;

}
