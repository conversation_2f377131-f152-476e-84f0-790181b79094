package com.xiaoshan.edu.survey.service.impl;

import com.common.utils.ClassConverter;
import com.common.utils.ResponseUtils;
import com.xiaoshan.edu.ao.survey.TemplateAO;
import com.xiaoshan.edu.ao.survey.TemplateNameAO;
import com.xiaoshan.edu.survey.dao.TemplateDao;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.survey.entity.TemplateDO;
import com.xiaoshan.edu.survey.service.TaskTemplateService;
import com.xiaoshan.edu.survey.service.TemplateQuestionService;
import com.xiaoshan.edu.survey.service.TemplateService;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import com.xiaoshan.edu.vo.survey.TemplateAndQuestionVO;
import com.xiaoshan.edu.vo.survey.TemplateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.IoUtils;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.core.ApplicationException;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.utils.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service("templateService")
public class TemplateServiceImpl extends SqlBaseServiceImplV2Ext<TemplateDO,Long>
implements TemplateService {

	@SuppressWarnings("unused")
	private TemplateDao templateDao;
	@Autowired
	private TemplateQuestionService templateQuestionService;
	@Autowired
	private TaskTemplateService taskTemplateService;

	public TemplateServiceImpl(@Qualifier("templateDao")TemplateDao templateDao) {
		super(templateDao);
		this.templateDao=templateDao;
	}

	/**
	 * 分页查询
	 * @param ao 查询条件
	 * @return 模板列表
	 */
    @Override
    public QueryResult<List<TemplateVO>> page(TemplateNameAO ao) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.select(
				"id AS templateId",
				"name AS name",
				"surveyObj AS surveyObj",
				"DATE_FORMAT( created_date, '%Y-%m-%d' ) AS date"
		);
		wrapper.like(!StringUtils.isEmpty(ao.getName()), "name" ,ao.getName());
		wrapper.orderByDesc("created_date");
		wrapper.last(String.format("limit %d,%d", ao.index(), ao.getPageSize()));
		return queryForPage(TemplateVO.class, wrapper);
    }

	/**
	 * id查询详细内容
	 * @param id 模板id
	 * @return 详细内容
	 */
	@Override
	public TemplateAndQuestionVO getById(Long id) {
		TemplateDO load = load(id);
		List<QuestionVO> questionVoList = templateQuestionService.getByTemplateId(id);
		TemplateAndQuestionVO vo = ClassConverter.aTob(load, new TemplateAndQuestionVO());
		vo.setQuestionList(questionVoList);
		vo.setTemplateId(load.getId());
		return vo;
	}

	/**
	 * 保存或更新
	 */
	@Override
	public Long saveOrUpdate(TemplateAO ao) {
		TemplateDO templateDO = ClassConverter.aTob(ao, new TemplateDO());
		templateDO.setId(ao.getTemplateId());
		save(templateDO);
		return templateDO.getId();
	}

	/**
	 * 根据id删除
	 */
	@Override
	public Integer removeById(Long id) {
		List<TaskTemplateDO> list = taskTemplateService.getByTemplate(id);
		if (list.size() > 0){
			return 400;
		}
		remove(id);
		return 200;
	}

	/**
	 * 下载模板
	 */
    @Override
    public void downloadTemplate(HttpServletResponse response) {
		try (InputStream in = getClass().getClassLoader().getResourceAsStream("resource/question_template.xlsx");
			 BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());) {
			ResponseUtils.setExcel(response, "excel模板");
			bos.write(IoUtils.inputStreamConvertBytes(in, -1));
			response.flushBuffer();
		} catch (Exception e) {
			throw new ApplicationException(e);
		}

	}

	@Override
	public TemplateAndQuestionVO getCurrById(Long id, Long taskId) {
		TemplateDO load = load(id);
		List<QuestionVO> questionVoList = templateQuestionService.getByTemplateId(id);
		TemplateAndQuestionVO vo = ClassConverter.aTob(load, new TemplateAndQuestionVO());
		vo.setQuestionList(questionVoList);
		vo.setTemplateId(load.getId());
		TaskTemplateDO byTaskTemplateId = taskTemplateService.getByTaskTemplateId(taskId, id);
		Set<String> currList = new HashSet<>();
		if(byTaskTemplateId != null){
			List<QuestionVO> questionList = byTaskTemplateId.getQuestionList();
			for (QuestionVO questionVO : questionList) {
				currList.addAll(questionVO.getCurriculumList());
			}
		}
		vo.setNewCurriculumList(currList);
		return vo;
	}
}
