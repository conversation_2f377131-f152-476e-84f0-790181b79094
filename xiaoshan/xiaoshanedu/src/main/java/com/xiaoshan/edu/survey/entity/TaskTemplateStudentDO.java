package com.xiaoshan.edu.survey.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.IndexesUnion;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.UniqueUnion;

@Getter@Setter@ToString
@Entity("taskTemplateStudent")
@Table("sur_task_template_student")
@TableDef(comment = "调查任务--调查模板--学生关联表", indexes =
@IndexesUnion(unique = @UniqueUnion(fields = {"taskId","templateId","studentId","deleted"})))
public class TaskTemplateStudentDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;
	
	public TaskTemplateStudentDO(){}

	public TaskTemplateStudentDO(Long taskId, Long templateId, Long studentId){
		this.taskId = taskId;
		this.templateId = templateId;
		this.studentId = studentId;
	}

	@ColumnDef(comment = "调查任务id")
	private Long taskId;

	@ColumnDef(comment = "调查模板id")
	private Long templateId;

	@ColumnDef(comment = "学生id")
	private Long studentId;

}
