package com.xiaoshan.edu.timetable.service;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.xiaoshan.edu.ao.timetable.CheckAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTableAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTablePageAO;
import com.xiaoshan.edu.ao.timetable.PublishedAO;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.vo.timetable.CurriculumTableVO;

import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

/**
 * <AUTHOR>
 */
public interface CurriculumTableService extends SqlBaseService<CurriculumTableDO,Long> {

	/**
	 * 获取上传ID
	 * @param ao
	 * @return
	 */
	Long nextId(CurriculumTableAO ao);

	/**
	 *  获取课表列表
	 * @param semesterId 学期ID
	 * @param orderNo
	 * @return
	 */
	List<CurriculumTableDO> getTable(Long semesterId,Integer orderNo);

	/**
	 * 获取课表
	 * @param sectionId 学段ID
	 * @param enrollmentYear 入学年份
	 * @param semesterId 学期ID
	 * @param orderNo
	 * @return
	 */
	CurriculumTableDO getTable(Long sectionId,int enrollmentYear,Long semesterId,Integer orderNo);

	/**
	 * 获取课表
	 * @param sectionId 学段ID
	 * @param enrollmentYear 入学年份
	 * @param semesterId 学期ID
	 * @param orderNo
	 * @param firstPublished
	 * @return
	 */
	CurriculumTableDO getTable(Long sectionId, int enrollmentYear, Long semesterId, Integer orderNo,Boolean firstPublished);

	/**
	 * 获取课表列表
	 * @param studentId 学生ID
	 * @param teacherId 老师ID
	 * @return
	 */
	List<CurriculumTableVO> getCurrentCurriculums(Long studentId,Long teacherId);

	/**
	 * 获取课表列表
	 * @param semesterId 学期ID
	 * @param studentId 学生ID
	 * @param teacherId 老师ID
	 * @return
	 */
	List<CurriculumTableVO> getCurrentCurriculums(Long semesterId,Long studentId,Long teacherId);

	/**
	 * 获取课表ID列表
	 * @param studentId 学生ID
	 * @param teacherId 老师ID
	 * @return
	 */
	List<Long> getCurrentCurriculumIds(Long studentId,Long teacherId);

	/**
	 * 获取课表ID列表
	 * @param semesterId 学期ID
	 * @param studentId 学生ID
	 * @param teacherId 老师ID
	 * @return
	 */
	List<Long> getCurrentCurriculumIds(Long semesterId,Long studentId,Long teacherId);

	/**
	 * 获取课表ID列表
	 * @param semesterId
	 * @param studentIds
	 * @param teacherIds
	 * @return
	 */
	List<Long> getCurrentCurriculumList(Long semesterId,List<Long> studentIds,List<Long> teacherIds);

	/**
	 * 分页
	 * @param ao
	 * @param studentId 学生ID
	 * @param teacherId 老师ID
	 * @return
	 */
	QueryResult<List<CurriculumTableVO>> page(CurriculumTablePageAO ao,Long studentId,Long teacherId);

	/**
	 * 分页
	 * @param ao
	 * @param studentIds
	 * @param teacherIds
	 * @return
	 */
	QueryResult<List<CurriculumTableVO>> pageList(CurriculumTablePageAO ao,List<Long> studentIds,List<Long> teacherIds);

	/**
	 * 上传
	 * @param uploadId 上传ID
	 * @param type 课表类型
	 * @param weekType 周类型
	 * @param file 课表文件
	 */
	void upload(Long uploadId,CurriculumTableTypeEnum type,WeekTypeEnum weekType,MultipartFile file);

	/**
	 * 下载
	 * @param response
	 * @param uploadId 上传ID
	 * @param type 课表类型
	 * @param weekType 周类型
	 */
	void download(HttpServletResponse response,Long uploadId,CurriculumTableTypeEnum type,WeekTypeEnum weekType);

	/**
	 * 下载模版
	 * @param response
	 */
	void downloadTemplate(HttpServletResponse response);

	/**
	 * 检测课表
	 * @param outputStream excel输出流
	 * @param ao
	 */
	void check(OutputStream outputStream,CheckAO ao);

	/**
	 * 检测课表
	 * @param ao
	 * @return
	 */
	Map<CurriculumTableTypeEnum,Set<String>> checkMessage(CheckAO ao);

	/**
	 * 生成课表
	 * @param ao
	 */
	void build(PublishedAO ao);

	/**
	 * 获取授权值
	 * @param semesterId 学期ID
	 * @param orderNo
	 * @param userId
	 * @return
	 */
	List<String> getAuth(Long semesterId,Integer orderNo,Long userId);

	/**
	 * 获取上传ID对应的课表映射
	 * @param uploadId 上传ID
	 * @return
	 */
	Map<WeekTypeEnum,List<CurriculumTableTypeEnum>> getUploadTableMap(Long uploadId);
	
}
