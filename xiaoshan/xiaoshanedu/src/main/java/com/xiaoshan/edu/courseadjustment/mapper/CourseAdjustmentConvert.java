package com.xiaoshan.edu.courseadjustment.mapper;

import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import com.xiaoshan.edu.model.ao.CourseAdjustmentDetailAO;
import com.xiaoshan.edu.model.bo.CourseAdjustmentBO;
import com.xiaoshan.edu.vo.courseadjustment.AdjustmentOrReplaceVO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CourseAdjustmentConvert {

    CourseAdjustmentConvert INSTANCE = Mappers.getMapper(CourseAdjustmentConvert.class);

    /**
     * 模型转换
     * @param courseAdjustmentDetailAo
     * @return
     */
    CourseAdjustmentDO aoToDoDetail(CourseAdjustmentDetailAO courseAdjustmentDetailAo);

    /**
     * 模型转换
     *
     * @param courseAdjustmentDO
     * @return
     */
    CourseAdjustmentDO doToDo(CourseAdjustmentDO courseAdjustmentDO);


    /**
     * 模型转换
     *
     * @param courseAdjustmentDO
     * @return
     */
    AdjustmentOrReplaceVO doToVo(CourseAdjustmentDO courseAdjustmentDO);


    /**
     * 更新DO数据
     *
     * @param source
     * @param update
     */
    void updateDoFromResult(CourseAdjustmentDO source, @MappingTarget CourseAdjustmentDO update);

    /**
     * 更新模型
     *
     * @param courseAdjustmentBO
     * @param courseAdjustmentDO
     */
    void updateCourseAdjustment(CourseAdjustmentBO courseAdjustmentBO, @MappingTarget CourseAdjustmentDO courseAdjustmentDO);
}
