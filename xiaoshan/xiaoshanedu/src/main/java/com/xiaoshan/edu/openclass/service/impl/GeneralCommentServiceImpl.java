package com.xiaoshan.edu.openclass.service.impl;

import com.xiaoshan.edu.openclass.dao.GeneralCommentDao;
import com.xiaoshan.edu.openclass.entity.GeneralCommentDO;
import com.xiaoshan.edu.openclass.entity.OpenClassPersonDO;
import com.xiaoshan.edu.openclass.service.GeneralCommentService;
import com.xiaoshan.edu.openclass.service.OpenClassPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.service.impl.SqlBaseServiceImpl;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;

import java.util.HashMap;
import java.util.List;

@Service("generalEvaluationService")
public class GeneralCommentServiceImpl extends SqlBaseServiceImpl<GeneralCommentDO,Long>
implements GeneralCommentService {

	private GeneralCommentDao generalCommentDao;
	@Autowired
    private OpenClassPersonService personService;
	
	public GeneralCommentServiceImpl(@Qualifier("generalEvaluationDao") GeneralCommentDao generalCommentDao) {
		super(generalCommentDao);
		this.generalCommentDao = generalCommentDao;
	}


    @Override
    public Double updateScore(Long openClassId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("open_class_id",openClassId);
        List<OpenClassPersonDO> list = personService.queryForMap(OpenClassPersonDO.class, map);
        Integer expression = 0;
        Integer interaction = 0;
        Integer timeAllocation = 0;
        Integer blackboardContent = 0;
        Integer informationize = 0;
        int count = 0;
        for (OpenClassPersonDO d : list) {
            if (!d.getEvaluated()){
                continue;
            }
            expression += d.getExpression();
            interaction += d.getInteraction();
            timeAllocation += d.getTimeAllocation();
            blackboardContent += d.getBlackboardContent();
            informationize += d.getInformationize();
            count++;
        }
        double value = (expression + informationize + timeAllocation + blackboardContent + interaction) * 1.0 / count;
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.set("expression",expression*1.0/count);
        updateWrapper.set("interaction",interaction*1.0/count);
        updateWrapper.set("time_allocation",timeAllocation*1.0/count);
        updateWrapper.set("blackboard_content",blackboardContent*1.0/count);
        updateWrapper.set("informationize",informationize*1.0/count);
        updateWrapper.set("total_score",value);
        updateWrapper.set("total_people",count);
        updateWrapper.eq("open_class_id",openClassId);
        generalCommentDao.executeUpdate(updateWrapper);
        return value;
    }

    @Override
    public void removeByOpenClassId(Long id) {
        DeleteWrapper wrapper = new DeleteWrapper();
        wrapper.eq("open_class_id",id);
        generalCommentDao.executeUpdate(wrapper);
    }

    @Override
    public GeneralCommentDO getByOpenClassId(Long openClassId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("open_class_id",openClassId);
        return get(generalCommentDao.queryForList(wrapper));
    }
}
