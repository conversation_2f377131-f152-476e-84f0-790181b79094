package com.xiaoshan.edu.model.bo;

import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CourseAdjustmentBO {
    private String courseTime;
    private String adjustedCourseTime;
    private String courseTimeStartTime;
    private String courseTimeEndTime;
    private String adjustedCourseTimeStartTime;
    private String adjustedCourseTimeEndTime;
    private Long fatherId;
    private Long adjustedFatherId;
    private AdjustmentType fatherType;
    private AdjustmentType adjustedFatherType;
    private String originCourseTime;
    private String adjustedOriginCourseTime;
    private Long originTeacherId;
    private String beforeOriginCourseTime;
    private String adjustedBeforeOriginCourseTime;
    private Long adjustedOriginTeacherId;

    public CourseAdjustmentBO() {
        this.fatherId = 0L;
        this.adjustedFatherId = 0L;
    }
}
