package com.xiaoshan.edu.model.ao;

import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CourseTimeAO {
    @ApiModelProperty("课程时间：如：第一周周一第一节")
    private String courseTime;
    @ApiModelProperty("课程开始时间：如：8:00")
    private String courseStartTime;
    @ApiModelProperty("课程结束时间：如：9:00")
    private String courseEndTime;

    public Integer getWeekNumber() {
        int index = getIndex();
        String weekStr = courseTime.substring(1, index);
        return CourseAdjustmentUtils.chinese2Number(weekStr);
    }

    public Integer getIndex() {
        return courseTime.indexOf("周周");
    }

    public String getWeek() {
        return courseTime.substring(getIndex() + 1, getIndex() + 3);
    }

    public String getSection() {
        return courseTime.substring(getIndex() + 3, getIndex() + 6);
    }
}
