package com.xiaoshan.edu.model.ao;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class GetReplaceTeacherListAO {
    @ApiModelProperty("课程教师ID")
    @NotNull
    private Long teacherId;

    @ApiModelProperty("代课节次详情列表")
    @NotNull
    private List<ReplaceCourseDetailAO> replaceCourseDetailList;

    @ApiModelProperty("课程")
    @NotNull
    @NotEmpty
    private String courseName;
}
