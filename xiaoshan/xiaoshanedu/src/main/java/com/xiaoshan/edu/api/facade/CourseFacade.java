package com.xiaoshan.edu.api.facade;

import com.xiaoshan.basic.ao.CourseQuery;
import com.xiaoshan.basic.vo.CourseVO;
import com.xiaoshan.basic.vo.StudentCourseListVO;
import com.xiaoshan.basic.vo.StudentVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CourseFacade {

    /**
     * 获取选修课信息
     *
     * @param courseQuery
     * @return
     */
    List<CourseVO> postCourseInfo(CourseQuery courseQuery);


    /**
     * 获取课程关联学生
     *
     * @param courseCode
     * @return
     */
    List<StudentVO> getCourseStudents(String courseCode);


    /**
     *根据学生查询课程列表
     * @param studentIds
     * @return
     */
    List<StudentCourseListVO> studentCourseList(List<Long> studentIds);
}
