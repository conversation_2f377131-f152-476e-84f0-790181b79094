package com.xiaoshan.edu.survey.entity;

import java.util.List;
import java.util.Objects;

import com.xiaoshan.edu.converter.survey.QuestionOptionListConverterEditor;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.ScriptConverter;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.converter.FieldJson;

@Getter@Setter@ToString
@Entity("taskTemplateStudentTeacherQuestion")
@Table("sur_task_template_student_teacher_question")
@TableDef(comment = "调查任务--调查模板--学生--教师--题目关联表")
public class TaskTemplateStudentTeacherQuestionDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;

	public TaskTemplateStudentTeacherQuestionDO(){}

	@ColumnDef(comment = "调查任务id")
	private Long taskId;

	@ColumnDef(comment = "调查模板id")
	private Long templateId;

	@ColumnDef(comment = "学生id")
	private Long studentId;

	@ColumnDef(comment = "教师id")
	private Long teacherId;

	@ColumnDef(comment = "教师姓名")
	private String teacherName;

	@ColumnDef(comment = "任课教师教的课程", isNull = true)
	private String curriculum;

	@ColumnDef(comment = "班级，如果是班主任模板，则填班主任班级，如果是任课教师模板，则填学生的班级")
	private String className;

	@ColumnDef(comment = "题目id")
	private Long questionId;

	@ColumnDef(comment = "题目")
	private String questionTitle;

	@ColumnDef(comment = "题号")
	private Integer questionSort;

	@ColumnDef(comment = "题目类型")
	private QuestionType questionType;

	@ColumnDef(comment = "所选答案")
	@ScriptConverter(FieldJson.class)
	@PropertyConverter(QuestionOptionListConverterEditor.class)
	private List<QuestionOptionDTO> answer;

	@ColumnDef(comment = "结果是否教师可见", defaultValue = "'true'")
	private Boolean isVisible;

	@ColumnDef(comment = "是否已经完成填写", defaultValue = "'false'")
	private Boolean isFinish;

	@ColumnDef(comment = "是否已经提交问卷", defaultValue = "'false'")
	private Boolean isSubmit;

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof TaskTemplateStudentTeacherQuestionDO)) {
			return false;
		}
		if (!super.equals(o)) {
			return false;
		}
		TaskTemplateStudentTeacherQuestionDO that = (TaskTemplateStudentTeacherQuestionDO) o;
		return Objects.equals(taskId, that.taskId) && Objects.equals(templateId, that.templateId) && Objects.equals(studentId, that.studentId) && Objects.equals(teacherId, that.teacherId) && Objects.equals(teacherName, that.teacherName) && Objects.equals(curriculum, that.curriculum) && Objects.equals(className, that.className) && Objects.equals(questionId, that.questionId) && Objects.equals(questionTitle, that.questionTitle) && Objects.equals(questionSort, that.questionSort) && questionType == that.questionType && Objects.equals(answer, that.answer) && Objects.equals(isVisible, that.isVisible) && Objects.equals(isFinish, that.isFinish) && Objects.equals(isSubmit, that.isSubmit);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), taskId, templateId, studentId, teacherId, teacherName, curriculum, className, questionId, questionTitle, questionSort, questionType, answer, isVisible, isFinish, isSubmit);
	}
}
