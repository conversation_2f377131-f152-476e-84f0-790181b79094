package com.xiaoshan.edu.openclass.service;

import com.xiaoshan.edu.openclass.entity.OpenClassPersonDO;
import com.xiaoshan.edu.vo.openclass.TeacherInfoVO;
import start.framework.service.SqlBaseService;

import java.util.List;

public interface OpenClassPersonService extends SqlBaseService<OpenClassPersonDO,Long> {

    /**
     * 根据公开课id和教师userId判断是否存在记录
     * @param openClassId
     * @param teacherUserId
     * @return
     */
    Boolean exist(Long openClassId, Long teacherUserId);

    /**
     * 根据公开课id删除
     * @param id
     */
    void removeByOpenClassId(Long id);

    /**
     * 根据教师userId和公开课id删除
     * @param openClassId
     * @param userId
     */
    void removeByUserIdAndOpenClassId(Long userId, Long openClassId);

    /**
     * 根据公开课id和用户id获取个人记录，获取不到返回null
     * @param openClassId
     * @param userId
     * @return
     */
    OpenClassPersonDO getByOpenClassIdAndUserId(Long openClassId, Long userId);

    /**
     * 根据公开课id获取个人评价列表
     * @param openClassId
     * @return
     */
    List<OpenClassPersonDO> getByOpenClassId(Long openClassId);

    /**
     * 获取听课教师列表
     * @param openClassId
     * @return
     */
    List<TeacherInfoVO> getListenList(Long openClassId);
}
