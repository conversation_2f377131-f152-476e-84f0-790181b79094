package com.xiaoshan.edu.survey.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.framework.entity.BaseV2Ext;
import start.magic.core.converter.PropertyConverter;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.ScriptConverter;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.converter.FieldText;

import java.util.Date;

@Getter@Setter@ToString
@Entity("task")
@Table("sur_task")
@TableDef(comment = "调查任务表", charset = "utf8mb4", collate = "utf8mb4_bin")
public class TaskDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;

	public TaskDO(){}

	@ColumnDef(comment = "开始学年")
	private Integer startYear;

	@ColumnDef(comment = "结束学年")
	private Integer endYear;

	@ColumnDef(comment = "学期，1-第一学期，2-第二学期")
	private Integer term;

	@ColumnDef(comment = "调查名称", length = 30)
	private String name;

	@ColumnDef(comment = "调查年级，初一到高三")
	private String grade;

	@ColumnDef(comment = "调查开始时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date startDate;

	@ColumnDef(comment = "调查结束时间")
	@PropertyConverter(DateTimefConverterEditor.class)
	private Date endDate;

	@ColumnDef(comment = "调查提醒")
	private String reminder;

	@ColumnDef(comment = "调查说明", isNull = true)
	@ScriptConverter(FieldText.class)
	private String explanation;

	@ColumnDef(comment = "学生总数")
	private Integer total;

	@ColumnDef(comment = "是否公布", defaultValue = "'false'")
	private Boolean isPublish;
}
