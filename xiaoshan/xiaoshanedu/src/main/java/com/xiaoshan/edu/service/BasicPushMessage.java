package com.xiaoshan.edu.service;

import com.alibaba.fastjson.JSONObject;
import com.common.utils.DateUtils;
import com.xiaoshan.basic.ao.MessageParentAO;
import com.xiaoshan.basic.dto.MessageDTO;
import com.xiaoshan.basic.enums.MessageTypeEnum;
import com.xiaoshan.basic.enums.PushTypeEnum;
import com.xiaoshan.basic.enums.SourceTypeEnum;
import com.xiaoshan.basic.enums.TemplatePushTypeEnum;
import com.xiaoshan.basic.vo.PushItemVO;
import com.xiaoshan.basic.vo.PushTemplateVO;
import com.xiaoshan.basic.vo.TeacherVO;
import com.xiaoshan.edu.ao.courseadjustment.AdjustmentCancelMessageAO;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.MessageFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import com.xiaoshan.edu.enums.patrol.NotifyObject;
import com.xiaoshan.edu.enums.patrol.PatrolType;
import com.xiaoshan.edu.openclass.entity.OpenClassDO;
import com.xiaoshan.edu.patrol.entity.PatrolDO;
import com.xiaoshan.edu.survey.entity.TaskDO;
import com.xiaoshan.oa.dto.StudentDto;
import com.xiaoshan.oa.vo.notify.NotifyParentVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;
import start.magic.utils.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("deprecation")
@Service
public class BasicPushMessage {

    @Autowired
    private MessageFacade messageFacade;
    @Autowired
    private SettingFacade settingFacade;
    @Autowired
    private FoundationFacade foundationFacade;

    protected final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Value("${xiaoshan.weixin.appid}")
    private String weixinAppId;


    // ==================== 公开课消息 ================================

    @Value("${xiaoshan.open_class.source_id}")
    private Long openClassSourceId;
    @Value("${xiaoshan.open_class.personal.source_id}")
    private Long openClassPersonSourceId;
    @Value("${xiaoshan.push_item.message.edu_open_class_publish}")
    private String eduOpenClassPublish;
    @Value("${xiaoshan.push_item.message.edu_open_class_update}")
    private String eduOpenClassUpdate;
    @Value("${xiaoshan.open_class.approval.path}")
    private String eduOpenClassDetailPath;

    private final String OPEN_COURSE_DETAIL_PAGE = "/subpackages/teaching-manage/pages/openclassManager/detail/detail";

    /**
     * 上课详情地址
     */
    private final String CLASS_DETAIL = "/subpackages/teaching-manage/pages/openclass/detail/detail";
    /**
     * 听课详情地址
     */
    private final String LISTEN_DETAIL = "/subpackages/teaching-manage/pages/openclass/classdetail/classdetail";


    // 管理员发布公开课  // 发给所有老师，跳公开课广场
    public void openClassPublish(OpenClassDO openClassDO) {
        String teacherName = openClassDO.getTeacherName();
        String courseName = openClassDO.getCourseName();
        String type = openClassDO.getType().getDescription();

        List<TeacherVO> teachers = foundationFacade.getTeachers();
        //听课教师
        List<Long> listenUserIds = teachers.stream().map(TeacherVO::getUserId).collect(Collectors.toList());
        //上课教师
        List<Long> classTeacher = Collections.singletonList(openClassDO.getTeacherUserId());
        listenUserIds.removeAll(classTeacher);
        //发消息给听课教师
        sendToTeacher(openClassDO, teacherName, courseName, type, listenUserIds,LISTEN_DETAIL);
        //发消息给上课教师
        sendToTeacher(openClassDO, teacherName, courseName, type, classTeacher,CLASS_DETAIL);
    }

    private void sendToTeacher(OpenClassDO openClassDO, String teacherName, String courseName, String type, List<Long> listenUserIds,String url) {
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(eduOpenClassPublish);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(eduOpenClassDetailPath);
                    dto.setQuery("id=" + openClassDO.getId());
                    dto.setSmsContent(String.format(template.getContent(), teacherName, courseName));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(template.getTitle());
                    dto.setContent(String.format(template.getContent(), teacherName, type, courseName));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(openClassDO.getId()));
                    json.put("relatedType", "7");
                    json.put("publish","true");
                    json.put("teacherId", openClassDO.getTeacherId());
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", openClassDO.getTeacherName() + "的" + openClassDO.getType().getDescription() + "《" + openClassDO.getCourseName() + "》" +  "信息已发布，请及时查看");
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", "《" + openClassDO.getCourseName() + "》" + "已发布");
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", openClassDO.getId());
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    keyword3.put("value", sdf.format(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    wxObject.put("data", dataObject);

                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath",  url + "?openClassId=" + openClassDO.getId() + "&from=msg");
                    wxObject.put("miniprogram", miniprogram);
                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(openClassSourceId);
        dto.setTargetUserIds(listenUserIds);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessage(dto);
    }

    // 管理员更新公开课  // 如果是上课老师，跳到上课记录，如果是听课老师，跳到听课记录
    public void openClassUpdate(OpenClassDO openClassDO, List<Long> teacherUserIds, Boolean isListenTeacher) {
        String teacherName = openClassDO.getTeacherName();
        String courseName = openClassDO.getCourseName();
        String type = openClassDO.getType().getDescription();
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(eduOpenClassUpdate);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(eduOpenClassDetailPath);
                    dto.setQuery("id=" + openClassDO.getId());
                    dto.setSmsContent(String.format(template.getContent(), teacherName, type, courseName));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(template.getTitle());
                    dto.setContent(String.format(template.getContent(), teacherName, type, courseName));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(openClassDO.getId()));
                    json.put("relatedType", "7");
                    json.put("isListenTeacher", isListenTeacher);
                    json.put("teacherId", openClassDO.getTeacherId());
                    json.put("update", "true");
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", openClassDO.getTeacherName() + "的" + openClassDO.getType().getDescription() + "《" + openClassDO.getCourseName() + "》" +  "信息有更新，请及时查看");
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", "《" + openClassDO.getCourseName() + "》" + "信息更新");
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", openClassDO.getId());
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    keyword3.put("value", sdf.format(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);

                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    if (isListenTeacher) {
                        miniprogram.put("pagepath",  LISTEN_DETAIL + "?openClassId=" + openClassDO.getId() + "&from=msg");
                    }else {
                        miniprogram.put("pagepath",  CLASS_DETAIL + "?openClassId=" + openClassDO.getId() + "&from=msg");
                    }
                    wxObject.put("miniprogram", miniprogram);

                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(openClassPersonSourceId);
        dto.setTargetUserIds(teacherUserIds);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessage(dto);
    }


    // ==================== 教师巡课通知 ================================

    @Value("${xiaoshan.patrol.source_id}")
    private Long patrolSourceId;
    @Value("${xiaoshan.patrol.parent.source_id}")
    private Long patrolParentSourceId;
    @Value("${xiaoshan.push_item.message.edu_patrol_publish}")
    private String eduPatrolPublish;
    @Value("${xiaoshan.patrol.relatedType}")
    private String patrolRelatedType;
    @Value("${xiaoshan.patrol.parent.relatedType}")
    private String parentPatrolRelatedType;
    @Value("${xiaoshan.miniProgramUrl.patrol_teacher.detail.path}")
    private String patrolTeacherMiniProgramUrl;
    @Value("${xiaoshan.miniProgramUrl.patrol_parents.detail.path}")
    private String patrolParentsMiniProgramUrl;

    // 教师巡课通知
    public void patrolNotify(PatrolDO patrolDO, NotifyObject notifyObject, List<Long> userIds, StudentDto student, String name) {
        String title = "";
        String content = "";
        String appraiseStr = patrolDO.getAppraise().getName();
        String relatedType = patrolRelatedType;
        Long sourceId = patrolSourceId;
        String miniProgramUrl = patrolTeacherMiniProgramUrl;
        Long stuId = null;
        switch (notifyObject) {
            case CLASS_ADVISER:
                // 班主任
                if (patrolDO.getType().equals(PatrolType.STUDENT)) {
                    title = "您的学生有条巡课记录，请查看！";
                    content = "您的学生在" + patrolDO.getCourseTime() + "的" + patrolDO.getCourseName() + "课上，" + appraiseStr + "，请及时查看！";
                } else if (patrolDO.getType().equals(PatrolType.TEACHER)) {
                    title = "巡课通知";
                    if (!StringUtils.isEmpty(name)){
                        content = name + "老师在" + patrolDO.getCourseTime() + patrolDO.getCourseName() + "课巡课评价为" + appraiseStr + "，理由是：" + patrolDO.getContent();
                    } else {
                        content = patrolDO.getClassName() + patrolDO.getCourseTime() + patrolDO.getCourseName() + "课巡课评价为" + appraiseStr + "，理由是：" + patrolDO.getContent();
                    }
                } else {
                    title = "巡课通知";
                    content = patrolDO.getClassName() + patrolDO.getCourseTime() + patrolDO.getCourseName() + "课巡课评价为" + appraiseStr + "，理由是：" + patrolDO.getContent();
                }
                break;
            case COURSE_TEACHER:
                // 任课老师
                title = "巡课通知";
                content = "您在" + patrolDO.getCourseTime() + patrolDO.getClassName() + patrolDO.getCourseName() + "课巡课评价为" + appraiseStr + "，理由是：" + patrolDO.getContent();
                break;
            case PARENT:
                // 家长
                title = "您的孩子有条巡课记录，请查看！";
                content = "您的孩子在" + patrolDO.getCourseTime() + "的" + patrolDO.getCourseName() + "课上，" + appraiseStr + "，请及时查看！";
                relatedType = parentPatrolRelatedType;
                sourceId = patrolParentSourceId;
                miniProgramUrl = patrolParentsMiniProgramUrl;
                if (student != null) {
                    stuId = student.getId();
                }
                break;
            default:
        }

        String shortContent;
        int contentLength = 50;
        if (content.length() > contentLength) {
            shortContent = content.substring(0, 50) + "...";
        } else {
            shortContent = content;
        }
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(eduPatrolPublish);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(miniProgramUrl);
                    dto.setQuery("id=" + patrolDO.getId());
                    dto.setSmsContent(String.format(template.getContent(), shortContent));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(title);
                    dto.setContent(String.format(template.getContent(), content));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", patrolDO.getId());
                    json.put("relatedType", relatedType);
                    json.put("status", "PUBLISH");
                    if (stuId != null){
                        json.put("stuId", stuId);
                    }
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", shortContent);
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", title);
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", patrolDO.getId());
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", DateUtils.formatDateTime(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看巡课详细信息");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath", miniProgramUrl + "?id=" + patrolDO.getId());
                    wxObject.put("miniprogram", miniprogram);
                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(sourceId);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        if (userIds != null && userIds.size() > 0) {
            dto.setTargetUserIds(userIds);
            String jsonData = JSONObject.toJSONString(dto);
            LOGGER.info("发送巡课消息json：" + jsonData);
            messageFacade.createMessage(dto);
        }
        if (student != null) {
            List<MessageParentAO> messageParents = new ArrayList<>();
            List<NotifyParentVO> parents = student.getParents();
            if (parents != null && parents.size() > 0) {
                for (NotifyParentVO parent : parents) {
                    MessageParentAO ao = new MessageParentAO();
                    ao.setUserId(parent.getUserId());
                    ao.setStudentId(student.getId());
                    messageParents.add(ao);
                }
            }
            if (messageParents.size() > 0) {
                dto.setTargetUserIds(null);
                dto.setMessageParents(messageParents);
                String jsonData = JSONObject.toJSONString(dto);
                LOGGER.info("发送家长消息json：" + jsonData);
                messageFacade.createMessageToParent(dto);
            }
        }
    }

    /**
     * 获取催办公众号模板内容
     *
     * @param content      详细内容
     * @param teacherName  催办人
     * @param type         类型
     * @param appletPath   小程序跳转地址
     * @param id           跳转业务id
     * @param pushItemCode 推送事项编码
     * @return 模板内容
     */
    public String getOfficialAccountModel(String content, String teacherName, String type, String appletPath, Long id, String pushItemCode) {
        PushItemVO items = settingFacade.pushItems(pushItemCode);
        JsonObject wxObject = new JsonObject();
        // 封装推送事项
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", content);
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", teacherName);
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", "点击查看详情！");
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", DateUtils.formatDateTime(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看" + type + "详细信息");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    if (type.equals(AdjustmentType.ADJUSTMENT.getName())){
                        type = AdjustmentType.ADJUSTMENT.getType().toString();
                    } else if (type.equals(AdjustmentType.REPLACE.getName())) {
                        type = AdjustmentType.REPLACE.getType().toString();
                    }
                    miniprogram.put("pagepath", String.format("%s?id=%s&type=%s&from=msg", appletPath, id, type));
                    wxObject.put("miniprogram", miniprogram);
                }
            }
        }
        return wxObject.toString();
    }

    /**
     * 获取催办公众号模板内容
     *
     * @param content      详细内容
     * @param teacherName  催办人
     * @param type         类型
     * @param id           跳转业务id
     * @param pushItemCode 推送事项编码
     * @return 模板内容
     */
    public String getTemplateForOpenClass(String content, String teacherName, String type, Long id, String pushItemCode) {
        PushItemVO items = settingFacade.pushItems(pushItemCode);
        JsonObject wxObject = new JsonObject();
        // 封装推送事项
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", content);
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", teacherName);
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", "公开课申请审批");
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", DateUtils.formatDateTime(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    if (type.equals(AdjustmentType.ADJUSTMENT.getName())){
                        type = AdjustmentType.ADJUSTMENT.getType().toString();
                    } else if (type.equals(AdjustmentType.REPLACE.getName())) {
                        type = AdjustmentType.REPLACE.getType().toString();
                    }
                    miniprogram.put("pagepath", CLASS_DETAIL + "?openClassId=" + id + "&from=msg");
                    wxObject.put("miniprogram", miniprogram);
                }
            }
        }
        return wxObject.toString();
    }

    // ==================== 调代课通知 ================================

    @Value("${xiaoshan.course_adjustment.source_id}")
    private Long adjustmentSourceId;
    @Value("${xiaoshan.course_adjustment.related_type}")
    private String adjustmentRelatedType;
    @Value("${xiaoshan.push_item.message.edu_course_adjustment_pass}")
    private String adjustmentPassPublishItem;
    @Value("${xiaoshan.push_item.message.edu_course_adjustment_auto_cancel}")
    private String adjustmentCancelPublishItem;
    @Value("${xiaoshan.miniProgramUrl.edu_course_adjustment.detail.path}")
    private String adjustmentMiniProgramUrl;


    // 管理端，调代课通过消息推送
    @Async
    public void adjustmentPassPublish(Long adjustmentId, AdjustmentType adjustmentType, List<Long> teacherUserIds) {
        String type = adjustmentType.getName();
        Integer typeNum = adjustmentType.getType();
        String content = "您有一条" + type + "通知，请及时查看！";
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(adjustmentPassPublishItem);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(adjustmentMiniProgramUrl);
                    dto.setQuery(String.format("id=%s&type=%s", adjustmentId, typeNum));
                    dto.setSmsContent(content);
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(String.format(template.getTitle(), type));
                    dto.setContent(content);
                    JsonObject json = new JsonObject();
                    json.put("isAdminApply", 1);
                    json.put("adjustmentType", typeNum);
                    json.put("type", type);
                    json.put("relatedId", String.valueOf(adjustmentId));
                    json.put("relatedType", adjustmentRelatedType);
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", content);
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", type + "通知");
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", adjustmentId);
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", DateUtils.formatDateTime(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看" + type + "详细信息");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath", adjustmentMiniProgramUrl + "?id=" + adjustmentId + "&type=" + typeNum);
                    wxObject.put("miniprogram", miniprogram);
                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(adjustmentSourceId);
        dto.setTargetUserIds(teacherUserIds);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessage(dto);
    }

    // 调代课自动撤销提醒推送
    public void adjustmentCancelPublish(AdjustmentCancelMessageAO ao) {
        Long adjustmentId = ao.getAdjustmentId();
        AdjustmentType adjustmentType = ao.getAdjustmentType();
        Integer typeNum = adjustmentType.getType();
        String adjustedCourseTime = ao.getAdjustedCourseTime();
        Long courseTeacherUserId = ao.getCourseTeacherUserId();
        Long reverseTeacherUserId = ao.getReverseTeacherUserId();
        List<String> replaceCourseTimeList = ao.getReplaceCourseTimeList();
        String cancelType = ao.getType() == 1 ? "课表" : "校历" ;
        String courseTime;
        List<Long> userIds = new ArrayList<>();
        userIds.add(courseTeacherUserId);
        if (adjustmentType.equals(AdjustmentType.ADJUSTMENT)) {
            // 调课
            courseTime = ao.getCourseTime();
        } else {
            // 代课
            courseTime = StringUtils.listToString(replaceCourseTimeList);
            if (reverseTeacherUserId != null) {
                userIds.add(reverseTeacherUserId);
            }
        }
        String type = adjustmentType.getName();
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(adjustmentCancelPublishItem);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(adjustmentMiniProgramUrl);
                    dto.setQuery(String.format("id=%s&type=%s", adjustmentId, typeNum));
                    dto.setSmsContent(String.format(template.getContent(), cancelType, courseTime, type));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(template.getTitle());
                    dto.setContent(String.format(template.getContent(), cancelType, courseTime, type));
                    JsonObject json = new JsonObject();
                    json.put("adjustmentType", typeNum);
                    json.put("type", type);
                    json.put("relatedId", String.valueOf(adjustmentId));
                    json.put("relatedType", adjustmentRelatedType);
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JSONObject jsonObject = JSONObject.parseObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JSONObject wxObject = new JSONObject();
                    wxObject.put("template_id", templateId);
                    JSONObject dataObject = new JSONObject();
                    JSONObject firstObject = new JSONObject();
                    firstObject.put("value", "因" + cancelType +"调整导致" + courseTime + "的" + type + "申请异常，请及时查看。");
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);

                    JSONObject keyword1Object = new JSONObject();
                    keyword1Object.put("value", "调代课异常");
                    keyword1Object.put("color", "#173177");
                    dataObject.put("keyword1", keyword1Object);

                    JSONObject keyword2Object = new JSONObject();
                    keyword2Object.put("value", ao.getAdjustmentId());
                    keyword2Object.put("color", "#173177");
                    dataObject.put("keyword2", keyword2Object);

                    JSONObject keyword3Object = new JSONObject();
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    keyword3Object.put("value", sdf.format(date));
                    keyword3Object.put("color", "#173177");
                    dataObject.put("keyword3", keyword3Object);

                    JSONObject miniprogram = new JSONObject();
                    miniprogram.put("appid", "wx86aa340a09e076a5");
                    miniprogram.put("pagepath", "/subpackages/classSwitch/pages/detail/detail?id=" + adjustmentId +"&type=" + adjustmentType + "&role=record");
                    wxObject.put("miniprogram", miniprogram);

                    wxObject.put("data", dataObject);
                    dto.setOfficeAccountData(wxObject.toJSONString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(adjustmentSourceId);
        dto.setTargetUserIds(userIds);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessage(dto);

        if (adjustmentType.equals(AdjustmentType.ADJUSTMENT) && reverseTeacherUserId != null && adjustedCourseTime != null && !adjustedCourseTime.isEmpty()) {
            // 发送撤销通知给被调课老师
            for (PushTemplateVO template : items.getTemplates()) {
                if (template.getOpenState()) {
                    if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                        //短信
                        dto.setPath(adjustmentMiniProgramUrl);
                        dto.setQuery(String.format("id=%s&type=%s", adjustmentId, typeNum));
                        dto.setSmsContent(String.format(template.getContent(), cancelType, adjustedCourseTime, type));
                    } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                        // 站内
                        dto.setTitle(template.getTitle());
                        dto.setContent(String.format(template.getContent(), cancelType, adjustedCourseTime, type));
                        JsonObject json = new JsonObject();
                        json.put("adjustmentType", typeNum);
                        json.put("type", type);
                        json.put("relatedId", String.valueOf(adjustmentId));
                        json.put("relatedType", adjustmentRelatedType);
                        dto.setExtra(json.toString());
                    } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                        // 公众号
                    }
                }
            }
            dto.setTargetUserIds(Collections.singletonList(reverseTeacherUserId));
            messageFacade.createMessage(dto);
        }
    }


    // ==========================================教学调查通知============================================================
    @Value("${xiaoshan.survey.sourceId.manage}")
    private Long sourceIdManage;
    @Value("${xiaoshan.survey.sourceid.personal}")
    private Long sourceIdPersonal;
    @Value("${xiaoshan.survey.push_item.publish}")
    private String pushItemPublish;
    @Value("${xiaoshan.survey.push_item.result}")
    private String pushItemResult;
    @Value("${xiaoshan.survey.push_item.undo}")
    private String pushItemUndo;
    @Value("${xiaoshan.survey.path.parent}")
    private String pathParent;
    @Value("${xiaoshan.survey.path.teacher}")
    private String pathTeacher;
    // 教学调查关联类型
    private static final int SURVEY_RELATED_TYPE = 24;

    /**
     * 一键提醒未完成学生
     */
    public void surveyUndoMessage(TaskDO taskDO, List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return;
        }
        HashMap<String, String> params = new HashMap<>();
        params.put("current", "1");
        params.put("size", "-1");
        params.put("stuIds", StringUtils.listToString(idList));
        params.put("scope", "2");
        List<StudentDto> students = foundationFacade.studentsPost(params);
        List<MessageParentAO> parentAos = new ArrayList<>();
        for (StudentDto student : students) {
            for (NotifyParentVO parent : student.getParents()) {
                MessageParentAO ao = new MessageParentAO();
                ao.setUserId(parent.getUserId());
                ao.setStudentId(student.getId());
                parentAos.add(ao);
            }
        }
        if (parentAos.isEmpty()) {
            return;
        }

        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(pushItemUndo);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(pathParent);
                    dto.setSmsContent(String.format(template.getContent(), taskDO.getName()));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(template.getTitle());
                    dto.setContent(String.format(template.getContent(), taskDO.getName()));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(taskDO.getId()));
                    json.put("relatedType", SURVEY_RELATED_TYPE);
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JSONObject jsonObject = JSONObject.parseObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JSONObject wxObject = new JSONObject();
                    wxObject.put("template_id", templateId);
                    JSONObject dataObject = new JSONObject();
                    JSONObject firstObject = new JSONObject();
                    firstObject.put("value", taskDO.getName() + "还未完成，请尽快进行填写");
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);

                    JSONObject keyword1Object = new JSONObject();
                    keyword1Object.put("value", "教学调查填写");
                    keyword1Object.put("color", "#173177");
                    dataObject.put("keyword1", keyword1Object);

                    JSONObject keyword2Object = new JSONObject();
                    keyword2Object.put("value", taskDO.getId());
                    keyword2Object.put("color", "#173177");
                    dataObject.put("keyword2", keyword2Object);

                    JSONObject keyword3Object = new JSONObject();
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    keyword3Object.put("value", sdf.format(date));
                    keyword3Object.put("color", "#173177");
                    dataObject.put("keyword3", keyword3Object);

                    JSONObject miniprogram = new JSONObject();
                    miniprogram.put("appid", "wx86aa340a09e076a5");
                    miniprogram.put("pagepath", "/pages/inverstigation-parent/pages/index/index?taskId=" + taskDO.getId() + "&name=" + taskDO.getName() + "&modifiedDate=" + DateUtils.formatDateTime(taskDO.getModifiedDate()));
                    wxObject.put("miniprogram", miniprogram);

                    wxObject.put("data", dataObject);
                    dto.setOfficeAccountData(wxObject.toJSONString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(sourceIdManage);
        dto.setTargetUserIds(null);
        dto.setMessageParents(parentAos);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessageToParent(dto);
    }

    /**
     * 发布教学调查，发消息给家长
     */
    public void surveyPublish(TaskDO taskDO, List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return;
        }
        HashMap<String, String> params = new HashMap<>();
        params.put("current", "1");
        params.put("size", "-1");
        params.put("stuIds", StringUtils.listToString(idList));
        params.put("scope", "2");
        List<StudentDto> students = foundationFacade.studentsPost(params);
        List<MessageParentAO> parentAos = new ArrayList<>();
        for (StudentDto student : students) {
            for (NotifyParentVO parent : student.getParents()) {
                MessageParentAO ao = new MessageParentAO();
                ao.setUserId(parent.getUserId());
                ao.setStudentId(student.getId());
                parentAos.add(ao);
            }
        }
        if (parentAos.isEmpty()) {
            return;
        }

        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(pushItemPublish);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn =false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(pathParent);
                    dto.setSmsContent(String.format(template.getContent(), taskDO.getName()));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(template.getTitle());
                    dto.setContent(String.format(template.getContent(), taskDO.getName()));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(taskDO.getId()));
                    json.put("relatedType", SURVEY_RELATED_TYPE);
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", taskDO.getName() + "已发布，请在规定时间内登录小程序进行填写");
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", "教学调查发布通知");
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", String.valueOf(taskDO.getId()));
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", DateUtils.formatDateTime(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看调查任务");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath", pathParent);
                    wxObject.put("miniprogram", miniprogram);
                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(sourceIdManage);
        dto.setTargetUserIds(null);
        dto.setMessageParents(parentAos);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessageToParent(dto);
    }

    /**
     * 公布教学调查，发消息给教师个人
     */
    public void surveyResult(TaskDO taskDO, List<Long> userIds) {

        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(pushItemResult);
        boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(pathTeacher);
                    dto.setQuery(String.format("taskId=%s", taskDO.getId()));
                    dto.setSmsContent(String.format(template.getContent(), taskDO.getName()));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(template.getTitle());
                    dto.setContent(String.format(template.getContent(), taskDO.getName()));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(taskDO.getId()));
                    json.put("relatedType", SURVEY_RELATED_TYPE);
                    json.put("name", taskDO.getName());
                    json.put("modifiedDate", DateUtils.formatDateTime(taskDO.getModifiedDate()));
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);

                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", "教学调查结果查看");
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", taskDO.getName());
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", String.valueOf(taskDO.getId()));
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", DateUtils.formatDateTime(new Date()));
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看调查结果");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath", pathTeacher + String.format("?taskId=%s", taskDO.getId()));
                    wxObject.put("miniprogram", miniprogram);
                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(sourceIdPersonal);
        dto.setTargetUserIds(userIds);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        messageFacade.createMessage(dto);
    }

}
