package com.xiaoshan.edu.openclass.service.impl;

import com.common.utils.ClassConverter;
import com.topnetwork.ao.PageAO;
import com.xiaoshan.basic.dto.ClassRoomTreeDTO;
import com.xiaoshan.basic.vo.RoomClassTreeDTO;
import com.xiaoshan.edu.ao.openclass.CloudRoomAO;
import com.xiaoshan.edu.ao.openclass.RoomNameAO;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import com.xiaoshan.edu.model.ClassFloorTreeDTO;
import com.xiaoshan.edu.model.RoomClassFloorTreeVO;
import com.xiaoshan.edu.model.RoomClassVO;
import com.xiaoshan.edu.openclass.dao.CloudRoomDao;
import com.xiaoshan.edu.openclass.entity.CloudRoomDO;
import com.xiaoshan.edu.openclass.service.CloudRoomService;
import com.xiaoshan.edu.vo.openclass.CloudRoomVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import start.framework.commons.result.QueryResult;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service("cloudRoomService")
public class CloudRoomServiceImpl extends SqlBaseServiceImplV2Ext<CloudRoomDO,Long> implements CloudRoomService{

	@SuppressWarnings("unused")
	private CloudRoomDao cloudRoomDao;

	@Autowired
	private SettingFacade settingFacade;

	public CloudRoomServiceImpl(@Qualifier("cloudRoomDao")CloudRoomDao cloudRoomDao) {
		super(cloudRoomDao);
		this.cloudRoomDao=cloudRoomDao;
	}

	/**
	 * 获取云课堂教室列表
	 */
	@Override
	public QueryResult<List<CloudRoomVO>> getCloudRoom(PageAO ao) {
		return queryForPage(CloudRoomVO.class, ao);
	}

	/**
	 * 获取云课堂教室树形结构
	 */
    @Override
    public List<ClassBuildingTreeDTO> getCloudRoomTree() {
		ArrayList<ClassBuildingTreeDTO> res = new ArrayList<>();
		List<CloudRoomDO> list = queryForAll();

		// 根据楼宇名称和楼层名称分组
		Map<String, Map<String, List<CloudRoomDO>>> collect = list.stream()
				.collect(Collectors.groupingBy(CloudRoomDO::getBuildingName, Collectors.groupingBy(CloudRoomDO::getFloorName)));

		for (Map.Entry<String, Map<String, List<CloudRoomDO>>> buildEntry : collect.entrySet()) {
			// 同一楼宇
			ClassBuildingTreeDTO dto = new ClassBuildingTreeDTO();
			dto.setBuildingName(buildEntry.getKey());
			List<ClassFloorTreeDTO> floorList = new ArrayList<>();

			for (Map.Entry<String, List<CloudRoomDO>> floorEntry : buildEntry.getValue().entrySet()) {
				// 同一楼层
				ClassFloorTreeDTO floorDto = new ClassFloorTreeDTO();
				floorDto.setFloorName(floorEntry.getKey());
				List<ClassRoomTreeDTO> roomList = new ArrayList<>();

				for (CloudRoomDO cloudRoomDO : floorEntry.getValue()) {
					// 同一楼层下的所有房间
					ClassRoomTreeDTO classRoomDto = new ClassRoomTreeDTO();
					classRoomDto.setRoomName(cloudRoomDO.getRoomName());
					roomList.add(classRoomDto);
				}
				floorDto.setClassRoomTreeDTOList(roomList);
				floorList.add(floorDto);
			}

			dto.setClassFloorTreeDTOList(floorList);
			res.add(dto);
		}
		return res;
    }

	/**
	 * 添加云课堂
	 */
    @Override
    public void addCloudRoom(List<CloudRoomAO> ao) {
		List<CloudRoomDO> doList = queryForAll();
		List<CloudRoomDO> aoList = ao.stream().map(item -> ClassConverter.aTob(item, new CloudRoomDO())).collect(Collectors.toList());
		if (aoList.size() > 0){
			aoList.removeIf(doList::contains);
		}
		saveBatch(aoList);
	}

	/**
	 * 获取所有房间，除去云课堂房间
	 */
    @Override
    public List<CloudRoomVO> all(RoomNameAO ao) {
		List<RoomClassVO> roomsTree = settingFacade.getRoomsTree();
		Set<CloudRoomVO> cloudRoomVos = new HashSet<>(queryForAll(CloudRoomVO.class));

		List<CloudRoomVO> res = new ArrayList<>();
		for (RoomClassVO roomClassVO : roomsTree) {
			for (RoomClassFloorTreeVO floorTreeDTO : roomClassVO.getFloorTreeDTOList()) {
				for (RoomClassTreeDTO roomTreeDTO : floorTreeDTO.getRoomClassTreeDTOList()) {
					CloudRoomVO vo = new CloudRoomVO(roomClassVO.getBuildingName(), floorTreeDTO.getFloorName(), roomTreeDTO.getRoomName());
					if (!cloudRoomVos.contains(vo)){
						if (!StringUtils.isEmpty(ao.getBuildingName()) && !vo.getBuildingName().equals(ao.getBuildingName())){
							continue;
						}
						if (!StringUtils.isEmpty(ao.getFloorName()) && !vo.getFloorName().equals(ao.getFloorName())){
							continue;
						}
						if (!StringUtils.isEmpty(ao.getRoomName()) && !vo.getRoomName().equals(ao.getRoomName())){
							continue;
						}
						res.add(vo);
					}
				}
			}
		}

		return res;
    }

}
