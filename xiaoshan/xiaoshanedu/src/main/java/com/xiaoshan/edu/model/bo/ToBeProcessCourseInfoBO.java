package com.xiaoshan.edu.model.bo;

import com.xiaoshan.edu.courseadjustment.dto.ReplaceAndSectionDTO;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 需要被处理的调代课记录信息
 *
 * <AUTHOR>
 */
@Data
public class ToBeProcessCourseInfoBO {

    /**
     * 需要取消的调代课记录
     */
    List<CourseAdjustmentDO> needCancelCourseAdjustmentDoList;

    /**
     * 需要取消的代课节次信息
     */
    Set<ReplaceAndSectionDTO> needCancelReplaceAndSectionDtoSet;

    /**
     * 需要设置失效的调代课记录
     */
    List<CourseAdjustmentDO> needInvalidCourseAdjustmentDoList;

    /**
     * 需要设置失效的代课记录
     */
    Set<ReplaceAndSectionDTO> needInvalidReplaceAndSectionDtoSet;

    public ToBeProcessCourseInfoBO() {
        this.needCancelCourseAdjustmentDoList = new ArrayList<>();
        this.needCancelReplaceAndSectionDtoSet = new HashSet<>();
        this.needInvalidCourseAdjustmentDoList = new ArrayList<>();
        this.needInvalidReplaceAndSectionDtoSet = new HashSet<>();
    }

    /**
     * 添加需要取消的调代课记录
     *
     * @param courseAdjustmentDO
     */
    public void addNeedCancelCourseAdjustment(CourseAdjustmentDO courseAdjustmentDO) {
        this.needCancelCourseAdjustmentDoList.add(courseAdjustmentDO);
    }


    /**
     * 添加需要设置失效的调代课记录
     *
     * @param courseAdjustmentDO
     */
    public void addNeedInvalidCourseAdjustment(CourseAdjustmentDO courseAdjustmentDO) {
        this.needInvalidCourseAdjustmentDoList.add(courseAdjustmentDO);
    }

    /**
     * 添加需要取消的代课记录
     *
     * @param replaceAndSectionDTO
     */
    public void addNeedCancelReplaceAndSection(ReplaceAndSectionDTO replaceAndSectionDTO) {
        this.needCancelReplaceAndSectionDtoSet.add(replaceAndSectionDTO);
    }


    /**
     * 添加需要取消的代课记录
     *
     * @param replaceAndSectionDTO
     */
    public void addNeedInvalidReplaceAndSection(ReplaceAndSectionDTO replaceAndSectionDTO) {
        this.needInvalidReplaceAndSectionDtoSet.add(replaceAndSectionDTO);
    }
}
