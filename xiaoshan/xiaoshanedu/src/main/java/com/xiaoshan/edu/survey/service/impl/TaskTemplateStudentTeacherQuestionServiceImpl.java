package com.xiaoshan.edu.survey.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.common.utils.ResponseUtils;
import com.xiaoshan.edu.ao.survey.*;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.dto.survey.PercentageDTO;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import com.xiaoshan.edu.enums.survey.StatisticType;
import com.xiaoshan.edu.enums.survey.SurveyObj;
import com.xiaoshan.edu.survey.dao.TaskTemplateStudentTeacherQuestionDao;
import com.xiaoshan.edu.survey.entity.TaskDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.*;
import com.xiaoshan.edu.survey.utils.DataComputeUtil;
import com.xiaoshan.edu.survey.utils.DateExportUtil;
import com.xiaoshan.edu.survey.utils.excel.ClassResultImportListener;
import com.xiaoshan.edu.survey.utils.excel.CurriculumResultImportHandler;
import com.xiaoshan.edu.survey.utils.format.FormatUtil;
import com.xiaoshan.edu.vo.survey.*;
import com.xiaoshan.oa.dto.TeacherDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.utils.IoUtils;
import start.framework.commons.utils.MapConvert;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.core.ApplicationException;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;
import start.magic.thirdparty.json.JsonArray;
import start.magic.utils.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

@Service("taskTemplateStudentTeacherQuestionService")
public class TaskTemplateStudentTeacherQuestionServiceImpl extends SqlBaseServiceImplV2Ext<TaskTemplateStudentTeacherQuestionDO, Long>
        implements TaskTemplateStudentTeacherQuestionService {

    @SuppressWarnings("unused")
    private TaskTemplateStudentTeacherQuestionDao taskTemplateStudentTeacherQuestionDao;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private TaskTemplateStudentService taskTemplateStudentService;
    @Autowired
    private TaskServiceImpl taskService;
    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private TaskTemplateService taskTemplateService;
    @Autowired
    private TaskStudentService taskStudentService;
    @Autowired
    private CurriculumResultImportHandler curriculumResultImportHandler;
    @Autowired
    private DateExportUtil dateExportUtil;

    public TaskTemplateStudentTeacherQuestionServiceImpl(@Qualifier("taskTemplateStudentTeacherQuestionDao") TaskTemplateStudentTeacherQuestionDao taskTemplateStudentTeacherQuestionDao) {
        super(taskTemplateStudentTeacherQuestionDao);
        this.taskTemplateStudentTeacherQuestionDao = taskTemplateStudentTeacherQuestionDao;
    }


    /**
     * 删除调查调查任务管理的所有题目
     */
    @Override
    public void removeByTaskId(Long taskId) {
        remove("taskId", taskId);
    }

    /**
     * 获取学生对应的调查模板的科目列表
     */
    @Override
    public List<CurriculumStateVO> curriculumList(Long taskId, Long templateId, Long studentId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("templateId", templateId);
        map.put("studentId", studentId);
        return queryForMapMapper(CurriculumStateVO.class, "curriculumList", map);
    }

    /**
     * 小程序 -- 题目列表
     */
    @Override
    public List<QuestionShowVO> questionList(QuestionListAO ao) {
        List<QuestionShowVO> voList = queryForMapMapper(QuestionShowVO.class, "questionList", MapConvert.convert(ao));
        TaskTemplateDO taskTemplateDO = taskTemplateService.getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        List<QuestionVO> questionList = taskTemplateDO.getQuestionList();

        Map<String, QuestionVO> map = new HashMap<>();
        for (QuestionVO questionVO : questionList) {
            map.put(questionVO.getTitle(), questionVO);
        }

        for (QuestionShowVO vo : voList) {
            QuestionVO questionVO = map.get(vo.getTitle());
            vo.setIsRequired(questionVO.getIsRequired());
            vo.setItems(questionVO.getItems());
        }

        return voList;
    }

    /**
     * 小程序 -- 提交题目
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(List<QuestionData> ao) {
        Date date = new Date();
        List<Object[]> list = ao.stream().map(
                        item -> new Object[]{new JsonArray(item.getAnswer()).toString(), date, item.getQuestionId()})
                .collect(toList());
        taskTemplateStudentTeacherQuestionDao.executeBatch(
                "update sur_task_template_student_teacher_question set answer = ?, isFinish = true, modified_date = ? where id = ? and deleted=0", list);

        // 如果已经完成所有科目或完成班主任模板的所有题目，则网task_template_student表中添加一条记录
        TaskTemplateStudentTeacherQuestionDO load = load(ao.get(0).getQuestionId());
        TemplateAndQuestionVO template = templateService.getById(load.getTemplateId());
        List<CurriculumStateVO> curriculumList = curriculumList(load.getTaskId(), load.getTemplateId(), load.getStudentId());
        boolean isFinish = curriculumList.stream().allMatch(CurriculumStateVO::getIsFinish);
        if (isFinish || template.getSurveyObj().equals(SurveyObj.CLASS)) {
            taskTemplateStudentService.submitCurriculum(load.getTaskId(), load.getTemplateId(), load.getStudentId());
        }
    }

    /**
     * 根据任务id、模板id、学生id查询
     */
    @Override
    public List<TaskTemplateStudentTeacherQuestionDO> getByTaskTemplateStudentId(Long taskId, Long templateId, Long studentId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("taskId", taskId).eq("templateId", templateId).eq("studentId", studentId);
        return taskTemplateStudentTeacherQuestionDao.queryForList(wrapper);
    }

    /**
     * 统计 -- 查看统计结果
     */
    @Override
    public Map<String, Map<String, Object>> statistic(StatisticAO ao) {
        String topHeader;
        String avgName;

        Map<Long, QuestionVO> questionVoMap = getQuestionMap(ao.getTaskId(), ao.getTemplateId());
        Map<Long, String> studentMap = taskStudentService.getStudentMap(ao.getTaskId());
        // 获取源数据，并根据对应类型过滤，得到要处理的数据，班主任模板类型不需要过滤
        List<TaskTemplateStudentTeacherQuestionDO> sourceList = getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        String subAvgStr = "科目平均";
        if (ao.getType() == null) {  // 班主任模板
            topHeader = "班级$班主任";
            avgName = "年级平均";
        } else if (ao.getType().equals(StatisticType.CLASS)) {  // 按班级查看
            topHeader = "科目$教师";
            avgName = "班级平均";
            sourceList = sourceList.stream().filter(item -> item.getClassName().equals(ao.getClassName())).collect(Collectors.toList());
        } else {  // 按课程查看
            topHeader = "教学班$教师";
            avgName = subAvgStr;
            sourceList = sourceList.stream().filter(item -> item.getCurriculum().equals(ao.getCurriculum())).collect(Collectors.toList());
        }

        Set<String> title = new HashSet<>();
        for (TaskTemplateStudentTeacherQuestionDO taskTemplateStudentTeacherQuestionDO : sourceList) {
            title.add(taskTemplateStudentTeacherQuestionDO.getQuestionTitle());
        }
        // 用于计算平均值 <题目, <选项，选择该选项的所有人>>,平均值分子
        Map<Long, Map<String, List<Long>>> totalMap = new LinkedHashMap<>();
        // {'2301班$周敏轩': {"本学期班主任早自习出勤情况": {"较多": {"人数": 45,"比例": "39.5%"}}}}
        Map<String, Map<String, Map<String, Map<String, Object>>>> teacherResMap = new LinkedHashMap<>();
        // 简答题
        Map<String, Map<String, List<Map<String, String>>>> answerResMap = new LinkedHashMap<>();
        // 根据老师分组，老师是根节点
        Map<Long, List<TaskTemplateStudentTeacherQuestionDO>> teacherMap = sourceList.stream().collect(Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getTeacherId));

        // 有该科目的班级数
        Map<String, Integer> classMap = new HashMap<>();
        Stream<TaskTemplateStudentTeacherQuestionDO> stream = sourceList.stream();
        if (ao.getType() == null){
            stream = stream.peek(item -> item.setCurriculum(""));
        }
        Map<String, List<TaskTemplateStudentTeacherQuestionDO>> currGroup = stream.collect(groupingBy(TaskTemplateStudentTeacherQuestionDO::getCurriculum));
        for (List<TaskTemplateStudentTeacherQuestionDO> list : currGroup.values()) {
            Set<String> set = list.stream().map(TaskTemplateStudentTeacherQuestionDO::getClassName).collect(toSet());classMap.put(list.get(0).getCurriculum(), set.size());
        }

        // 有该题目的科目数
        Map<String, Integer> currNumMap = new HashMap<>();
        Map<Integer, List<TaskTemplateStudentTeacherQuestionDO>> titleGroup = sourceList.stream().collect(groupingBy(TaskTemplateStudentTeacherQuestionDO::getQuestionSort));
        for (List<TaskTemplateStudentTeacherQuestionDO> list : titleGroup.values()) {
            TaskTemplateStudentTeacherQuestionDO data = list.get(0);
            Set<String> collect = list.stream().map(TaskTemplateStudentTeacherQuestionDO::getCurriculum).collect(Collectors.toSet());
            currNumMap.put(data.getQuestionTitle(), collect.size());
        }
        // 走班课特殊处理，同一个老师教两个班级，班级需要分开
        List<List<TaskTemplateStudentTeacherQuestionDO>> tmp = new ArrayList<>();
        for (List<TaskTemplateStudentTeacherQuestionDO> value : teacherMap.values()) {
            Map<String, List<TaskTemplateStudentTeacherQuestionDO>> map = value.stream().collect(groupingBy(TaskTemplateStudentTeacherQuestionDO::getClassName));
            tmp.addAll(map.values());
        }
        // 遍历所有教师的数据，每一项是一个教师的所有数据
        for (List<TaskTemplateStudentTeacherQuestionDO> teacherList : subAvgStr.equals(avgName) ? tmp : teacherMap.values()) {
            TaskTemplateStudentTeacherQuestionDO teacher = teacherList.get(0);
            Map<String, Map<String, Map<String, Object>>> questionResMap = new LinkedHashMap<>(); // {"本学期班主任早自习出勤情况": {"较多": {"人数": 45,"比例": "39.5%"}}}
            // 根据题目分组，按题号升序
            Map<Integer, List<TaskTemplateStudentTeacherQuestionDO>> questionMap = teacherList.stream().collect(Collectors.groupingBy(TaskTemplateStudentTeacherQuestionDO::getQuestionSort));
            // 处理不同课程老师调查题目不一样的情况，有些老师有语文，有些没有
            Map<Integer, List<QuestionVO>> collect = questionVoMap.values().stream().collect(groupingBy(QuestionVO::getQuestionSort));
            for (Map.Entry<Integer, List<QuestionVO>> entry : collect.entrySet()) {
                if (questionMap.get(entry.getKey()) == null) {
                    TaskTemplateStudentTeacherQuestionDO slot = new TaskTemplateStudentTeacherQuestionDO();
                    QuestionVO questionVO = entry.getValue().get(0);
                    slot.setQuestionId(questionVO.getQuestionId());
                    slot.setIsSubmit(false);
                    slot.setQuestionType(questionVO.getQuestionType());
                    questionMap.put(entry.getKey(), Collections.singletonList(slot));
                }
            }
            // 问答题答案  {题目1: [{studentName: 张三, txt: '哈哈哈'}]}
            Map<String, List<Map<String, String>>> answerMap = new LinkedHashMap<>();
            for (List<TaskTemplateStudentTeacherQuestionDO> questionList : questionMap.values()) {  // 遍历每道题目的数据，每一项是同一道题的所有数据
                // 获取该题目的详细信息
                QuestionVO questionVO = questionVoMap.get(questionList.get(0).getQuestionId());
                if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                    // 简答题特殊处理
                    List<Map<String, String>> list = new ArrayList<>();
                    for (TaskTemplateStudentTeacherQuestionDO question : questionList) {
                        if (question.getIsSubmit() && question.getAnswer() != null && question.getAnswer().size() > 0) {
                            Map<String, String> map = new LinkedHashMap<>();
                            map.put("studentName", studentMap.get(question.getStudentId()));
                            map.put("txt", question.getAnswer().get(0).getTxt());
                            list.add(map);
                        }
                    }
                    answerMap.put(questionVO.getTitle(), list);
                    continue;
                }

                // 用于计算平均值 {'较多': 2, '较少': 3}
                Map<String, List<Long>> questionTotalMap = totalMap.computeIfAbsent(questionVO.getQuestionId(), k -> new LinkedHashMap<>());

                // 每个选项对应人数，所有个人数据都用人头计算
                Map<String, List<Long>> itemSelectNumMap = new LinkedHashMap<>();
                for (QuestionOptionDTO item : questionVO.getItems()) {
                    itemSelectNumMap.put(item.getTxt(), new ArrayList<>());  // 先初始化，防止出现某个选项无人选择时出现选项不存入map的情况
                }

                // 总填写完成人数，多个科目同一道题去重
                Set<Long> totalSet = new HashSet<>();

                // 计算每个选项填写人数
                for (TaskTemplateStudentTeacherQuestionDO dataDO : questionList) {
                    if (dataDO.getIsSubmit()) {
                        List<QuestionOptionDTO> answer = dataDO.getAnswer();
                        for (QuestionOptionDTO dto : answer) {
                            totalSet.add(dataDO.getStudentId());
                            List<Long> list = itemSelectNumMap.get(dto.getTxt());
                            list.add(dataDO.getStudentId());
                        }
                    }
                }

                // 把所有老师的每个选项的人数累加起来，用于计算平均值
                for (Map.Entry<String, List<Long>> entry : itemSelectNumMap.entrySet()) {
                    List<Long> list = questionTotalMap.computeIfAbsent(entry.getKey(), key -> new ArrayList<>());
                    list.addAll(entry.getValue());
                }

                // 每个选项对应的人数计算完毕，下面开始计算比例
                Map<String, Map<String, Object>> dataResMap = new LinkedHashMap<>();  // {'较多': {'人数': 2, '比例': '10.00%'}, '较少': {'人数': 2, '比例': '10.00%'}}
                for (Map.Entry<String, List<Long>> itemNumEntry : itemSelectNumMap.entrySet()) {

                    // 选择该选项的人头
                    int selectNum = new HashSet<>(itemNumEntry.getValue()).size();

                    // 比例 : 选该选项的人头 / 总人头
                    double percent = totalSet.size() == 0 ? 0 : selectNum * 1.0 / totalSet.size() * 100;

                    Map<String, Object> numPercentMap = new LinkedHashMap<>();
                    numPercentMap.put("人数", selectNum);
                    numPercentMap.put("比例", FormatUtil.formatDouble(percent) + "%");

                    dataResMap.put(itemNumEntry.getKey(), numPercentMap);
                }

                questionResMap.put(questionVO.getTitle(), dataResMap);
            }


            if (ao.getType() == null) {
                teacherResMap.put(teacher.getClassName() + "班$" + teacher.getTeacherName(), questionResMap);
                answerResMap.put(teacher.getClassName() + "班$" + teacher.getTeacherName(), answerMap);
            } else if (ao.getType().equals(StatisticType.CLASS)) {
                teacherResMap.put(teacher.getCurriculum() + "$" + teacher.getTeacherName(), questionResMap);
                answerResMap.put(teacher.getCurriculum() + "$" + teacher.getTeacherName(), answerMap);
            } else {
                teacherResMap.put(teacher.getClassName() + "$" + teacher.getTeacherName(), questionResMap);
                answerResMap.put(teacher.getClassName() + "$" + teacher.getTeacherName(), answerMap);
            }
        }

        // 开始求平均值
        Map<String, Map<String, Map<String, Object>>> avgResMap = new LinkedHashMap<>();
        for (Map.Entry<Long, Map<String, List<Long>>> entry : totalMap.entrySet()) {
            QuestionVO questionVo = questionVoMap.get(entry.getKey());
            // 题目回答总人数
            Map<String, Map<String, Object>> avgMap = new LinkedHashMap<>();

            // 这道题填写完成的总人头（去重）
            Set<Long> questionTotalSet = new HashSet<>();
            for (Map.Entry<String, List<Long>> itemEntry : entry.getValue().entrySet()) {
                questionTotalSet.addAll(itemEntry.getValue());
            }

            // 这道题填写完成的总人头
            List<TaskTemplateStudentTeacherQuestionDO> taskTemplateStudentTeacherQuestionDos = titleGroup.get(questionVo.getQuestionSort());
            int total = 0;
            if (!CollectionUtils.isEmpty(taskTemplateStudentTeacherQuestionDos)) {
                total = taskTemplateStudentTeacherQuestionDos.size();
            }
            // 遍历每个选项和填写总人数
            for (Map.Entry<String, List<Long>> itemEntry : entry.getValue().entrySet()) {
                double avg = 0;
                if ("班级平均".equals(avgName)) {
                    List<Long> list = new ArrayList<>(itemEntry.getValue());
                    avg = list.size() == 0 ? 0 : list.size() * 1.0 / total * 100;
                }else {
                    HashSet<Long> set = new HashSet<>(itemEntry.getValue());
                    avg = set.size() == 0 ? 0 : set.size() * 1.0 / questionTotalSet.size() * 100;
                }
                Map<String, Object> map = new LinkedHashMap<>();

                int div = 0;
                if ("年级平均".equals(avgName) || subAvgStr.equals(avgName)){
                    String curriculum = ao.getCurriculum();
                    if (curriculum == null) {
                        curriculum = "";
                    }
                    div = classMap.get(curriculum);
                } else {
                    Integer a = currNumMap.get(questionVo.getTitle());
                    if (a == null){
                        a = 1;
                    }
                    div = a;

                }

                Object n = DataComputeUtil.divRound(itemEntry.getValue().size(), div);
                map.put("人数", n);
                map.put("比例", FormatUtil.formatDouble(avg) + "%");
                avgMap.put(itemEntry.getKey(), map);
            }
            avgResMap.put(questionVo.getTitle(), avgMap);
        }

        // 结果封装
        // {"班级$班主任": {"average": {...}, "select": {...}, "answer": {...}}}
        Map<String, Map<String, Object>> res = new LinkedHashMap<>();
        // {"average": {...}, "select": {...}, "answer": {...}}
        Map<String, Object> resMap = new LinkedHashMap<>();

        // 平均值结果
        Map<String, Map<String, Map<String, Map<String, Object>>>> resAvgMap = new LinkedHashMap<>();
        resAvgMap.put(avgName, avgResMap);
        resMap.put("average", resAvgMap);
        //排序
        LinkedHashMap<String, Map<String, Map<String, Map<String, Object>>>> teacherSortResMap = teacherResMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oleValue, newValue) -> oleValue, LinkedHashMap::new));
        LinkedHashMap<String, Map<String, List<Map<String, String>>>> answerSortResMap = answerResMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oleValue, newValue) -> oleValue, LinkedHashMap::new));
        // 选择题结果
        resMap.put("select", teacherSortResMap);

        // 简答题结果
        resMap.put("answer", answerSortResMap);

        // 题目列表
        resMap.put("title", title);

        // 最终结果
        res.put(topHeader, resMap);

        return res;
    }


    /**
     * 统计 -- 数据导出
     */
    @Override
    public void export(HttpServletResponse response, BaseStatisticAO ao) {
        if (ao.getType() == null) {
            dateExportUtil.headerTeacherExport(response, ao);
        } else if (ao.getType().equals(StatisticType.CURRICULUM)) {
            dateExportUtil.curriculumExport(response, ao);
        } else if (ao.getType().equals(StatisticType.CLASS)) {
            dateExportUtil.classExport(response, ao);
        }
    }


    /**
     * 统计 -- 查询班级
     */
    @Override
    public List<String> getClasses(Long taskId, Long templateId) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId",taskId);
        map.put("templateId",templateId);
        List<TaskTemplateStudentTeacherQuestionDO> list = queryForMapMapper(TaskTemplateStudentTeacherQuestionDO.class,"getClasses",map);
        return list.stream().map(TaskTemplateStudentTeacherQuestionDO::getClassName).collect(toList());
    }

    /**
     * 获取teacherUserId列表
     */
    @Override
    public List<Long> getTeacherUserIdList(Long taskId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("taskId", taskId);
        List<TaskTemplateStudentTeacherQuestionDO> list = taskTemplateStudentTeacherQuestionDao.queryForList(wrapper);
        List<Long> teacherIds = list.stream().map(TaskTemplateStudentTeacherQuestionDO::getTeacherId).distinct().collect(toList());
        if (teacherIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<TeacherDto> dtos = foundationFacade.getTeachersByIds(StringUtils.listToString(teacherIds));
        return dtos.stream().map(TeacherDto::getUserId).collect(toList());
    }

    /**
     * 根据调查任务和教师id查询，用于统计教师端每个模板的学生数量
     */
    @Override
    public List<TaskTemplateStudentTeacherQuestionDO> getDistinctStudentByTaskAndTeacherId(Long taskId, Long teacherId) {
        QueryWrapper wrapper = new QueryWrapper("distinct studentId, templateId").eq("taskId", taskId).eq("teacherId", teacherId).eq("isFinish", String.valueOf(true));
        return taskTemplateStudentTeacherQuestionDao.queryForList(wrapper);
    }

    /**
     * 提交调查问卷，isSubmit为true
     */
    @Override
    public void taskSubmit(Long taskId, Long studentId) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("taskId", taskId).eq("studentId", studentId).set("isSubmit", String.valueOf(true));
        taskTemplateStudentTeacherQuestionDao.executeUpdate(wrapper);
    }

    /**
     * 统计 -- 下载Excel模板
     */
    @Override
    public void downloadTemplate(HttpServletResponse response, SurveyObj type) {
        try (InputStream in = getClass().getClassLoader().getResourceAsStream("resource/" + (type.equals(SurveyObj.CLASS) ? "sur_class_template.xlsx" : "sur_curriculum_template.xlsx"));
             BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());) {
            ResponseUtils.setExcel(response, type.getDescription() + "模板");
            bos.write(IoUtils.inputStreamConvertBytes(in, -1));
            response.flushBuffer();
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }


    /**
     * 统计 -- 导入结果
     */
    @Override
    public void importResult(Long taskId, Long templateId, MultipartFile file) {
        if (file == null) {
            throw new BusinessException("导入excel为空");
        }
        TaskTemplateDO taskTemplate = taskTemplateService.getByTaskTemplateId(taskId, templateId);
        SurveyObj surveyObj = taskTemplate.getSurveyObj();

        try {
            if (SurveyObj.CLASS.equals(surveyObj)) {
                // 班主任模板
                EasyExcel.read(file.getInputStream(), new ClassResultImportListener(taskTemplate)).sheet().headRowNumber(2).doRead();
            } else {
                // 任课教师模板，直接解析
                curriculumResultImportHandler.doAnalyze(taskTemplate, file);
            }
        } catch (BusinessException e){
            throw e;
        } catch (ExcelAnalysisException e){
            if (e.getCause() instanceof BusinessException){
                throw (BusinessException)e.getCause();
            }else {
                e.printStackTrace();
                throw new BusinessException("导入调查模板数据有误，请仔细检查excel");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("导入调查模板数据有误，请仔细检查excel");
        }
    }


    @Override
    public List<TaskTemplateStudentTeacherQuestionDO> getByTaskStudentIds(Long taskId, Collection<Long> studentIds) {
        QueryWrapper wrapper = new QueryWrapper("distinct taskId", "studentId", "templateId");
        wrapper.eq("taskId", taskId);
        wrapper.in("studentId", studentIds);
        return taskTemplateStudentTeacherQuestionDao.queryForList(wrapper);
    }

    @Override
    public List<Long> getStudentIdByTaskAndTemplateId(Long taskId, Long templateId) {
        QueryWrapper wrapper = new QueryWrapper("distinct studentId");
        wrapper.eq("taskId", taskId);
        wrapper.eq("templateId", templateId);
        List<TaskTemplateStudentTeacherQuestionDO> list = taskTemplateStudentTeacherQuestionDao.queryForList(wrapper);
        return list.stream().map(TaskTemplateStudentTeacherQuestionDO::getStudentId).collect(Collectors.toList());
    }


    /**
     * 根据任务id、模板id
     */
    @Override
    public List<TaskTemplateStudentTeacherQuestionDO> getByTaskTemplateId(Long taskId, Long templateId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("taskId", taskId).eq("templateId", templateId);
        return taskTemplateStudentTeacherQuestionDao.queryForList(wrapper);
    }

    /**
     * 教师端 -- 查看统计结果
     * 本人数据，不管是任课教师模板还是班主任模板，都是一样的，筛选出和自己相关的数据做统计，
     * 只不过当初一到高一年级时，任课教师模板还要根据班级切换，需要在上面筛选数据的基础上再按班级字段筛一遍就行了
     *
     * 班主任模板的**年级平均**对应任课教师模板的**科目平均**，都和自己的数据没关系，和年级也没关系，都取的整个年级的数据，
     * 只不过科目平均需要在所有数据的基础上根据自己任课的科目筛一下就行了
     *
     * 班级平均是任课教师模板初一到高一年级独有的，和科目无关，取得是班级里这道题目的平均，需要根据班级切换，取全部数据按班级筛选即可
     */
    @Override
    public List<TeacherStatisticVO> teacherResult(TeacherStatisticAO ao) {
        TaskDO taskDO = taskService.load(ao.getTaskId());

        Map<Long, QuestionVO> questionMap = new LinkedHashMap<>();
        TaskTemplateDO taskTemplate = taskTemplateService.getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        for (QuestionVO vo : taskTemplate.getQuestionList()) {
            questionMap.put(vo.getQuestionId(), vo);
        }

        // 校验
        String highTwo = "高二";
        String highThree = "高三";
        if (taskTemplate.getSurveyObj().equals(SurveyObj.CLASS)) {
            ao.setClassName(null);
        } else if (!(highTwo.equals(taskDO.getGrade()) || highThree.equals(taskDO.getGrade()))) {
            if (StringUtils.isEmpty(ao.getClassName())) {
                throw new BusinessException("请选择班级");
            }
        }

        int total;  // 计算填写总人数

        List<TaskTemplateStudentTeacherQuestionDO> sourceList = getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());  // 所有数据

        // 所有和我相关的数据，用于计算本人数据
        // 如果是初中到高一年级，需要切换班级，还需根据班级字段进一步筛选
        List<TaskTemplateStudentTeacherQuestionDO> myList = sourceList.stream().filter(item -> item.getTeacherId().equals(UserContextHolder.getUser().getId())).collect(toList());

        // 所有的选择题，用于计算科目平均、班级平均、年级平均，后面还有根据条件再进步一般筛选，如下
        // 科目平均：按科目筛
        // 班级平均：按班级筛
        // 年级平均：不用筛
        List<TaskTemplateStudentTeacherQuestionDO> selectList = sourceList.stream().filter(item -> !item.getQuestionType().equals(QuestionType.ANSWER)).collect(toList());

        // 本人数据中填写完成的总人数，只能用在班主任模板和任课教师模板的高二、高三年级，因为取的是所有填写的学生总人数
        total = myList.stream().filter(TaskTemplateStudentTeacherQuestionDO::getIsSubmit).map(TaskTemplateStudentTeacherQuestionDO::getStudentId).collect(Collectors.toSet()).size();

        // 班级名称不为空，说明是任课教师模板（初一到高一）
        // 因此本人数据需要再根据班级筛选，总人数也要根据班级筛选
        if (!StringUtils.isEmpty(ao.getClassName())) {
            myList = myList.stream().filter(item -> item.getClassName().equals(ao.getClassName())).collect(toList());
            total = myList.stream().filter(TaskTemplateStudentTeacherQuestionDO::getIsSubmit).map(TaskTemplateStudentTeacherQuestionDO::getStudentId).collect(Collectors.toSet()).size();
        }

        // 根据题号分组，保证有序
        Map<Integer, List<TaskTemplateStudentTeacherQuestionDO>> questionSortMap = myList.stream().collect(groupingBy(TaskTemplateStudentTeacherQuestionDO::getQuestionSort));
        ArrayList<TeacherStatisticVO> voList = new ArrayList<>();
        for (List<TaskTemplateStudentTeacherQuestionDO> sameQuestionList : questionSortMap.values()) {  // 遍历所有题目，每一项是该题目所有相关学生的填写数据
            QuestionVO questionVO = questionMap.get(sameQuestionList.get(0).getQuestionId());

            TeacherStatisticVO vo = ClassConverter.aTob(questionVO, new TeacherStatisticVO());
            vo.setTotal(total);  // 这个总人数是小程序端需要的，代表填写的总人数

            // 问答题
            if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                if (questionVO.getIsVisible()) {
                    ArrayList<String> writeDatas = new ArrayList<>();
                    for (TaskTemplateStudentTeacherQuestionDO tDO : sameQuestionList) {
                        if (tDO.getIsSubmit() && tDO.getAnswer().size() > 0) {
                            writeDatas.add(tDO.getAnswer().get(0).getTxt());
                        }
                    }
                    vo.setWriteData(writeDatas);
                    voList.add(vo);
                }
                continue;
            }

            // 本人数据
            Map<String, PercentageDTO> myItemComputeMap = DataComputeUtil.computeDataMy(sameQuestionList, questionVO);

            // 科目平均（任课教师模板，不分年级）
            Map<String, PercentageDTO> currItemComputeMap = null;
            if (taskTemplate.getSurveyObj().equals(SurveyObj.CURRICULUM)) {
                // 取这个老师上的科目进行筛选，因为sameQuestionList来自myList，都是这个老师相关的数据，所以直接取科目没问题
                String curriculum = sameQuestionList.get(0).getCurriculum();

                // 这里数据就不能从sameQuestionList取了，因为科目平均是整个年级范围，而sameQuestionList只有和自己相关的数据，没有相关科目其他老师的数据
                List<TaskTemplateStudentTeacherQuestionDO> currList = selectList.stream()
                        .filter(item -> item.getQuestionSort().equals(questionVO.getQuestionSort()))
                        .filter(item -> item.getCurriculum().equals(curriculum)).collect(toList());
                currItemComputeMap = DataComputeUtil.computeDataCurrGrade(currList, questionVO);
            }

            // 班级平均(任课教师模板，初中、高一)，根据班级切换
            Map<String, PercentageDTO> classItemComputeMap = null;
            if (taskTemplate.getSurveyObj().equals(SurveyObj.CURRICULUM) && !StringUtils.isEmpty(ao.getClassName())) {
                List<TaskTemplateStudentTeacherQuestionDO> quesList = selectList.stream()
                        .filter(item -> item.getClassName().equals(ao.getClassName()))
                        .filter(item -> item.getQuestionSort().equals(questionVO.getQuestionSort()))
                        .collect(toList());
                classItemComputeMap = DataComputeUtil.computeDataClass(quesList, questionVO);
            }

            // 年级平均(班主任模板)
            Map<String, PercentageDTO> gradeItemComputeMap = null;
            if (taskTemplate.getSurveyObj().equals(SurveyObj.CLASS)) {
                List<TaskTemplateStudentTeacherQuestionDO> gradeList = selectList.stream().filter(item -> item.getQuestionSort().equals(questionVO.getQuestionSort())).collect(toList());
                gradeItemComputeMap = DataComputeUtil.computeDataCurrGrade(gradeList, questionVO);
            }

            // 封装结果
            if (taskTemplate.getSurveyObj().equals(SurveyObj.CLASS)) {
                // 班主任  本人数据  年级平均
                LinkedHashMap<String, Map<String, PercentageDTO>> resMap = new LinkedHashMap<>();
                for (QuestionOptionDTO item : questionVO.getItems()) {
                    LinkedHashMap<String, PercentageDTO> dataMap = new LinkedHashMap<>();
                    dataMap.put("本人数据", myItemComputeMap.get(item.getOption()));
                    dataMap.put("年级平均", gradeItemComputeMap.get(item.getOption()));
                    resMap.put(item.getOption() + "." + item.getTxt(), dataMap);
                }
                vo.setData(resMap);
            } else if (!(highTwo.equals(taskDO.getGrade()) || highThree.equals(taskDO.getGrade()))) {
                LinkedHashMap<String, Map<String, PercentageDTO>> resMap = new LinkedHashMap<>();
                for (QuestionOptionDTO item : questionVO.getItems()) {
                    LinkedHashMap<String, PercentageDTO> dataMap = new LinkedHashMap<>();
                    dataMap.put("本人数据", myItemComputeMap.get(item.getOption()));
                    dataMap.put("科目平均", currItemComputeMap.get(item.getOption()));
                    dataMap.put("班级平均", classItemComputeMap.get(item.getOption()));
                    resMap.put(item.getOption() + "." + item.getTxt(), dataMap);
                }
                vo.setData(resMap);
            } else {
                LinkedHashMap<String, Map<String, PercentageDTO>> resMap = new LinkedHashMap<>();
                for (QuestionOptionDTO item : questionVO.getItems()) {
                    LinkedHashMap<String, PercentageDTO> dataMap = new LinkedHashMap<>();
                    dataMap.put("本人数据", myItemComputeMap.get(item.getOption()));
                    dataMap.put("科目平均", currItemComputeMap.get(item.getOption()));
                    resMap.put(item.getOption() + "." + item.getTxt(), dataMap);
                }
                vo.setData(resMap);
            }
            voList.add(vo);
        }

        return voList;

    }


    /**
     * 获取题目Map 题目id->题目
     */
    private Map<Long, QuestionVO> getQuestionMap(Long taskId, Long templateId) {
        Map<Long, QuestionVO> questionMap = new LinkedHashMap<>();
        TaskTemplateDO taskTemplate = taskTemplateService.getByTaskTemplateId(taskId, templateId);
        for (QuestionVO vo : taskTemplate.getQuestionList()) {
            questionMap.put(vo.getQuestionId(), vo);
        }
        return questionMap;
    }

}
