package com.xiaoshan.edu.survey.utils;

import com.common.utils.ClassConverter;
import com.common.utils.ResponseUtils;
import com.xiaoshan.edu.ao.survey.BaseStatisticAO;
import com.xiaoshan.edu.ao.survey.StatisticAO;
import com.xiaoshan.edu.dto.survey.QuestionOptionDTO;
import com.xiaoshan.edu.enums.survey.QuestionType;
import com.xiaoshan.edu.survey.entity.TaskDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.TaskService;
import com.xiaoshan.edu.survey.service.TaskTemplateService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentTeacherQuestionService;
import com.xiaoshan.edu.survey.utils.excel.SurveyExcelUtils;
import com.xiaoshan.edu.vo.survey.QuestionVO;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import start.magic.core.ApplicationException;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DateExportUtil {

    @Autowired
    private TaskTemplateService taskTemplateService;

    @Autowired
    private TaskTemplateStudentTeacherQuestionService service;

    @Autowired
    private TaskService taskService;

    /**
     * 班主任统计导出
     */
    public void headerTeacherExport(HttpServletResponse response, BaseStatisticAO ao) {
        TaskDO taskDo = taskService.load(ao.getTaskId());
        TaskTemplateDO template = taskTemplateService.getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        Iterator<QuestionVO> quesIter = template.getQuestionList().iterator();

        // 班主任模板
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("sheet1");
        int two = 2;
        for (int columnIndex = 0; columnIndex < two; columnIndex++) {
            sheet.addMergedRegion(new CellRangeAddress(0, two, columnIndex, columnIndex));
        }
        XSSFRow rowZone = sheet.createRow(0);
        rowZone.createCell(0).setCellValue("班级");
        rowZone.createCell(1).setCellValue("班主任");

        XSSFRow rowOne = sheet.createRow(1);
        XSSFRow rowTwo = sheet.createRow(two);
        int columnIndex = two;

        HashMap<Integer, Integer> mergeMap = new HashMap<>();
        int quesIndex = 0;
        while (quesIter.hasNext()) {
            QuestionVO next = quesIter.next();
            if (next.getQuestionType().equals(QuestionType.ANSWER)) {
                continue;
            }
            int size = next.getItems().size();
            rowZone.createCell(columnIndex).setCellValue(next.getTitle());

            // 用于给单元格添加颜色
            sheet.addMergedRegion(new CellRangeAddress(0, 0, columnIndex, columnIndex + two * size - 1));
            for (int i = columnIndex; i <= columnIndex + two * size - 1; i++) {
                mergeMap.put(i, quesIndex);
            }
            quesIndex++;

            for (QuestionOptionDTO item : next.getItems()) {
                rowOne.createCell(columnIndex).setCellValue(item.getTxt());
                sheet.addMergedRegion(new CellRangeAddress(1, 1, columnIndex, columnIndex + 1));
                rowTwo.createCell(columnIndex++).setCellValue("人数");
                rowTwo.createCell(columnIndex++).setCellValue("比例");
            }
        }

        int rowIndex = 3;
        StatisticAO statisticAo = ClassConverter.aTob(ao, new StatisticAO());
        Map<String, Map<String, Object>> statistic = service.statistic(statisticAo);
        Collection<Map<String, Object>> values = statistic.values();
        Map<String, Object> typeMap = values.iterator().next();

        // 平均值处理
        Map<String, Map<String, Map<String, Map<String, Object>>>> averageMap = (Map<String, Map<String, Map<String, Map<String, Object>>>>) typeMap.get("average");
        String avgName = null;
        for (String key : averageMap.keySet()) {
            avgName = key;
        }
        XSSFRow avgRow = sheet.createRow(rowIndex);
        avgRow.createCell(0).setCellValue(avgName);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 1));
        rowIndex++;
        int avgColumnIndex = two;
        for (QuestionVO questionVO : template.getQuestionList()) {
            if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                continue;
            }
            for (QuestionOptionDTO item : questionVO.getItems()) {
                Map<String, Object> dataMap = averageMap.get(avgName).get(questionVO.getTitle()).get(item.getTxt());
                avgRow.createCell(avgColumnIndex++).setCellValue(dataMap.get("人数").toString());
                avgRow.createCell(avgColumnIndex++).setCellValue(dataMap.get("比例").toString());
            }
        }

        // 选择题处理
        Map<String, Map<String, Map<String, Map<String, Object>>>> teacherMap = (Map<String, Map<String, Map<String, Map<String, Object>>>>) typeMap.get("select");
        for (String subjectTeacher : teacherMap.keySet()) {
            String[] split = subjectTeacher.split("\\$");
            XSSFRow teacherRow = sheet.createRow(rowIndex);
            teacherRow.createCell(0).setCellValue(split[0]);
            teacherRow.createCell(1).setCellValue(split[1]);
            rowIndex++;
            int teacherColumnIndex = two;
            for (QuestionVO questionVO : template.getQuestionList()) {
                if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                    continue;
                }
                for (QuestionOptionDTO item : questionVO.getItems()) {
                    Map<String, Object> dataMap = teacherMap.get(subjectTeacher).get(questionVO.getTitle()).get(item.getTxt());
                    teacherRow.createCell(teacherColumnIndex++).setCellValue(dataMap.get("人数").toString());
                    teacherRow.createCell(teacherColumnIndex++).setCellValue(dataMap.get("比例").toString());
                }
            }
        }

        ResponseUtils.setExcel(response, taskDo.getName());
        try {
            SurveyExcelUtils.exportExcelStyle(workbook, mergeMap);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new ApplicationException(e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    public void curriculumExport(HttpServletResponse response, BaseStatisticAO ao) {
        // 按课程分类
        XSSFWorkbook workbook = new XSSFWorkbook();
        TaskDO taskDo = taskService.load(ao.getTaskId());
        TaskTemplateDO template = taskTemplateService.getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        List<TaskTemplateStudentTeacherQuestionDO> sourceList = service.getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        Set<String> curriculumList = new HashSet<>();
        for (QuestionVO questionVO : template.getQuestionList()) {
            if(questionVO.getCurriculumList() != null && questionVO.getCurriculumList().size() > 0){
                curriculumList.addAll(questionVO.getCurriculumList());
            }
        }
        int index = 0;
        Map<String,Map<String, Map<String, Object>>> map = new HashMap<>();
        for (String curr : curriculumList) {
            //首先获得map
            StatisticAO statisticAo = ClassConverter.aTob(ao, new StatisticAO());
            statisticAo.setCurriculum(curr);
            Map<String, Map<String, Object>> res = service.statistic(statisticAo);
            map.put(curr,res);
            Iterator<QuestionVO> quesIter = template.getQuestionList().iterator();
            List<TaskTemplateStudentTeacherQuestionDO> collect = sourceList.stream().filter(item -> item.getCurriculum().equals(curr)).collect(Collectors.toList());
            Set<String> title = new HashSet<>();
            for (TaskTemplateStudentTeacherQuestionDO taskTemplateStudentTeacherQuestionDO : collect) {
                title.add(taskTemplateStudentTeacherQuestionDO.getQuestionTitle());
            }
            XSSFSheet sheet = workbook.createSheet(curr);
            int three = 3;
            int two = 2;
            for (int columnIndex = 0; columnIndex < three; columnIndex++) {
                sheet.addMergedRegion(new CellRangeAddress(0, two, columnIndex, columnIndex));
            }
            XSSFRow rowZone = sheet.createRow(0);
            rowZone.createCell(0).setCellValue("科目");
            rowZone.createCell(1).setCellValue("班级");
            rowZone.createCell(two).setCellValue("教师");

            XSSFRow rowOne = sheet.createRow(1);
            XSSFRow rowTwo = sheet.createRow(two);
            int columnIndex = three;

            Map<Integer, Integer> mergeMap = new LinkedHashMap<>();
            int quesIndex = 0;
            while (quesIter.hasNext()) {
                QuestionVO next = quesIter.next();
                if (next.getQuestionType().equals(QuestionType.ANSWER)) {
                    continue;
                }
                if(!title.contains(next.getTitle())){
                    continue;
                }
                int size = next.getItems().size();
                rowZone.createCell(columnIndex).setCellValue(next.getTitle());
                sheet.addMergedRegion(new CellRangeAddress(0, 0, columnIndex, columnIndex + two * size - 1));
                for (int i = columnIndex; i <= columnIndex + two * size - 1; i++) {
                    mergeMap.put(i, quesIndex);
                }
                quesIndex++;

                for (QuestionOptionDTO item : next.getItems()) {
                    rowOne.createCell(columnIndex).setCellValue(item.getTxt());
                    sheet.addMergedRegion(new CellRangeAddress(1, 1, columnIndex, columnIndex + 1));
                    rowTwo.createCell(columnIndex++).setCellValue("人数");
                    rowTwo.createCell(columnIndex++).setCellValue("比例");
                }
            }

            int rowIndex = three;
            for (String curriculum : curriculumList) {
                if(!curr.equals(curriculum)){
                    continue;
                }
                Map<String, Map<String, Object>> statistic = map.get(curr);
                Collection<Map<String, Object>> values = statistic.values();
                Map<String, Object> typeMap = values.iterator().next();

                // 平均值处理
                Map<String, Map<String, Map<String, Map<String, Object>>>> averageMap = (Map<String, Map<String, Map<String, Map<String, Object>>>>) typeMap.get("average");
                String avgName = null;
                for (String key : averageMap.keySet()) {
                    avgName = key;
                }
                XSSFRow avgRow = sheet.createRow(rowIndex);
                avgRow.createCell(0).setCellValue(curriculum);
                avgRow.createCell(1).setCellValue(avgName);
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, two));
                rowIndex++;
                int avgColumnIndex = three;
                for (QuestionVO questionVO : template.getQuestionList()) {
                    if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                        continue;
                    }
                    if(!title.contains(questionVO.getTitle())){
                        continue;
                    }
                    for (QuestionOptionDTO item : questionVO.getItems()) {

                        Map<String, Map<String, Object>> stringMapMap = averageMap.get(avgName).get(questionVO.getTitle());
                        if (stringMapMap == null) {
                            avgRow.createCell(avgColumnIndex++).setCellValue("0");
                            avgRow.createCell(avgColumnIndex++).setCellValue("0.00%");
                        } else {
                            Map<String, Object> dataMap = stringMapMap.get(item.getTxt());
                            avgRow.createCell(avgColumnIndex++).setCellValue(dataMap.get("人数").toString());
                            avgRow.createCell(avgColumnIndex++).setCellValue(dataMap.get("比例").toString());
                        }
                    }
                }

                // 选择题处理
                Map<String, Map<String, Map<String, Map<String, Object>>>> teacherMap = (Map<String, Map<String, Map<String, Map<String, Object>>>>) typeMap.get("select");
                int teacherNums = 0;
                for (String classTeacher : teacherMap.keySet()) {
                    teacherNums++;
                    String[] split = classTeacher.split("\\$");
                    XSSFRow teacherRow = sheet.createRow(rowIndex);
                    teacherRow.createCell(0).setCellValue(curriculum);
                    teacherRow.createCell(1).setCellValue(split[0]);
                    teacherRow.createCell(two).setCellValue(split[1]);
                    rowIndex++;
                    int teacherColumnIndex = three;
                    for (QuestionVO questionVO : template.getQuestionList()) {
                        if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                            continue;
                        }
                        if(!title.contains(questionVO.getTitle())){
                            continue;
                        }
                        for (QuestionOptionDTO item : questionVO.getItems()) {
                            Map<String, Map<String, Object>> stringMapMap = teacherMap.get(classTeacher).get(questionVO.getTitle());
                            if (stringMapMap == null) {
                                teacherRow.createCell(teacherColumnIndex++).setCellValue("0");
                                teacherRow.createCell(teacherColumnIndex++).setCellValue("0.00%");
                            } else {
                                Map<String, Object> dataMap = stringMapMap.get(item.getTxt());
                                teacherRow.createCell(teacherColumnIndex++).setCellValue(dataMap.get("人数").toString());
                                teacherRow.createCell(teacherColumnIndex++).setCellValue(dataMap.get("比例").toString());
                            }
                        }
                    }
                }
                if (teacherNums != 0) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - teacherNums - 1, rowIndex - 1, 0, 0));
                }

            }
            SurveyExcelUtils.exportExcelStyleNew(workbook, mergeMap,index++);
        }

        ResponseUtils.setExcel(response, taskDo.getName() + "-按科目查看");
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new ApplicationException(e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    public void classExport(HttpServletResponse response, BaseStatisticAO ao) {
        TaskDO taskDo = taskService.load(ao.getTaskId());
        TaskTemplateDO template = taskTemplateService.getByTaskTemplateId(ao.getTaskId(), ao.getTemplateId());
        Iterator<QuestionVO> quesIter = template.getQuestionList().iterator();

        // 按班级分类
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("sheet1");
        int three = 3;
        int two = 2;
        for (int columnIndex = 0; columnIndex < three; columnIndex++) {
            sheet.addMergedRegion(new CellRangeAddress(0, two, columnIndex, columnIndex));
        }
        XSSFRow rowZone = sheet.createRow(0);
        rowZone.createCell(0).setCellValue("班级");
        rowZone.createCell(1).setCellValue("科目");
        rowZone.createCell(two).setCellValue("教师");

        XSSFRow rowOne = sheet.createRow(1);
        XSSFRow rowTwo = sheet.createRow(two);
        int columnIndex = three;

        Map<Integer, Integer> mergeMap = new LinkedHashMap<>();
        int quesIndex = 0;

        while (quesIter.hasNext()) {
            QuestionVO next = quesIter.next();
            if (next.getQuestionType().equals(QuestionType.ANSWER)) {
                continue;
            }
            int size = next.getItems().size();
            rowZone.createCell(columnIndex).setCellValue(next.getTitle());
            sheet.addMergedRegion(new CellRangeAddress(0, 0, columnIndex, columnIndex + two * size - 1));
            for (int i = columnIndex; i <= columnIndex + two * size - 1; i++) {
                mergeMap.put(i, quesIndex);
            }
            quesIndex++;

            for (QuestionOptionDTO item : next.getItems()) {
                rowOne.createCell(columnIndex).setCellValue(item.getTxt());
                sheet.addMergedRegion(new CellRangeAddress(1, 1, columnIndex, columnIndex + 1));
                rowTwo.createCell(columnIndex++).setCellValue("人数");
                rowTwo.createCell(columnIndex++).setCellValue("比例");
            }
        }

        int rowIndex = three;
        for (String className : service.getClasses(ao.getTaskId(), ao.getTemplateId())) {
            StatisticAO statisticAo = ClassConverter.aTob(ao, new StatisticAO());
            statisticAo.setClassName(className);
            Map<String, Map<String, Object>> statistic = service.statistic(statisticAo);
            Collection<Map<String, Object>> values = statistic.values();
            Map<String, Object> typeMap = values.iterator().next();

            // 平均值处理
            Map<String, Map<String, Map<String, Map<String, Object>>>> averageMap = (Map<String, Map<String, Map<String, Map<String, Object>>>>) typeMap.get("average");
            String avgName = null;
            for (String key : averageMap.keySet()) {
                avgName = key;
            }
            XSSFRow avgRow = sheet.createRow(rowIndex);
            avgRow.createCell(0).setCellValue(className);
            avgRow.createCell(1).setCellValue(avgName);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, two));
            rowIndex++;
            int avgColumnIndex = three;
            for (QuestionVO questionVO : template.getQuestionList()) {
                if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                    continue;
                }
                for (QuestionOptionDTO item : questionVO.getItems()) {
                    Map<String, Map<String, Object>> stringMapMap = averageMap.get(avgName).get(questionVO.getTitle());
                    if (stringMapMap == null) {
                        avgRow.createCell(avgColumnIndex++).setCellValue("0");
                        avgRow.createCell(avgColumnIndex++).setCellValue("0.00%");
                    } else {
                        Map<String, Object> dataMap = stringMapMap.get(item.getTxt());
                        avgRow.createCell(avgColumnIndex++).setCellValue(dataMap.get("人数").toString());
                        avgRow.createCell(avgColumnIndex++).setCellValue(dataMap.get("比例").toString());
                    }
                }
            }

            // 选择题处理
            Map<String, Map<String, Map<String, Map<String, Object>>>> teacherMap = (Map<String, Map<String, Map<String, Map<String, Object>>>>) typeMap.get("select");
            int teacherNums = 0;
            for (String subjectTeacher : teacherMap.keySet()) {
                teacherNums++;
                String[] split = subjectTeacher.split("\\$");
                XSSFRow teacherRow = sheet.createRow(rowIndex);
                teacherRow.createCell(0).setCellValue(className);
                teacherRow.createCell(1).setCellValue(split[0]);
                teacherRow.createCell(two).setCellValue(split[1]);
                rowIndex++;
                int teacherColumnIndex = three;
                for (QuestionVO questionVO : template.getQuestionList()) {
                    if (questionVO.getQuestionType().equals(QuestionType.ANSWER)) {
                        continue;
                    }
                    for (QuestionOptionDTO item : questionVO.getItems()) {
                        Map<String, Map<String, Object>> stringMapMap = teacherMap.get(subjectTeacher).get(questionVO.getTitle());
                        if (stringMapMap == null) {
                            teacherRow.createCell(teacherColumnIndex++).setCellValue("0");
                            teacherRow.createCell(teacherColumnIndex++).setCellValue("0.00%");
                        } else {
                            Map<String, Object> dataMap = stringMapMap.get(item.getTxt());
                            teacherRow.createCell(teacherColumnIndex++).setCellValue(dataMap.get("人数").toString());
                            teacherRow.createCell(teacherColumnIndex++).setCellValue(dataMap.get("比例").toString());
                        }
                    }
                }
            }
            if (teacherNums != 0) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - teacherNums - 1, rowIndex - 1, 0, 0));
            }
        }

        ResponseUtils.setExcel(response, taskDo.getName() + "-按班级查看");
        try {
            SurveyExcelUtils.exportExcelStyle(workbook, mergeMap);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new ApplicationException(e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


}
