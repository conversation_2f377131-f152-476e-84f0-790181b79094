package com.xiaoshan.edu.courseadjustment.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.common.model.JwtUser;
import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.common.utils.DateUtils;
import com.xiaoshan.basic.vo.FlowVO;
import com.xiaoshan.basic.vo.ItemConfigVO;
import com.xiaoshan.basic.vo.SemesterVO;
import com.xiaoshan.edu.courseadjustment.dao.CourseAdjustmentDao;
import com.xiaoshan.edu.courseadjustment.utils.CourseTimeUtils;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.edu.ao.courseadjustment.*;
import com.xiaoshan.edu.api.facade.FlowFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.courseadjustment.dao.CourseReplaceDao;
import com.xiaoshan.edu.courseadjustment.dto.ReplaceAndSectionDTO;
import com.xiaoshan.edu.courseadjustment.entity.CourseAdjustmentDO;
import com.xiaoshan.edu.courseadjustment.entity.CourseReplaceDO;
import com.xiaoshan.edu.courseadjustment.entity.ReplaceSectionDO;
import com.xiaoshan.edu.courseadjustment.excel.CustomCellWriteHandler;
import com.xiaoshan.edu.courseadjustment.excel.ExcelHeadsUtil;
import com.xiaoshan.edu.courseadjustment.excel.export.ReplaceRecordExport;
import com.xiaoshan.edu.courseadjustment.service.CourseAdjustmentService;
import com.xiaoshan.edu.courseadjustment.service.CourseReplaceService;
import com.xiaoshan.edu.courseadjustment.service.ReplaceSectionService;
import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import com.xiaoshan.edu.dto.CourseReplaceDTO;
import com.xiaoshan.edu.dto.ReplaceCountDTO;
import com.xiaoshan.edu.enums.courseadjustment.AdjustmentType;
import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.model.ao.CourseReplaceApplyAO;
import com.xiaoshan.edu.model.ao.GetReplaceTeacherListAO;
import com.xiaoshan.edu.model.ao.GetSectionAO;
import com.xiaoshan.edu.model.ao.ReplaceCourseDetailAO;
import com.xiaoshan.edu.service.BasicPushMessage;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.courseadjustment.*;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.oa.dto.TeacherDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.QueryResult;
import start.framework.commons.utils.MapConvert;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.UpdateWrapper;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("courseReplaceService")
public class CourseReplaceServiceImpl extends SqlBaseServiceImplV2Ext<CourseReplaceDO, Long>
        implements CourseReplaceService {

    @Autowired
    private CourseReplaceDao courseReplaceDao;
    @Autowired
    private ReplaceSectionService replaceSectionService;
    @Autowired
    private FlowFacade flowFacade;
    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private CourseAdjustmentService courseAdjustmentService;
    @Autowired
    private SettingFacade settingFacade;
    @Autowired
    private CurriculumTableService curriculumTableService;
    @Autowired
    private GeneralTableService generalTableService;
    @Autowired
    private BasicPushMessage basicPushMessage;
    @Autowired
    private StartApprovalAsncy startApprovalAsncy;
    @Autowired
    private CourseAdjustmentDao courseAdjustmentDao;

    public static final String FLOW_IS_START = "1";
    public static final Integer NUMBER_TWO = 2;
    public static final Integer NUMBER_NINE = 9;

    @Value("${xiaoshan.course_adjustment.related_type}")
    private Integer relatedType;

    public CourseReplaceServiceImpl(@Qualifier("courseReplaceDao") CourseReplaceDao courseReplaceDao) {
        super(courseReplaceDao);
        this.courseReplaceDao = courseReplaceDao;
    }

    /**
     * pc管理-代课记录分页
     */
    @Override
    public QueryResult<List<CourseReplaceVO>> queryPage(ReplacePageAO ao) {
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        QueryResult<List<CourseReplaceDTO>> replaceRecords = null;
        if (ao.getQueryAll()) {
            // 代课次数与被代次数查询
            Map<Long, Set<Long>> beReplaceCount = replaceSectionService.getReplaceOrReplacedCount(map, 1);
            Map<Long, Set<Long>> toReplaceCount = replaceSectionService.getReplaceOrReplacedCount(map, 2);
            Set<Long> replaceIds = new HashSet<>();
            addReplaceIds(beReplaceCount, replaceIds);
            addReplaceIds(toReplaceCount, replaceIds);
            if (!replaceIds.isEmpty()) {
                List<Long> ids = new ArrayList<>(replaceIds);
                map.put("ids", ids);
            }
            // 查询课程老师和代课老师的记录
            replaceRecords = queryForPage(CourseReplaceDTO.class, "replaceAllRecords", map);

        } else {
            // 只查询课程老师的代课记录
            replaceRecords = queryForPage(CourseReplaceDTO.class, "replaceRecords", map);
        }
        if (replaceRecords == null) {
            return null;
        }
        return getListQueryResult(replaceRecords);
    }

    private void addReplaceIds(Map<Long, Set<Long>> toReplaceCount, Set<Long> replaceIds) {
        if (toReplaceCount != null && !toReplaceCount.isEmpty()) {
            for (Map.Entry<Long, Set<Long>> longSetEntry : toReplaceCount.entrySet()) {
                Set<Long> value = longSetEntry.getValue();
                if (value != null && !value.isEmpty()) {
                    replaceIds.addAll(value);
                }
            }
        }
    }

    public QueryResult<List<CourseReplaceVO>> getListQueryResult(QueryResult<List<CourseReplaceDTO>> replaceRecords) {
        List<ReplaceSectionVO> replaceSectionVoList = replaceSectionService.queryForAll(ReplaceSectionVO.class);
        Map<Long, List<ReplaceSectionVO>> replaceSectionListMap = new HashMap<>(10);
        if (replaceSectionVoList != null) {
            replaceSectionListMap = replaceSectionVoList.stream().collect(Collectors.groupingBy(ReplaceSectionVO::getReplaceId));
        }
        if (replaceRecords != null) {
            List<CourseReplaceVO> result1 = new ArrayList<>();
            for (CourseReplaceDTO courseReplaceDTO : replaceRecords.getResult()) {
                Long id = courseReplaceDTO.getId();
                List<ReplaceSectionVO> replaceSectionVos = replaceSectionListMap.get(id);
                String courseRoom = "";
                if (replaceSectionVos != null && replaceSectionVos.size() > 0) {
                    courseRoom = replaceSectionVos.stream().map(ReplaceSectionVO::getCourseRoom).distinct().collect(Collectors.joining("、"));
                }
                CourseReplaceVO courseReplaceVO = ClassConverter.aTob(courseReplaceDTO, new CourseReplaceVO());
                courseReplaceVO.setCourseRoom(courseRoom);
                courseReplaceVO.setSection(courseReplaceDTO.getSection() + "节次");
                courseReplaceVO.setReplaceTime(courseReplaceDTO.getReplaceStartTime() + " 至 " + courseReplaceDTO.getReplaceEndTime());
                courseReplaceVO.setApprovalState(courseReplaceDTO.getApprovalState().getName());
                result1.add(courseReplaceVO);
            }
            QueryResult<List<CourseReplaceVO>> replaceRecords1 = new QueryResult<>();
            replaceRecords1.setPageInfo(replaceRecords.getPageInfo());
            replaceRecords1.setResult(result1);
            return replaceRecords1;
        }
        return null;
    }

    /**
     * pc个人-代课记录分页
     */
    @Override
    public QueryResult<List<CourseReplaceVO>> queryMyPage(ReplacePageAO ao) {
        JwtUser user = getJwtUser();
        Map<String, Object> map = MapConvert.convert(ao);
        if (ao.getPageSize() != -1) {
            map.put("pageIndex", ao.index());
        }
        map.put("myTeacherName", user.getName());
        QueryResult<List<CourseReplaceDTO>> personalReplaceRecords = queryForPage(CourseReplaceDTO.class, "personalReplaceRecords", map);
        if (personalReplaceRecords == null) {
            return null;
        }
        return getListQueryResult(personalReplaceRecords);
    }

    /**
     * 代课数据导出
     */
    @Override
    public void replaceExport(ReplacePageAO ao, HttpServletResponse response) {
        ao.setPageSize(-1);
        QueryResult<List<CourseReplaceVO>> listQueryResult = queryPage(ao);
        if (listQueryResult == null) {
            listQueryResult = new QueryResult<>();
            listQueryResult.setResult(new ArrayList<>());
        }
        List<CourseReplaceVO> result = listQueryResult.getResult();
        List<ReplaceRecordExport> list = new ArrayList<>();

        if (result != null && result.size() > 0) {
            list = result.stream().map(courseReplaceVO -> ClassConverter.aTob(courseReplaceVO, new ReplaceRecordExport())).collect(Collectors.toList());
        }
        String title = "教师代课记录";
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ReplaceRecordExport.class).head(ExcelHeadsUtil.replaceRecordHead(title)).registerWriteHandler(new CustomCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (IOException e) {
            throw new BusinessException(CodeRes.CODE_800003);
        }
    }

    /**
     * 根据id删除代课记录（逻辑删除）
     */
    @Override
    public void removeCourseReplaceById(Long id) {
        CourseReplaceDO load = courseReplaceDao.load(id);
        if (load != null) {
            if (load.getApprovalState().equals(ApprovalState.UNDER_APPROVAL)) {
                FlowVO flowVO = flowFacade.getFlowInformation(id, relatedType);
                AdjustmentCancelAO ao = new AdjustmentCancelAO();
                ao.setId(id);
                ao.setType(AdjustmentType.REPLACE.getType());
                ao.setProcessInstanceId(flowVO.getEngineFlowId());
                courseAdjustmentService.cancel(ao);
            }
            List<ReplaceSectionDO> listByReplaceId = replaceSectionService.getListByReplaceId(id);
            replaceSectionSetFatherIsInvalidFalse(load, listByReplaceId);
            remove(id);
            replaceSectionService.removeByReplaceId(id);
        }
    }

    /**
     * 发起代课
     */
    @Override
    public void applyReplace(CourseReplaceApplyAO courseReplaceApplyAo) {
        SemesterVO semester = settingFacade.currentSemester();
        StartFlowAO startFlowAo = courseReplaceApplyAo.getStartFlowAo();
        Date date = new Date();
        CourseReplaceDO courseReplaceDO = ClassConverter.aTob(courseReplaceApplyAo, new CourseReplaceDO());
        Long teacherId = courseReplaceApplyAo.getTeacherId();
        Long replaceTeacherId = courseReplaceApplyAo.getReplaceTeacherId();
        List<String> replaceCourseTime = courseReplaceApplyAo.getReplaceCourseDetailList().stream().map(ReplaceCourseDetailAO::getCourseTime).collect(Collectors.toList());
        List<TeacherDto> teacherList = foundationFacade.getTeachersByIds(String.valueOf(teacherId));
        //判断是否有审批中的调代课
        checkCourseTime(teacherList,replaceCourseTime, teacherId);
        //判断课表是否有更新
        checkWhetherSameTable(courseReplaceApplyAo, semester, teacherId);
        String result = null;
        List<Long> publishTeacherUserIds = null;
        if (replaceTeacherId != null) {
            String teacherIdStr = teacherId + "," + replaceTeacherId;
            List<TeacherDto> teachers = foundationFacade.getTeachersByIds(teacherIdStr);
            if (teachers != null && !teachers.isEmpty()) {
                publishTeacherUserIds = teachers.stream().map(TeacherDto::getUserId).collect(Collectors.toList());
                setTeacherInfo(courseReplaceDO, teacherId, replaceTeacherId, teachers);
            }
            List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumIds(semester.getId(), null, replaceTeacherId);
            courseReplaceDO.setReplaceCurriculumTableId(curriculumTableIds.get(0));
        } else {
            if (courseReplaceApplyAo.getIsAcademicAffairsSectionArrange()) {
                courseReplaceDO.setReplaceTeacherName("待安排");
            }
            if (teacherList != null && teacherList.size() > 0) {
                TeacherDto teacherDto = teacherList.get(0);
                courseReplaceDO.setTeacherName(teacherDto.getName());
                courseReplaceDO.setDepartmentName(teacherDto.getDepartmentName());
            }
        }
        result = saveSection(courseReplaceApplyAo, startFlowAo, date, courseReplaceDO, teacherId, result, publishTeacherUserIds);
        if (result != null) {
            JwtUser jwtUser = this.getJwtUser();
            startApprovalAsncy.startReplaceApprovalApply(jwtUser,startFlowAo, courseReplaceDO, result);
        }
    }

    private void checkWhetherSameTable(CourseReplaceApplyAO courseReplaceApplyAo, SemesterVO semester, Long teacherId) {
        Map<Integer, WeekConfigVO> weekMap = settingFacade.weekConfigNumberMap(semester.getId());
        //获取教师周课表
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTable = generalTableService.teacherTableWeekDetailBatch(Collections.singletonList(teacherId), new ArrayList<>(weekMap.keySet()));
        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> teacherMap = teacherTable.get(teacherId);
        courseReplaceApplyAo.getReplaceCourseDetailList().forEach(courseDetailAo ->{
            String courseTime = courseDetailAo.getCourseTime();
            Integer weekNumber = CourseTimeUtils.getWeekNumber(courseTime);
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> nowTeacherTable = new HashMap<>(6);
            if (!CollectionUtils.isEmpty(teacherMap)) {
                nowTeacherTable = teacherMap.get(weekNumber);
            }
            if (nowTeacherTable == null || nowTeacherTable.isEmpty()) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
            // 周几
            Map<String, List<TeacherAbbDetailVO>> dayOfWeekTable = nowTeacherTable.get(CourseTimeUtils.getDayOfWeek(courseTime));
            if (dayOfWeekTable == null || dayOfWeekTable.isEmpty()) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
            // 节次
            List<TeacherAbbDetailVO> teacherAbbDetailVo = dayOfWeekTable.get(CourseTimeUtils.getSection(courseTime));
            if (teacherAbbDetailVo == null || teacherAbbDetailVo.isEmpty()) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
            TeacherAbbDetailVO teacherAbbDetail = teacherAbbDetailVo.get(0);
            if (!teacherAbbDetail.getClassOrRoomId().equals(courseDetailAo.getCourseRoomId())) {
                throw new BusinessException(CodeRes.CODE_800013);
            }
        });
    }

    private void checkCourseTime(List<TeacherDto> teacherList,List<String> replaceCourseTime, Long teacherId) {
        String teacherName = "";
        if (!CollectionUtils.isEmpty(teacherList)) {
            teacherName = teacherList.get(0).getName();
        }
        Map<String, Object> map = new HashMap<>(3);
        map.put("courseTimeList", replaceCourseTime);
        map.put("approvalState", ApprovalState.UNDER_APPROVAL.name());
        map.put("teacherIds", Collections.singletonList(teacherId));
        // 查询代课教师记录
        List<CourseReplaceDO> getReplaceRecords = courseReplaceDao.queryForListMapper("getReplaceRecords", map);
        //查询调课教师记录
        List<CourseAdjustmentDO> courseAdjustmentDos = courseAdjustmentDao.queryForListMapper("getAdjustedRecords", map);
        if (!CollectionUtils.isEmpty(getReplaceRecords)) {
            throw new BusinessException(CodeRes.CODE_800018,teacherName);
        }
        if (!CollectionUtils.isEmpty(courseAdjustmentDos)) {
            throw new BusinessException(CodeRes.CODE_800017,teacherName);
        }
    }

    private String saveSection(CourseReplaceApplyAO courseReplaceApplyAo, StartFlowAO startFlowAo, Date date, CourseReplaceDO courseReplaceDO, Long teacherId, String result, List<Long> publishTeacherUserIds) {
        if (courseReplaceApplyAo.getIsAdminApply()) {
            courseReplaceDO.setApprovalState(ApprovalState.PASS);
            CourseReplaceServiceImpl.this.save(courseReplaceDO);
            saveSection(courseReplaceApplyAo, date, courseReplaceDO);
            if (!courseReplaceApplyAo.getIsAcademicAffairsSectionArrange() && courseReplaceApplyAo.getReplaceTeacherId() != null && courseReplaceApplyAo.getReplaceTeacherId() != 0L) {
                List<ReplaceCourseDetailAO> replaceCourseDetailList = courseReplaceApplyAo.getReplaceCourseDetailList();
                if (!replaceCourseDetailList.isEmpty()) {
                    for (ReplaceCourseDetailAO replaceCourseDetailAo : replaceCourseDetailList) {
                        Long fatherId = replaceCourseDetailAo.getAdjustmentId();
                        AbbTypeEnum type = replaceCourseDetailAo.getType();
                        AdjustmentType fatherType = null;
                        if (type.equals(AbbTypeEnum.ADJUSTMENT)) {
                            fatherType = AdjustmentType.ADJUSTMENT;
                        } else if (type.equals(AbbTypeEnum.SUBSTITUTE)) {
                            fatherType = AdjustmentType.REPLACE;
                        }
                        if (fatherType != null && fatherId != null && fatherId != 0L) {
                            if (fatherType.equals(AdjustmentType.ADJUSTMENT)) {
                                CourseAdjustmentDO fatherLoad = courseAdjustmentService.loadById(fatherId);
                                if (fatherLoad != null) {
                                    if (teacherId.equals(fatherLoad.getTeacherId())) {
                                        fatherLoad.setIsNotStatistics(true);
                                        fatherLoad.setIsInvalid(true);
                                        courseAdjustmentService.save(fatherLoad);
                                    } else if (teacherId.equals(fatherLoad.getAdjustedTeacherId())) {
                                        fatherLoad.setIsNotStatistics(true);
                                        fatherLoad.setAdjustedIsInvalid(true);
                                        courseAdjustmentService.save(fatherLoad);
                                    }
                                }
                            } else {
                                ReplaceSectionDO fatherLoad = replaceSectionService.loadById(fatherId);
                                if (fatherLoad != null) {
                                    fatherLoad.setIsConsequent(true);
                                    fatherLoad.setReplaceIsInvalid(true);
                                    replaceSectionService.save(fatherLoad);
                                }
                            }
                        }
                    }
                }
            }
            basicPushMessage.adjustmentPassPublish(courseReplaceDO.getId(), AdjustmentType.REPLACE, publishTeacherUserIds);
        } else {
            String modelId = flowFacade.getFlowsModel(startFlowAo.getRelatedType());
            result = flowFacade.checkModelStarting(modelId);
            if (!FLOW_IS_START.equals(result)) {
                courseReplaceDO.setApprovalState(ApprovalState.PASS);
            }
            CourseReplaceServiceImpl.this.save(courseReplaceDO);
            saveSection(courseReplaceApplyAo, date, courseReplaceDO);
        }
        return result;
    }

    private void setTeacherInfo(CourseReplaceDO courseReplaceDO, Long teacherId, Long replaceTeacherId, List<TeacherDto> teachers) {
        for (TeacherDto teacher : teachers) {
            if (teacher.getId().equals(teacherId)) {
                courseReplaceDO.setTeacherName(teacher.getName());
                courseReplaceDO.setDepartmentName(teacher.getDepartmentName());
            }
            if (teacher.getId().equals(replaceTeacherId)) {
                courseReplaceDO.setReplaceTeacherName(teacher.getName());
                courseReplaceDO.setReplacedDepartmentName(teacher.getDepartmentName());
            }
        }
    }

    private void startApproval(StartFlowAO startFlowAo, CourseReplaceDO courseReplaceDO, String result) {
        // 如果流程已开启，则开始审批流程
        if (FLOW_IS_START.equals(result)) {
            startFlowAo.setRelatedId(courseReplaceDO.getId());
            LOGGER.info("开始一个新的审批流程，请求JSON: " + JSON.toJSONString(startFlowAo));
            courseAdjustmentService.startFlow(startFlowAo, null, courseReplaceDO);
        } else {
            return;
        }
        FlowVO flowVO = courseAdjustmentService.getFlowDetails(courseReplaceDO.getId(), startFlowAo.getRelatedType(), AdjustmentType.REPLACE);
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("id", courseReplaceDO.getId());
        updateWrapper.set("approval_no", flowVO.getSerialNumber());
        courseReplaceDao.executeUpdate(updateWrapper);
    }

    /**
     * 保存对应节次
     */
    public void saveSection(CourseReplaceApplyAO courseReplaceApplyAo, Date date, CourseReplaceDO courseReplaceDO) {
        List<ReplaceCourseDetailAO> replaceCourseDetailList = courseReplaceApplyAo.getReplaceCourseDetailList();
        List<ReplaceSectionDO> replaceSectionDoList = new ArrayList<>();
        SemesterVO semesterVO = settingFacade.currentSemester();
        Map<Integer, WeekConfigVO> weekConfigs = settingFacade.weekConfigNumberMap(semesterVO.getId());
        List<ItemConfigVO> items = settingFacade.items(semesterVO.getId());
        Map<Integer, WeekConfigVO> weekMap = settingFacade.weekConfigNumberMap(semesterVO.getId());
        for (ReplaceCourseDetailAO replaceCourseDetailAo : replaceCourseDetailList) {
            AbbTypeEnum type = replaceCourseDetailAo.getType();
            Long fatherId = replaceCourseDetailAo.getAdjustmentId();
            ReplaceSectionDO replaceSectionDO = new ReplaceSectionDO();
            BeanUtils.copyProperties(replaceCourseDetailAo, replaceSectionDO);
            replaceSectionDO.setIsConsequent(false);
            replaceSectionDO.setReplaceIsInvalid(false);
            replaceSectionDO.setReplaceId(courseReplaceDO.getId());
            replaceSectionDO.setCreatedDate(date);
            replaceSectionDO.setModifiedDate(date);
            java.sql.Date courseTimeDate = CourseAdjustmentUtils.getDateByCourseTime(weekConfigs, replaceCourseDetailAo.getCourseTime());
            replaceSectionDO.setReplaceDate(courseTimeDate);
            if (type != null) {
                if (type.equals(AbbTypeEnum.NORMAL)) {
                    replaceSectionDO.setReplaceFatherId(0L);
                    replaceSectionDO.setOriginCourseTime(replaceCourseDetailAo.getCourseTime());
                    replaceSectionDO.setOriginTeacherId(courseReplaceDO.getTeacherId());
                    replaceSectionDO.setBeforeOriginCourseTime(replaceCourseDetailAo.getBeforeOriginCourseTime(items, weekMap));
                } else if (type.equals(AbbTypeEnum.ADJUSTMENT)) {
                    // 代课课程是调课数据
                    if (fatherId == null) {
                        throw new BusinessException(CodeRes.CODE_800008, "代课", "fatherId");
                    } else {
                        if (fatherId != 0L) {
                            CourseAdjustmentDO fatherData = courseAdjustmentService.loadById(fatherId);
                            if (fatherData != null) {
                                replaceSectionDO.setReplaceFatherType(AdjustmentType.ADJUSTMENT);
                                replaceSectionDO.setReplaceFatherId(fatherId);
                                if (courseReplaceApplyAo.getTeacherId().equals(fatherData.getTeacherId())) {
                                    replaceSectionDO.setOriginCourseTime(fatherData.getOriginCourseTime());
                                    replaceSectionDO.setOriginTeacherId(fatherData.getOriginTeacherId());
                                    replaceSectionDO.setBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                                } else if (courseReplaceApplyAo.getTeacherId().equals(fatherData.getAdjustedTeacherId())) {
                                    replaceSectionDO.setOriginCourseTime(fatherData.getAdjustedOriginCourseTime());
                                    replaceSectionDO.setOriginTeacherId(fatherData.getAdjustedOriginTeacherId());
                                    replaceSectionDO.setBeforeOriginCourseTime(fatherData.getAdjustedBeforeOriginCourseTime());
                                }
                            }
                        }
                    }
                } else if (type.equals(AbbTypeEnum.COVERADJUSTMENT)) {
                    throw new BusinessException(CodeRes.CODE_800009, "代课");
                } else {
                    if (fatherId == null) {
                        throw new BusinessException(CodeRes.CODE_800008, "代课", "fatherId");
                    } else {
                        if (fatherId != 0L) {
                            ReplaceSectionDO fatherData = replaceSectionService.loadById(fatherId);
                            if (fatherData != null) {
                                replaceSectionDO.setReplaceFatherType(AdjustmentType.REPLACE);
                                replaceSectionDO.setReplaceFatherId(fatherId);
                                replaceSectionDO.setOriginCourseTime(fatherData.getOriginCourseTime());
                                replaceSectionDO.setOriginTeacherId(fatherData.getOriginTeacherId());
                                replaceSectionDO.setBeforeOriginCourseTime(fatherData.getBeforeOriginCourseTime());
                            }
                        }
                    }
                }
            }
            replaceSectionDoList.add(replaceSectionDO);
        }
        replaceSectionService.saveBatch(replaceSectionDoList);
    }

    /**
     * 单个教师在时间范围内代课与被代课记录分页（审批通过）
     */
    @Override
    public QueryResult<List<CourseReplaceVO>> singleReplacePage(SinglePageAO ao) {
        ReplacePageAO replacePageAo = ClassConverter.aTob(ao, new ReplacePageAO());
        replacePageAo.setApprovalState(ApprovalState.PASS);
        replacePageAo.setQueryAll(true);
        return queryPage(replacePageAo);
    }

    /**
     * 根据id获取代课详情
     */
    @Override
    public CourseReplaceVO getReplaceById(Long id) {
        CourseReplaceDO load = courseReplaceDao.load(id);
        if (load != null) {
            CourseReplaceVO courseReplaceVO = ClassConverter.aTob(load, new CourseReplaceVO());
            List<ReplaceSectionDO> replaceSectionDoList = replaceSectionService.getListByReplaceId(id);
            if (replaceSectionDoList != null && !replaceSectionDoList.isEmpty()) {
                String courseRoom = replaceSectionDoList.stream().map(ReplaceSectionDO::getCourseRoom).distinct().collect(Collectors.joining("、"));
                if (!StringUtils.isEmpty(courseRoom)) {
                    courseReplaceVO.setCourseRoom(courseRoom);
                }
                List<ReplaceCourseDetailVO> replaceCourseDetailVoList = new ArrayList<>();
                for (ReplaceSectionDO replaceSectionDO : replaceSectionDoList) {
                    ReplaceCourseDetailVO replaceCourseDetailVO = new ReplaceCourseDetailVO();
                    replaceCourseDetailVO.setCourseTime(replaceSectionDO.getCourseTime());
                    replaceCourseDetailVoList.add(replaceCourseDetailVO);
                }
                courseReplaceVO.setReplaceCourseDetailList(replaceCourseDetailVoList);
            }
            courseReplaceVO.setSection(load.getSection() + "节次");
            courseReplaceVO.setReplaceTime(load.getReplaceStartDate() + " " + load.getStartSection() + " 至 " + load.getReplaceEndDate() + " " + load.getEndSection());
            courseReplaceVO.setApprovalState(load.getApprovalState().getName());
            return courseReplaceVO;
        }
        return null;
    }

    /**
     * 校验代课是否冲突
     */
    @Override
    public List<Long> replaceIsConflict(List<String> courseTimeList, List<Long> teacherIds, Boolean isQueryReplaced) {
        List<Long> list = new ArrayList<>();
        // 查询该教师是否被代课、调课或被调课记录
        Map<String, Object> map = new HashMap<>(10);
        map.put("courseTimeList", courseTimeList);
        map.put("teacherIds", teacherIds);
        map.put("approvalState", ApprovalState.UNDER_APPROVAL.name());
        if (isQueryReplaced) {
            // 查询代课教师记录
            List<CourseReplaceDO> getReplaceRecords = courseReplaceDao.queryForListMapper("getReplaceRecords", map);
            List<Long> teacherIdList = getReplaceRecords.stream().map(CourseReplaceDO::getTeacherId).collect(Collectors.toList());
            List<Long> replaceTeacherIds = getReplaceRecords.stream().map(CourseReplaceDO::getReplaceTeacherId).filter(Objects::nonNull).collect(Collectors.toList());
            list.addAll(teacherIdList);
            list.addAll(replaceTeacherIds);
            //查询调课教师记录
            List<CourseAdjustmentDO> courseAdjustmentDos = courseAdjustmentDao.queryForListMapper("getAdjustedRecords", map);
            List<Long> courseTeacherIds = courseAdjustmentDos.stream().map(CourseAdjustmentDO::getTeacherId).collect(Collectors.toList());
            List<Long> adjusterTeacherIds = courseAdjustmentDos.stream().map(CourseAdjustmentDO::getAdjustedTeacherId).filter(Objects::nonNull).collect(Collectors.toList());
            list.addAll(courseTeacherIds);
            list.addAll(adjusterTeacherIds);
            return list.stream().distinct().collect(Collectors.toList());
        } else {
            // 查询课程老师记录
            List<Map<String, Object>> result = courseReplaceDao.queryForMapMapper("getAdjustmentOrReplaceRecords2", map);
            if (result != null && !result.isEmpty()) {
                for (Map<String, Object> objectMap : result) {
                    Object teacherId = objectMap.get("teacherId");
                    if (teacherId != null) {
                        list.add(Long.valueOf(teacherId.toString()));
                    }
                }
                if (!list.isEmpty()) {
                    return list;
                }
            }
        }

        return null;
    }

    /**
     * 更新代课老师
     */
    @Override
    @Async
    public void updateReplaceTeacher(SetReplaceTeacherAO ao) {
        SemesterVO semester = settingFacade.currentSemester();
        // 查询代课老师涉及的课表ID，取第一个保存
        List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumIds(semester.getId(), null, ao.getTeacherId());
        List<TeacherDto> allTeacherByIds = foundationFacade.getAllTeacherByIds(ao.getTeacherId().toString());
        TeacherDto teacherDto = get(allTeacherByIds);
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("id", ao.getAdjustmentId())
                .set("replace_curriculum_table_id", curriculumTableIds.get(0))
                .set("replace_teacher_id", ao.getTeacherId())
                .set("replace_teacher_name", ao.getTeacherName())
                .set("replaced_department_name", teacherDto.getDepartmentName());
        courseReplaceDao.executeUpdate(wrapper);
        // 审批通过设置父ID为失效
        // 父数据置为失效
        List<ReplaceSectionDO> listByReplaceId = replaceSectionService.getListByReplaceId(ao.getAdjustmentId());
        Long teacherId = ao.getTeacherId();
        if (!listByReplaceId.isEmpty()) {
            for (ReplaceSectionDO replaceSectionDO : listByReplaceId) {
                Long fatherId = replaceSectionDO.getReplaceFatherId();
                AdjustmentType fatherType = replaceSectionDO.getReplaceFatherType();
                if (fatherType != null && fatherId != null && fatherId != 0L) {
                    if (fatherType.equals(AdjustmentType.ADJUSTMENT)) {
                        // 父数据为调课
                        CourseAdjustmentDO fatherLoad = courseAdjustmentService.loadById(fatherId);
                        if (fatherLoad != null) {
                            if (teacherId.equals(fatherLoad.getTeacherId())) {
                                // 父数据不统计
                                fatherLoad.setIsNotStatistics(true);
                                fatherLoad.setIsInvalid(true);
                                courseAdjustmentService.save(fatherLoad);
                            } else if (teacherId.equals(fatherLoad.getAdjustedTeacherId())) {
                                fatherLoad.setAdjustedIsInvalid(true);
                                courseAdjustmentService.save(fatherLoad);
                            }
                        }
                    } else {
                        // 父数据为代课
                        ReplaceSectionDO fatherLoad = replaceSectionService.loadById(fatherId);
                        if (fatherLoad != null) {
                            fatherLoad.setIsConsequent(true);
                            fatherLoad.setReplaceIsInvalid(true);
                            replaceSectionService.save(fatherLoad);
                        }
                    }
                }
            }
        }
        // 推送审批通过的消息给代课老师
        List<TeacherDto> teachers = foundationFacade.getTeachersByIds(String.valueOf(ao.getTeacherId()));
        if (teachers != null && !teachers.isEmpty()) {
            Long userId = teachers.get(0).getUserId();
            basicPushMessage.adjustmentPassPublish(ao.getAdjustmentId(), AdjustmentType.REPLACE, Collections.singletonList(userId));
        }
    }

    /**
     * 根据教师id和时间范围获取课表节次
     */
    @Override
    public SectionVO getSection(GetSectionAO getSectionAo) {
        Integer startSectionNum = getSectionAo.getStartSectionNumber();
        Integer endSectionNum = getSectionAo.getEndSectionNumber();
        checkDateDuration(getSectionAo);
        SemesterVO semesterVO = settingFacade.currentSemester();
        List<WeekConfigVO> weekConfigVos = settingFacade.weekConfig(semesterVO.getId());
        List<WeekConfigVO> resultWeek = new ArrayList<>();
        for (WeekConfigVO weekConfigVO : weekConfigVos) {
            boolean weekEndDayAfterSectionStart = DateUtil.endOfDay(weekConfigVO.getEndDay()).after(DateUtil.beginOfDay(getSectionAo.getStartDate())) || weekConfigVO.getEndDay().equals(getSectionAo.getStartDate());
            boolean weekStartDayBeforeSectionEnd = DateUtil.beginOfDay(weekConfigVO.getStartDay()).before(DateUtil.endOfDay(getSectionAo.getEndDate())) || weekConfigVO.getStartDay().equals(getSectionAo.getEndDate());
            if (weekEndDayAfterSectionStart && weekStartDayBeforeSectionEnd) {
                resultWeek.add(weekConfigVO);
            }
        }
        Map<Integer, Set<String>> courseTimeMaps = new HashMap<>(10);
        if (resultWeek.size() > 1) {
            setMapIfMoreThanOneWeek(getSectionAo, endSectionNum, resultWeek, courseTimeMaps);
        } else if (resultWeek.size() == 1) {
            setMapIfOnlyOneWeek(getSectionAo, startSectionNum, endSectionNum, resultWeek, courseTimeMaps);
        }
        Long teacherId = getSectionAo.getTeacherId();
        Set<Integer> integers = courseTimeMaps.keySet();
        List<ReplaceCourseDetailVO> replaceCourseDetailVoList = new ArrayList<>();
        for (Integer number : integers) {
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = generalTableService.teacherTableWeekDetail(teacherId, number);
            Set<String> courseTimeSets = courseTimeMaps.get(number);
            if (weekEnumMapMap != null && !weekEnumMapMap.isEmpty()) {
                for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapEntry : weekEnumMapMap.entrySet()) {
                    WeekEnum weekEnum = weekEnumMapEntry.getKey();
                    String week = WeekEnum.getDayOfWeek(weekEnum);
                    Map<String, List<TeacherAbbDetailVO>> value1 = weekEnumMapEntry.getValue();
                    if (value1 != null && !value1.isEmpty()) {
                        for (Map.Entry<String, List<TeacherAbbDetailVO>> stringListEntry : value1.entrySet()) {
                            String key = stringListEntry.getKey();
                            List<TeacherAbbDetailVO> value2 = stringListEntry.getValue();
                            if (value2 != null && !value2.isEmpty()) {
                                TeacherAbbDetailVO teacherAbbDetailVO = value2.get(0);
                                if (teacherAbbDetailVO != null && teacherAbbDetailVO.getTeacherId().equals(teacherId) && !teacherAbbDetailVO.getType().equals(AbbTypeEnum.ELECTIVE) && !"自修".equals(teacherAbbDetailVO.getCourseName())) {
                                    String sectionStr = "第" + key + "节";
                                    String courseTimeStr = week + sectionStr;
                                    for (String courseTime : courseTimeSets) {
                                        if (courseTime.substring(courseTime.indexOf("周周") + 1).equals(courseTimeStr)) {
                                            ReplaceCourseDetailVO replaceCourseDetailVO = new ReplaceCourseDetailVO();
                                            replaceCourseDetailVO.setCourseRoom(teacherAbbDetailVO.getClassOrRoomName());
                                            replaceCourseDetailVO.setCourseRoomId(teacherAbbDetailVO.getClassOrRoomId());
                                            replaceCourseDetailVO.setCourseTime(courseTime);
                                            replaceCourseDetailVO.setStartTime(teacherAbbDetailVO.getStartTime());
                                            replaceCourseDetailVO.setEndTime(teacherAbbDetailVO.getEndTime());
                                            replaceCourseDetailVO.setType(teacherAbbDetailVO.getType());
                                            replaceCourseDetailVO.setCurriculumTableId(teacherAbbDetailVO.getCurriculumTableId());
                                            replaceCourseDetailVO.setAdjustmentId(teacherAbbDetailVO.getAdjustmentId());
                                            replaceCourseDetailVoList.add(replaceCourseDetailVO);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!replaceCourseDetailVoList.isEmpty()) {
            List<String> courseTimeList = replaceCourseDetailVoList.stream().map(ReplaceCourseDetailVO::getCourseTime).collect(Collectors.toList());
            List<Long> list = replaceIsConflict(courseTimeList, Collections.singletonList(teacherId), false);
            if (list != null) {
                throw new BusinessException(CodeRes.CODE_800005);
            }
            SectionVO sectionVO = new SectionVO();
            String courseRoomNames = replaceCourseDetailVoList.stream().map(ReplaceCourseDetailVO::getCourseRoom).filter(courseRoom -> !StringUtils.isEmpty(courseRoom)).distinct().collect(Collectors.joining("、"));
            if (!StringUtils.isEmpty(courseRoomNames)) {
                sectionVO.setRoomNames(courseRoomNames);
            }
            sectionVO.setReplaceCourseDetailList(replaceCourseDetailVoList);
            sectionVO.setSection("共" + replaceCourseDetailVoList.size() + "节次");
            return sectionVO;
        }
        return null;
    }

    private void setMapIfOnlyOneWeek(GetSectionAO getSectionAo, Integer startSectionNum, Integer endSectionNum, List<WeekConfigVO> resultWeek, Map<Integer, Set<String>> courseTimeMaps) {
        WeekConfigVO weekConfigVO = resultWeek.get(0);
        Set<String> courseTimeSets = new HashSet<>();
        if (whetherSectionInTheSameDay(getSectionAo)) {
            if (startSectionNum > endSectionNum) {
                throw new BusinessException(CodeRes.CODE_800001);
            }
            String dayOfWeek = DateUtils.getDayOfWeek(getSectionAo.getStartDate());
            String section = CourseAdjustmentUtils.number2Chinese(startSectionNum);
            String fullSection = "第" + section + "节";
            String courseTime = weekConfigVO.getChineseWeekNumber() + dayOfWeek + fullSection;
            courseTimeSets.add(courseTime);
            while (!startSectionNum.equals(endSectionNum)) {
                startSectionNum++;
                section = CourseAdjustmentUtils.number2Chinese(startSectionNum);
                fullSection = "第" + section + "节";
                courseTime = weekConfigVO.getChineseWeekNumber() + dayOfWeek + fullSection;
                courseTimeSets.add(courseTime);
            }
        } else {
            List<Date> dateList = CourseAdjustmentUtils.findDates(getSectionAo.getStartDate(), getSectionAo.getEndDate());
            if (CourseAdjustmentUtils.getDatePlus(getSectionAo.getStartDate(), 1).equals(getSectionAo.getEndDate())) {
                for (Date date : dateList) {
                    String dayOfWeek = DateUtils.getDayOfWeek(date);
                    if (date.equals(getSectionAo.getStartDate())) {
                        courseTimeSets.addAll(getCourseTimeSetFromDayOfWeekAndStartSection(startSectionNum, weekConfigVO.getChineseWeekNumber(), dayOfWeek));
                    } else {
                        courseTimeSets.addAll(getCourseTimeSetFromDayOfWeekAndEndSection(endSectionNum, weekConfigVO.getChineseWeekNumber(), dayOfWeek));
                    }
                }
            } else {
                for (Date date : dateList) {
                    String weekStr = DateUtils.getDayOfWeek(date);
                    if (date.equals(getSectionAo.getStartDate())) {
                        courseTimeSets.addAll(getCourseTimeSetFromDayOfWeekAndStartSection(startSectionNum, weekConfigVO.getChineseWeekNumber(), weekStr));
                    } else if (date.equals(getSectionAo.getEndDate())) {
                        courseTimeSets.addAll(getCourseTimeSetFromDayOfWeekAndEndSection(endSectionNum, weekConfigVO.getChineseWeekNumber(), weekStr));
                    } else {
                        for (int i = 1; i <= NUMBER_NINE; i++) {
                            String section = CourseAdjustmentUtils.number2Chinese(i);
                            String fullSection = "第" + section + "节";
                            String courseTimeStr = weekConfigVO.getChineseWeekNumber() + weekStr + fullSection;
                            courseTimeSets.add(courseTimeStr);
                        }
                    }
                }
            }
        }
        courseTimeMaps.put(weekConfigVO.getNumber(), courseTimeSets);
    }

    private void setMapIfMoreThanOneWeek(GetSectionAO getSectionAo, Integer endSectionNum, List<WeekConfigVO> resultWeek, Map<Integer, Set<String>> courseTimeMaps) {
        if (resultWeek.size() == NUMBER_TWO) {
            for (WeekConfigVO weekConfigVO : resultWeek) {
                Set<String> courseTimeSets = new HashSet<>();
                if (whetherSectionStartDateAndWeekStartDayInTheSameDay(getSectionAo, weekConfigVO) || whetherSectionInTheSameDay(getSectionAo) || whetherSectionInWeekDuration(getSectionAo, weekConfigVO)) {
                    courseTimeSets.addAll(getCourseTimeSets(getSectionAo, weekConfigVO));
                } else if (whetherSectionEndAndWeekStartInTheSameDay(getSectionAo, weekConfigVO) || whetherSectionEndAndWeekEndInTheSameDay(getSectionAo, weekConfigVO) || whetherSectionAndWeekOverlapAroundSectionEnd(getSectionAo, weekConfigVO)) {
                    courseTimeSets.addAll(getCourseTimeSetFromEndSection(getSectionAo.getEndDate(), endSectionNum, weekConfigVO.getStartDay(), weekConfigVO.getChineseWeekNumber()));
                }
                Set<String> courseTimeSet = courseTimeMaps.get(weekConfigVO.getNumber());
                if (courseTimeSet == null) {
                    courseTimeMaps.put(weekConfigVO.getNumber(), courseTimeSets);
                } else {
                    courseTimeSet.addAll(courseTimeSets);
                    courseTimeMaps.put(weekConfigVO.getNumber(), courseTimeSet);
                }
            }
        } else {
            for (WeekConfigVO weekConfigVO : resultWeek) {
                Set<String> courseTimeSets = new HashSet<>();
                if (whetherSectionStartDateAndWeekStartDayInTheSameDay(getSectionAo, weekConfigVO) || whetherSectionInTheSameDay(getSectionAo) || whetherSectionInWeekDuration(getSectionAo, weekConfigVO)) {
                    courseTimeSets.addAll(getCourseTimeSets(getSectionAo, weekConfigVO));
                } else if (whetherSectionEndAndWeekStartInTheSameDay(getSectionAo, weekConfigVO) || whetherSectionEndAndWeekEndInTheSameDay(getSectionAo, weekConfigVO) || whetherSectionAndWeekOverlapAroundSectionEnd(getSectionAo, weekConfigVO)) {
                    courseTimeSets.addAll(getCourseTimeSetFromEndSection(getSectionAo.getEndDate(), endSectionNum, weekConfigVO.getStartDay(), weekConfigVO.getChineseWeekNumber()));
                } else {
                    List<Date> dateList = CourseAdjustmentUtils.findDates(weekConfigVO.getStartDay(), weekConfigVO.getEndDay());
                    for (Date date : dateList) {
                        String dayOfWeek = DateUtils.getDayOfWeek(date);
                        for (int i = 1; i <= NUMBER_NINE; i++) {
                            String section = CourseAdjustmentUtils.number2Chinese(i);
                            String fullSection = "第" + section + "节";
                            String courseTime = weekConfigVO.getChineseWeekNumber() + dayOfWeek + fullSection;
                            courseTimeSets.add(courseTime);
                        }
                    }

                }
                Set<String> strings = courseTimeMaps.get(weekConfigVO.getNumber());
                if (strings == null) {
                    courseTimeMaps.put(weekConfigVO.getNumber(), courseTimeSets);
                } else {
                    strings.addAll(courseTimeSets);
                    courseTimeMaps.put(weekConfigVO.getNumber(), strings);
                }
            }
        }
    }

    private boolean whetherSectionAndWeekOverlapAroundSectionEnd(GetSectionAO getSectionAo, WeekConfigVO weekConfigVO) {
        return getSectionAo.getEndDate().after(weekConfigVO.getStartDay()) && getSectionAo.getEndDate().before(weekConfigVO.getEndDay());
    }

    private boolean whetherSectionEndAndWeekEndInTheSameDay(GetSectionAO getSectionAo, WeekConfigVO weekConfigVO) {
        return getSectionAo.getEndDate().equals(weekConfigVO.getEndDay());
    }

    private boolean whetherSectionEndAndWeekStartInTheSameDay(GetSectionAO getSectionAo, WeekConfigVO weekConfigVO) {
        return getSectionAo.getEndDate().equals(weekConfigVO.getStartDay());
    }

    /**
     * 节次是否都在同一天
     *
     * @param getSectionAo
     * @return
     */
    private boolean whetherSectionInTheSameDay(GetSectionAO getSectionAo) {
        return getSectionAo.getStartDate().equals(getSectionAo.getEndDate());
    }

    /**
     * 节次是否在周次时间范围内
     *
     * @param getSectionAo
     * @param weekConfigVO
     * @return
     */
    private boolean whetherSectionInWeekDuration(GetSectionAO getSectionAo, WeekConfigVO weekConfigVO) {
        return getSectionAo.getStartDate().after(weekConfigVO.getStartDay()) && getSectionAo.getStartDate().before(weekConfigVO.getEndDay());
    }

    /**
     * 节次开始日期和周开始日期是否相同
     *
     * @param getSectionAo
     * @param weekConfigVO
     * @return
     */
    private boolean whetherSectionStartDateAndWeekStartDayInTheSameDay(GetSectionAO getSectionAo, WeekConfigVO weekConfigVO) {
        return getSectionAo.getStartDate().equals(weekConfigVO.getStartDay());
    }

    /**
     * 校验日期范围
     *
     * @param getSectionAo
     */
    private void checkDateDuration(GetSectionAO getSectionAo) {
        if (getSectionAo.getStartDate().getTime() > getSectionAo.getEndDate().getTime()) {
            throw new BusinessException(CodeRes.CODE_800001);
        }
    }

    /**
     * 根据课程和节次过滤出对应的代课教师列表
     */
    @Override
    public List<ReplaceTeacherVO> getReplaceTeacherList(GetReplaceTeacherListAO getReplaceTeacherListAo) {
        List<Long> sameCourseTeacherIds = getSameCourseTeacherIds(getReplaceTeacherListAo);
        Map<Integer, List<String>> weekNumberCourseTimeMap = new HashMap<>(10);
        Set<Integer> weekNumbers = new HashSet<>();
        for (ReplaceCourseDetailAO replaceCourseDetailAo : getReplaceTeacherListAo.getReplaceCourseDetailList()) {
            List<String> noWeekCourseTimeList = weekNumberCourseTimeMap.get(replaceCourseDetailAo.getWeekNumber());
            if (noWeekCourseTimeList == null) {
                ArrayList<String> someWeekCourseTimeList = new ArrayList<>();
                someWeekCourseTimeList.add(replaceCourseDetailAo.getNoWeekCourseTime());
                weekNumberCourseTimeMap.put(replaceCourseDetailAo.getWeekNumber(), someWeekCourseTimeList);
            } else {
                noWeekCourseTimeList.add(replaceCourseDetailAo.getNoWeekCourseTime());
                weekNumberCourseTimeMap.put(replaceCourseDetailAo.getWeekNumber(), noWeekCourseTimeList);
            }
            weekNumbers.add(replaceCourseDetailAo.getWeekNumber());
        }

        List<Long> replaceByOtherTeacherIds = getAlreadyReplaceByOtherTeacherIds(getReplaceTeacherListAo, sameCourseTeacherIds);
        if (!CollectionUtils.isEmpty(replaceByOtherTeacherIds)) {
            sameCourseTeacherIds.removeAll(replaceByOtherTeacherIds);
        }
        Set<Long> leavingOrBusinessTeacherIds = getLeavingOrBusinessTeacherIds(getReplaceTeacherListAo, sameCourseTeacherIds);
        if (!CollectionUtils.isEmpty(leavingOrBusinessTeacherIds)) {
            sameCourseTeacherIds.removeAll(leavingOrBusinessTeacherIds);
        }

        Set<Long> needRemoveTeacherIdSet = getNeedRemoveTeacherIdSet(sameCourseTeacherIds, weekNumberCourseTimeMap, weekNumbers);
        if (!CollectionUtils.isEmpty(needRemoveTeacherIdSet)) {
            sameCourseTeacherIds.removeAll(needRemoveTeacherIdSet);
        }
        List<ReplaceTeacherVO> resultList = new ArrayList<>();
        String teacherIds = sameCourseTeacherIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        List<TeacherDto> teachers = foundationFacade.getTeachersByIds(teacherIds);
        Map<Long, String[]> teacherNameMap = new HashMap<>(10);
        for (TeacherDto teacher : teachers) {
            Long id = teacher.getId();
            String[] name = {teacher.getName(), teacher.getPhone()};
            teacherNameMap.put(id, name);
        }
        for (Long teacherId : sameCourseTeacherIds) {
            ReplaceTeacherVO replaceTeacherVO = new ReplaceTeacherVO();
            replaceTeacherVO.setIsExistAdjustment(false);
            replaceTeacherVO.setTeacherId(teacherId);
            String[] strings = teacherNameMap.get(teacherId);
            replaceTeacherVO.setTeacherName(strings[0] + strings[1]);
            replaceTeacherVO.setTeacherName(strings[0]);
            resultList.add(replaceTeacherVO);
        }
        return resultList;
    }

    /**
     * todo 循环获取教师课表信息需优化
     * 获取需要移除的教师id列表
     *
     * @param sameCourseTeacherIds
     * @param weekNumberCourseTimeMap
     * @param weekNumbers
     * @return
     */
    private Set<Long> getNeedRemoveTeacherIdSet(List<Long> sameCourseTeacherIds, Map<Integer, List<String>> weekNumberCourseTimeMap, Set<Integer> weekNumbers) {
        Set<Long> needRemoveTeacherIdSet = new HashSet<>();
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> tableWeekDetailBatchMap = generalTableService.teacherTableWeekDetailBatch(sameCourseTeacherIds, new ArrayList<>(weekNumbers));
        for (Long teacherId : sameCourseTeacherIds) {
            Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekTypeMap = tableWeekDetailBatchMap.get(teacherId);
            if (!CollectionUtils.isEmpty(weekTypeMap)) {
                for (Integer weekNumber : weekNumbers) {
                    List<String> someWeekCourseTimeList = weekNumberCourseTimeMap.get(weekNumber);
                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = weekTypeMap.get(weekNumber);
                    if (!CollectionUtils.isEmpty(weekEnumMapMap)) {
                        for (Map.Entry<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapEntry : weekEnumMapMap.entrySet()) {
                            String dayOfWeek = WeekEnum.getDayOfWeek(weekEnumMapEntry.getKey());
                            Map<String, List<TeacherAbbDetailVO>> sectionCourseMap = weekEnumMapEntry.getValue();
                            for (Map.Entry<String, List<TeacherAbbDetailVO>> sectionCourse : sectionCourseMap.entrySet()) {
                                String section = sectionCourse.getKey();
                                String noWeekCourseTime = dayOfWeek + "第" + section + "节";
                                List<TeacherAbbDetailVO> courseInfoList = sectionCourse.getValue();
                                if (courseInfoList != null && !courseInfoList.isEmpty()) {
                                    for (TeacherAbbDetailVO teacherAbbDetailVO : courseInfoList) {
                                        if (teacherAbbDetailVO.getTeacherId().equals(teacherId) && someWeekCourseTimeList.contains(noWeekCourseTime)) {
                                            needRemoveTeacherIdSet.add(teacherId);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return needRemoveTeacherIdSet;
    }


    /**
     * 获取请假或者公出的教师id
     *
     * @param getReplaceTeacherListAo
     * @param sameCourseTeacherIds
     * @return
     */
    private Set<Long> getLeavingOrBusinessTeacherIds(GetReplaceTeacherListAO getReplaceTeacherListAo, List<Long> sameCourseTeacherIds) {
        return courseAdjustmentService.getLeavingOrBusinessTeacherIds(sameCourseTeacherIds, getReplaceTeacherListAo.getReplaceCourseDetailList());
    }

    /**
     * 获取已经有代课记录的教师id
     *
     * @param getReplaceTeacherListAo
     * @param sameCourseTeacherIds
     * @return
     */
    private List<Long> getAlreadyReplaceByOtherTeacherIds(GetReplaceTeacherListAO getReplaceTeacherListAo, List<Long> sameCourseTeacherIds) {
        List<String> courseTimeList = getReplaceTeacherListAo.getReplaceCourseDetailList().stream().map(ReplaceCourseDetailAO::getCourseTime).collect(Collectors.toList());
        return replaceIsConflict(courseTimeList, sameCourseTeacherIds, true);
    }

    /**
     * 获取课程相同的教师id列表
     *
     * @param getReplaceTeacherListAo
     * @return
     */
    private List<Long> getSameCourseTeacherIds(GetReplaceTeacherListAO getReplaceTeacherListAo) {
        List<CourseTeacherDetailVO> allTeacherAndCourse = courseAdjustmentService.getAllTeacherAndCourse();
        List<Long> sameCourseTeacherIds = allTeacherAndCourse.stream().filter(courseTeacherDetailVO -> courseTeacherDetailVO.getCourseName().equals(getReplaceTeacherListAo.getCourseName())).map(CourseTeacherDetailVO::getTeacherId).collect(Collectors.toList());
        sameCourseTeacherIds.remove(getReplaceTeacherListAo.getTeacherId());
        return sameCourseTeacherIds;
    }

    /**
     * 根据id改变审批状态
     *
     * @param id            代课id
     * @param approvalState 审批状态
     */
    @Override
    public void updateReplaceApprovalState(Long id, ApprovalState approvalState) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.eq("id", id).set("approval_state", approvalState.name());
        courseReplaceDao.executeUpdate(wrapper);
    }

    @Override
    public List<ReplaceAndSectionDTO> getReplaceAndSectionByCurriculumTableId(Long curriculumTableId) {
        Set<Long> replaceIds = replaceSectionService.getReplaceIdsByCurriculumTableId(curriculumTableId);
        if (replaceIds == null) {
            return null;
        }
        return getReplaceAndSectionDtos(replaceIds);
    }

    @Override
    public List<ReplaceAndSectionDTO> getReplaceAndSectionDtos(Set<Long> replaceIds) {
        QueryWrapper wrapper = new QueryWrapper();
        if (replaceIds != null) {
            wrapper.in("id", replaceIds);
        }
        wrapper.eq("is_unusual", "false")
                .and(w -> w.eq("approval_state", ApprovalState.PASS.name()).or().eq("approval_state", ApprovalState.UNDER_APPROVAL.name()));
        return getReplaceAndSectionDtos(wrapper);
    }

    @Override
    public List<CourseReplaceDO> getByReplaceTeacherAndIdIsNotInvalid(Long teacherId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("replace_teacher_id", teacherId).eq("approval_state", ApprovalState.PASS.name());
        return courseReplaceDao.queryForList(queryWrapper);
    }

    @Nullable
    private List<ReplaceAndSectionDTO> getReplaceAndSectionDtos(QueryWrapper wrapper) {
        List<CourseReplaceDO> courseReplaceDos = courseReplaceDao.queryForList(wrapper);
        if (courseReplaceDos == null || courseReplaceDos.isEmpty()) {
            return null;
        }
        List<Long> ids = courseReplaceDos.stream().map(CourseReplaceDO::getId).collect(Collectors.toList());
        List<ReplaceSectionDO> replaceSectionDos = replaceSectionService.getListByReplaceIds(ids);
        Map<Long, List<ReplaceSectionDO>> collect = replaceSectionDos.stream().collect(Collectors.groupingBy(ReplaceSectionDO::getReplaceId));
        List<ReplaceAndSectionDTO> result = new ArrayList<>();
        for (CourseReplaceDO courseReplaceDO : courseReplaceDos) {
            ReplaceAndSectionDTO replaceAndSectionDTO = ClassConverter.aTob(courseReplaceDO, new ReplaceAndSectionDTO());
            List<ReplaceSectionDO> replaceSectionDos1 = collect.get(courseReplaceDO.getId());
            replaceAndSectionDTO.setReplaceSectionDoList(replaceSectionDos1);
            result.add(replaceAndSectionDTO);
        }
        return result;
    }

    @Override
    public void updateStartByIds(List<Long> ids, Boolean isUnusual) {
        if (isUnusual) {
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.in("id", ids).set("is_unusual", "true");
            courseReplaceDao.executeUpdate(updateWrapper);
        }
        replaceSectionService.setInvalidByReplaceIds(ids);
        List<CourseReplaceDO> courseReplaceDos = this.queryForByIds(ids);
        if (courseReplaceDos != null && !courseReplaceDos.isEmpty()) {
            for (CourseReplaceDO courseReplaceDO : courseReplaceDos) {
                List<ReplaceSectionDO> listByReplaceId = replaceSectionService.getListByReplaceId(courseReplaceDO.getId());
                replaceSectionSetFatherIsInvalidFalse(courseReplaceDO, listByReplaceId);
            }
        }
    }

    private void replaceSectionSetFatherIsInvalidFalse(CourseReplaceDO courseReplaceDO, List<ReplaceSectionDO> listByReplaceId) {
        Long teacherId = courseReplaceDO.getTeacherId();
        if (!listByReplaceId.isEmpty()) {
            for (ReplaceSectionDO replaceSectionDO : listByReplaceId) {
                Long fatherId = replaceSectionDO.getReplaceFatherId();
                AdjustmentType fatherType = replaceSectionDO.getReplaceFatherType();
                if (fatherType != null && fatherId != null && fatherId != 0L) {
                    if (fatherType.equals(AdjustmentType.ADJUSTMENT)) {
                        // 父数据为调课
                        CourseAdjustmentDO fatherLoad = courseAdjustmentService.loadById(fatherId);
                        if (fatherLoad != null) {
                            if (teacherId.equals(fatherLoad.getTeacherId())) {
                                fatherLoad.setIsNotStatistics(false);
                                fatherLoad.setIsInvalid(false);
                                courseAdjustmentService.save(fatherLoad);
                            } else if (teacherId.equals(fatherLoad.getAdjustedTeacherId())) {
                                fatherLoad.setIsNotStatistics(false);
                                fatherLoad.setAdjustedIsInvalid(false);
                                courseAdjustmentService.save(fatherLoad);
                            }
                        }
                    } else {
                        // 父数据为代课
                        ReplaceSectionDO fatherLoad = replaceSectionService.loadById(fatherId);
                        if (fatherLoad != null) {
                            fatherLoad.setReplaceIsInvalid(false);
                            replaceSectionService.save(fatherLoad);
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<ReplaceAndSectionDTO> getReplaceAndSectionByCourseDates(List<java.sql.Date> dates) {
        Set<Long> replaceIds = replaceSectionService.getReplaceIdsByCourseDates(dates);
        if (replaceIds == null || replaceIds.isEmpty()) {
            return null;
        }
        return getReplaceAndSectionDtos(replaceIds);
    }

    @Override
    public Map<Long, ReplaceCountDTO> getReplaceCount(Set<Long> ids, String teacherName, String departmentName) {
        Map<String, Object> map = new HashMap<>(10);
        List<Long> idList = new ArrayList<>(ids);
        map.put("ids", idList);
        map.put("teacherName", teacherName);
        map.put("departmentName", departmentName);
        List<Map<String, Object>> getReplaceCount = courseReplaceDao.queryForMapMapper("getReplaceCount", map);
        if (getReplaceCount != null && !getReplaceCount.isEmpty()) {
            Map<Long, ReplaceCountDTO> result = new HashMap<>(10);
            for (Map<String, Object> objectMap : getReplaceCount) {
                ReplaceCountDTO replaceCountDTO = new ReplaceCountDTO();
                Long teacherId = Long.valueOf(objectMap.get("teacherId").toString());
                replaceCountDTO.setTeacherId(teacherId);
                replaceCountDTO.setBeReplaceCount(Integer.valueOf(objectMap.get("beReplacedCount").toString()));
                replaceCountDTO.setToReplaceCount(Integer.valueOf(objectMap.get("toReplaceCount").toString()));
                result.put(teacherId, replaceCountDTO);
            }
            return result;
        }
        return null;
    }

    @Override
    public List<ReplaceAndSectionDTO> getReplaceAndSectionByReplaceCurriculumTableId(Long curriculumTableId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("replace_curriculum_table_id", curriculumTableId)
                .eq("is_unusual", "false")
                .and(w -> w.eq("approval_state", ApprovalState.PASS.name()).or().eq("approval_state", ApprovalState.UNDER_APPROVAL.name()));
        return getReplaceAndSectionDtos(wrapper);
    }

    public Set<String> getCourseTimeSetFromDayOfWeekAndEndSection(Integer endSectionNum, String week, String dayOfWeek) {
        Set<String> courseTimeSets = new HashSet<>();
        String section = CourseAdjustmentUtils.number2Chinese(endSectionNum);
        String sectionStr = "第" + section + "节";
        String courseTimeStr = week + dayOfWeek + sectionStr;
        courseTimeSets.add(courseTimeStr);
        while (endSectionNum != 1) {
            endSectionNum--;
            section = CourseAdjustmentUtils.number2Chinese(endSectionNum);
            sectionStr = "第" + section + "节";
            courseTimeStr = week + dayOfWeek + sectionStr;
            courseTimeSets.add(courseTimeStr);
        }
        return courseTimeSets;
    }

    public Set<String> getCourseTimeSetFromDayOfWeekAndStartSection(Integer startSectionNum, String week, String dayOfWeek) {
        Set<String> courseTimeSets = new HashSet<>();
        String section = CourseAdjustmentUtils.number2Chinese(startSectionNum);
        String fullSection = "第" + section + "节";
        String courseTime = week + dayOfWeek + fullSection;
        courseTimeSets.add(courseTime);
        while (!startSectionNum.equals(NUMBER_NINE)) {
            startSectionNum++;
            section = CourseAdjustmentUtils.number2Chinese(startSectionNum);
            fullSection = "第" + section + "节";
            courseTime = week + dayOfWeek + fullSection;
            courseTimeSets.add(courseTime);
        }
        return courseTimeSets;
    }

    public Set<String> getCourseTimeSetFromEndSection(java.sql.Date endDate, Integer endSectionNum, java.sql.Date startDay, String week) {
        Set<String> courseTimeSets = new HashSet<>();
        if (endDate.equals(startDay)) {
            String dayOfWeek = DateUtils.getDayOfWeek(endDate);
            courseTimeSets = getCourseTimeSetFromDayOfWeekAndEndSection(endSectionNum, week, dayOfWeek);
        } else {
            List<Date> dateList = CourseAdjustmentUtils.findDates(startDay, endDate);
            for (Date date : dateList) {
                String weekStr = DateUtils.getDayOfWeek(date);
                if (date.equals(endDate)) {
                    courseTimeSets = getCourseTimeSetFromDayOfWeekAndEndSection(endSectionNum, week, weekStr);
                } else {
                    for (int i = 1; i <= NUMBER_NINE; i++) {
                        String section = CourseAdjustmentUtils.number2Chinese(i);
                        String sectionStr = "第" + section + "节";
                        String courseTimeStr = week + weekStr + sectionStr;
                        courseTimeSets.add(courseTimeStr);
                    }
                }
            }
        }
        return courseTimeSets;
    }

    /**
     * 获取范围内课程时间集
     *
     * @param getSectionAo
     * @param weekConfigVO
     * @return
     */
    public Set<String> getCourseTimeSets(GetSectionAO getSectionAo, WeekConfigVO weekConfigVO) {
        Set<String> courseTimeSets = new HashSet<>();
        if (getSectionAo.getStartDate().equals(weekConfigVO.getEndDay())) {
            String dayOfWeek = DateUtils.getDayOfWeek(getSectionAo.getStartDate());
            courseTimeSets = getCourseTimeSetFromDayOfWeekAndStartSection(getSectionAo.getStartSectionNumber(), weekConfigVO.getChineseWeekNumber(), dayOfWeek);
        } else {
            List<Date> dateList = CourseAdjustmentUtils.findDates(getSectionAo.getStartDate(), weekConfigVO.getEndDay());
            for (Date date : dateList) {
                String dayOfWeek = DateUtils.getDayOfWeek(date);
                if (date.equals(getSectionAo.getStartDate())) {
                    courseTimeSets.addAll(getCourseTimeSetFromDayOfWeekAndStartSection(getSectionAo.getStartSectionNumber(), weekConfigVO.getChineseWeekNumber(), dayOfWeek));
                } else {
                    for (int i = 1; i <= NUMBER_NINE; i++) {
                        String section = CourseAdjustmentUtils.number2Chinese(i);
                        String fullSection = "第" + section + "节";
                        String courseTime = weekConfigVO.getChineseWeekNumber() + dayOfWeek + fullSection;
                        courseTimeSets.add(courseTime);
                    }
                }
            }
        }
        return courseTimeSets;
    }


    /**
     * jwt 用户校验
     *
     * @return
     */
    private JwtUser getJwtUser() {
        JwtUser user = UserContextHolder.getUser();
        if (user == null) {
            throw new BusinessException(CodeRes.CODE_800002);
        }
        return user;
    }
}
