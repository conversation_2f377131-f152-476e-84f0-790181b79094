package com.xiaoshan.edu.courseadjustment.excel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExcelHeadsUtil {
    /**
     * 调课列表 excel表头
     * @param title
     * @return
     */
    public static List<List<String>> adjustmentRecordHead(String title) {
        String[] head = {"审批编号", "调课课程", "调课时间", "调课教室", "调课教师", "被调课程", "被调时间", "被调教室", "被调教师", "申请时间", "审批状态"};
        return getLists(title, head);
    }


    /**
     *
     * @param title
     * @return
     */
    public static List<List<String>> replaceRecordHead(String title) {
        String[] head = {"审批编号", "课程名称", "代课时间", "代课节次", "课程教室", "课程老师", "代课老师", "申请时间", "审批状态"};
        return getLists(title, head);
    }
    /**
     *  调代课统计 excel表头
     * @param title
     * @return
     */
    public static List<List<String>> statisticsAdjustmentAndReplace(String title){
        String[] head = {"序号", "教师姓名", "部门", "巡课次数", "代课次数" , "代课节数", "被代次数", "被代节数"};
        return getLists(title, head);
    }

    /***
     * 单个教师调课统计 excel表头
     * @param title
     * @return
     */
    public static List<List<String>> statisticsPersonalAdjustment(String title){
        String[] head = {"审批编号", "调课课程", "调课时间", "调课教师", "被调课程", "被调时间", "被调教师", "申请时间"};
        return getLists(title, head);
    }
    /**
     *
     * @param title
     * @return
     */
    public static List<List<String>> statisticsPersonalReplace(String title){
        String[] head = {"审批编号", "课程名称", "代课时间", "代课节次", "课程老师", "代课老师", "申请时间"};
        return getLists(title, head);
    }

    public static List<List<String>> getLists(String title, String[] head) {
        List<List<String>> list = new ArrayList<>();
        for (String s : head) {
            ArrayList<String> l = new ArrayList<>();
            l.add(title);
            l.add(s);
            list.add(l);
        }
        return list;
    }
}
