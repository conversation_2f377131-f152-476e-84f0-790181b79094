package com.xiaoshan.edu.timetable.utils;

import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.basic.vo.RoomVO;
import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class ClassTableUtils {

    private static final Pattern PATTERN_CLASS = Pattern.compile("[0-9]*");

    /**
     * 纯数字为班级
     */
    public static Boolean isClass(String classOrRoomName) {
        Matcher isNum = PATTERN_CLASS.matcher(classOrRoomName);
        return isNum.matches();
    }

    public static Boolean isClassRoom(String classOrRoomId) {
        return classOrRoomId.endsWith("ROOM");
    }

    public static Long getClassOrRoomId(String classOrRoomId) {
        if (isClassRoom(classOrRoomId)) {
            String roomId = classOrRoomId.substring(0, classOrRoomId.length() - 4);
            return Long.parseLong(roomId);
        } else {
            return Long.parseLong(classOrRoomId);
        }
    }

    public static String getClassOrRoomName(Map<Long, ClassVO> classesIdMap, Map<Long, RoomVO> roomIdMap, String classOrRoomId) {
        if (isClassRoom(classOrRoomId)) {
            return roomIdMap.get(getClassOrRoomId(classOrRoomId)).getName();
        } else {
            return classesIdMap.get(getClassOrRoomId(classOrRoomId)).getName();
        }
    }

    public static String getClassOrRoomId(Map<String, ClassVO> classesMap, Map<String, RoomVO> roomMap, String classOrRoomName) {
        if (classesMap != null) {
            if (classesMap.containsKey(classOrRoomName)) {
                return String.valueOf(classesMap.get(classOrRoomName).getId());
            }
        }
        if (roomMap != null) {
            if (roomMap.containsKey(classOrRoomName)) {
                return roomMap.get(classOrRoomName).getId() + "ROOM";
            }
        }
        return null;
    }

    /**
     * 检测是否为有效班级
     */
    public static Boolean checkClassId(List<DataTableDTO> classTable, String classId) {
        for (DataTableDTO dto : classTable) {
            if (dto.getClassOrRoomId().equals(classId)) {
                return true;
            }
        }
        return false;
    }

    public static Boolean checkClassAbbStudentAtClassOrRoom(List<DataTableDTO> classTable, Map<Long, List<StudentTableDTO>> studentTable, WeekEnum week,
                                                            String section, String abbreviation, Long teacherId, String classOrRoomId) {
        for (DataTableDTO dto : classTable) {
            if (dto.getDetail() != null) {
                Map<String, String> detailMap = dto.getDetail().get(week);
                if (detailMap != null) {
                    String courseAll = detailMap.get(section);
                    if (courseAll != null) {
                        String[] abbes = courseAll.split(Constant.SPLITSTRING);
                        Set<String> abbs = new HashSet<>(Arrays.asList(abbes));
                        for (String abb : abbs) {
                            if (abbreviation.equals(abb)) {
                                if (StudentTableUtils.checkAbbTeacherClassOrRoomId(studentTable, abb, teacherId, classOrRoomId)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Boolean checkClass(Map<String, List<String>> classLeaveMap, List<DataTableDTO> classTable, Long classId, String abbreviation) {
        for (DataTableDTO classDto : classTable) {
            if (classDto.getClassOrRoomId().equals(String.valueOf(classId))) {
                List<String> leaveClassList = classLeaveMap.get(classDto.getClassOrRoomId());
                for (WeekEnum week : classDto.getDetail().keySet()) {
                    Map<String, String> detaila = classDto.getDetail().get(week);
                    for (String section : detaila.keySet()) {
                        if (leaveClassList.contains(week + section + abbreviation)) {
                            String[] abbs = abbreviation.split(Constant.SPLITSTRING);
                            for (String abb : abbs) {
                                if (abbreviation.equals(abb)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public static Boolean checkLeaveClass(Map<String, List<String>> classLeaveMap, List<DataTableDTO> classTable, Long classId, WeekEnum wweek, String wsection, String abbreviation) {
        for (DataTableDTO classDto : classTable) {
            if (classDto.getClassOrRoomId().equals(String.valueOf(classId))) {
                List<String> leaveClassList = classLeaveMap.get(classDto.getClassOrRoomId());
                for (WeekEnum week : classDto.getDetail().keySet()) {
                    Map<String, String> detaila = classDto.getDetail().get(week);
                    for (String section : detaila.keySet()) {
                        String[] abbs = detaila.get(section).split(Constant.SPLITSTRING);
                        for (String abb : abbs) {
                            if (leaveClassList.contains(wweek + wsection + abbreviation)) {
                                if (abbreviation.equals(abb)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

}
