package com.xiaoshan.edu.timetable.entity;

import com.xiaoshan.edu.enums.timetable.CurriculumTypeEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV1Ext;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.Indexes;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.Unique;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity("curriculum")
@Table("tim_curriculum")
@TableDef(comment = "课程")
public class CurriculumDO extends BaseV1Ext {

    private static final long serialVersionUID = 1L;

    public CurriculumDO() {
    }

    @ColumnDef(indexes = @Indexes(unique = @Unique), comment = "课程编码")
    private String code;

    @ColumnDef(indexes = @Indexes(unique = @Unique), comment = "课程名称")
    private String name;

    @ColumnDef(indexes = @Indexes(unique = @Unique), comment = "课程简称")
    private String abbreviation;

    @ColumnDef(comment = "课程性质")
    private CurriculumTypeEnum type;

    @ColumnDef(comment = "系统级别")
    private Boolean sys;

    @ColumnDef(comment = "备注")
    private String remark;

}
