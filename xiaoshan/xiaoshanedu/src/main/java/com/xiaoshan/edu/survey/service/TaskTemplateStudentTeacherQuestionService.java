package com.xiaoshan.edu.survey.service;

import com.xiaoshan.edu.ao.survey.*;
import com.xiaoshan.edu.enums.survey.SurveyObj;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.vo.survey.CurriculumStateVO;
import com.xiaoshan.edu.vo.survey.QuestionShowVO;
import com.xiaoshan.edu.vo.survey.TeacherStatisticVO;
import org.springframework.web.multipart.MultipartFile;
import start.framework.service.SqlBaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface TaskTemplateStudentTeacherQuestionService extends SqlBaseService<TaskTemplateStudentTeacherQuestionDO,Long> {

    /**
     * 删除调查调查任务管理的所有题目
     * @param taskId
     */
    void removeByTaskId(Long taskId);

    /**
     * 选择科目列表、查询班主任
     * @param templateId
     * @param taskId
     * @param studentId
     * @return
     */
    List<CurriculumStateVO> curriculumList(Long taskId, Long templateId, Long studentId);

    /**
     * 小程序 -- 题目列表
     * @param ao
     * @return
     */
    List<QuestionShowVO> questionList(QuestionListAO ao);

    /**
     * 小程序 -- 提交题目
     * @param ao
     */
    void submit(List<QuestionData> ao);

    /**
     * 根据任务id、模板id、学生id查询
     * @param studentId
     * @param taskId
     * @param templateId
     * @return
     */
    List<TaskTemplateStudentTeacherQuestionDO> getByTaskTemplateStudentId(Long taskId, Long templateId, Long studentId);

    /**
     * 统计 -- 查看统计结果
     * @param ao
     * @return
     */
    Map<String, Map<String, Object>> statistic(StatisticAO ao);

    /**
     * 根据任务id、模板id
     * @param templateId
     * @param taskId
     * @return
     */
    List<TaskTemplateStudentTeacherQuestionDO> getByTaskTemplateId(Long taskId, Long templateId);

    /**
     * 教师端 -- 查看统计结果
     * @param ao
     * @return
     */
    List<TeacherStatisticVO> teacherResult(TeacherStatisticAO ao);

    /**
     * 统计 -- 数据导出
     * @param ao
     * @param response
     */
    void export(HttpServletResponse response, BaseStatisticAO ao);

    /**
     * 统计 -- 查询班级
     * @param taskId
     * @param templateId
     * @return
     */
    List<String> getClasses(Long taskId, Long templateId);

    /**
     * 获取teacherUserId列表
     * @param taskId
     * @return
     */
    List<Long> getTeacherUserIdList(Long taskId);

    /**
     * 根据调查任务和教师id查询，用于统计教师端每个模板的学生数量
     * @param taskId
     * @param teacherId
     * @return
     */
    List<TaskTemplateStudentTeacherQuestionDO> getDistinctStudentByTaskAndTeacherId(Long taskId, Long teacherId);

    /**
     * 提交调查问卷，isSubmit为true
     * @param taskId
     * @param studentId
     */
    void taskSubmit(Long taskId, Long studentId);

    /**
     * 统计 -- 下载Excel模板
     * @param response
     * @param type
     */
    void downloadTemplate(HttpServletResponse response, SurveyObj type);

    /**
     * 统计 -- 导入统计结果
     * @param taskId
     * @param templateId
     * @param file
     */
    void importResult(Long taskId, Long templateId, MultipartFile file);

    /**
     * 根据调查任务和学生id集合查询
     * @param taskId
     * @param studentIds
     * @return
     */
    List<TaskTemplateStudentTeacherQuestionDO> getByTaskStudentIds(Long taskId, Collection<Long> studentIds);

    /**
     * 根据taskId和templateId获取
     * @param taskId
     * @param templateId
     * @return
     */
    List<Long> getStudentIdByTaskAndTemplateId(Long taskId, Long templateId);
}
