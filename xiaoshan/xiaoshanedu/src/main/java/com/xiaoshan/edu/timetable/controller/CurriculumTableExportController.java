package com.xiaoshan.edu.timetable.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.timetable.utils.CurriculumTableExport;
import com.xiaoshan.edu.vo.timetable.OssFileVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.number.IntegerValid;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/curriculumtable/export")
@Api(tags = "课表导出接口")
public class CurriculumTableExportController extends BaseController {
	
	@Autowired
	private CurriculumTableExport curriculumTableExport;
	
	@ApiOperation("总课表")
	@AuthenticationCheck
	@GetMapping("/general/{semesterId}/{enrollmentYear}/{sectionId}")
	public ResultResponse<OssFileVO> general(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("学期") @IntegerValid Integer enrollmentYear,
			@PathVariable @ApiParam("学段ID") @LongValid Long sectionId) {
		return response(curriculumTableExport.exportGeneral(semesterId,enrollmentYear,sectionId));
	}
	
	@ApiOperation("班级教室课表-学期")
	@AuthenticationCheck
	@PostMapping("/classes/{semesterId}/{type}")
	public ResultResponse<OssFileVO> classes(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("类型(1-教室，2-班级)") @IntegerValid Integer type,
			@RequestBody @ApiParam("班级教室ID") List<Long> classOrRoomIds) {
		return response(curriculumTableExport.exportClasses(semesterId,classOrRoomIds,type));
	}
	
	@ApiOperation("老师课表-学期")
	@AuthenticationCheck
	@PostMapping("/teacher/{semesterId}")
	public ResultResponse<OssFileVO> teacher(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@RequestBody @ApiParam("老师ID") List<Long> teacherIds) {
		return response(curriculumTableExport.exportTeacher(semesterId,teacherIds));
	}
	
	@ApiOperation("学生课表-学期")
	@AuthenticationCheck
	@PostMapping("/student/{semesterId}")
	public ResultResponse<OssFileVO> student(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@RequestBody @ApiParam("学生ID") List<Long> studentIds) {
		return response(curriculumTableExport.exportStudent(semesterId,studentIds));
	}
	
	@ApiOperation("教室课表-学年学期")
	@AuthenticationCheck
	@GetMapping("/classroom/{semesterId}/{enrollmentYear}/{sectionId}/{classOrRoomId}")
	public ResultResponse<OssFileVO> classRoomExt(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("学期") @IntegerValid Integer enrollmentYear,
			@PathVariable @ApiParam("学段ID") @LongValid Long sectionId,
			@PathVariable @ApiParam("班级教室ID")@IntegerValid String classOrRoomId) {
		return response(curriculumTableExport.exportClassRoom(semesterId,enrollmentYear,sectionId,classOrRoomId));
	}
	
	@ApiOperation("班级课表-学年学期")
	@AuthenticationCheck
	@GetMapping("/classes/{semesterId}/{enrollmentYear}/{sectionId}/{classOrRoomId}")
	public ResultResponse<OssFileVO> classesExt(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("学期") @IntegerValid Integer enrollmentYear,
			@PathVariable @ApiParam("学段ID") @LongValid Long sectionId,
			@PathVariable @ApiParam("班级教室ID")@IntegerValid String classOrRoomId) {
		return response(curriculumTableExport.exportClasses(semesterId,enrollmentYear,sectionId,classOrRoomId));
	}
	
	@ApiOperation("老师课表-学年学期")
	@AuthenticationCheck
	@GetMapping("/teacher/{semesterId}/{enrollmentYear}/{sectionId}/{teacherId}")
	public ResultResponse<OssFileVO> teacherExt(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("学期") @IntegerValid Integer enrollmentYear,
			@PathVariable @ApiParam("学段ID") @LongValid Long sectionId,
			@PathVariable @ApiParam("老师ID")@IntegerValid Long teacherId) {
		return response(curriculumTableExport.exportTeacher(semesterId,enrollmentYear,sectionId,teacherId));
	}
	
	@ApiOperation("学生课表-学年学期")
	@AuthenticationCheck
	@GetMapping("/student/{semesterId}/{enrollmentYear}/{sectionId}/{studentId}")
	public ResultResponse<OssFileVO> studentExt(
			@PathVariable @ApiParam("学期ID") @LongValid Long semesterId,
			@PathVariable @ApiParam("学期") @IntegerValid Integer enrollmentYear,
			@PathVariable @ApiParam("学段ID") @LongValid Long sectionId,
			@PathVariable @ApiParam("学生ID")@IntegerValid Long studentId) {
		return response(curriculumTableExport.exportStudent(semesterId,enrollmentYear,sectionId,studentId));
	}
	
}
