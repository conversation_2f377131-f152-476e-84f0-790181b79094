package com.xiaoshan.edu.survey.controller;


import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.survey.service.TaskTemplateService;
import com.xiaoshan.edu.vo.survey.StudentTaskTemplateVO;
import com.xiaoshan.edu.vo.survey.TaskTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import java.util.List;

@RestController
@RequestMapping("/v1/survey/taskTemplate")
@Api(tags = "教学调查接口")
public class TaskTemplateController extends BaseController {

	@Autowired
	private TaskTemplateService taskTemplateService;

    @ApiOperation("统计 -- 调查模板列表")
    @AuthenticationCheck
    @GetMapping("/getByTaskId/{taskId}")
    public ResultResponse<List<TaskTemplateVO>> getByTaskId(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId) {
        return response(taskTemplateService.getByTaskId(taskId));
    }

    @ApiOperation("教师端 -- 调查模板列表")
    @AuthenticationCheck
    @GetMapping("/teacher/{taskId}")
    public ResultResponse<List<TaskTemplateVO>> templateList(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId){
        return response(taskTemplateService.templateList(taskId));
    }

    @ApiOperation("web完成情况/小程序 -- 调查模板列表")
    @AuthenticationCheck
    @GetMapping("/student/getByTaskId/{taskId}/{studentId}")
    public ResultResponse<List<StudentTaskTemplateVO>> studentGetByTaskId(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId,
                                                                          @PathVariable @ApiParam("学生id") @NotNull @LongValid Long studentId) {
        return response(taskTemplateService.studentGetByTaskId(taskId, studentId));
    }

}
