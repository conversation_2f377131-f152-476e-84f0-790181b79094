package com.xiaoshan.edu.timetable.utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.MessageFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.xiaoshan.basic.ao.MessageParentAO;
import com.xiaoshan.basic.dto.MessageDTO;
import com.xiaoshan.basic.enums.MessageTypeEnum;
import com.xiaoshan.basic.enums.PushTypeEnum;
import com.xiaoshan.basic.enums.SourceTypeEnum;
import com.xiaoshan.basic.enums.TemplatePushTypeEnum;
import com.xiaoshan.basic.vo.PushItemVO;
import com.xiaoshan.basic.vo.PushTemplateVO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.oa.dto.StudentDto;
import com.xiaoshan.oa.dto.TeacherDto;
import com.xiaoshan.oa.vo.notify.NotifyParentVO;

import lombok.extern.slf4j.Slf4j;
import start.framework.commons.rest.ConverterEditorUtils;
import start.framework.commons.utils.TimeUtils;
import start.magic.thirdparty.json.JsonArray;
import start.magic.thirdparty.json.JsonObject;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CurriculumTableDiff {

    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private MessageFacade messageFacade;

    /**
     * 课表比对
     *
     * @param curriculumTable
     * @param pretables
     * @param curtables
     */
    public void start(CurriculumTableDO curriculumTable, List<GeneralTableDO> pretables, List<GeneralTableDO> curtables) {
        Map<WeekTypeEnum, GeneralTableDO> preTablesMap = pretables.stream().collect(Collectors.toMap(GeneralTableDO::getType, Function.identity()));
        Map<WeekTypeEnum, GeneralTableDO> curtablesMap = curtables.stream().collect(Collectors.toMap(GeneralTableDO::getType, Function.identity()));
        Set<Long> teacherIds = new HashSet<>();
        Set<Long> studentIds = new HashSet<>();
        for (WeekTypeEnum type : curtablesMap.keySet()) {
            GeneralTableDO curGeneralTable = curtablesMap.get(type);
            GeneralTableDO preGeneralTable = preTablesMap.get(type);
            if (preGeneralTable == null) {
                //全部
                teacherIds.addAll(curGeneralTable.getClassTeacherIds());
                teacherIds.addAll(curGeneralTable.getTeacherIds());
                studentIds.addAll(curGeneralTable.getStuIds());
            } else {
                compare(preGeneralTable, curGeneralTable, teacherIds, studentIds);
                compare(curGeneralTable, preGeneralTable, teacherIds, studentIds);
            }
        }
        Set<Long> targetUsers = new HashSet<>();
        if (!teacherIds.isEmpty()) {
            List<TeacherDto> techs = foundationFacade.getAllTeacherByIds(StringUtils.listToString(teacherIds));
            for (TeacherDto d : techs) {
                targetUsers.add(d.getUserId());
            }
        }
        List<MessageParentAO> parentAos = new ArrayList<>();
        if (!studentIds.isEmpty()) {
            //学生需要生成对应的家长ID
            List<StudentDto> students = foundationFacade.studentsOfPost(StringUtils.listToString(new ArrayList<>(studentIds)));
            for (StudentDto stu : students) {
                if (!CollectionUtils.isEmpty(stu.getParents())) {
                    for (NotifyParentVO parent : stu.getParents()) {
                        MessageParentAO a = new MessageParentAO();
                        a.setStudentId(stu.getId());
                        a.setUserId(parent.getUserId());
                        parentAos.add(a);
                    }
                }
            }
        }
        if (targetUsers.size() > 0) {
            sendCurriculumUpdate(targetUsers, curriculumTable, CollectionUtils.isEmpty(pretables));
        }
        if (parentAos.size() > 0) {
            sendCurriculumUpdateParent(parentAos, curriculumTable, CollectionUtils.isEmpty(pretables));
        }
    }

    private void compare(GeneralTableDO preGeneralTable, GeneralTableDO curGeneralTable, Set<Long> teacherIds, Set<Long> studentIds) {
        //班主任
        for (Long teacherId : curGeneralTable.getClassTeacher().keySet()) {
            Long curClassId = curGeneralTable.getClassTeacher().get(teacherId);
            Long preClassId = preGeneralTable.getClassTeacher().get(teacherId);
            if (preClassId != null) {
                if (!curClassId.equals(preClassId)) {
                    teacherIds.add(teacherId);
                }
            } else {
                teacherIds.add(teacherId);
            }
        }
        //任课
        for (String abb : curGeneralTable.getTeacherTable().keySet()) {
            List<TeacherTableDTO> teachers = curGeneralTable.getTeacherTable().get(abb);
            for (TeacherTableDTO t : teachers) {
                for (WeekEnum week : t.getDetail().keySet()) {
                    for (String section : t.getDetail().get(week).keySet()) {
                        String classOrRoomeId = t.getDetail().get(week).get(section);
                        if (!TeacherTableUtils.checkTeacherIdClassOrRoomIdWeekSection(preGeneralTable.getTeacherTable(), week, section, classOrRoomeId, t.getTeacherId())) {
                            teacherIds.add(t.getTeacherId());
                        }
                    }
                }
            }
        }
        //学生
        Map<Long, StudentTableDTO> preStudentMap = preGeneralTable.getStudentTable().stream().collect(Collectors.toMap(StudentTableDTO::getStuId, Function.identity()));
        for (StudentTableDTO studentDto : curGeneralTable.getStudentTable()) {
            StudentTableDTO preStuDto = preStudentMap.get(studentDto.getStuId());
            if (preStuDto != null) {
                if (!preStuDto.getDetail().equals(studentDto.getDetail())) {
                    studentIds.add(studentDto.getStuId());
                }
            } else {
                studentIds.add(studentDto.getStuId());
            }
        }
    }

    @Autowired
    private SettingFacade settingFacade;

    @Value("${xiaoshan.weixin.appid}")
    private String weixinAppId;

    @Value("${xiaoshan.push_item.curriculum.message}")
    private String pushCurriculumMessage;

    @Value("${xiaoshan.push_item.curriculum.sourceid}")
    private Long pushCurriculumSourceId;
    @Value("${xiaoshan.push_item.curriculum.path}")
    private String pushCurriculumPath;
    @Value("${xiaoshan.push_item.curriculum.relatedType}")
    private String relatedTypeCurriculumPath;

    @Value("${xiaoshan.push_item.curriculum.parent.sourceid}")
    private Long pushCurriculumParentSourceId;
    @Value("${xiaoshan.push_item.curriculum.parent.path}")
    private String pushCurriculumParentPath;
    @Value("${xiaoshan.push_item.curriculum.parent.relatedType}")
    private String relatedTypeCurriculumParentPath;

    public void sendCurriculumUpdate(Set<Long> targetUserIds, CurriculumTableDO curriculumTable, Boolean isRelease) {
        String gradeName = curriculumTable.getSectionId() == 1 ? "高中" : "初中";
        gradeName = gradeName + (curriculumTable.getEnrollmentYear() + 3) + "届";
        String statusName = isRelease ? "已发布" : "有更新";
        String title = isRelease ? "课表发布通知" : "课表更新通知";
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(pushCurriculumMessage);
        Boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(pushCurriculumPath);
                    dto.setQuery("id=" + curriculumTable.getId());
                    dto.setSmsContent(String.format(template.getContent(), gradeName, curriculumTable.getOrderNo(), statusName));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(title);
                    dto.setContent(String.format(template.getContent(), gradeName, curriculumTable.getOrderNo(), statusName));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(curriculumTable.getId()));
                    json.put("relatedType", relatedTypeCurriculumPath);
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);
                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", title);
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", gradeName + "第" + curriculumTable.getOrderNo() + "学期课表" + statusName);
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", curriculumTable.getId());
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", TimeUtils.getSysTime());
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看课表详细信息");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath", pushCurriculumPath);
                    wxObject.put("miniprogram", miniprogram);

                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(pushCurriculumSourceId);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        if (targetUserIds.size() > 0) {
            dto.setTargetUserIds(new ArrayList<>(targetUserIds));
            String jsonData = ConverterEditorUtils.converter(dto, JsonObject.class).toString();
            log.info("/message/messages" + jsonData);
            messageFacade.createMessage(dto);
        }
    }

    public void sendCurriculumUpdateParent(List<MessageParentAO> parents, CurriculumTableDO curriculumTable, Boolean isRelease) {
        String gradeName = curriculumTable.getSectionId() == 1 ? "高中" : "初中";
        gradeName = gradeName + (curriculumTable.getEnrollmentYear() + 3) + "届";
        String statusName = isRelease ? "已发布" : "有更新";
        String title = isRelease ? "课表发布通知" : "课表更新通知";
        MessageDTO dto = new MessageDTO();
        PushItemVO items = settingFacade.pushItems(pushCurriculumMessage);
        Boolean isReturn = true;
        for (PushTemplateVO template : items.getTemplates()) {
            if (template.getOpenState()) {
                isReturn = false;
                if (TemplatePushTypeEnum.NOTE.getCode().equals(template.getPushType())) {
                    //短信
                    dto.setPath(pushCurriculumParentPath);
                    dto.setQuery("type=my&id=" + curriculumTable.getId());
                    dto.setSmsContent(String.format(template.getContent(), gradeName, curriculumTable.getOrderNo(), statusName));
                } else if (TemplatePushTypeEnum.MESSAGE.getCode().equals(template.getPushType())) {
                    // 站内
                    dto.setTitle(title);
                    dto.setContent(String.format(template.getContent(), gradeName, curriculumTable.getOrderNo(), statusName));
                    JsonObject json = new JsonObject();
                    json.put("relatedId", String.valueOf(curriculumTable.getId()));
                    json.put("relatedType", relatedTypeCurriculumParentPath);
                    dto.setExtra(json.toString());
                } else if (TemplatePushTypeEnum.officeAccount.getCode().equals(template.getPushType())) {
                    // 公众号
                    String templateContent = template.getContent();
                    JsonObject jsonObject = new JsonObject(templateContent);
                    String templateId = jsonObject.getString("template_id");
                    JsonObject wxObject = new JsonObject();
                    wxObject.put("template_id", templateId);
                    JsonObject dataObject = new JsonObject();
                    JsonObject firstObject = new JsonObject();
                    firstObject.put("value", title);
                    firstObject.put("color", "#173177");
                    dataObject.put("first", firstObject);
                    JsonObject keyword1 = new JsonObject();
                    keyword1.put("value", gradeName + "第" + curriculumTable.getOrderNo() + "学期课表" + statusName);
                    keyword1.put("color", "#173177");
                    dataObject.put("keyword1", keyword1);
                    JsonObject keyword2 = new JsonObject();
                    keyword2.put("value", curriculumTable.getId());
                    keyword2.put("color", "#173177");
                    dataObject.put("keyword2", keyword2);
                    JsonObject keyword3 = new JsonObject();
                    keyword3.put("value", TimeUtils.getSysTime());
                    keyword3.put("color", "#173177");
                    dataObject.put("keyword3", keyword3);
                    JsonObject remark = new JsonObject();
                    remark.put("value", "点击下方小程序可查看课表详细信息");
                    remark.put("color", "#173177");
                    dataObject.put("remark", remark);
                    wxObject.put("data", dataObject);
                    JsonObject miniprogram = new JsonObject();
                    miniprogram.put("appid", weixinAppId);
                    miniprogram.put("pagepath", pushCurriculumParentPath + "?type=my");
                    wxObject.put("miniprogram", miniprogram);

                    dto.setOfficeAccountData(wxObject.toString());
                }
            }
        }
        if (isReturn) {
            return;
        }
        dto.setSourceId(pushCurriculumParentSourceId);
        dto.setType(MessageTypeEnum.BUSINESS.getType());
        dto.setSourceType(SourceTypeEnum.APPLICATION.getType());
        dto.setPushType(PushTypeEnum.NOTICE.getType());
        if (parents.size() > 0) {
            dto.setMessageParents(parents);
            String jsonData = ConverterEditorUtils.converter(dto, JsonObject.class).toString();
            log.info("/message/messages/parents" + jsonData);
            messageFacade.createMessageToParent(dto);
        }
    }

}
