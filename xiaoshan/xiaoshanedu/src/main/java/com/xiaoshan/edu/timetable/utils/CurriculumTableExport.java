package com.xiaoshan.edu.timetable.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.basic.vo.StudentCourseListVO;
import com.xiaoshan.edu.api.facade.CourseFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.OssFacade;
import com.xiaoshan.edu.dto.*;
import com.xiaoshan.edu.enums.CourseNameEnum;
import com.xiaoshan.edu.enums.GenderEnum;
import com.xiaoshan.edu.enums.timetable.ClassTableTypeEnum;
import com.xiaoshan.edu.timetable.dto.StudentCourseTeacherDTO;
import com.xiaoshan.edu.timetable.dto.StudentTableExportDTO;
import com.xiaoshan.edu.timetable.excel.StudentExportMergeStrategy;
import com.xiaoshan.oa.dto.StudentDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.common.utils.IoUtil;
import com.xiaoshan.basic.dto.OssFileInfoDTO;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.timetable.excel.ClassesMergeStrategy;
import com.xiaoshan.edu.timetable.excel.GeneralMergeStrategy;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.timetable.GeneralTableVO;
import com.xiaoshan.edu.vo.timetable.OssFileVO;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherTableVO;

import start.framework.commons.utils.TimeUtils;
import start.magic.core.ApplicationException;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CurriculumTableExport {

    @Autowired
    private GeneralTableService generalTableService;

    @Autowired
    private CurriculumTableService curriculumTableService;

    @Autowired
    private FoundationFacade foundationFacade;

    @Autowired
    private CourseFacade courseFacade;

    @Value("${local.file.path}")
    private String localFilePath;

    @Autowired
    private OssFacade ossFacade;

    public OssFileVO exportGeneral(Long semesterId, Integer enrollmentYear, Long sectionId) {
        CurriculumTableDO tableDo = curriculumTableService.getTable(sectionId, enrollmentYear, semesterId, 0);
        if (tableDo != null) {
            List<GeneralTableVO> generalTables = generalTableService.mainTable(tableDo.getId());
            if (!CollectionUtils.isEmpty(generalTables)) {
                Map<String, List<List<String>>> tableMap = getMainTable(generalTables);
                return outExcel(CurriculumTableTypeEnum.GENERAL, tableMap, new GeneralMergeStrategy());
            }
        }

        GeneralTableVO v = new GeneralTableVO();
        v.setType(WeekTypeEnum.SINGLE);
        DataTableDTO d = new DataTableDTO();
        d.setClassOrRoomId("0");
        d.setClassOrRoomName("");
        d.setDetailv(new HashMap<>(10));
        v.setData(Collections.singletonList(d));
        Map<String, List<List<String>>> tableMap = getMainTable(Collections.singletonList(v));
        return outExcel(CurriculumTableTypeEnum.GENERAL, tableMap, new GeneralMergeStrategy());
    }

    public OssFileVO exportClasses(Long semesterId, List<Long> classOrRoomIds, Integer type) {
        List<CurriculumTableDO> curriculumTables = curriculumTableService.getTable(semesterId, 0);
        if (!CollectionUtils.isEmpty(curriculumTables)) {
            List<ExportClassesDTO> exportClassesDtos = new ArrayList<>();
            for (CurriculumTableDO curriculumTable : curriculumTables) {
                List<GeneralTableVO> classTables = generalTableService.classesTable(curriculumTable.getId());
                Map<String, DataTableDTO> singleMap = new HashMap<>(6);
                Map<String, DataTableDTO> doubleMap = new HashMap<>(6);
                //根据周类型分组
                Map<WeekTypeEnum, GeneralTableVO> typeEnumMap = classTables.stream().collect(Collectors.toMap(GeneralTableVO::getType, generalTableVo -> generalTableVo));
                //单周课表
                GeneralTableVO singleTable = typeEnumMap.get(WeekTypeEnum.SINGLE);
                if (Objects.nonNull(singleTable)) {
                    for (DataTableDTO dataTableDto : singleTable.getData()) {
                        singleMap.put(dataTableDto.getClassOrRoomId(), dataTableDto);
                    }
                }
                //双周课表
                GeneralTableVO doubleTable = typeEnumMap.get(WeekTypeEnum.DOUBLE);
                if (Objects.nonNull(doubleTable)) {
                    for (DataTableDTO dataTableDto : doubleTable.getData()) {
                        doubleMap.put(dataTableDto.getClassOrRoomId(), dataTableDto);
                    }
                }
                classOrRoomIds.forEach(s -> {
                    String classOrRoomName = null;
                    String classOrRoomId = null;
                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceSingle = new HashMap<>(10);
                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceDouble = new HashMap<>(10);
                    DataTableDTO singleDataTableDto = singleMap.get(String.valueOf(s));
                    if (Objects.nonNull(singleDataTableDto)) {
                        classOrRoomName = singleDataTableDto.getClassOrRoomName();
                        classOrRoomId = singleDataTableDto.getClassOrRoomId();
                        detailCopyAdd(detailSourceSingle, singleDataTableDto.getDetailv());
                    }
                    DataTableDTO doubleDataTableDto = doubleMap.get(String.valueOf(s));
                    if (Objects.nonNull(doubleDataTableDto)) {
                        classOrRoomName = doubleDataTableDto.getClassOrRoomName();
                        classOrRoomId = doubleDataTableDto.getClassOrRoomId();
                        detailCopyAdd(detailSourceDouble, doubleDataTableDto.getDetailv());
                    }
                    if (StringUtils.isNotEmpty(classOrRoomName)) {
                        ExportClassesDTO exportClassesDto = new ExportClassesDTO();
                        exportClassesDto.setClassOrRoomId(Long.parseLong(classOrRoomId));
                        exportClassesDto.setClassOrRoomName(classOrRoomName);
                        exportClassesDto.setDetailSourceSingle(detailSourceSingle);
                        exportClassesDto.setDetailSourceDouble(detailSourceDouble);
                        exportClassesDtos.add(exportClassesDto);
                    }
                });
            }
            Map<String, List<List<String>>> tableMap = getClassTableData(exportClassesDtos, CurriculumTableTypeEnum.CLASSES, type);
            return outExcel(CurriculumTableTypeEnum.CLASSES, tableMap, new ClassesMergeStrategy());
        }
        return outExcel(CurriculumTableTypeEnum.CLASSES);
    }

    public OssFileVO exportTeacher(Long semesterId, List<Long> teacherIds) {
        List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumList(semesterId, null, teacherIds);
        if (!CollectionUtils.isEmpty(curriculumTableIds)) {
            List<ExportTeacherDTO> exportTeacherDtos = new ArrayList<>();
            for (Long curriculumTableId : curriculumTableIds) {
                List<TeacherTableVO> teacherTables = generalTableService.teacherTable(curriculumTableId);
                Map<Long, TeacherTableDTO> singleMap = new HashMap<>(6);
                Map<Long, TeacherTableDTO> doubleMap = new HashMap<>(6);
                //根据周类型分组
                Map<WeekTypeEnum, TeacherTableVO> typeEnumMap = teacherTables.stream().collect(Collectors.toMap(TeacherTableVO::getType, teacherTableVo -> teacherTableVo));
                //单周课表
                TeacherTableVO singleTable = typeEnumMap.get(WeekTypeEnum.SINGLE);
                if (Objects.nonNull(singleTable)) {
                    singleTable.getData().forEach((k, v) -> {
                        for (TeacherTableDTO teacherTableDto : v) {
                            teacherTableDto.setCourseName(k);
                            singleMap.put(teacherTableDto.getTeacherId(), teacherTableDto);
                        }
                    });
                }
                //双周课表
                TeacherTableVO doubleTable = typeEnumMap.get(WeekTypeEnum.DOUBLE);
                if (Objects.nonNull(doubleTable)) {
                    doubleTable.getData().forEach((k, v) -> {
                        for (TeacherTableDTO teacherTableDto : v) {
                            teacherTableDto.setCourseName(k);
                            doubleMap.put(teacherTableDto.getTeacherId(), teacherTableDto);
                        }
                    });
                }
                teacherIds.forEach(l -> {
                    String cellTitle = null;
                    String teacherName = null;
                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceSingle = new HashMap<>(10);
                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceDouble = new HashMap<>(10);
                    TeacherTableDTO singleTeacherTableDto = singleMap.get(l);
                    if (Objects.nonNull(singleTeacherTableDto)) {
                        cellTitle = singleTeacherTableDto.getTeacherName() + " 教师课程表(" + singleTeacherTableDto.getCourseName() + "组)";
                        teacherName = singleTeacherTableDto.getTeacherName();
                        detailCopyAdd(detailSourceSingle, singleTeacherTableDto.getDetailv());
                    }
                    TeacherTableDTO doubleTeacherTableDto = doubleMap.get(l);
                    if (Objects.nonNull(doubleTeacherTableDto)) {
                        cellTitle = doubleTeacherTableDto.getTeacherName() + " 教师课程表(" + doubleTeacherTableDto.getCourseName() + "组)";
                        teacherName = doubleTeacherTableDto.getTeacherName();
                        detailCopyAdd(detailSourceDouble, doubleTeacherTableDto.getDetailv());
                    }
                    if (StringUtils.isNotEmpty(cellTitle)) {
                        ExportTeacherDTO exportTeacherDto = new ExportTeacherDTO();
                        exportTeacherDto.setCellTitle(cellTitle);
                        exportTeacherDto.setTeacherName(teacherName);
                        exportTeacherDto.setDetailSourceSingle(detailSourceSingle);
                        exportTeacherDto.setDetailSourceDouble(detailSourceDouble);
                        exportTeacherDtos.add(exportTeacherDto);
                    }
                });
            }
            Map<String, List<List<String>>> tableMap = getTeacherTableData(exportTeacherDtos, CurriculumTableTypeEnum.TEACHER);
            return outExcel(CurriculumTableTypeEnum.TEACHER, tableMap, new ClassesMergeStrategy());
        }
        return outExcel(CurriculumTableTypeEnum.TEACHER);
    }

    public OssFileVO exportStudent(Long semesterId, List<Long> studentIds) {
        long begin = System.currentTimeMillis();
        List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumList(semesterId, studentIds, null);
        if (!CollectionUtils.isEmpty(curriculumTableIds)) {
            Map<String, List<List<String>>> tableMap = new HashMap<>(6);
            List<GeneralTableDO> generalTableDos = generalTableService.queryByCurriculumTableIdList(curriculumTableIds);
            Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTables = generalTableService.batchStudentTableDetail(curriculumTableIds);
            ArrayList<Long> studentIdList = Lists.newArrayList(studentTables.keySet());
            List<Long> outStudentIds = studentIds.stream().filter(s -> studentIdList.contains(s)).collect(Collectors.toList());
            //学生选课
            List<StudentCourseListVO> studentCourseListVOS = courseFacade.studentCourseList(studentIds);
            Map<Long, List<StudentCourseListVO>> courseMap = studentCourseListVOS.stream().collect(Collectors.groupingBy(StudentCourseListVO::getStudentId));
            //查询学生
            HashMap<String, String> params = new HashMap<>();
            params.put("current", "1");
            params.put("size", "-1");
            params.put("stuIds", Joiner.on(",").join(outStudentIds));
            params.put("scope", "1");
            params.put("full", "true");
            params.put("headTeacher", "true");
            List<StudentDto> students = foundationFacade.studentsPost(params);
            Map<Long, StudentDto> studentIdMap = students.stream().collect(Collectors.toMap(StudentDto::getId, studentDto -> studentDto));
            //所有学生汇总信息
            List<StudentTableExportDTO> studentTableExportDtos = new ArrayList<>();
            Set<String> idNoSet = new HashSet<>(10);
            for (GeneralTableDO generalTableDo : generalTableDos) {
                List<StudentTableDTO> studentTable = generalTableDo.getStudentTable();
                List<StudentTableDTO> stuList = studentTable.stream().filter(studentTableDto -> studentIds.contains(studentTableDto.getStuId())).collect(Collectors.toList());
                getStudentTable(tableMap,stuList, studentTables,studentIdMap,studentTableExportDtos,courseMap,idNoSet);
            }
            long end = System.currentTimeMillis();
            log.info("学生导出组装数据消耗时间{}",end-begin);
            return studentOutExcel(CurriculumTableTypeEnum.STUDENT, tableMap, new StudentExportMergeStrategy(),studentTableExportDtos);
        }
        return outExcel(CurriculumTableTypeEnum.STUDENT);
    }

    public OssFileVO exportClassRoom(Long semesterId, Integer enrollmentYear, Long sectionId, String classOrRoomId) {
        classOrRoomId = classOrRoomId + "ROOM";
        CurriculumTableDO tableDo = curriculumTableService.getTable(sectionId, enrollmentYear, semesterId, 0);
        if (tableDo == null) {
            return outExcel(CurriculumTableTypeEnum.CLASSES);
        }
        String classOrRoomName = null;
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceSingle = new HashMap<>(10);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceDouble = new HashMap<>(10);
        List<GeneralTableVO> generalTables = generalTableService.mainTable(tableDo.getId());
        if (CollectionUtils.isEmpty(generalTables)) {
            return outExcel(CurriculumTableTypeEnum.CLASSES);
        }
        for (GeneralTableVO vo : generalTables) {
            for (DataTableDTO dtd : vo.getData()) {
                if (dtd.getClassOrRoomId().equals(classOrRoomId)) {
                    classOrRoomName = dtd.getClassOrRoomName();
                    if (vo.getType() == WeekTypeEnum.SINGLE) {
                        detailCopyAdd(detailSourceSingle, dtd.getDetailv());
                    } else {
                        detailCopyAdd(detailSourceDouble, dtd.getDetailv());
                    }
                }
            }
        }
        Map<String, List<List<String>>> tableMap = getTableData(classOrRoomName, detailSourceSingle, detailSourceDouble, CurriculumTableTypeEnum.CLASSES);
        return outExcel(CurriculumTableTypeEnum.CLASSES, tableMap, new ClassesMergeStrategy());

    }

    public OssFileVO exportClasses(Long semesterId, Integer enrollmentYear, Long sectionId, String classOrRoomId) {
        CurriculumTableDO tableDo = curriculumTableService.getTable(sectionId, enrollmentYear, semesterId, 0);
        if (tableDo == null) {
            return outExcel(CurriculumTableTypeEnum.CLASSES);
        }
        String classOrRoomName = null;
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceSingle = new HashMap<>(10);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceDouble = new HashMap<>(10);
        List<GeneralTableVO> classTables = generalTableService.classesTable(tableDo.getId());
        if (CollectionUtils.isEmpty(classTables)) {
            return outExcel(CurriculumTableTypeEnum.CLASSES);
        }
        for (GeneralTableVO vo : classTables) {
            for (DataTableDTO dtd : vo.getData()) {
                if (dtd.getClassOrRoomId().equals(classOrRoomId)) {
                    classOrRoomName = dtd.getClassOrRoomName();
                    if (vo.getType() == WeekTypeEnum.SINGLE) {
                        detailCopyAdd(detailSourceSingle, dtd.getDetailv());
                    } else {
                        detailCopyAdd(detailSourceDouble, dtd.getDetailv());
                    }
                }
            }
        }
        Map<String, List<List<String>>> tableMap = getTableData(classOrRoomName, detailSourceSingle, detailSourceDouble, CurriculumTableTypeEnum.CLASSES);
        return outExcel(CurriculumTableTypeEnum.CLASSES, tableMap, new ClassesMergeStrategy());
    }

    public OssFileVO exportTeacher(Long semesterId, Integer enrollmentYear, Long sectionId, Long teacherId) {
        CurriculumTableDO tableDo = curriculumTableService.getTable(sectionId, enrollmentYear, semesterId, 0);
        if (tableDo == null) {
            return outExcel(CurriculumTableTypeEnum.TEACHER);
        }
        String courseName = null;
        String teacherName = null;
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceSingle = new HashMap<>(10);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSourceDouble = new HashMap<>(10);
        List<TeacherTableVO> teacherTables = generalTableService.teacherTable(tableDo.getId());
        if (CollectionUtils.isEmpty(teacherTables)) {
            return outExcel(CurriculumTableTypeEnum.TEACHER);
        }
        for (TeacherTableVO vo : teacherTables) {
            for (String abb : vo.getData().keySet()) {
                List<TeacherTableDTO> teacherDatas = vo.getData().get(abb);
                for (TeacherTableDTO dtd : teacherDatas) {
                    if (dtd.getTeacherId().equals(teacherId)) {
                        courseName = abb;
                        teacherName = dtd.getTeacherName();
                        if (vo.getType() == WeekTypeEnum.SINGLE) {
                            detailCopyAdd(detailSourceSingle, dtd.getDetailv());
                        } else {
                            detailCopyAdd(detailSourceDouble, dtd.getDetailv());
                        }
                    }
                }
            }
        }
        String cellTitle = teacherName + " 教师课程表(" + courseName + "组)";
        Map<String, List<List<String>>> tableMap = getTableData(cellTitle, detailSourceSingle, detailSourceDouble, CurriculumTableTypeEnum.TEACHER);
        return outExcel(CurriculumTableTypeEnum.TEACHER, tableMap, new ClassesMergeStrategy());
    }

    public OssFileVO exportStudent(Long semesterId, Integer enrollmentYear, Long sectionId, Long studentId) {
        CurriculumTableDO tableDo = curriculumTableService.getTable(sectionId, enrollmentYear, semesterId, 0);
        if (tableDo == null) {
            return outExcel(CurriculumTableTypeEnum.STUDENT);
        }
        List<GeneralTableDO> list = generalTableService.queryByCurriculumTableId(tableDo.getId());
        for (GeneralTableDO vo : list) {
            List<StudentTableDTO> stu = new ArrayList<>();
            for (StudentTableDTO student : vo.getStudentTable()) {
                if (student.getStuId().equals(studentId)) {
                    stu.add(student);
                    break;
                }
            }
            Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTables = generalTableService.studentTableDetail(tableDo.getId());
            Map<String, List<List<String>>> tableMap = getStudentTable(stu, studentTables);
            return outExcel(CurriculumTableTypeEnum.STUDENT, tableMap, new ClassesMergeStrategy());
        }
        return outExcel(CurriculumTableTypeEnum.STUDENT);
    }

    public OssFileVO outExcel(CurriculumTableTypeEnum curriculumTableType) {
        Map<String, List<List<String>>> tableMap = new HashMap<>(10);
        List<List<String>> tableList = new LinkedList<>();
        tableMap.put("sheet1", tableList);
        tableList.add(getCellTitle(curriculumTableType.getDescription() + ""));
        tableList.add(getCellHead());
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "一", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "二", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "三", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "四", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "五", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "六", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "七", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "八", curriculumTableType));
        tableList.add(getCellValue(new HashMap<>(10), new HashMap<>(10), "九", curriculumTableType));
        return outExcel(curriculumTableType, tableMap, new ClassesMergeStrategy());
    }

    public OssFileVO outExcel(CurriculumTableTypeEnum type, Map<String, List<List<String>>> tableMap, WriteHandler writeHandler) {
        long begin = System.currentTimeMillis();
        File dir = new File(localFilePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String fileName = type.getDescription() + TimeUtils.getSysTimeLong() + ".xlsx";
        File uploadFile = new File(dir, fileName);
        try (FileOutputStream fos = new FileOutputStream(uploadFile)) {
            ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(fos);
            ExcelWriter excelWriter = excelWriterBuilder.registerWriteHandler(writeHandler).build();
            for (String key : tableMap.keySet()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                writeSheet.setSheetName(key);
                excelWriter.write(tableMap.get(key), writeSheet);
            }
            excelWriter.finish();
            OssFileInfoDTO dto = ossFacade.uploadFile(uploadFile.getPath(), "xlsx", null);
            OssFileVO vo = new OssFileVO();
            vo.setFileId(dto.getId() + "");
            vo.setFileName(dto.getName());
            vo.setUrl(dto.getUrl());
            long end = System.currentTimeMillis();
            log.info("学生导出写入excel消耗时间{}",end-begin);
            return vo;
        } catch (IOException e) {
            throw new ApplicationException(e);
        }
    }

    public OssFileVO studentOutExcel(CurriculumTableTypeEnum type, Map<String, List<List<String>>> tableMap, WriteHandler writeHandler,List<StudentTableExportDTO> studentTableExportDtos) {
        long begin = System.currentTimeMillis();
        File dir = new File(localFilePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String fileName = type.getDescription() + TimeUtils.getSysTimeLong() + ".xlsx";
        File uploadFile = new File(dir, fileName);
        ExcelWriter excelWriter = null;
        try (FileOutputStream fos = new FileOutputStream(uploadFile)) {
            ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(fos);
            //创建第一个sheet
            excelWriter = excelWriterBuilder.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "所有学生课程信息").head(StudentTableExportDTO.class).build();
            excelWriter.write(studentTableExportDtos, writeSheet);
            ExcelWriter newExcelWriter = excelWriter;
            int num = 1;
            for (String key : tableMap.keySet()) {
                writeSheet = EasyExcel.writerSheet(num,key).registerWriteHandler(writeHandler).build();
                newExcelWriter.write(tableMap.get(key), writeSheet);
                num = num+1;
            }
            newExcelWriter.finish();
            OssFileInfoDTO dto = ossFacade.uploadFile(uploadFile.getPath(), "xlsx", null);
            OssFileVO vo = new OssFileVO();
            vo.setFileId(dto.getId() + "");
            vo.setFileName(dto.getName());
            vo.setUrl(dto.getUrl());
            long end = System.currentTimeMillis();
            log.info("学生导出写入excel消耗时间{}",end-begin);
            return vo;
        } catch (IOException e) {
            throw new ApplicationException(e);
        }
    }

    public Map<String, List<List<String>>> getMainTable(List<GeneralTableVO> generalTables) {
        Map<WeekTypeEnum, List<DataTableDTO>> dataMap = new HashMap<>(10);
        for (GeneralTableVO vo : generalTables) {
            dataMap.put(vo.getType(), vo.getData());
        }
        List<DataTableDTO> dtListSingle = dataMap.get(WeekTypeEnum.SINGLE);
        List<DataTableDTO> dtListDouble = dataMap.get(WeekTypeEnum.DOUBLE);
        Map<String, DataTableDTO> dtMapDouble = null;
        if (dtListDouble != null) {
            dtMapDouble = dtListDouble.stream().collect(Collectors.toMap(DataTableDTO::getClassOrRoomId, Function.identity(), (t, t1) -> t1));
        }

        Map<String, List<List<String>>> tableMap = new HashMap<>(10);
        List<List<String>> tableList = new LinkedList<>();
        tableMap.put("sheet1", tableList);
        List<String> cells = new ArrayList<>();
        cells.add("节次/教室");
        cells.add(null);
        List<String> cells1 = new ArrayList<>();
        List<String> cells2 = new ArrayList<>();
        List<String> cells3 = new ArrayList<>();
        List<String> cells4 = new ArrayList<>();
        List<String> cells5 = new ArrayList<>();
        List<String> cells6 = new ArrayList<>();
        List<String> cells7 = new ArrayList<>();
        List<String> cells8 = new ArrayList<>();
        List<String> cells9 = new ArrayList<>();

        List<String> cellsTue1 = new ArrayList<>();
        List<String> cellsTue2 = new ArrayList<>();
        List<String> cellsTue3 = new ArrayList<>();
        List<String> cellsTue4 = new ArrayList<>();
        List<String> cellsTue5 = new ArrayList<>();
        List<String> cellsTue6 = new ArrayList<>();
        List<String> cellsTue7 = new ArrayList<>();
        List<String> cellsTue8 = new ArrayList<>();
        List<String> cellsTue9 = new ArrayList<>();

        List<String> cellsWed1 = new ArrayList<>();
        List<String> cellsWed2 = new ArrayList<>();
        List<String> cellsWed3 = new ArrayList<>();
        List<String> cellsWed4 = new ArrayList<>();
        List<String> cellsWed5 = new ArrayList<>();
        List<String> cellsWed6 = new ArrayList<>();
        List<String> cellsWed7 = new ArrayList<>();
        List<String> cellsWed8 = new ArrayList<>();
        List<String> cellsWed9 = new ArrayList<>();

        List<String> cellsThu1 = new ArrayList<>();
        List<String> cellsThu2 = new ArrayList<>();
        List<String> cellsThu3 = new ArrayList<>();
        List<String> cellsThu4 = new ArrayList<>();
        List<String> cellsThu5 = new ArrayList<>();
        List<String> cellsThu6 = new ArrayList<>();
        List<String> cellsThu7 = new ArrayList<>();
        List<String> cellsThu8 = new ArrayList<>();
        List<String> cellsThu9 = new ArrayList<>();

        List<String> cellsFri1 = new ArrayList<>();
        List<String> cellsFri2 = new ArrayList<>();
        List<String> cellsFri3 = new ArrayList<>();
        List<String> cellsFri4 = new ArrayList<>();
        List<String> cellsFri5 = new ArrayList<>();
        List<String> cellsFri6 = new ArrayList<>();
        List<String> cellsFri7 = new ArrayList<>();
        List<String> cellsFri8 = new ArrayList<>();
        List<String> cellsFri9 = new ArrayList<>();

        List<String> cellsSat1 = new ArrayList<>();
        List<String> cellsSat2 = new ArrayList<>();
        List<String> cellsSat3 = new ArrayList<>();
        List<String> cellsSat4 = new ArrayList<>();
        List<String> cellsSat5 = new ArrayList<>();
        List<String> cellsSat6 = new ArrayList<>();
        List<String> cellsSat7 = new ArrayList<>();
        List<String> cellsSat8 = new ArrayList<>();
        List<String> cellsSat9 = new ArrayList<>();

        List<String> cellsSun1 = new ArrayList<>();
        List<String> cellsSun2 = new ArrayList<>();
        List<String> cellsSun3 = new ArrayList<>();
        List<String> cellsSun4 = new ArrayList<>();
        List<String> cellsSun5 = new ArrayList<>();
        List<String> cellsSun6 = new ArrayList<>();
        List<String> cellsSun7 = new ArrayList<>();
        List<String> cellsSun8 = new ArrayList<>();
        List<String> cellsSun9 = new ArrayList<>();
        for (DataTableDTO dtd : dtListSingle) {
            DataTableDTO dtdDoub = null;
            if (dtMapDouble != null) {
                dtdDoub = dtMapDouble.get(dtd.getClassOrRoomId());
            }

            cells.add(dtd.getClassOrRoomName());
            mainTable(cells1, dtd, dtdDoub, WeekEnum.Monday, "一");
            mainTable(cells2, dtd, dtdDoub, WeekEnum.Monday, "二");
            mainTable(cells3, dtd, dtdDoub, WeekEnum.Monday, "三");
            mainTable(cells4, dtd, dtdDoub, WeekEnum.Monday, "四");
            mainTable(cells5, dtd, dtdDoub, WeekEnum.Monday, "五");
            mainTable(cells6, dtd, dtdDoub, WeekEnum.Monday, "六");
            mainTable(cells7, dtd, dtdDoub, WeekEnum.Monday, "七");
            mainTable(cells8, dtd, dtdDoub, WeekEnum.Monday, "八");
            mainTable(cells9, dtd, dtdDoub, WeekEnum.Monday, "九");

            mainTable(cellsTue1, dtd, dtdDoub, WeekEnum.Tuesday, "一");
            mainTable(cellsTue2, dtd, dtdDoub, WeekEnum.Tuesday, "二");
            mainTable(cellsTue3, dtd, dtdDoub, WeekEnum.Tuesday, "三");
            mainTable(cellsTue4, dtd, dtdDoub, WeekEnum.Tuesday, "四");
            mainTable(cellsTue5, dtd, dtdDoub, WeekEnum.Tuesday, "五");
            mainTable(cellsTue6, dtd, dtdDoub, WeekEnum.Tuesday, "六");
            mainTable(cellsTue7, dtd, dtdDoub, WeekEnum.Tuesday, "七");
            mainTable(cellsTue8, dtd, dtdDoub, WeekEnum.Tuesday, "八");
            mainTable(cellsTue9, dtd, dtdDoub, WeekEnum.Tuesday, "九");

            mainTable(cellsWed1, dtd, dtdDoub, WeekEnum.Wednesday, "一");
            mainTable(cellsWed2, dtd, dtdDoub, WeekEnum.Wednesday, "二");
            mainTable(cellsWed3, dtd, dtdDoub, WeekEnum.Wednesday, "三");
            mainTable(cellsWed4, dtd, dtdDoub, WeekEnum.Wednesday, "四");
            mainTable(cellsWed5, dtd, dtdDoub, WeekEnum.Wednesday, "五");
            mainTable(cellsWed6, dtd, dtdDoub, WeekEnum.Wednesday, "六");
            mainTable(cellsWed7, dtd, dtdDoub, WeekEnum.Wednesday, "七");
            mainTable(cellsWed8, dtd, dtdDoub, WeekEnum.Wednesday, "八");
            mainTable(cellsWed9, dtd, dtdDoub, WeekEnum.Wednesday, "九");

            mainTable(cellsThu1, dtd, dtdDoub, WeekEnum.Thursday, "一");
            mainTable(cellsThu2, dtd, dtdDoub, WeekEnum.Thursday, "二");
            mainTable(cellsThu3, dtd, dtdDoub, WeekEnum.Thursday, "三");
            mainTable(cellsThu4, dtd, dtdDoub, WeekEnum.Thursday, "四");
            mainTable(cellsThu5, dtd, dtdDoub, WeekEnum.Thursday, "五");
            mainTable(cellsThu6, dtd, dtdDoub, WeekEnum.Thursday, "六");
            mainTable(cellsThu7, dtd, dtdDoub, WeekEnum.Thursday, "七");
            mainTable(cellsThu8, dtd, dtdDoub, WeekEnum.Thursday, "八");
            mainTable(cellsThu9, dtd, dtdDoub, WeekEnum.Thursday, "九");

            mainTable(cellsFri1, dtd, dtdDoub, WeekEnum.Friday, "一");
            mainTable(cellsFri2, dtd, dtdDoub, WeekEnum.Friday, "二");
            mainTable(cellsFri3, dtd, dtdDoub, WeekEnum.Friday, "三");
            mainTable(cellsFri4, dtd, dtdDoub, WeekEnum.Friday, "四");
            mainTable(cellsFri5, dtd, dtdDoub, WeekEnum.Friday, "五");
            mainTable(cellsFri6, dtd, dtdDoub, WeekEnum.Friday, "六");
            mainTable(cellsFri7, dtd, dtdDoub, WeekEnum.Friday, "七");
            mainTable(cellsFri8, dtd, dtdDoub, WeekEnum.Friday, "八");
            mainTable(cellsFri9, dtd, dtdDoub, WeekEnum.Friday, "九");

            mainTable(cellsSat1, dtd, dtdDoub, WeekEnum.Saturday, "一");
            mainTable(cellsSat2, dtd, dtdDoub, WeekEnum.Saturday, "二");
            mainTable(cellsSat3, dtd, dtdDoub, WeekEnum.Saturday, "三");
            mainTable(cellsSat4, dtd, dtdDoub, WeekEnum.Saturday, "四");
            mainTable(cellsSat5, dtd, dtdDoub, WeekEnum.Saturday, "五");
            mainTable(cellsSat6, dtd, dtdDoub, WeekEnum.Saturday, "六");
            mainTable(cellsSat7, dtd, dtdDoub, WeekEnum.Saturday, "七");
            mainTable(cellsSat8, dtd, dtdDoub, WeekEnum.Saturday, "八");
            mainTable(cellsSat9, dtd, dtdDoub, WeekEnum.Saturday, "九");

            mainTable(cellsSun1, dtd, dtdDoub, WeekEnum.Sunday, "一");
            mainTable(cellsSun2, dtd, dtdDoub, WeekEnum.Sunday, "二");
            mainTable(cellsSun3, dtd, dtdDoub, WeekEnum.Sunday, "三");
            mainTable(cellsSun4, dtd, dtdDoub, WeekEnum.Sunday, "四");
            mainTable(cellsSun5, dtd, dtdDoub, WeekEnum.Sunday, "五");
            mainTable(cellsSun6, dtd, dtdDoub, WeekEnum.Sunday, "六");
            mainTable(cellsSun7, dtd, dtdDoub, WeekEnum.Sunday, "七");
            mainTable(cellsSun8, dtd, dtdDoub, WeekEnum.Sunday, "八");
            mainTable(cellsSun9, dtd, dtdDoub, WeekEnum.Sunday, "九");

        }
        tableList.add(cells);
        tableList.add(cells1);
        tableList.add(cells2);
        tableList.add(cells3);
        tableList.add(cells4);
        tableList.add(cells5);
        tableList.add(cells6);
        tableList.add(cells7);
        tableList.add(cells8);
        tableList.add(cells9);

        tableList.add(cellsTue1);
        tableList.add(cellsTue2);
        tableList.add(cellsTue3);
        tableList.add(cellsTue4);
        tableList.add(cellsTue5);
        tableList.add(cellsTue6);
        tableList.add(cellsTue7);
        tableList.add(cellsTue8);
        tableList.add(cellsTue9);

        tableList.add(cellsWed1);
        tableList.add(cellsWed2);
        tableList.add(cellsWed3);
        tableList.add(cellsWed4);
        tableList.add(cellsWed5);
        tableList.add(cellsWed6);
        tableList.add(cellsWed7);
        tableList.add(cellsWed8);
        tableList.add(cellsWed9);

        tableList.add(cellsThu1);
        tableList.add(cellsThu2);
        tableList.add(cellsThu3);
        tableList.add(cellsThu4);
        tableList.add(cellsThu5);
        tableList.add(cellsThu6);
        tableList.add(cellsThu7);
        tableList.add(cellsThu8);
        tableList.add(cellsThu9);

        tableList.add(cellsFri1);
        tableList.add(cellsFri2);
        tableList.add(cellsFri3);
        tableList.add(cellsFri4);
        tableList.add(cellsFri5);
        tableList.add(cellsFri6);
        tableList.add(cellsFri7);
        tableList.add(cellsFri8);
        tableList.add(cellsFri9);

        tableList.add(cellsSat1);
        tableList.add(cellsSat2);
        tableList.add(cellsSat3);
        tableList.add(cellsSat4);
        tableList.add(cellsSat5);
        tableList.add(cellsSat6);
        tableList.add(cellsSat7);
        tableList.add(cellsSat8);
        tableList.add(cellsSat9);

        tableList.add(cellsSun1);
        tableList.add(cellsSun2);
        tableList.add(cellsSun3);
        tableList.add(cellsSun4);
        tableList.add(cellsSun5);
        tableList.add(cellsSun6);
        tableList.add(cellsSun7);
        tableList.add(cellsSun8);
        tableList.add(cellsSun9);
        return tableMap;
    }

    public void mainTable(List<String> cells, DataTableDTO dtd, DataTableDTO dtdDouble, WeekEnum week, String section) {
        table(cells, dtd.getDetailv(),
                dtdDouble == null ? new HashMap<>(10) : dtdDouble.getDetailv(),
                week, section, CurriculumTableTypeEnum.GENERAL);
    }

    public Map<String, List<List<String>>> getTableData(
            String cellTitle,
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailv,
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvDouble,
            CurriculumTableTypeEnum curriculumTableType) {
        Map<String, List<List<String>>> tableMap = new HashMap<>(10);
        List<List<String>> tableList = new LinkedList<>();
        tableMap.put("sheet1", tableList);
        tableList.add(getCellTitle(cellTitle));
        tableList.add(getCellHead());
        tableList.add(getCellValue(detailv, detailvDouble, "一", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "二", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "三", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "四", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "五", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "六", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "七", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "八", curriculumTableType));
        tableList.add(getCellValue(detailv, detailvDouble, "九", curriculumTableType));
        return tableMap;
    }

    public Map<String, List<List<String>>> getClassTableData(List<ExportClassesDTO> exportClassesDtos, CurriculumTableTypeEnum curriculumTableType, Integer type) {
        List<ClassVO> classVos = foundationFacade.allClass();
        Map<Long, ClassVO> classIdMap = classVos.stream().collect(Collectors.toMap(ClassVO::getId, classVo -> classVo));
        Map<String, List<List<String>>> tableMap = new LinkedHashMap<>();
        exportClassesDtos.forEach(exportClassesDto -> {
            List<List<String>> tableList = new LinkedList<>();
            if (ClassTableTypeEnum.ROOM.getType().equals(type)) {
                ClassVO classVo = classIdMap.get(exportClassesDto.getClassOrRoomId());
                if (Objects.nonNull(classVo)) {
                    tableMap.put(classVo.getRoomName(), tableList);
                } else {
                    tableMap.put(exportClassesDto.getClassOrRoomName(), tableList);
                }
            } else {
                tableMap.put(exportClassesDto.getClassOrRoomName(), tableList);
            }
            tableList.add(getCellTitle(exportClassesDto.getClassOrRoomName()));
            tableList.add(getCellHead());
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "一", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "二", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "三", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "四", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "五", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "六", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "七", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "八", curriculumTableType));
            tableList.add(getCellValue(exportClassesDto.getDetailSourceSingle(), exportClassesDto.getDetailSourceDouble(), "九", curriculumTableType));
        });
        return tableMap;
    }

    public Map<String, List<List<String>>> getTeacherTableData(List<ExportTeacherDTO> exportTeacherDtos, CurriculumTableTypeEnum curriculumTableType) {
        Map<String, List<List<String>>> tableMap = new LinkedHashMap<>();
        exportTeacherDtos.forEach(exportTeacherDto -> {
            List<List<String>> tableList = new LinkedList<>();
            tableMap.put(exportTeacherDto.getTeacherName(), tableList);
            tableList.add(getCellTitle(exportTeacherDto.getCellTitle()));
            tableList.add(getCellHead());
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "一", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "二", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "三", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "四", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "五", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "六", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "七", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "八", curriculumTableType));
            tableList.add(getCellValue(exportTeacherDto.getDetailSourceSingle(), exportTeacherDto.getDetailSourceDouble(), "九", curriculumTableType));
        });
        return tableMap;
    }

    public void getStudentTable(Map<String, List<List<String>>> tableMap,List<StudentTableDTO> stuList,
                                Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTables,Map<Long, StudentDto> studentIdMap,
                                List<StudentTableExportDTO> studentTableExportDtos,Map<Long, List<StudentCourseListVO>> courseMa,
                                Set<String> idNoSet) {
        stuList.forEach(studentTableDto -> {
            Map<WeekTypeEnum, DataTableDTO> dataTables = studentTables.get(studentTableDto.getStuId());
            if (!CollectionUtils.isEmpty(dataTables)) {
                StudentTableExportDTO studentTableExportDto = new StudentTableExportDTO();
                StudentDto studentDto = studentIdMap.get(studentTableDto.getStuId());
                studentTableExportDto.setClassName(studentTableDto.getClassName());
                studentTableExportDto.setStudentNo(studentTableDto.getStudentNo());
                studentTableExportDto.setName(studentTableDto.getName());
                if (Objects.nonNull(studentDto)) {
                    studentTableExportDto.setIdNo(studentDto.getIdNo());
                    if (Objects.nonNull(studentDto.getGender())) {
                        studentTableExportDto.setGenderName(GenderEnum.codeToMessage(studentDto.getGender()));
                    }
                    if (Objects.nonNull(studentDto.getHeadTeacherInfo())) {
                        studentTableExportDto.setHeaderName(studentDto.getHeadTeacherInfo().getName());
                    }
                }
                //组装选修课
                List<StudentCourseListVO> studentCourseListVos = courseMa.get(studentTableDto.getStuId());
                if (!CollectionUtils.isEmpty(studentCourseListVos)) {
                    List<String> courseNames = studentCourseListVos.stream().map(StudentCourseListVO::getCourseName).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(courseNames)) {
                        studentTableExportDto.setCourseName(Joiner.on(",").join(courseNames));
                    }
                }
                //组装课程
                Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvSingle = new HashMap<>(10);
                Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvDouble = new HashMap<>(10);
                DataTableDTO dtd = dataTables.get(WeekTypeEnum.SINGLE);
                if (dtd != null) {
                    detailvSingle.putAll(dtd.getDetailv());
                }
                DataTableDTO dtdDouble = dataTables.get(WeekTypeEnum.DOUBLE);
                if (dtdDouble != null) {
                    detailvDouble.putAll(dtdDouble.getDetailv());
                }
                //组装数据
                setStudentTaleExportData(studentTableExportDtos, studentTableExportDto, detailvSingle, detailvDouble,idNoSet);
                List<List<String>> tableList = new LinkedList<>();
                tableMap.put(studentTableDto.getName(), tableList);
                tableList.add(getCellTitle(studentTableDto.getClassName() + "班 " + studentTableDto.getName() + "(" + studentTableDto.getStudentNo() + ") 学生课表"));
                tableList.add(getCellHead());
                tableList.add(getCellValue(detailvSingle, detailvDouble, "一", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "二", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "三", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "四", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "五", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "六", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "七", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "八", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "九", CurriculumTableTypeEnum.STUDENT));
            }
        });
    }

    private void setStudentTaleExportData(List<StudentTableExportDTO> studentTableExportDtos, StudentTableExportDTO studentTableExportDto, Map<WeekEnum, Map<String,
            List<TeacherAbbDetailVO>>> detailvSingle, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvDouble,Set<String> idNoSet) {
        //课程-教师、地点map
        Map<String, StudentCourseTeacherDTO> courseTeacherDtoMap = new HashMap<>(6);
        for (Map<String, List<TeacherAbbDetailVO>> value : detailvSingle.values()) {
            for (List<TeacherAbbDetailVO> teacherAbbDetailVos : value.values()) {
                for (TeacherAbbDetailVO teacherAbbDetailVo : teacherAbbDetailVos) {
                    if (!courseTeacherDtoMap.containsKey(teacherAbbDetailVo.getCourseName())) {
                        StudentCourseTeacherDTO studentCourseTeacherDto = new StudentCourseTeacherDTO();
                        studentCourseTeacherDto.setTeacherName(teacherAbbDetailVo.getTeacherName());
                        studentCourseTeacherDto.setRoomName(teacherAbbDetailVo.getClassOrRoomName());
                        courseTeacherDtoMap.put(teacherAbbDetailVo.getCourseName(),studentCourseTeacherDto);
                    }
                }
            }
        }
        for (Map<String, List<TeacherAbbDetailVO>> value : detailvDouble.values()) {
            for (List<TeacherAbbDetailVO> teacherAbbDetailVos : value.values()) {
                for (TeacherAbbDetailVO teacherAbbDetailVo : teacherAbbDetailVos) {
                    if (!courseTeacherDtoMap.containsKey(teacherAbbDetailVo.getCourseName())) {
                        StudentCourseTeacherDTO studentCourseTeacherDto = new StudentCourseTeacherDTO();
                        studentCourseTeacherDto.setTeacherName(teacherAbbDetailVo.getTeacherName());
                        studentCourseTeacherDto.setRoomName(teacherAbbDetailVo.getClassOrRoomName());
                        courseTeacherDtoMap.put(teacherAbbDetailVo.getCourseName(),studentCourseTeacherDto);
                    }
                }
            }
        }
        courseTeacherDtoMap.forEach((k,v) ->{
            if (CourseNameEnum.CHINESE.getMessage().equals(k)) {
                studentTableExportDto.setChinese(v.getTeacherName());
                studentTableExportDto.setChineseRoom(v.getRoomName());
            }else if (CourseNameEnum.MATH.getMessage().equals(k)) {
                studentTableExportDto.setMath(v.getTeacherName());
                studentTableExportDto.setMathRoom(v.getRoomName());
            }else if (CourseNameEnum.ENGLISH.getMessage().equals(k)) {
                studentTableExportDto.setEnglish(v.getTeacherName());
                studentTableExportDto.setEnglishRoom(v.getRoomName());
            }else if (CourseNameEnum.PHYSICAL.getMessage().equals(k)) {
                studentTableExportDto.setPhysical(v.getTeacherName());
                studentTableExportDto.setPhysicalRoom(v.getRoomName());
            }else if (CourseNameEnum.CHEMICAL.getMessage().equals(k)) {
                studentTableExportDto.setChemical(v.getTeacherName());
                studentTableExportDto.setChemicalRoom(v.getRoomName());
            }else if (CourseNameEnum.BIOLOGICAL.getMessage().equals(k)) {
                studentTableExportDto.setBiological(v.getTeacherName());
                studentTableExportDto.setBiologicalRoom(v.getRoomName());
            }else if (CourseNameEnum.POLITICAL.getMessage().equals(k)) {
                studentTableExportDto.setPolitical(v.getTeacherName());
                studentTableExportDto.setPoliticalRoom(v.getRoomName());
            }else if (CourseNameEnum.HISTORY.getMessage().equals(k)) {
                studentTableExportDto.setHistory(v.getTeacherName());
                studentTableExportDto.setHistoryRoom(v.getRoomName());
            }else if (CourseNameEnum.GEOGRAPHIC.getMessage().equals(k)) {
                studentTableExportDto.setGeographic(v.getTeacherName());
                studentTableExportDto.setGeographicRoom(v.getRoomName());
            }else if (CourseNameEnum.INFORMATION_TECH.getMessage().equals(k)) {
                studentTableExportDto.setInformationTech(v.getTeacherName());
                studentTableExportDto.setInformationTechRoom(v.getRoomName());
            }else if (CourseNameEnum.GENERAL_TECH.getMessage().equals(k)) {
                studentTableExportDto.setGeneralTech(v.getTeacherName());
                studentTableExportDto.setGeneralTechRoom(v.getRoomName());
            }
        });
        if (!idNoSet.contains(studentTableExportDto.getIdNo())) {
            studentTableExportDtos.add(studentTableExportDto);
            idNoSet.add(studentTableExportDto.getIdNo());
        }
    }

    public Map<String, List<List<String>>> getStudentTable(List<StudentTableDTO> stuList,
                                                           Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTables) {
        Map<String, List<List<String>>> tableMap = new HashMap<>(10);
        stuList.forEach(studentTableDto -> {
            Map<WeekTypeEnum, DataTableDTO> dataTables = studentTables.get(studentTableDto.getStuId());
            if (!CollectionUtils.isEmpty(dataTables)) {
                Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvSingle = new HashMap<>(10);
                Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvDouble = new HashMap<>(10);
                DataTableDTO dtd = dataTables.get(WeekTypeEnum.SINGLE);
                if (dtd != null) {
                    detailvSingle.putAll(dtd.getDetailv());
                }
                DataTableDTO dtdDouble = dataTables.get(WeekTypeEnum.DOUBLE);
                if (dtdDouble != null) {
                    detailvDouble.putAll(dtdDouble.getDetailv());
                }
                List<List<String>> tableList = new LinkedList<>();
                tableMap.put(studentTableDto.getName(), tableList);
                tableList.add(getCellTitle(studentTableDto.getClassName() + "班 " + studentTableDto.getName() + "(" + studentTableDto.getStudentNo() + ") 学生课表"));
                tableList.add(getCellHead());
                tableList.add(getCellValue(detailvSingle, detailvDouble, "一", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "二", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "三", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "四", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "五", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "六", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "七", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "八", CurriculumTableTypeEnum.STUDENT));
                tableList.add(getCellValue(detailvSingle, detailvDouble, "九", CurriculumTableTypeEnum.STUDENT));
            }
        });
        return tableMap;
    }

    public List<String> getCellTitle(String title) {
        List<String> cells = new ArrayList<>();
        cells.add(title);
        cells.add(null);
        cells.add(null);
        cells.add(null);
        cells.add(null);
        cells.add(null);
        cells.add(null);
        cells.add(null);
        return cells;
    }

    public List<String> getCellHead() {
        List<String> cells = new ArrayList<>();
        cells.add("节次");
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Monday));
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Tuesday));
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Wednesday));
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Thursday));
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Friday));
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Saturday));
        cells.add(WeekEnum.getDayOfWeek(WeekEnum.Sunday));
        return cells;
    }

    public List<String> getCellValue(
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailv,
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailvDouble,
            String section, CurriculumTableTypeEnum type) {
        List<String> cells = new ArrayList<>();
        table(cells, detailv, detailvDouble, WeekEnum.Monday, section, type);
        table(cells, detailv, detailvDouble, WeekEnum.Tuesday, section, type);
        table(cells, detailv, detailvDouble, WeekEnum.Wednesday, section, type);
        table(cells, detailv, detailvDouble, WeekEnum.Thursday, section, type);
        table(cells, detailv, detailvDouble, WeekEnum.Friday, section, type);
        table(cells, detailv, detailvDouble, WeekEnum.Saturday, section, type);
        table(cells, detailv, detailvDouble, WeekEnum.Sunday, section, type);
        return cells;
    }

    public void table(List<String> cells1,
                      Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSingle,
                      Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailDouble,
                      WeekEnum week, String section, CurriculumTableTypeEnum type) {
        if (cells1.isEmpty()) {
            if (type == CurriculumTableTypeEnum.GENERAL) {
                String firstSection = "一";
                if (firstSection.equals(section)) {
                    cells1.add(WeekEnum.getDayOfWeek(week));
                } else {
                    cells1.add(null);
                }
            }
            cells1.add(section);
        }
        List<TeacherAbbDetailVO> abbSingle = null;
        if (detailSingle.containsKey(week)) {
            Map<String, List<TeacherAbbDetailVO>> sectionAbbs = detailSingle.get(week);
            if (sectionAbbs.containsKey(section)) {
                abbSingle = sectionAbbs.get(section);
            }
        }
        List<TeacherAbbDetailVO> abbDoubles = null;
        if (detailDouble.containsKey(week)) {
            Map<String, List<TeacherAbbDetailVO>> sectionAbbs = detailDouble.get(week);
            if (sectionAbbs.containsKey(section)) {
                abbDoubles = sectionAbbs.get(section);
            }
        }
        if (!CollectionUtils.isEmpty(abbSingle) || !CollectionUtils.isEmpty(abbDoubles)) {
            if (!CollectionUtils.isEmpty(abbSingle) && !CollectionUtils.isEmpty(abbDoubles)) {
                if (abbSingle.size() == 1 && abbDoubles.size() == 1) {
                    TeacherAbbDetailVO abbs = abbSingle.get(0);
                    TeacherAbbDetailVO abbd = abbDoubles.get(0);
                    if (abbs.getAbbreviation().equals(abbd.getAbbreviation()) &&
                            abbs.getTeacherId().equals(abbd.getTeacherId())) {
                        cells1.add(getAbbName(type, abbs));
                    } else {
                        cells1.add("单:" + getAbbName(type, abbs) + "双:" + getAbbName(type, abbd));
                    }
                } else {
                    StringBuilder sbStr = new StringBuilder();
                    if (abbSingle.size() == 1) {
                        sbStr.append("单:" + getAbbName(type, abbSingle.get(0)));
                    } else {
                        sbStr.append("单:走班课");
                    }
                    if (abbDoubles.size() == 1) {
                        sbStr.append("双:" + getAbbName(type, abbDoubles.get(0)));
                    } else {
                        sbStr.append("双:走班课");
                    }
                    cells1.add(sbStr.toString());
                }
            } else {
                if (CollectionUtils.isEmpty(detailDouble)) {
                    if (!CollectionUtils.isEmpty(abbSingle)) {
                        if (abbSingle.size() == 1) {
                            cells1.add(getAbbName(type, abbSingle.get(0)));
                        } else {
                            cells1.add("走班课");
                        }
                    } else {
                        cells1.add(null);
                    }
                } else {
                    if (!CollectionUtils.isEmpty(abbSingle)) {
                        if (abbSingle.size() == 1) {
                            cells1.add("单:" + getAbbName(type, abbSingle.get(0)));
                        } else {
                            cells1.add("走班课");
                        }
                    } else if (!CollectionUtils.isEmpty(abbDoubles)) {
                        if (abbDoubles.size() == 1) {
                            cells1.add("双:" + getAbbName(type, abbDoubles.get(0)));
                        } else {
                            cells1.add("双:走班课");
                        }
                    } else {
                        cells1.add(null);
                    }
                }
            }
        } else {
            cells1.add(null);
        }
    }

    public String getAbbName(CurriculumTableTypeEnum type, TeacherAbbDetailVO abb) {
        if (CurriculumTableTypeEnum.GENERAL == type) {
            return abb.getAbbreviation();
        } else if (CurriculumTableTypeEnum.CLASSES == type) {
            return abb.getAbbreviation() + " " + abb.getTeacherName();
        } else if (CurriculumTableTypeEnum.STUDENT == type) {
            return abb.getAbbreviation() + " " + abb.getClassOrRoomName() + " " + abb.getTeacherName();
        } else {
            return abb.getAbbreviation() + " " + abb.getClassOrRoomName();
        }
    }

    public void detailCopyAdd(
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSource,
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailTarget) {
        for (WeekEnum week : detailTarget.keySet()) {
            Map<String, List<TeacherAbbDetailVO>> abbDetailCopy = detailSource.computeIfAbsent(week, k -> new HashMap<>(10));
            Map<String, List<TeacherAbbDetailVO>> abbDetail = detailTarget.get(week);
            for (String section : abbDetail.keySet()) {
                List<TeacherAbbDetailVO> abbsCopy = new ArrayList<>();
                List<TeacherAbbDetailVO> abbs = abbDetail.get(section);
                for (TeacherAbbDetailVO v : abbs) {
                    abbsCopy.add(IoUtil.clone(v));
                }
                abbDetailCopy.put(section, abbsCopy);
            }
        }
    }

}
