package com.xiaoshan.edu.openclass.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;

@Getter@Setter@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class StatisticsDetailsExport {

    @ExcelProperty(value = "序号", index = 0)
    private Integer index;

    @ExcelProperty(value = "课题",index = 1)
    private String courseName;

    @ExcelProperty(value = "科目", index = 2)
    private String subject;

    @ExcelProperty(value = "教师", index = 3)
    private String teacherName;

    @ExcelProperty(value = "公开课类型", index = 4)
    private String type;

    @ColumnWidth(24)
    @ExcelProperty(value = "上课日期", index = 5)
    private String date;

    @ExcelProperty(value = "节次", index = 6)
    private String section;

    @ExcelProperty(value = "上课班级", index = 7)
    private String attendClass;

    @ExcelProperty(value = "上课地点", index = 8)
    private String place;
}
