package com.xiaoshan.edu.survey.controller;

import com.common.annotation.AuthenticationCheck;
import com.common.mvc.BaseController;
import com.xiaoshan.edu.ao.survey.*;
import com.xiaoshan.edu.enums.survey.SurveyObj;
import com.xiaoshan.edu.survey.service.TaskStudentService;
import com.xiaoshan.edu.survey.service.TaskTemplateStudentTeacherQuestionService;
import com.xiaoshan.edu.survey.utils.check.ListNotEmptyCheck;
import com.xiaoshan.edu.vo.survey.CurriculumStateVO;
import com.xiaoshan.edu.vo.survey.QuestionShowVO;
import com.xiaoshan.edu.vo.survey.TeacherStatisticVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import start.framework.commons.result.BaseResponse;
import start.framework.commons.result.ResultResponse;
import start.magic.core.valid.annotation.Custom;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/v1/survey/statistic")
@Api(tags = "教学调查接口")
public class TaskTemplateStudentTeacherQuestionController extends BaseController {

	@Autowired
	private TaskTemplateStudentTeacherQuestionService taskTemplateStudentTeacherQuestionService;
    @Autowired
    private TaskStudentService taskStudentService;

    @ApiOperation("统计 -- 查看统计结果")
    @AuthenticationCheck
    @PostMapping("/result")
    public ResultResponse<Map<String, Map<String, Object>>> statistic(@RequestBody StatisticAO ao){
        return response(taskTemplateStudentTeacherQuestionService.statistic(ao));
    }

    @ApiOperation("教师端 -- 查看统计结果")
    @AuthenticationCheck
    @PostMapping("/teacher/result")
    public ResultResponse<List<TeacherStatisticVO>> result(@RequestBody TeacherStatisticAO ao){
        return response(taskTemplateStudentTeacherQuestionService.teacherResult(ao));
    }

    @ApiOperation("统计 -- 数据导出")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody BaseStatisticAO ao){
        taskTemplateStudentTeacherQuestionService.export(response, ao);
    }

    @ApiOperation("统计/小程序  --  查询班级")
    @GetMapping("/classes/{taskId}/{templateId}")
    @AuthenticationCheck
    public ResultResponse<List<String>> getClasses(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId,
                                                   @PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long templateId){
        return response(taskTemplateStudentTeacherQuestionService.getClasses(taskId, templateId));
    }




    // ===========================================小程序=====================================================


    @ApiOperation("小程序 -- 选择科目列表、查询班主任")
    @AuthenticationCheck
    @GetMapping("/student/curriculum/{taskId}/{templateId}/{studentId}")
    public ResultResponse<List<CurriculumStateVO>> curriculumList(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId,
                                                                  @PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long templateId,
                                                                  @PathVariable @ApiParam("学生id") @NotNull @LongValid Long studentId){
        return response(taskTemplateStudentTeacherQuestionService.curriculumList(taskId, templateId, studentId));
    }

    @ApiOperation("小程序 -- 题目列表")
    @AuthenticationCheck
    @PostMapping("/student/question/list")
    public ResultResponse<List<QuestionShowVO>> questionList(@RequestBody QuestionListAO ao){
        return response(taskTemplateStudentTeacherQuestionService.questionList(ao));
    }

    @ApiOperation("小程序 -- 提交题目")
    @AuthenticationCheck
    @PostMapping("/student/submit")
    public BaseResponse submit(@RequestBody @Custom(value=ListNotEmptyCheck.class, message = "请填写完整") List<QuestionData> ao){
        taskTemplateStudentTeacherQuestionService.submit(ao);
        return response();
    }

    @ApiOperation("小程序 -- 提交问卷(所有调查模板都完成后调用)")
    @AuthenticationCheck
    @GetMapping("/student/task/submit/{taskId}/{studentId}")
    public BaseResponse taskSubmit(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId,
                                   @PathVariable @ApiParam("学生id") @NotNull @LongValid Long studentId){
        taskStudentService.finish(taskId, studentId);
        return response();
    }

    @ApiOperation("统计 -- 下载Excel模板")
    @GetMapping("/download/{type}")
    public void download(HttpServletResponse response,
                         @PathVariable @ApiParam("模板类型") @NotNull @Enum SurveyObj type){
        taskTemplateStudentTeacherQuestionService.downloadTemplate(response, type);
    }

    @ApiOperation("统计 -- 导入统计")
    @PostMapping("import/{taskId}/{templateId}")
    public void importResult(@PathVariable @ApiParam("调查任务id") @NotNull @LongValid Long taskId,
                             @PathVariable @ApiParam("调查模板id") @NotNull @LongValid Long templateId,
                             @PathVariable @ApiParam("模板文件") @NotNull MultipartFile file){
        taskTemplateStudentTeacherQuestionService.importResult(taskId, templateId, file);
    }

}
