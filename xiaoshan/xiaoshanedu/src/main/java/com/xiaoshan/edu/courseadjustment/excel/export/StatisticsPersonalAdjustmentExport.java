package com.xiaoshan.edu.courseadjustment.excel.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.poi.ss.usermodel.FillPatternType;
import start.framework.commons.converter.DateTimefConverterEditor;
import start.magic.core.converter.PropertyConverter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter@Setter@ToString
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@ColumnWidth(16)
public class StatisticsPersonalAdjustmentExport {
    @ExcelProperty(value = "审批编号",index = 0)
    private String approvalNo;

    @ExcelProperty(value = "调课课程",index = 1)
    private String courseName;

    @ExcelProperty(value = "调课时间", index = 2)
    private String courseTime;

    @ExcelProperty(value = "调课教师", index = 3)
    private String teacherName;

    @ExcelProperty(value = "被调课程", index = 4)
    private String adjustedCourseName;

    @ExcelProperty(value = "被调时间", index = 5)
    @ColumnWidth(24)
    private String adjustedCourseTime;

    @ExcelProperty(value = "被调教师", index = 6)
    private String adjustedTeacherName;

    @ExcelProperty(value = "申请时间", index = 7)
    @ColumnWidth(24)
    @PropertyConverter(DateTimefConverterEditor.class)
    private Date createdDate;
}
