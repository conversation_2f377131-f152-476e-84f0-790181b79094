package com.xiaoshan.edu.timetable.service.impl;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.common.collect.ImmutableMap;
import com.xiaoshan.basic.vo.*;
import com.xiaoshan.edu.api.facade.CourseFacade;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.enums.ItemTypeEnum;
import com.xiaoshan.edu.model.ElectiveQuery;
import com.xiaoshan.edu.model.RoomClassFloorTreeVO;
import com.xiaoshan.edu.model.RoomClassVO;
import com.xiaoshan.edu.timetable.mapper.TableCopyConvert;
import com.xiaoshan.edu.vo.timetable.*;
import com.xiaoshan.edu.vo.timetable.TeacherDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.common.utils.IoUtil;
import com.xiaoshan.basic.ao.CourseQuery;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.ao.timetable.ClassesQuery;
import com.xiaoshan.edu.ao.timetable.StudentQuery;
import com.xiaoshan.edu.ao.timetable.StudentListQuery;
import com.xiaoshan.edu.ao.timetable.StudentWeekQuery;
import com.xiaoshan.edu.ao.timetable.TeacherQuery;
import com.xiaoshan.edu.ao.timetable.TeacherWeekQuery;
import com.xiaoshan.edu.courseadjustment.service.CourseAdjustmentService;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.StudentTableDTO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.dao.GeneralTableDao;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.timetable.utils.ClassTableUtils;
import com.xiaoshan.edu.timetable.utils.DateUtils;
import com.xiaoshan.edu.timetable.utils.MainTableUtils;
import com.xiaoshan.edu.timetable.utils.RuleUtils;
import com.xiaoshan.edu.timetable.utils.StudentTableUtils;
import com.xiaoshan.edu.timetable.utils.TeacherTableUtils;
import com.xiaoshan.oa.dto.ClassDTO;

import start.framework.commons.exception.BusinessException;
import start.framework.service.impl.SqlBaseServiceImpl;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;
import start.magic.persistence.source.jdbc.sqlplus.conditions.update.DeleteWrapper;
import start.magic.thirdparty.json.JsonArray;
import start.magic.utils.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("generalTableService")
public class GeneralTableServiceImpl extends SqlBaseServiceImpl<GeneralTableDO, Long> implements GeneralTableService {

    private final GeneralTableDao generalTableDao;

    @Autowired
    private CurriculumTableService curriculumTableService;

    @Autowired
    private SettingFacade settingFacade;

    @Autowired
    private FoundationFacade foundationFacade;

    @Autowired
    private CourseFacade courseFacade;

    @Autowired
    private CurriculumService curriculumService;

    @Lazy
    @Autowired
    private CourseAdjustmentService courseAdjustmentService;

    public GeneralTableServiceImpl(@Qualifier("generalTableDao") GeneralTableDao generalTableDao) {
        super(generalTableDao);
        this.generalTableDao = generalTableDao;
    }

    @Override
    public List<GeneralTableDO> queryByCurriculumTableId(Long curriculumTableId) {
        return queryForList("curriculumTableId", curriculumTableId);
    }

    @Override
    public List<GeneralTableDO> queryByCurriculumTableIdList(List<Long> curriculumTableIds) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId", curriculumTableIds);
        return generalTableDao.queryForList(wrapper);
    }

    @Override
    public int removeByCurriculumTableIdDouble(Long curriculumTableId) {
        DeleteWrapper wrapper = new DeleteWrapper();
        wrapper.eq("curriculumTableId", curriculumTableId).eq("type", String.valueOf(WeekTypeEnum.DOUBLE));
        return generalTableDao.executeUpdate(wrapper);
    }

    @Override
    public List<Long> getCurriculumTableIds(Long stuId, Long teacherId, Boolean isClassTeacher, Boolean only) {
        QueryWrapper wrapper = new QueryWrapper();
        if (stuId != null) {
            wrapper.apply("JSON_CONTAINS(stuIds->'$[*]', '\"" + stuId + "\"', '$')");
        }
        if (teacherId != null) {
            StringBuilder sqlStr = new StringBuilder();
            if (isClassTeacher) {
                List<ClassVO> classes = foundationFacade.allClass();
                Map<Long, ClassVO> classesMap = classes.stream().collect(Collectors.toMap(ClassVO::getHeadTeacherId, Function.identity(), (t, t1) -> t1));
                if (classesMap.containsKey(teacherId)) {
                    ClassVO vo = classesMap.get(teacherId);
                    sqlStr.append("JSON_CONTAINS(classIds->'$[*]', '\"").append(vo.getId()).append("\"', '$')");
                } else {
                    return new ArrayList<>();
                }
            }
            if (only) {
                if (!isClassTeacher) {
                    sqlStr.append("JSON_CONTAINS(teacherIds->'$[*]', '\"").append(teacherId).append("\"', '$')");
                }
            } else {
                if (isClassTeacher) {
                    sqlStr.append(" OR ");
                }
                sqlStr.append("JSON_CONTAINS(teacherIds->'$[*]', '\"").append(teacherId).append("\"', '$')");
            }
            wrapper.apply(sqlStr.toString());
        }
        List<GeneralTableDO> list = generalTableDao.queryForList(wrapper);
        return list.stream().map(GeneralTableDO::getCurriculumTableId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> getCurriculumTableList(List<Long> stuIds, List<Long> teacherIds) {
        QueryWrapper wrapper = new QueryWrapper();
        if (!CollectionUtils.isEmpty(stuIds)) {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("JSON_CONTAINS(stuIds->'$[*]', '\"").append(stuIds.get(0)).append("\"', '$')");
            if (stuIds.size() > 1) {
                for (int i = 1; i < stuIds.size(); i++) {
                    sqlStr.append(" OR ");
                    sqlStr.append("JSON_CONTAINS(stuIds->'$[*]', '\"").append(stuIds.get(i)).append("\"', '$')");
                }
            }
            wrapper.apply(sqlStr.toString());
        }
        if (!CollectionUtils.isEmpty(teacherIds)) {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("JSON_CONTAINS(teacherIds->'$[*]', '\"").append(teacherIds.get(0)).append("\"', '$')");
            if (teacherIds.size() > 1) {
                for (int i = 1; i < teacherIds.size(); i++) {
                    sqlStr.append(" OR ");
                    sqlStr.append("JSON_CONTAINS(teacherIds->'$[*]', '\"").append(teacherIds.get(i)).append("\"', '$')");
                }
            }
            wrapper.apply(sqlStr.toString());
        }
        List<GeneralTableDO> list = generalTableDao.queryForList(wrapper);
        return list.stream().map(GeneralTableDO::getCurriculumTableId).distinct().collect(Collectors.toList());
    }

    @Override
    public GeneralTableDO getGeneralTableDO(Long curriculumTableId, WeekTypeEnum weekType) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("curriculumTableId", curriculumTableId).eq("type", String.valueOf(weekType));
        return get(generalTableDao.queryForList(wrapper));
    }

    @Override
    public List<GeneralTableDO> getAllSemesterTable(Long semesterId, Integer orderNo, Long curriculumTableId) {
        List<CurriculumTableDO> curriculumTables = curriculumTableService.getTable(semesterId, orderNo);
        //移除当前自己
        List<Long> curriculumTableIds;
        if (curriculumTableId == null) {
            curriculumTableIds = curriculumTables.stream().map(CurriculumTableDO::getId).distinct().collect(Collectors.toList());
        } else {
            curriculumTableIds = curriculumTables.stream().map(CurriculumTableDO::getId)
                    .filter(id -> !id.equals(curriculumTableId)).distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(curriculumTableIds)) {
            return Collections.emptyList();
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId", curriculumTableIds);
        return generalTableDao.queryForList(wrapper);
    }

    /**
     * 获取总课表
     *
     * @param curriculumTableId 课表ID
     * @return 总课程列表
     */
    @Override
    public List<GeneralTableVO> mainTable(Long curriculumTableId) {
        List<GeneralTableVO> result = new ArrayList<>();
        List<GeneralTableDO> generalTableDOList = queryForList("curriculumTableId", curriculumTableId);
        for (GeneralTableDO generalTableDO : generalTableDOList) {
            GeneralTableVO generalTableVO = new GeneralTableVO();
            generalTableVO.setType(generalTableDO.getType());
            generalTableVO.setData(generalTableDO.getMainTable());
            result.add(generalTableVO);
            for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                for (WeekEnum dayOfWeek : dataTableDTO.getDetail().keySet()) {
                    Map<String, String> sectionCourseNameAbbreviationMap = dataTableDTO.getDetail().get(dayOfWeek);
                    for (String section : sectionCourseNameAbbreviationMap.keySet()) {
                        convertAbbreviationToCourseInfo(sectionCourseNameAbbreviationMap, generalTableDO, dataTableDTO, dayOfWeek, section);
                    }
                }
                dataTableDTO.setResult(dataTableDTO.getDetail());
            }
        }
        return result;
    }

    /**
     * 将课程缩写信息转换为课程信息
     *
     * @param generalTableDO
     * @param dataTableDTO
     * @param dayOfWeek
     * @param sectionCourseNameAbbreviationMap
     * @param section
     */
    private void convertAbbreviationToCourseInfo(Map<String, String> sectionCourseNameAbbreviationMap, GeneralTableDO generalTableDO, DataTableDTO dataTableDTO, WeekEnum dayOfWeek, String section) {
        String abbreviation = sectionCourseNameAbbreviationMap.get(section);
        if (RuleUtils.ignoreCourse(abbreviation)) {
            String courseName = generalTableDO.getCurriculumAlias().get(abbreviation);
            String classOrRoomId = dataTableDTO.getClassOrRoomId();
            String classOrRoomName = dataTableDTO.getClassOrRoomName();
            Long teacherId = 0L;
            String teacherName = "待安排";
            String courseInfo = RuleUtils.separate(abbreviation, courseName, classOrRoomId, classOrRoomName, teacherId, teacherName);
            sectionCourseNameAbbreviationMap.put(section, courseInfo);
        } else {
            TeacherTableDTO teacherTable = TeacherTableUtils.getTeacherTable(generalTableDO.getTeacherTable(), dayOfWeek, section, abbreviation, dataTableDTO.getClassOrRoomId());
            if (teacherTable != null) {
                String courseName = generalTableDO.getCurriculumAlias().get(abbreviation);
                String classOrRoomId = dataTableDTO.getClassOrRoomId();
                String classOrRoomName = dataTableDTO.getClassOrRoomName();
                Long teacherId = teacherTable.getTeacherId();
                String teacherName = teacherTable.getTeacherName();
                String courseInfo = RuleUtils.separate(abbreviation, courseName, classOrRoomId, classOrRoomName, teacherId, teacherName);
                sectionCourseNameAbbreviationMap.put(section, courseInfo);
            }
        }

    }


    @Override
    public List<GeneralTableVO> classesTable(Long curriculumTableId) {
        List<GeneralTableVO> result = new ArrayList<>();
        List<GeneralTableDO> generalTableDOList = queryForList("curriculumTableId", curriculumTableId);
        for (GeneralTableDO generalTableDO : generalTableDOList) {
            GeneralTableVO generalTableVO = new GeneralTableVO();
            generalTableVO.setType(generalTableDO.getType());
            generalTableVO.setData(generalTableDO.getClassTable());
            result.add(generalTableVO);
            for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                for (WeekEnum dayOfWeek : dataTableDTO.getDetail().keySet()) {
                    Map<String, String> sectionAbbreviationMap = dataTableDTO.getDetail().get(dayOfWeek);
                    for (String section : sectionAbbreviationMap.keySet()) {
                        String abbreviation = sectionAbbreviationMap.get(section);
                        String[] abbreviationArray = abbreviation.split(Constant.SPLITSTRING);
                        Set<String> courseNameAbbreviationSet = new HashSet<>(Arrays.asList(abbreviationArray));
                        List<String> courseInfoList = new ArrayList<>();
                        for (String courseNameAbbreviation : courseNameAbbreviationSet) {
                            String courseInfo = getClassTableCourseInfo(generalTableDO, dataTableDTO, dayOfWeek, section, courseNameAbbreviation);
                            if (courseInfo != null) {
                                courseInfoList.add(courseInfo);
                            }
                        }
                        sectionAbbreviationMap.put(section, StringUtils.listToString(courseInfoList, Constant.SPLITABBSTRING));
                    }
                }
                dataTableDTO.setResult(dataTableDTO.getDetail());
            }
        }
        CurriculumTableDO table = curriculumTableService.load(curriculumTableId);
        Map<String, RestSectionDetailVO> restSectionDetailMap = settingFacade.getRestAll(table.getSectionId(), table.getEnrollmentYear());
        setClassTableSectionDuration(result, restSectionDetailMap);
        return result;
    }


    /**
     * 获取教室课表课程信息
     *
     * @param generalTableDO
     * @param dataTableDTO
     * @param dayOfWeek
     * @param section
     * @param courseNameAbbreviation
     * @return
     */
    private String getClassTableCourseInfo(GeneralTableDO generalTableDO, DataTableDTO dataTableDTO, WeekEnum dayOfWeek, String section, String courseNameAbbreviation) {
        if (RuleUtils.ignoreCourse(courseNameAbbreviation)) {
            String courseName = generalTableDO.getCurriculumAlias().get(courseNameAbbreviation);
            String classOrRoomId = dataTableDTO.getClassOrRoomId();
            Long teacherId = 0L;
            String teacherName = "待安排";
            return RuleUtils.separate(courseNameAbbreviation, courseName, classOrRoomId, dataTableDTO.getClassOrRoomName(), teacherId, teacherName);
        } else {
            Set<Long> teacherIds = StudentTableUtils.getTeacherIds(generalTableDO.getStudentTable(), courseNameAbbreviation, Long.parseLong(dataTableDTO.getClassOrRoomId()));
            if (!CollectionUtils.isEmpty(teacherIds)) {
                for (Long teacherId : teacherIds) {
                    TeacherTableDTO teacherDto = TeacherTableUtils.getTeacherTableDTO(generalTableDO.getTeacherTable(), dayOfWeek, section, courseNameAbbreviation, teacherId);
                    if (teacherDto != null) {
                        String classOrRoomId = teacherDto.getDetail().get(dayOfWeek).get(section);
                        DataTableDTO dataTable = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), classOrRoomId);
                        if (dataTable != null) {
                            String courseName = generalTableDO.getCurriculumAlias().get(courseNameAbbreviation);
                            classOrRoomId = dataTable.getClassOrRoomId();
                            String classOrRoomName = dataTable.getClassOrRoomName();
                            String teacherName = teacherDto.getTeacherName();
                            return RuleUtils.separate(courseNameAbbreviation, courseName, classOrRoomId, classOrRoomName, teacherId, teacherName);
                        }
                    }
                }
            } else {
                TeacherTableDTO teacherDto = TeacherTableUtils.getTeacherTable(generalTableDO.getTeacherTable(), dayOfWeek, section, courseNameAbbreviation, dataTableDTO.getClassOrRoomId());
                if (teacherDto != null) {
                    String classOrRoomId = teacherDto.getDetail().get(dayOfWeek).get(section);
                    DataTableDTO dataTable = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), classOrRoomId);
                    if (dataTable != null) {
                        String courseName = generalTableDO.getCurriculumAlias().get(courseNameAbbreviation);
                        classOrRoomId = dataTable.getClassOrRoomId();
                        String classOrRoomName = dataTable.getClassOrRoomName();
                        Long teacherId = teacherDto.getTeacherId();
                        String teacherName = teacherDto.getTeacherName();
                        return RuleUtils.separate(courseNameAbbreviation, courseName, classOrRoomId, classOrRoomName, teacherId, teacherName);
                    }
                }
            }
        }
        return null;
    }


    /**
     * 设置课程开始结束时间
     *
     * @param result
     * @param restSectionDetailMap
     */
    private void setClassTableSectionDuration(List<GeneralTableVO> result, Map<String, RestSectionDetailVO> restSectionDetailMap) {
        for (GeneralTableVO generalTableVO : result) {
            for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                for (WeekEnum dayOfWeek : dataTableDTO.getDetailv().keySet()) {
                    Map<String, List<TeacherAbbDetailVO>> detail = dataTableDTO.getDetailv().get(dayOfWeek);
                    for (String section : detail.keySet()) {
                        RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                        for (TeacherAbbDetailVO teacherAbbDetailVO : detail.get(section)) {
                            if (restSectionDetailVO != null) {
                                teacherAbbDetailVO.setStartTime(restSectionDetailVO.getStartTime());
                                teacherAbbDetailVO.setEndTime(restSectionDetailVO.getEndTime());
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<TeacherTableVO> teacherTable(Long curriculumTableId) {
        List<TeacherTableVO> result = new ArrayList<>();
        List<GeneralTableDO> generalTableDOList = queryForList("curriculumTableId", curriculumTableId);
        for (GeneralTableDO generalTableDO : generalTableDOList) {
            TeacherTableVO teacherTableVO = new TeacherTableVO();
            teacherTableVO.setType(generalTableDO.getType());
            Map<String, List<TeacherTableDTO>> teacherTableMap = new HashMap<>(10);
            for (String name : generalTableDO.getTeacherTable().keySet()) {
                teacherTableMap.put(generalTableDO.getCurriculumAlias().get(name), generalTableDO.getTeacherTable().get(name));
            }
            teacherTableVO.setData(teacherTableMap);
            result.add(teacherTableVO);
            for (List<TeacherTableDTO> teacherTableDTOList : teacherTableVO.getData().values()) {
                for (TeacherTableDTO teacherTableDTO : teacherTableDTOList) {
                    String courseNameAbbreviation = TeacherTableUtils.getTeacherAbbreviation(generalTableDO.getTeacherTable(), teacherTableDTO.getTeacherId());
                    for (WeekEnum dayOfWeek : teacherTableDTO.getDetail().keySet()) {
                        Map<String, String> sectionCourseInfoMap = teacherTableDTO.getDetail().get(dayOfWeek);
                        for (String section : sectionCourseInfoMap.keySet()) {
                            String classOrRoomId = sectionCourseInfoMap.get(section);
                            DataTableDTO dataTableDTO = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), classOrRoomId);
                            if (dataTableDTO != null) {
                                String courseName = generalTableDO.getCurriculumAlias().get(courseNameAbbreviation);
                                String classOrRoomName = dataTableDTO.getClassOrRoomName();
                                Long teacherId = teacherTableDTO.getTeacherId();
                                String teacherName = teacherTableDTO.getTeacherName();
                                String courseInfo = RuleUtils.separate(courseNameAbbreviation, courseName, classOrRoomId, classOrRoomName, teacherId, teacherName);
                                sectionCourseInfoMap.put(section, courseInfo);
                            }
                        }
                    }
                    teacherTableDTO.setResult(teacherTableDTO.getDetail());
                }
            }
        }
        CurriculumTableDO curriculumTableDO = curriculumTableService.load(curriculumTableId);
        Map<String, RestSectionDetailVO> restSectionDetailMap = settingFacade.getRestAll(curriculumTableDO.getSectionId(), curriculumTableDO.getEnrollmentYear());
        setTeacherTableDuration(result, restSectionDetailMap);
        return result;
    }

    @Override
    public List<TeacherTableVO> teacherTableBatch(Map<Long, CurriculumTableDO> curriculumTableMap,Map<String, RestSectionVO> restSectionMap,List<Long> curriculumTableIds) {
        List<TeacherTableVO> result = new ArrayList<>();
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId",curriculumTableIds);
        List<GeneralTableDO> generalTableDOList = queryForMap(GeneralTableDO.class,wrapper);
        for (GeneralTableDO generalTableDO : generalTableDOList) {
            TeacherTableVO teacherTableVO = new TeacherTableVO();
            teacherTableVO.setType(generalTableDO.getType());
            Map<String, List<TeacherTableDTO>> teacherTableMap = new HashMap<>(10);
            for (String name : generalTableDO.getTeacherTable().keySet()) {
                teacherTableMap.put(generalTableDO.getCurriculumAlias().get(name), generalTableDO.getTeacherTable().get(name));
            }
            teacherTableVO.setData(teacherTableMap);
            result.add(teacherTableVO);
            for (List<TeacherTableDTO> teacherTableDTOList : teacherTableVO.getData().values()) {
                for (TeacherTableDTO teacherTableDTO : teacherTableDTOList) {
                    String courseNameAbbreviation = TeacherTableUtils.getTeacherAbbreviation(generalTableDO.getTeacherTable(), teacherTableDTO.getTeacherId());
                    for (WeekEnum dayOfWeek : teacherTableDTO.getDetail().keySet()) {
                        Map<String, String> sectionCourseInfoMap = teacherTableDTO.getDetail().get(dayOfWeek);
                        for (String section : sectionCourseInfoMap.keySet()) {
                            String classOrRoomId = sectionCourseInfoMap.get(section);
                            DataTableDTO dataTableDTO = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), classOrRoomId);
                            if (dataTableDTO != null) {
                                String courseName = generalTableDO.getCurriculumAlias().get(courseNameAbbreviation);
                                String classOrRoomName = dataTableDTO.getClassOrRoomName();
                                Long teacherId = teacherTableDTO.getTeacherId();
                                String teacherName = teacherTableDTO.getTeacherName();
                                String courseInfo = RuleUtils.separate(courseNameAbbreviation, courseName, classOrRoomId, classOrRoomName, teacherId, teacherName);
                                sectionCourseInfoMap.put(section, courseInfo);
                            }
                        }
                    }
                    teacherTableDTO.setResult(teacherTableDTO.getDetail());
                }
            }
        }
        curriculumTableIds.forEach(id ->{
            CurriculumTableDO curriculumTableDo = curriculumTableMap.get(id);
            Map<String, RestSectionDetailVO> restSectionDetailMap = restSectionDetailMap(restSectionMap, curriculumTableDo.getSectionId(), curriculumTableDo.getEnrollmentYear());
            setTeacherTableDuration(result, restSectionDetailMap);
        });
        return result;
    }

    public Map<String, RestSectionDetailVO> restSectionDetailMap(Map<String, RestSectionVO> restSectionMap,Long sectionId, Integer enrollmentYear) {
        RestSectionVO restSectionVo = restSectionMap.get(sectionId + "-" + enrollmentYear);
        if (Objects.nonNull(restSectionVo)) {
            return restSectionVo.getDetails().stream().collect(Collectors.toMap(k -> (k.getTimeRange() + "-" + k.getSectionName() + "-" + k.getType()), Function.identity(), (v1, v2) -> v1));
        }
        return Collections.emptyMap();
    }

    /**
     * 设置教师课表开始结束时间
     *
     * @param result
     * @param restSectionDetailMap
     */
    private void setTeacherTableDuration(List<TeacherTableVO> result, Map<String, RestSectionDetailVO> restSectionDetailMap) {
        for (TeacherTableVO teacherTableVO : result) {
            for (String abb : teacherTableVO.getData().keySet()) {
                List<TeacherTableDTO> teacherTableDTOList = teacherTableVO.getData().get(abb);
                for (TeacherTableDTO teacherTable : teacherTableDTOList) {
                    for (WeekEnum dayOfWeek : teacherTable.getDetailv().keySet()) {
                        Map<String, List<TeacherAbbDetailVO>> sectionAbbMap = teacherTable.getDetailv().get(dayOfWeek);
                        for (String section : sectionAbbMap.keySet()) {
                            RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                            for (TeacherAbbDetailVO teacherAbbDetailVO : sectionAbbMap.get(section)) {
                                if (restSectionDetailVO != null) {
                                    teacherAbbDetailVO.setStartTime(restSectionDetailVO.getStartTime());
                                    teacherAbbDetailVO.setEndTime(restSectionDetailVO.getEndTime());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<StudentTableVO> studentTable(Long curriculumTableId) {
        List<StudentTableVO> result = new ArrayList<>();
        List<GeneralTableDO> list = queryForList("curriculumTableId", curriculumTableId);
        for (GeneralTableDO generalTableDO : list) {
            StudentTableVO studentTableVO = new StudentTableVO();
            studentTableVO.setType(generalTableDO.getType());
            studentTableVO.setData(generalTableDO.getStudentTable());
            result.add(studentTableVO);
            for (StudentTableDTO studentTableDTO : studentTableVO.getData()) {
                Map<String, TeacherTableDetailVO> tableDetailMap = new LinkedHashMap<>();
                for (String abbreviation : studentTableDTO.getDetail().keySet()) {
                    for (Long teacherId : studentTableDTO.getDetail().get(abbreviation).keySet()) {
                        if (teacherId == 0L) {
                            TeacherTableDetailVO teacherTableDetailVO = new TeacherTableDetailVO();
                            teacherTableDetailVO.setTeacherId(teacherId);
                            teacherTableDetailVO.setTeacherName("待安排");
                            tableDetailMap.put(abbreviation, teacherTableDetailVO);
                        } else {
                            TeacherTableDTO teacherTable = TeacherTableUtils.getTeacherTable(generalTableDO.getTeacherTable(), abbreviation, teacherId);
                            if (teacherTable != null) {
                                TeacherTableDetailVO teacherTableDetailVO = new TeacherTableDetailVO();
                                teacherTableDetailVO.setTeacherId(teacherTable.getTeacherId());
                                teacherTableDetailVO.setTeacherName(teacherTable.getTeacherName());
                                tableDetailMap.put(abbreviation, teacherTableDetailVO);
                            }
                        }
                    }
                }
                studentTableDTO.setDetailv(tableDetailMap);
                studentTableDTO.setDetail(null);
            }
        }
        return result;
    }

    @Override
    public Map<WeekTypeEnum, DataTableDTO> mainTableDetail(ClassesQuery ao) {
        List<GeneralTableVO> classesTable = mainTable(ao.getCurriculumTableId());
        Map<WeekTypeEnum, DataTableDTO> result = new LinkedHashMap<>();
        for (GeneralTableVO generalTableVO : classesTable) {
            for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                if (dataTableDTO.getClassOrRoomId().equals(ao.getClassId())) {
                    result.put(generalTableVO.getType(), dataTableDTO);
                }
            }
        }
        return result;
    }

    @Override
    public Map<WeekTypeEnum, DataTableDTO> classesTableDetail(ClassesQuery ao) {
        List<GeneralTableVO> classesTable = classesTable(ao.getCurriculumTableId());
        Map<WeekTypeEnum, DataTableDTO> result = new LinkedHashMap<>();
        for (GeneralTableVO generalTableVO : classesTable) {
            for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                if (dataTableDTO.getClassOrRoomId().equals(ao.getClassId())) {
                    result.put(generalTableVO.getType(), dataTableDTO);
                }
            }
        }
        return result;
    }

    @Override
    public Map<WeekTypeEnum, TeacherTableDTO> teacherTableDetail(TeacherQuery ao) {
        List<TeacherTableVO> teacherTable = teacherTable(ao.getCurriculumTableId());
        Map<WeekTypeEnum, TeacherTableDTO> result = new LinkedHashMap<>();
        for (TeacherTableVO teacher : teacherTable) {
            for (String abb : teacher.getData().keySet()) {
                List<TeacherTableDTO> teacherList = teacher.getData().get(abb);
                for (TeacherTableDTO dto : teacherList) {
                    if (dto.getTeacherId().equals(ao.getTeacherId())) {
                        result.put(teacher.getType(), dto);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Map<WeekTypeEnum, List<TeacherTableDTO>> teacherTableDetailBatch(Map<Long, CurriculumTableDO> curriculumTableMap,List<Long> curriculumTableIds,Map<String, RestSectionVO> restSectionMap,TeacherWeekQuery teacherWeekQuery) {
        List<TeacherTableDTO> singleTeacherTableList = new ArrayList<>();
        List<TeacherTableDTO> doubleTeacherTableList = new ArrayList<>();
        List<TeacherTableVO> teacherTable = teacherTableBatch(curriculumTableMap,restSectionMap,curriculumTableIds);
        Map<WeekTypeEnum, List<TeacherTableDTO>> result = new LinkedHashMap<>();
        for (TeacherTableVO teacher : teacherTable) {
            for (String abb : teacher.getData().keySet()) {
                List<TeacherTableDTO> teacherList = teacher.getData().get(abb);
                for (TeacherTableDTO dto : teacherList) {
                    if (teacherWeekQuery.getTeacherIds().contains(dto.getTeacherId())) {
                        if (WeekTypeEnum.SINGLE.equals(teacher.getType())) {
                            singleTeacherTableList.add(dto);
                        }else {
                            doubleTeacherTableList.add(dto);
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(singleTeacherTableList)) {
            result.put(WeekTypeEnum.SINGLE,singleTeacherTableList);
        }
        if (!CollectionUtils.isEmpty(doubleTeacherTableList)) {
            result.put(WeekTypeEnum.DOUBLE,doubleTeacherTableList);
        }
        return result;
    }

    @Override
    public Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTableDetail(Long curriculumTableId) {
        List<GeneralTableDO> list = queryForList("curriculumTableId", curriculumTableId);
        Map<Long, Map<WeekTypeEnum, DataTableDTO>> resultMap = new LinkedHashMap<>();
        for (GeneralTableDO vo : list) {
            for (StudentTableDTO studentDto : vo.getStudentTable()) {
                Map<WeekTypeEnum, DataTableDTO> result = resultMap.computeIfAbsent(studentDto.getStuId(), k -> new HashMap<>(5));
                Map<String, DataTableDTO> classTable = vo.getClassTable().stream()
                        .collect(Collectors.toMap(DataTableDTO::getClassOrRoomId, Function.identity()));
                DataTableDTO dt = classTable.get(String.valueOf(studentDto.getClassId()));
                if (dt == null) {
                    break;
                }
                DataTableDTO dtCopy = new DataTableDTO();
                dtCopy.setClassOrRoomId(dt.getClassOrRoomId());
                dtCopy.setClassOrRoomName(dt.getClassOrRoomName());
                dtCopy.setDetail(new HashMap<>(5));
                for (WeekEnum week : dt.getDetail().keySet()) {
                    Map<String, String> allDetail = new LinkedHashMap<>();
                    Map<String, String> detail = dt.getDetail().get(week);
                    for (String section : detail.keySet()) {
                        String abbreviation = detail.get(section);
                        String[] abbreviationArray = abbreviation.split(Constant.SPLITSTRING);
                        Set<String> abbSet = new HashSet<>(Arrays.asList(abbreviationArray));
                        List<String> items = new ArrayList<>();
                        for (String abb : abbSet) {
                            if (studentDto.getDetail().containsKey(abb)) {
                                Map<Long, String> abbDetail = studentDto.getDetail().get(abb);
                                if (RuleUtils.ignoreCourse(abb)) {
                                    if (abbDetail.containsValue(dt.getClassOrRoomId())) {
                                        items.add(RuleUtils.separate(
                                                abb, vo.getCurriculumAlias().get(abb),
                                                dt.getClassOrRoomId(), dt.getClassOrRoomName(),
                                                0L, "待安排"));
                                    }
                                } else {
                                    for (Long teacherId : abbDetail.keySet()) {
                                        String classOrRoomId = TeacherTableUtils.getTeacherClassOrRoomId(vo.getTeacherTable(), week, section, abb, teacherId);
                                        if (abbDetail.get(teacherId).equals(classOrRoomId)) {
                                            DataTableDTO dataTableDTO = MainTableUtils.getDataTableDTO(vo.getMainTable(), classOrRoomId);
                                            TeacherTableDTO teacherDto = TeacherTableUtils.getTeacherTable(vo.getTeacherTable(), abb, teacherId);
                                            if (dataTableDTO != null && teacherDto != null) {
                                                items.add(RuleUtils.separate(
                                                        abb, vo.getCurriculumAlias().get(abb),
                                                        classOrRoomId, dataTableDTO.getClassOrRoomName(),
                                                        teacherDto.getTeacherId(), teacherDto.getTeacherName()));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (items.size() > 0) {
                            allDetail.put(section, StringUtils.listToString(items, Constant.SPLITABBSTRING));
                        }
                    }
                    dtCopy.getDetail().put(week, allDetail);
                }
                dtCopy.setResult(dtCopy.getDetail());
                result.put(vo.getType(), dtCopy);
            }
        }
        return resultMap;
    }

    @Override
    public Map<Long, Map<WeekTypeEnum, DataTableDTO>> batchStudentTableDetail(List<Long> curriculumTableIds) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId", curriculumTableIds);
        List<GeneralTableDO> list = generalTableDao.queryForList(wrapper);
        Map<Long, Map<WeekTypeEnum, DataTableDTO>> resultMap = new LinkedHashMap<>();
        for (GeneralTableDO vo : list) {
            for (StudentTableDTO studentDto : vo.getStudentTable()) {
                Map<WeekTypeEnum, DataTableDTO> result = resultMap.computeIfAbsent(studentDto.getStuId(), k -> new HashMap<>(5));
                Map<String, DataTableDTO> classTable = vo.getClassTable().stream()
                        .collect(Collectors.toMap(DataTableDTO::getClassOrRoomId, Function.identity()));
                DataTableDTO dt = classTable.get(String.valueOf(studentDto.getClassId()));
                if (dt == null) {
                    break;
                }
                DataTableDTO dtCopy = new DataTableDTO();
                dtCopy.setClassOrRoomId(dt.getClassOrRoomId());
                dtCopy.setClassOrRoomName(dt.getClassOrRoomName());
                dtCopy.setDetail(new HashMap<>(5));
                for (WeekEnum week : dt.getDetail().keySet()) {
                    Map<String, String> allDetail = new LinkedHashMap<>();
                    Map<String, String> detail = dt.getDetail().get(week);
                    for (String section : detail.keySet()) {
                        String abbreviation = detail.get(section);
                        String[] abbreviationArray = abbreviation.split(Constant.SPLITSTRING);
                        Set<String> abbSet = new HashSet<>(Arrays.asList(abbreviationArray));
                        List<String> items = new ArrayList<>();
                        for (String abb : abbSet) {
                            if (studentDto.getDetail().containsKey(abb)) {
                                Map<Long, String> abbDetail = studentDto.getDetail().get(abb);
                                if (RuleUtils.ignoreCourse(abb)) {
                                    if (abbDetail.containsValue(dt.getClassOrRoomId())) {
                                        items.add(RuleUtils.separate(
                                                abb, vo.getCurriculumAlias().get(abb),
                                                dt.getClassOrRoomId(), dt.getClassOrRoomName(),
                                                0L, "待安排"));
                                    }
                                } else {
                                    for (Long teacherId : abbDetail.keySet()) {
                                        String classOrRoomId = TeacherTableUtils.getTeacherClassOrRoomId(vo.getTeacherTable(), week, section, abb, teacherId);
                                        if (abbDetail.get(teacherId).equals(classOrRoomId)) {
                                            DataTableDTO mainDto = MainTableUtils.getDataTableDTO(vo.getMainTable(), classOrRoomId);
                                            TeacherTableDTO teacherDto = TeacherTableUtils.getTeacherTable(vo.getTeacherTable(), abb, teacherId);
                                            items.add(RuleUtils.separate(abb, vo.getCurriculumAlias().get(abb), classOrRoomId, mainDto.getClassOrRoomName(), teacherDto.getTeacherId(), teacherDto.getTeacherName()));
                                        }
                                    }
                                }
                            }
                        }
                        if (items.size() > 0) {
                            allDetail.put(section, StringUtils.listToString(items, Constant.SPLITABBSTRING));
                        }
                    }
                    dtCopy.getDetail().put(week, allDetail);
                }
                dtCopy.setResult(dtCopy.getDetail());
                result.put(vo.getType(), dtCopy);
            }
        }
        return resultMap;
    }

    @Override
    public Map<WeekTypeEnum, DataTableDTO> studentTableDetail(StudentQuery ao) {
        return studentTableDetail(ao.getCurriculumTableId()).get(ao.getStuId());
    }

    @Override
    public Map<WeekTypeEnum, List<TeachClassesDetailVO>> studentListByClassId(StudentListQuery ao) {
        CurriculumTableDO table = curriculumTableService.load(ao.getCurriculumTableId());
        String gradeName;
        if (table.getSectionId() == 1) {
            gradeName = "高中" + (table.getEnrollmentYear() + 3) + "届";
        } else {
            gradeName = "初中" + (table.getEnrollmentYear() + 3) + "届";
        }
        if (ClassTableUtils.isClassRoom(ao.getClassOrRoomId())) {
            throw new BusinessException("classOrRoomId必须为班级ID");
        }
        Long classId = Long.parseLong(ao.getClassOrRoomId());
        Map<String, RestSectionDetailVO> restSectionDetailMap = settingFacade.getRestAll(table.getSectionId(), table.getEnrollmentYear());
        RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(ao.getSection()));
        Map<WeekTypeEnum, List<TeachClassesDetailVO>> result = new LinkedHashMap<>();
        List<GeneralTableDO> list = queryForList("curriculumTableId", ao.getCurriculumTableId());
        for (GeneralTableDO generalTableDO : list) {
            List<TeachClassesDetailVO> studentGroup = new ArrayList<>();
            result.put(generalTableDO.getType(), studentGroup);
            String[] abbs = ao.getAbbreviation().split(Constant.SPLITSTRING);
            Set<String> abbSet = new HashSet<>(Arrays.asList(abbs));
            for (String abbre : abbSet) {
                List<StudentTableDTO> studentList = StudentTableUtils.getStudentAbbClassGroup(generalTableDO.getStudentTable(), abbre, classId);
                List<StudentTableDTO> studentItems = new ArrayList<>();
                if (MainTableUtils.checkClassOrRoomIdWeekSectionAbb(generalTableDO.getMainTable(), ao.getClassOrRoomId(), ao.getWeek(), ao.getSection(), abbre)) {
                    for (StudentTableDTO d : studentList) {
                        if (!d.getLeaveClass().contains(abbre)) {
                            studentItems.add(d);
                        }
                    }
                }
                if (ClassTableUtils.checkLeaveClass(generalTableDO.getClassLeaveMap(), generalTableDO.getClassTable(), classId, ao.getWeek(), ao.getSection(), abbre)) {
                    for (StudentTableDTO d : studentList) {
                        if (d.getLeaveClass().contains(abbre)) {
                            studentItems.add(d);
                        }
                    }
                }
                Map<Long, List<StudentTableDTO>> studentTableTeacherGroup = StudentTableUtils.getStudentTableByTeacherAbbGroup(studentItems, abbre);
                for (Long teacherId : studentTableTeacherGroup.keySet()) {
                    TeachClassesDetailVO stuGroup = new TeachClassesDetailVO();
                    stuGroup.setTeacherId(teacherId);
                    String classOrRoomId;
                    if (teacherId == 0L) {
                        stuGroup.setTeacherName("待安排");
                        classOrRoomId = ao.getClassOrRoomId();
                    } else {
                        TeacherTableDTO techDto = TeacherTableUtils.getTeacherTableDTO(generalTableDO.getTeacherTable(), ao.getWeek(), ao.getSection(), abbre, teacherId);
                        if (techDto == null) {
                            continue;
                        }
                        stuGroup.setTeacherName(techDto.getTeacherName());
                        classOrRoomId = techDto.getDetail().get(ao.getWeek()).get(ao.getSection());
                    }
                    DataTableDTO classOrRoomDto = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), classOrRoomId);
                    stuGroup.setClassOrRoomId(classOrRoomDto.getClassOrRoomId());
                    stuGroup.setClassOrRoomName(classOrRoomDto.getClassOrRoomName());
                    if (restSectionDetailVO != null) {
                        stuGroup.setStartTime(restSectionDetailVO.getStartTime());
                        stuGroup.setEndTime(restSectionDetailVO.getEndTime());
                    }
                    stuGroup.setGradeName(gradeName);
                    stuGroup.setAbbreviation(abbre);
                    stuGroup.setCourseName(generalTableDO.getCurriculumAlias().get(abbre));
                    DataTableDTO classDto = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), ao.getClassOrRoomId());
                    stuGroup.setClassId(classId);
                    stuGroup.setClassName(classDto.getClassOrRoomName());
                    List<StudentTableDetailVO> students = new ArrayList<>();
                    stuGroup.setStudents(students);
                    List<StudentTableDTO> stuList = studentTableTeacherGroup.get(teacherId);
                    students.addAll(stuList);
                    studentGroup.add(stuGroup);
                }
            }
        }
        return result;
    }

    @Override
    public Map<WeekTypeEnum, List<TeachClassesDetailVO>> studentListByClassOrRoomId(StudentListQuery ao) {
        CurriculumTableDO table = curriculumTableService.load(ao.getCurriculumTableId());
        String gradeName;
        if (table.getSectionId() == 1) {
            gradeName = "高中" + (table.getEnrollmentYear() + 3) + "届";
        } else {
            gradeName = "初中" + (table.getEnrollmentYear() + 3) + "届";
        }
        Map<String, RestSectionDetailVO> restSectionDetailMap = settingFacade.getRestAll(table.getSectionId(), table.getEnrollmentYear());
        RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(ao.getSection()));
        Map<WeekTypeEnum, List<TeachClassesDetailVO>> result = new LinkedHashMap<>();
        List<GeneralTableDO> list = queryForList("curriculumTableId", ao.getCurriculumTableId());
        for (GeneralTableDO generalTableDO : list) {
            List<TeachClassesDetailVO> studentGroup = new ArrayList<>();
            result.put(generalTableDO.getType(), studentGroup);
            String[] abbs = ao.getAbbreviation().split(Constant.SPLITSTRING);
            Set<String> abbSet = new HashSet<>(Arrays.asList(abbs));
            for (String abbre : abbSet) {
                TeacherTableDTO techDto = null;
                List<StudentTableDTO> studentListDto = new ArrayList<>();
                if (RuleUtils.ignoreCourse(abbre)) {
                    techDto = new TeacherTableDTO();
                    techDto.setTeacherId(0L);
                    techDto.setTeacherName("待安排");
                } else {
                    techDto = TeacherTableUtils.getTeacherTable(generalTableDO.getTeacherTable(), ao.getWeek(), ao.getSection(), abbre, ao.getClassOrRoomId());
                    if (techDto == null) {
                        continue;
                    }
                    List<StudentTableDTO> stud = StudentTableUtils.getStudentLeavelTeacher(generalTableDO.getStudentTable(), abbre, techDto.getTeacherId());
                    for (StudentTableDTO s : stud) {
                        if (ClassTableUtils.checkLeaveClass(generalTableDO.getClassLeaveMap(), generalTableDO.getClassTable(), s.getClassId(), ao.getWeek(), ao.getSection(), abbre)) {
                            studentListDto.add(s);
                        }
                    }
                }
                if (!ClassTableUtils.isClassRoom(ao.getClassOrRoomId())) {
                    //如果为班级则为本班课获取所有该课程未走班的学生
                    studentListDto.addAll(StudentTableUtils.getStudentNoLeavelClass(generalTableDO.getStudentTable(), abbre, Long.parseLong(ao.getClassOrRoomId())));
                }
                Map<Long, List<StudentTableDTO>> studentTableTeacherGroup = StudentTableUtils.getStudentTable(studentListDto);
                for (Long classId : studentTableTeacherGroup.keySet()) {
                    List<StudentTableDTO> stuList = studentTableTeacherGroup.get(classId);
                    TeachClassesDetailVO stuGroup = new TeachClassesDetailVO();
                    stuGroup.setAbbreviation(abbre);
                    stuGroup.setCourseName(generalTableDO.getCurriculumAlias().get(abbre));
                    stuGroup.setTeacherId(techDto.getTeacherId());
                    stuGroup.setTeacherName(techDto.getTeacherName());
                    stuGroup.setGradeName(gradeName);
                    if (restSectionDetailVO != null) {
                        stuGroup.setStartTime(restSectionDetailVO.getStartTime());
                        stuGroup.setEndTime(restSectionDetailVO.getEndTime());
                    }
                    DataTableDTO classDto = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), String.valueOf(classId));
                    stuGroup.setClassId(classId);
                    stuGroup.setClassName(classDto.getClassOrRoomName());
                    DataTableDTO classOrRoomDto = MainTableUtils.getDataTableDTO(generalTableDO.getMainTable(), ao.getClassOrRoomId());
                    stuGroup.setClassOrRoomId(classOrRoomDto.getClassOrRoomId());
                    stuGroup.setClassOrRoomName(classOrRoomDto.getClassOrRoomName());
                    List<StudentTableDetailVO> students = new ArrayList<>();
                    stuGroup.setStudents(students);
                    students.addAll(stuList);
                    studentGroup.add(stuGroup);
                }
            }
        }
        return result;
    }

    @Override
    public Map<WeekTypeEnum, List<ClassesVO>> allClassList(Long semesterId, Integer orderNo, Boolean isClassRoom) {
        List<CurriculumTableDO> allCurriculumTables = curriculumTableService.getTable(semesterId, orderNo);
        if (allCurriculumTables.isEmpty()) {
            return new LinkedHashMap<>();
        }
        List<Long> curriculumTableIds = allCurriculumTables.stream().map(CurriculumTableDO::getId).collect(Collectors.toList());
        return allClassList(curriculumTableIds, isClassRoom);
    }

    @Override
    public Map<WeekTypeEnum, List<ClassesVO>> allClassList(List<Long> curriculumTableIds, Boolean isClassRoom) {
        Map<WeekTypeEnum, List<ClassesVO>> allMap = new LinkedHashMap<>();
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId", curriculumTableIds);
        List<GeneralTableDO> list = generalTableDao.queryForList(wrapper);
        for (GeneralTableDO vo : list) {
            List<ClassesVO> classesList = allMap.computeIfAbsent(vo.getType(), k -> new ArrayList<>());
            for (DataTableDTO dt : vo.getMainTable()) {
                if (isClassRoom) {
                    //只筛选教室
                    if (dt.getClassOrRoomId().endsWith("ROOM")) {
                        ClassesVO classes = new ClassesVO();
                        classes.setClassId(Long.parseLong(dt.getClassOrRoomId().substring(0, dt.getClassOrRoomId().length() - 4)));
                        classes.setClassName(dt.getClassOrRoomName());
                        classes.setRoomId(classes.getClassId());
                        if (classesList.stream().noneMatch(w -> w.getClassId().equals(classes.getClassId()))) {
                            classesList.add(classes);
                        }
                    }
                } else {
                    //只筛选班级
                    if (!dt.getClassOrRoomId().endsWith("ROOM")) {
                        ClassesVO classes = new ClassesVO();
                        classes.setClassId(Long.parseLong(dt.getClassOrRoomId()));
                        classes.setClassName(dt.getClassOrRoomName());
                        classes.setRoomId(vo.getClassRoomMap().get(classes.getClassId()));
                        if (classesList.stream().noneMatch(w -> w.getClassId().equals(classes.getClassId()))) {
                            classesList.add(classes);
                        }
                    }
                }
            }
        }
        return allMap;
    }

    @Override
    public List<RoomClassVO> getAllRoomClass(Long curriculumTableId) {
        Map<WeekTypeEnum, List<ClassesVO>> allClassRoom = allClassList(Collections.singletonList(curriculumTableId), true);
        Map<Long, ClassesVO> allClassesMap = new LinkedHashMap<>();
        for (WeekTypeEnum wt : allClassRoom.keySet()) {
            List<ClassesVO> classes = allClassRoom.get(wt);
            for (ClassesVO v : classes) {
                if (!allClassesMap.containsKey(v.getClassId())) {
                    allClassesMap.put(v.getClassId(), v);
                }
            }
        }
        allClassRoom = allClassList(Collections.singletonList(curriculumTableId), false);
        List<Long> classIds = new ArrayList<>();
        for (WeekTypeEnum wt : allClassRoom.keySet()) {
            List<ClassesVO> classes = allClassRoom.get(wt);
            for (ClassesVO classesVO : classes) {
                if (!allClassesMap.containsKey(classesVO.getClassId())) {
                    classIds.add(classesVO.getClassId());
                }
            }
        }
        List<ClassDTO> classDTOList = foundationFacade.getAllClass(StringUtils.listToString(classIds));
        for (ClassDTO classDTO : classDTOList) {
            if (classDTO.getRoomId() != null) {
                ClassesVO classesVO = new ClassesVO();
                classesVO.setClassId(classDTO.getId());
                classesVO.setClassName(classDTO.getName());
                classesVO.setRoomId(classDTO.getRoomId());
                allClassesMap.put(classDTO.getRoomId(), classesVO);
            }
        }

        List<RoomClassVO> roomClass = settingFacade.getRoomsTree();
        List<RoomClassVO> resultRoomClass = new ArrayList<>();
        for (RoomClassVO roomClassVO : roomClass) {
            List<RoomClassFloorTreeVO> trees = new ArrayList<>();
            for (RoomClassFloorTreeVO floorTree : roomClassVO.getFloorTreeDTOList()) {
                List<RoomClassTreeDTO> roomList = new ArrayList<>();
                for (RoomClassTreeDTO room : floorTree.getRoomClassTreeDTOList()) {
                    if (room.getRoomId() != null) {
                        ClassesVO classesVO = allClassesMap.get(room.getRoomId());
                        if (classesVO != null) {
                            if (classIds.contains(classesVO.getClassId())) {
                                room.setClassId(classesVO.getClassId());
                            }
                            roomList.add(room);
                        }
                    }
                }
                floorTree.setRoomClassTreeDTOList(roomList);
                if (!roomList.isEmpty()) {
                    trees.add(floorTree);
                }
            }
            roomClassVO.setFloorTreeDTOList(trees);
            if (!trees.isEmpty()) {
                resultRoomClass.add(roomClassVO);
            }
        }
        return resultRoomClass;
    }

    @Override
    public Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> classTableWeekDetail(Long classId, Integer weekNumber) {
        List<ClassDTO> classList = foundationFacade.getAllClass(String.valueOf(classId));
        if (CollectionUtils.isEmpty(classList)) {
            return new HashMap<>();
        }
        ClassDTO classes = classList.get(0);
        SemesterVO semester = settingFacade.currentSemester();
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> sourceWeekTableMap = new HashMap<>(5);
        ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semester.getId()).classId(classId).build();
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = getElectiveTeacherWeekTableMap(electiveQuery);
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap = new HashMap<>(5);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultTeacherWeekTableMap = new HashMap<>(5);
        CurriculumTableDO curriculumTable = curriculumTableService.getTable(classes.getSectionId(), classes.getEnrollmentYear(), semester.getId(), semester.getOrderNo());
        if (curriculumTable != null) {
            List<GeneralTableVO> classesTable = classesTable(curriculumTable.getId());
            Map<WeekTypeEnum, DataTableDTO> table = new LinkedHashMap<>();
            for (GeneralTableVO generalTableVO : classesTable) {
                for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                    if (dataTableDTO.getClassOrRoomId().equals(String.valueOf(classId))) {
                        table.put(generalTableVO.getType(), dataTableDTO);
                    }
                }
            }
            boolean isSingle = weekNumber % 2 == 1;
            if (curriculumTable.getSingleWeek()) {
                isSingle = true;
            }
            DataTableDTO dataTable = table.get(isSingle ? WeekTypeEnum.SINGLE : WeekTypeEnum.DOUBLE);
            if (Objects.nonNull(dataTable)) {
                for (WeekEnum week : dataTable.getDetailv().keySet()) {
                    Map<String, List<TeacherAbbDetailVO>> sectionCloneMap = new HashMap<>(5);
                    Map<String, List<TeacherAbbDetailVO>> sectionMap = dataTable.getDetailv().get(week);
                    for (String section : sectionMap.keySet()) {
                        List<TeacherAbbDetailVO> courseInfoCopyList = new ArrayList<>();
                        List<TeacherAbbDetailVO> courseInfoList = sectionMap.get(section);
                        for (TeacherAbbDetailVO abb : courseInfoList) {
                            if (abb.getClassOrRoomId().equals(String.valueOf(classId))) {
                                courseInfoCopyList.add(IoUtil.clone(abb));
                            }
                        }
                        sectionCloneMap.put(section, courseInfoCopyList);
                    }
                    resultTeacherWeekTableMap.put(week, sectionCloneMap);
                }
            }
            courseAdjustmentWeekTableMap = courseAdjustmentService.getRecordsByTeacherIdAndTimeScope(classId, weekNumber, curriculumTable.getId());
            initSourceWeekTable(curriculumTable.getId(), resultTeacherWeekTableMap, electiveTeacherWeekTableMap, courseAdjustmentWeekTableMap);
            sourceWeekTableMap = TableCopyConvert.INSTANCE.copy(resultTeacherWeekTableMap);
        }
        syncElectiveTableMap(Collections.emptyList(), electiveTeacherWeekTableMap, resultTeacherWeekTableMap, sourceWeekTableMap);
        return syncWeekTableWithHoliday(semester.getId(), weekNumber, ImmutableMap.copyOf(sourceWeekTableMap), resultTeacherWeekTableMap, courseAdjustmentWeekTableMap, electiveTeacherWeekTableMap);
    }

    @Override
    public Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> classTableWeekDetailBatch(Long classId, Integer weekNumber) {
        List<ClassDTO> classList = foundationFacade.getAllClass(String.valueOf(classId));
        ClassDTO classes = classList.get(0);
        SemesterVO semester = settingFacade.currentSemester();
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> sourceWeekTableMap = new HashMap<>(5);
        ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semester.getId()).classId(classId).build();
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = getElectiveTeacherWeekTableMap(electiveQuery);
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap = new HashMap<>(5);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultTeacherWeekTableMap = new HashMap<>(5);
        CurriculumTableDO curriculumTable = curriculumTableService.getTable(classes.getSectionId(), classes.getEnrollmentYear(), semester.getId(), semester.getOrderNo());
        if (curriculumTable != null) {
            List<GeneralTableVO> classesTable = classesTable(curriculumTable.getId());
            Map<WeekTypeEnum, DataTableDTO> table = new LinkedHashMap<>();
            for (GeneralTableVO generalTableVO : classesTable) {
                for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                    if (dataTableDTO.getClassOrRoomId().equals(String.valueOf(classId))) {
                        table.put(generalTableVO.getType(), dataTableDTO);
                    }
                }
            }
            boolean isSingle = weekNumber % 2 == 1;
            if (curriculumTable.getSingleWeek()) {
                isSingle = true;
            }
            DataTableDTO dataTable = table.get(isSingle ? WeekTypeEnum.SINGLE : WeekTypeEnum.DOUBLE);
            for (WeekEnum week : dataTable.getDetailv().keySet()) {
                Map<String, List<TeacherAbbDetailVO>> sectionCloneMap = new HashMap<>(5);
                Map<String, List<TeacherAbbDetailVO>> sectionMap = dataTable.getDetailv().get(week);
                for (String section : sectionMap.keySet()) {
                    List<TeacherAbbDetailVO> courseInfoCopyList = new ArrayList<>();
                    List<TeacherAbbDetailVO> courseInfoList = sectionMap.get(section);
                    for (TeacherAbbDetailVO abb : courseInfoList) {
                        courseInfoCopyList.add(IoUtil.clone(abb));
                    }
                    sectionCloneMap.put(section, courseInfoCopyList);
                }
                resultTeacherWeekTableMap.put(week, sectionCloneMap);
            }
            courseAdjustmentWeekTableMap = courseAdjustmentService.getRecordsByTeacherIdAndTimeScope(classId, weekNumber, curriculumTable.getId());
            initSourceWeekTable(curriculumTable.getId(), resultTeacherWeekTableMap, electiveTeacherWeekTableMap, courseAdjustmentWeekTableMap);
            sourceWeekTableMap = TableCopyConvert.INSTANCE.copy(resultTeacherWeekTableMap);
        }
        syncElectiveTableMap(Collections.emptyList(), electiveTeacherWeekTableMap, resultTeacherWeekTableMap, sourceWeekTableMap);
        return syncWeekTableWithHoliday(semester.getId(), weekNumber, ImmutableMap.copyOf(sourceWeekTableMap), resultTeacherWeekTableMap, courseAdjustmentWeekTableMap, electiveTeacherWeekTableMap);
    }

    @Override
    public Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> classRoomTableWeekDetail(Long classRoomId, Integer weekNumber, Boolean cache) {
        String roomId = classRoomId + "ROOM";
        SemesterVO semester = settingFacade.currentSemester();
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> result = new HashMap<>(5);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSource = new HashMap<>(5);
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap = new HashMap<>(5);
        ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semester.getId()).roomId(classRoomId).build();
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = getElectiveTeacherWeekTableMap(electiveQuery);
        List<CurriculumTableDO> curriculumTables = curriculumTableService.getTable(semester.getId(), semester.getOrderNo());
        if (!CollectionUtils.isEmpty(curriculumTables)) {
            List<Long> curriculumTableIds = new ArrayList<>();
            for (CurriculumTableDO curriculumTable : curriculumTables) {
                curriculumTableIds.add(curriculumTable.getId());
            }
            courseAdjustmentWeekTableMap = courseAdjustmentService.getRecordsByTeacherIdAndTimeScope(classRoomId, weekNumber, curriculumTableIds);
            for (CurriculumTableDO curriculumTable : curriculumTables) {
                List<GeneralTableVO> generalTableVOList = mainTable(curriculumTable.getId());
                Map<WeekTypeEnum, DataTableDTO> table = new HashMap<>(10);
                for (GeneralTableVO generalTableVO : generalTableVOList) {
                    for (DataTableDTO dataTableDTO : generalTableVO.getData()) {
                        if (dataTableDTO.getClassOrRoomId().equals(roomId)) {
                            table.put(generalTableVO.getType(), dataTableDTO);
                        }
                    }
                }
                boolean isSingle = weekNumber % 2 == 1;
                if (curriculumTable.getSingleWeek()) {
                    isSingle = true;
                }
                DataTableDTO classRoomTable = table.get(isSingle ? WeekTypeEnum.SINGLE : WeekTypeEnum.DOUBLE);
                if (classRoomTable == null) {
                    continue;
                }
                initSourceWeekTable(curriculumTable.getId(), classRoomTable.getDetailv(), electiveTeacherWeekTableMap, courseAdjustmentWeekTableMap);
                result = TableCopyConvert.INSTANCE.copy(classRoomTable.getDetailv());
                detailSource = TableCopyConvert.INSTANCE.copy(classRoomTable.getDetailv());
            }
        }
        syncElectiveTableMap(Collections.emptyList(), electiveTeacherWeekTableMap, result, detailSource);
        return syncWeekTableWithHoliday(semester.getId(), weekNumber, ImmutableMap.copyOf(detailSource), result, courseAdjustmentWeekTableMap, electiveTeacherWeekTableMap);
    }


    @Override
    public String teacherTableSemesterDetailAbbs(Long semesterId, Long teacherId) {
        Map<WeekTypeEnum, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> map = teacherTableSemesterDetail(semesterId, teacherId);
        if (map != null) {
            for (WeekTypeEnum wt : map.keySet()) {
                Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> wtl = map.get(wt);
                for (WeekEnum week : wtl.keySet()) {
                    Map<String, List<TeacherAbbDetailVO>> wtlsection = wtl.get(week);
                    for (String section : wtlsection.keySet()) {
                        List<TeacherAbbDetailVO> abbs = wtlsection.get(section);
                        for (TeacherAbbDetailVO v : abbs) {
                            return v.getCourseName();
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Map<WeekTypeEnum, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> teacherTableSemesterDetail(Long semesterId, Long teacherId) {
        WeekConfigVO week = settingFacade.currentWeek();
        Map<WeekTypeEnum, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> list = new HashMap<>(5);
        if (Objects.nonNull(week)) {
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> abbDetail = teacherTableWeekDetail(semesterId, teacherId, week.getNumber());
            int n = 2;
            if (week.getNumber() % n == 1) {
                list.put(WeekTypeEnum.SINGLE, abbDetail);
            } else {
                list.put(WeekTypeEnum.DOUBLE, abbDetail);
            }
        }
        return list;
    }

    @Override
    public Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> teacherTableWeekDetail(Long teacherId, Integer weekNumber) {
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherMap = this.teacherTableWeekDetailBatch(Collections.singletonList(teacherId), Collections.singletonList(weekNumber));
        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekMap = teacherMap.get(teacherId);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = new HashMap<>(6);
        if (!CollectionUtils.isEmpty(weekMap)) {
            weekEnumMapMap = weekMap.get(weekNumber);
        }
        return weekEnumMapMap;
    }

    @Override
    public Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetailBatch(List<Long> teacherIds, List<Integer> weekNumbers) {
        SemesterVO semester = settingFacade.currentSemester();
        return teacherTableWeekDetailBatch(semester.getId(), teacherIds, weekNumbers);
    }

    @Override
    public Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> teacherTableWeekDetail(Long semesterId, Long teacherId, Integer weekNumber) {
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherMap = this.teacherTableWeekDetailBatch(semesterId, Collections.singletonList(teacherId), Collections.singletonList(weekNumber));
        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekMap = teacherMap.get(teacherId);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> enumMapMap = new HashMap<>(6);
        if (!CollectionUtils.isEmpty(weekMap)) {
            enumMapMap = weekMap.get(weekNumber);
        }
        return enumMapMap;
    }

    @Override
    public Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetailBatch(Long semesterId, List<Long> teacherIds, List<Integer> weekNumbers) {
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> resultWeekTableMap = new HashMap<>(5);
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> sourceWeekTableMap = new HashMap<>(5);
        List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumList(semesterId, null, teacherIds);
        if (CollectionUtils.isEmpty(curriculumTableIds)) {
            return Collections.emptyMap();
        }
        //获取作息时间
        List<RestSectionVO> restAllBySection = settingFacade.getRestAllBySection();
        Map<String, RestSectionVO> restSectionMap = restAllBySection.stream().collect(Collectors.toMap(restSectionVo -> restSectionVo.getSectionType() + "-" + restSectionVo.getEnrollmentYear(), restSectionVo -> restSectionVo));
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = getElectiveTeacherWeekTableMapBatch(restSectionMap,semesterId, teacherIds);
        Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> courseAdjustmentWeekTableMap = getCourseAdjustmentWeekTableMapBatch(teacherIds, weekNumbers, curriculumTableIds);
        List<CurriculumTableDO> curriculumTableDos = curriculumTableService.queryForByIds(curriculumTableIds);
        Map<Long, CurriculumTableDO> curriculumTableMap = curriculumTableDos.stream().collect(Collectors.toMap(CurriculumTableDO::getId, curriculumTableDo -> curriculumTableDo));

        TeacherWeekQuery teacherWeekQuery = new TeacherWeekQuery();
        teacherWeekQuery.setNumbers(weekNumbers);
        teacherWeekQuery.setTeacherIds(teacherIds);
        Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> tableWeekDetailBatch = teacherTableWeekDetailBatch(curriculumTableIds, restSectionMap, teacherWeekQuery, electiveTeacherWeekTableMap, courseAdjustmentWeekTableMap, curriculumTableMap);
        if (tableWeekDetailBatch != null) {
            resultWeekTableMap = TableCopyConvert.INSTANCE.weekCopy(tableWeekDetailBatch);
            sourceWeekTableMap = TableCopyConvert.INSTANCE.weekCopy(tableWeekDetailBatch);
        }

        syncElectiveTableMapBatch(Collections.emptyList(), electiveTeacherWeekTableMap, resultWeekTableMap, sourceWeekTableMap);
        syncWeekTableWithHolidayBatch(semesterId, weekNumbers, ImmutableMap.copyOf(sourceWeekTableMap), resultWeekTableMap, courseAdjustmentWeekTableMap, electiveTeacherWeekTableMap);
        return resultWeekTableMap;
    }

    /**
     * 获取调代课课表信息
     *
     * @param teacherId
     * @param weekNumber
     * @param curriculumTableIds
     * @return
     */
    private Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getCourseAdjustmentWeekTableMap(Long teacherId, Integer weekNumber, List<Long> curriculumTableIds) {
        return courseAdjustmentService.getRecordsByTeacherIdAndTimeScope(Collections.singletonList(teacherId), weekNumber, curriculumTableIds);
    }

    /**
     * 获取调代课课表信息
     * @param teacherIds
     * @param weekNumbers
     * @param curriculumTableIds
     * @return
     */
    private Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> getCourseAdjustmentWeekTableMapBatch(List<Long> teacherIds, List<Integer> weekNumbers, List<Long> curriculumTableIds) {
        return courseAdjustmentService.getRecordsByTeacherIdAndTimeScopeBatch(teacherIds, weekNumbers, curriculumTableIds);
    }




    /**
     * 获取选修课课表信息
     *
     * @param semesterId
     * @param teacherId
     * @return
     */
    private Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getElectiveTeacherWeekTableMap(Long semesterId, Long teacherId) {
        ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semesterId).teacherId(teacherId).build();
        return getElectiveTeacherWeekTableMap(electiveQuery);
    }

    /**
     * 获取选修课课表信息
     *
     * @param semesterId
     * @param teacherIds
     * @return
     */
    private Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getElectiveTeacherWeekTableMapBatch(Map<String, RestSectionVO> restSectionMap,Long semesterId, List<Long> teacherIds) {
        ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semesterId).teacherIds(teacherIds).build();
        return getElectiveTeacherWeekTableMapBatch(restSectionMap,electiveQuery);
    }



    /**
     * 获取最终教师周课表
     * @param curriculumTableIds
     * @param restSectionMap
     * @param teacherWeekQuery
     * @param electiveTeacherWeekTableMap
     * @param courseAdjustmentTeacherWeekTableMap
     * @param curriculumTableMap
     * @return
     */
    public Map<Long,Map<Integer,Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetailBatch(List<Long> curriculumTableIds, Map<String, RestSectionVO> restSectionMap,TeacherWeekQuery teacherWeekQuery,
                                                                                       Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap,
                                                                                                      Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> courseAdjustmentTeacherWeekTableMap,
                                                                                                      Map<Long, CurriculumTableDO> curriculumTableMap) {
        Map<Long,Map<Integer,Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> weekTypeMap = new HashMap<>(6);
        Map<WeekTypeEnum, List<TeacherTableDTO>> teacherTableDtoMap = teacherTableDetailBatch(curriculumTableMap,curriculumTableIds,restSectionMap,teacherWeekQuery);

        for (Integer number : teacherWeekQuery.getNumbers()) {
            Map<Long,Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> teacherMap = new HashMap<>(6);
            boolean isSingle = number % 2 == 1;
            for (Long curriculumTableId : curriculumTableIds) {
                CurriculumTableDO curriculumTableDO = curriculumTableMap.get(curriculumTableId);
                if (curriculumTableDO.getSingleWeek()) {
                    isSingle = true;
                }
                List<TeacherTableDTO> teacherTables = teacherTableDtoMap.get(isSingle ? WeekTypeEnum.SINGLE : WeekTypeEnum.DOUBLE);
                if (!CollectionUtils.isEmpty(teacherTables)) {
                    for (TeacherTableDTO teacherTable : teacherTables) {
                        initSourceWeekTableBatch(number,restSectionMap,curriculumTableDO,curriculumTableId, teacherTable.getDetailv(), electiveTeacherWeekTableMap, courseAdjustmentTeacherWeekTableMap);
                        teacherMap.put(teacherTable.getTeacherId(),teacherTable.getDetailv());
                    }
                }
            }
            teacherMap.forEach((k,v) ->{
                Map<Integer,Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekMap = new HashMap<>(6);
                weekMap.put(number,v);
                Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> teachersMap = weekTypeMap.get(k);
                if (CollectionUtils.isEmpty(teachersMap)) {
                    weekTypeMap.put(k,weekMap);
                }else {
                    teachersMap.put(number,v);
                }
            });
        }
        return weekTypeMap;
    }

    @Override
    public Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> studentTableWeekDetail(Long stuId, Integer weekNumber) {
        SemesterVO semester = settingFacade.currentSemester();
        List<Long> curriculumTableIds = curriculumTableService.getCurrentCurriculumIds(semester.getId(), stuId, null);
        Long curriculumTableId = curriculumTableIds == null ? null : curriculumTableIds.get(0);
        StudentWeekQuery studentWeekQuery = new StudentWeekQuery();
        studentWeekQuery.setCurriculumTableId(curriculumTableId);
        studentWeekQuery.setNumber(weekNumber);
        studentWeekQuery.setStuId(stuId);

        boolean isSingle = studentWeekQuery.getNumber() % 2 == 1;
        List<Long> techIds = new ArrayList<>();
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTable = new HashMap<>(5);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailv = new HashMap<>(5);
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = new HashMap<>(5);
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> detailSource = new HashMap<>(5);
        if (studentWeekQuery.getCurriculumTableId() != null) {
            Map<WeekTypeEnum, DataTableDTO> table = studentTableDetail(studentWeekQuery);
            CurriculumTableDO ctd = curriculumTableService.load(studentWeekQuery.getCurriculumTableId());
            if (ctd.getSingleWeek()) {
                isSingle = true;
            }
            DataTableDTO dataTable = table.get(isSingle ? WeekTypeEnum.SINGLE : WeekTypeEnum.DOUBLE);
            detailv = TableCopyConvert.INSTANCE.copy(dataTable.getDetailv());
            Set<Long> teacherIds = new HashSet<>();
            for (WeekEnum week : dataTable.getDetailv().keySet()) {
                Map<String, List<TeacherAbbDetailVO>> sectionMap = dataTable.getDetailv().get(week);
                for (String section : sectionMap.keySet()) {
                    List<TeacherAbbDetailVO> teacherAbbList = sectionMap.get(section);
                    for (TeacherAbbDetailVO v : teacherAbbList) {
                        teacherIds.add(v.getTeacherId());
                    }
                }
            }
            techIds.addAll(teacherIds);
            courseAdjustmentWeekTable = courseAdjustmentService.getRecordsByTeacherIdAndTimeScope(studentWeekQuery.getStuId(), Long.parseLong(dataTable.getClassOrRoomId()), techIds, studentWeekQuery.getNumber(), studentWeekQuery.getCurriculumTableId());
            ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semester.getId()).studentId(studentWeekQuery.getStuId()).sectionType(ctd.getSectionId()).enrollmentYear(ctd.getEnrollmentYear()).build();
            electiveTeacherWeekTableMap.putAll(getElectiveTeacherWeekTableMap(electiveQuery));
            initSourceWeekTable(studentWeekQuery.getCurriculumTableId(), detailv, electiveTeacherWeekTableMap, courseAdjustmentWeekTable);
            detailSource = TableCopyConvert.INSTANCE.copy(detailv);
        } else {
            ElectiveQuery electiveQuery = ElectiveQuery.builder().semesterId(semester.getId()).studentId(studentWeekQuery.getStuId()).build();
            electiveTeacherWeekTableMap.putAll(getElectiveTeacherWeekTableMap(electiveQuery));
        }
        syncElectiveTableMap(Collections.emptyList(), electiveTeacherWeekTableMap, detailv, detailSource);
        return syncWeekTableWithHoliday(semester.getId(), studentWeekQuery.getNumber(), ImmutableMap.copyOf(detailSource), detailv, courseAdjustmentWeekTable, electiveTeacherWeekTableMap);
    }


    /**
     * 多来源课表合并
     *
     * @param curriculumTableId            　课表id
     * @param electiveTeacherWeekTableMap  　选修课表
     * @param courseAdjustmentWeekTableMap 　调代课课表
     */
    private void initSourceWeekTable(Long curriculumTableId,
                                     Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekMap,
                                     Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap,
                                     Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap) {
        CurriculumTableDO table = curriculumTableService.load(curriculumTableId);
        Map<String, RestSectionDetailVO> restSectionDetailMap = settingFacade.getRestAll(table.getSectionId(), table.getEnrollmentYear());
        addElectiveCourses(curriculumTableId, resultWeekMap, electiveTeacherWeekTableMap, restSectionDetailMap);
        addSectionTimeToCourseAdjustmentWeekTable(courseAdjustmentWeekTableMap, restSectionDetailMap);

    }

    /**
     *多来源课表合并
     * @param table
     * @param curriculumTableId
     * @param resultWeekMap
     * @param electiveTeacherWeekTableMap
     * @param courseAdjustmentWeekTableMap
     */
    private void initSourceWeekTableBatch(Integer num,Map<String, RestSectionVO> restSectionMap,CurriculumTableDO table,Long curriculumTableId,
                                     Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekMap,
                                     Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap,
                                          Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> courseAdjustmentWeekTableMap) {
        Map<String, RestSectionDetailVO> restSectionDetailMap = restSectionDetailMap(restSectionMap,table.getSectionId(), table.getEnrollmentYear());
        addElectiveCourses(curriculumTableId, resultWeekMap, electiveTeacherWeekTableMap, restSectionDetailMap);
        addSectionTimeToCourseAdjustmentWeekTableBatch(num,courseAdjustmentWeekTableMap, restSectionDetailMap);
    }

    /**
     * 添加节次时间到调代课课表
     *
     * @param courseAdjustmentWeekTableMap
     * @param restSectionDetailMap
     */
    private void addSectionTimeToCourseAdjustmentWeekTable(Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap, Map<String, RestSectionDetailVO> restSectionDetailMap) {
        if (!CollectionUtils.isEmpty(courseAdjustmentWeekTableMap)) {
            for (Long teacherId : courseAdjustmentWeekTableMap.keySet()) {
                Map<WeekEnum, Map<String, TeacherAbbDetailVO>> teacherWeekTableMap = courseAdjustmentWeekTableMap.get(teacherId);
                for (WeekEnum week : teacherWeekTableMap.keySet()) {
                    Map<String, TeacherAbbDetailVO> sectionMap = teacherWeekTableMap.get(week);
                    for (String section : sectionMap.keySet()) {
                        RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                        TeacherAbbDetailVO teacherAbbDetailVO = sectionMap.get(section);
                        if (restSectionDetailVO != null) {
                            teacherAbbDetailVO.setStartTime(restSectionDetailVO.getStartTime());
                            teacherAbbDetailVO.setEndTime(restSectionDetailVO.getEndTime());
                        }
                    }
                }
            }
        }
    }

    private void addSectionTimeToCourseAdjustmentWeekTableBatch(Integer num,Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> courseAdjustmentWeekTableMap, Map<String, RestSectionDetailVO> restSectionDetailMap) {
        if (!CollectionUtils.isEmpty(courseAdjustmentWeekTableMap)) {
            for (Long teacherId : courseAdjustmentWeekTableMap.keySet()) {
                Map<Integer, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> integerMapMap = courseAdjustmentWeekTableMap.get(teacherId);
                if (!CollectionUtils.isEmpty(integerMapMap)) {
                    Map<WeekEnum, Map<String, TeacherAbbDetailVO>> teacherWeekTableMap = integerMapMap.get(num);
                    if (!CollectionUtils.isEmpty(teacherWeekTableMap)) {
                        for (WeekEnum week : teacherWeekTableMap.keySet()) {
                            Map<String, TeacherAbbDetailVO> sectionMap = teacherWeekTableMap.get(week);
                            for (String section : sectionMap.keySet()) {
                                RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                                TeacherAbbDetailVO teacherAbbDetailVO = sectionMap.get(section);
                                if (restSectionDetailVO != null) {
                                    teacherAbbDetailVO.setStartTime(restSectionDetailVO.getStartTime());
                                    teacherAbbDetailVO.setEndTime(restSectionDetailVO.getEndTime());
                                }
                            }
                        }
                    }
                }

            }
        }
    }

    /**
     * 添加选修课程
     *
     * @param curriculumTableId
     * @param resultWeekTableMap
     * @param electiveTeacherWeekTableMap
     * @param restSectionDetailMap
     */
    private void addElectiveCourses(Long curriculumTableId, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekTableMap, Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap, Map<String, RestSectionDetailVO> restSectionDetailMap) {
        for (WeekEnum dayOfWeek : resultWeekTableMap.keySet()) {
            Map<String, List<TeacherAbbDetailVO>> sectionMap = resultWeekTableMap.get(dayOfWeek);
            for (String section : sectionMap.keySet()) {
                RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                List<TeacherAbbDetailVO> list = sectionMap.get(section);
                for (TeacherAbbDetailVO teacherAbbDetailVO : list) {
                    teacherAbbDetailVO.setCurriculumTableId(curriculumTableId);
                    if (restSectionDetailVO != null) {
                        teacherAbbDetailVO.setStartTime(restSectionDetailVO.getStartTime());
                        teacherAbbDetailVO.setEndTime(restSectionDetailVO.getEndTime());
                    }
                    if (RuleUtils.ignoreCourse(teacherAbbDetailVO.getAbbreviation())) {
                        if (electiveTeacherWeekTableMap.containsKey(teacherAbbDetailVO.getTeacherId())) {
                            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> electiveWeekMap = electiveTeacherWeekTableMap.get(teacherAbbDetailVO.getTeacherId());
                            if (electiveWeekMap.containsKey(dayOfWeek)) {
                                Map<String, TeacherAbbDetailVO> electiveSectionMap = electiveWeekMap.get(dayOfWeek);
                                if (electiveSectionMap.containsKey(section)) {
                                    TeacherAbbDetailVO courseInfo = electiveSectionMap.get(section);
                                    sectionMap.put(section, Collections.singletonList(courseInfo));
                                    resultWeekTableMap.put(dayOfWeek, sectionMap);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 获取选修课老师周课表
     *
     * @param electiveQuery@return
     */
    private Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getElectiveTeacherWeekTableMap(ElectiveQuery electiveQuery) {
        Map<String, RestSectionDetailVO> restSectionDetailMap = getStringRestSectionDetailVoMap(electiveQuery.getSectionType(), electiveQuery.getEnrollmentYear());
        List<CourseVO> courseVOList = getCourseVOList(electiveQuery);
        Map<String, String> curriculumAliasMap = curriculumService.aliasMap(true);
        Map<Long, List<CourseVO>> courseMap = courseVOList.stream().collect(Collectors.groupingBy(CourseVO::getTeacherId));
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = new HashMap<>(5);
        getTeacherWeekTableFromCourseMap(electiveQuery.getClassId(), restSectionDetailMap, curriculumAliasMap, courseMap, electiveTeacherWeekTableMap);
        return electiveTeacherWeekTableMap;
    }

    /**
     * 获取选修课老师周课表
     * @param electiveQuery
     * @return
     */
    private Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> getElectiveTeacherWeekTableMapBatch(Map<String, RestSectionVO> restSectionMap,ElectiveQuery electiveQuery) {
        Map<String, RestSectionDetailVO> restSectionDetailMap = restSectionDetailMap(restSectionMap,electiveQuery.getSectionType(), electiveQuery.getEnrollmentYear());
        List<CourseVO> courseVOList = getCourseVoListBatch(electiveQuery);
        Map<String, String> curriculumAliasMap = curriculumService.aliasMap(true);
        Map<Long, List<CourseVO>> courseMap = courseVOList.stream().collect(Collectors.groupingBy(CourseVO::getTeacherId));
        Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap = new HashMap<>(5);
        getTeacherWeekTableFromCourseMapBatch(restSectionMap,electiveQuery.getClassId(), restSectionDetailMap, curriculumAliasMap, courseMap, electiveTeacherWeekTableMap);
        return electiveTeacherWeekTableMap;
    }


    /**
     * 获取教师周课表以选修课为基础
     *
     * @param classId
     * @param restSectionDetailMap
     * @param curriculumAliasMap
     * @param courseMap
     * @param electiveTeacherWeekTableMap
     */
    private void getTeacherWeekTableFromCourseMap(Long classId, Map<String, RestSectionDetailVO> restSectionDetailMap, Map<String, String> curriculumAliasMap, Map<Long, List<CourseVO>> courseMap, Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap) {
        for (Long teacherId : courseMap.keySet()) {
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> tableMap = electiveTeacherWeekTableMap.computeIfAbsent(teacherId, k -> new HashMap<>(5));
            List<CourseVO> courses = courseMap.get(teacherId);
            for (CourseVO v : courses) {
                if (!StringUtils.isEmpty(v.getRestDetailString())) {
                    if (restSectionDetailMap == null) {
                        if (v.getSectionByCourseCodeList() != null &&
                                v.getSectionByCourseCodeList().size() == 1) {
                            SectionByCourseCodeListVO t = v.getSectionByCourseCodeList().get(0);
                            if (t.getEnrollmentYears().size() == 1) {
                                Long st = t.getSectionType();
                                Integer ey = t.getEnrollmentYears().get(0);
                                restSectionDetailMap = settingFacade.getRestAll(st, ey);
                            }
                        }
                    }
                    JsonArray jsonArray = new JsonArray(v.getRestDetailString());
                    Integer weekIndex = jsonArray.getInt(0);
                    int sectionT = jsonArray.getInt(1);
                    if (sectionT == 2) {
                        continue;
                    }
                    String sectionN = jsonArray.getString(2);
                    WeekEnum week = WeekEnum.getWeekMap().get(weekIndex);
                    Map<String, TeacherAbbDetailVO> detailMap = tableMap.computeIfAbsent(week, k -> new HashMap<>(5));
                    String section = RuleUtils.getSection(sectionT, sectionN);
                    TeacherAbbDetailVO tabbDetail = new TeacherAbbDetailVO();
                    if (restSectionDetailMap != null) {
                        RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                        if (restSectionDetailVO != null) {
                            tabbDetail.setStartTime(restSectionDetailVO.getStartTime());
                            tabbDetail.setEndTime(restSectionDetailVO.getEndTime());
                        }
                    }
                    tabbDetail.setType(AbbTypeEnum.ELECTIVE);
                    tabbDetail.setTeacherId(v.getTeacherId());
                    tabbDetail.setTeacherName(v.getTeacherName());
                    if (classId != null) {
                        tabbDetail.setClassOrRoomId(v.getClassId() + "");
                        tabbDetail.setClassOrRoomName(v.getClassName());
                    } else {
                        tabbDetail.setClassOrRoomId(v.getRoomId() + "");
                        tabbDetail.setClassOrRoomName(v.getRoomName());
                    }
                    if (curriculumAliasMap.containsKey(v.getCourseName())) {
                        tabbDetail.setAbbreviation(curriculumAliasMap.get(v.getCourseName()));
                    } else {
                        if (v.getCourseName().length() > 2) {
                            tabbDetail.setAbbreviation(v.getCourseName().substring(0, 2) + "...");
                        } else {
                            tabbDetail.setAbbreviation(v.getCourseName());
                        }
                    }
                    tabbDetail.setCourseCode(v.getCourseCode());
                    tabbDetail.setCourseName(v.getCourseName());
                    detailMap.put(section, tabbDetail);
                }
            }
        }
    }


    private void getTeacherWeekTableFromCourseMapBatch(Map<String, RestSectionVO> restSectionMap,Long classId, Map<String, RestSectionDetailVO> restSectionDetailMap, Map<String, String> curriculumAliasMap, Map<Long, List<CourseVO>> courseMap, Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap) {
        for (Long teacherId : courseMap.keySet()) {
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> tableMap = electiveTeacherWeekTableMap.computeIfAbsent(teacherId, k -> new HashMap<>(5));
            List<CourseVO> courses = courseMap.get(teacherId);
            for (CourseVO v : courses) {
                if (!StringUtils.isEmpty(v.getRestDetailString())) {
                    if (restSectionDetailMap == null) {
                        if (v.getSectionByCourseCodeList() != null &&
                                v.getSectionByCourseCodeList().size() == 1) {
                            SectionByCourseCodeListVO t = v.getSectionByCourseCodeList().get(0);
                            if (t.getEnrollmentYears().size() == 1) {
                                Long st = t.getSectionType();
                                Integer ey = t.getEnrollmentYears().get(0);
                                restSectionDetailMap = restSectionDetailMap(restSectionMap,st, ey);
                            }
                        }
                    }
                    JsonArray jsonArray = new JsonArray(v.getRestDetailString());
                    Integer weekIndex = jsonArray.getInt(0);
                    int sectionT = jsonArray.getInt(1);
                    if (sectionT == 2) {
                        continue;
                    }
                    String sectionN = jsonArray.getString(2);
                    WeekEnum week = WeekEnum.getWeekMap().get(weekIndex);
                    Map<String, TeacherAbbDetailVO> detailMap = tableMap.computeIfAbsent(week, k -> new HashMap<>(5));
                    String section = RuleUtils.getSection(sectionT, sectionN);
                    TeacherAbbDetailVO tabbDetail = new TeacherAbbDetailVO();
                    if (restSectionDetailMap != null) {
                        RestSectionDetailVO restSectionDetailVO = restSectionDetailMap.get(RuleUtils.getSectionIndexKey(section));
                        if (restSectionDetailVO != null) {
                            tabbDetail.setStartTime(restSectionDetailVO.getStartTime());
                            tabbDetail.setEndTime(restSectionDetailVO.getEndTime());
                        }
                    }
                    tabbDetail.setType(AbbTypeEnum.ELECTIVE);
                    tabbDetail.setTeacherId(v.getTeacherId());
                    tabbDetail.setTeacherName(v.getTeacherName());
                    if (classId != null) {
                        tabbDetail.setClassOrRoomId(v.getClassId() + "");
                        tabbDetail.setClassOrRoomName(v.getClassName());
                    } else {
                        tabbDetail.setClassOrRoomId(v.getRoomId() + "");
                        tabbDetail.setClassOrRoomName(v.getRoomName());
                    }
                    if (curriculumAliasMap.containsKey(v.getCourseName())) {
                        tabbDetail.setAbbreviation(curriculumAliasMap.get(v.getCourseName()));
                    } else {
                        if (v.getCourseName().length() > 2) {
                            tabbDetail.setAbbreviation(v.getCourseName().substring(0, 2) + "...");
                        } else {
                            tabbDetail.setAbbreviation(v.getCourseName());
                        }
                    }
                    tabbDetail.setCourseCode(v.getCourseCode());
                    tabbDetail.setCourseName(v.getCourseName());
                    detailMap.put(section, tabbDetail);
                }
            }
        }
    }

    /**
     * 获取选修课课程列表信息
     *
     * @param electiveQuery
     * @return
     */
    private List<CourseVO> getCourseVOList(ElectiveQuery electiveQuery) {
        CourseQuery courseQuery = new CourseQuery();
        courseQuery.setSemesterId(electiveQuery.getSemesterId());
        if (electiveQuery.getClassId() != null) {
            courseQuery.setClassIds(Collections.singletonList(electiveQuery.getClassId()));
        }
        if (electiveQuery.getRoomId() != null) {
            courseQuery.setRoomIds(Collections.singletonList(electiveQuery.getRoomId()));
        }
        if (electiveQuery.getTeacherId() != null) {
            courseQuery.setTeacherIds(Collections.singletonList(electiveQuery.getTeacherId()));
        }
        if (electiveQuery.getStudentId() != null) {
            courseQuery.setStudentId(Collections.singletonList(electiveQuery.getStudentId()));
        }
        return courseFacade.postCourseInfo(courseQuery);
    }

    /**
     * 获取选修课课程列表信息
     *
     * @param electiveQuery
     * @return
     */
    private List<CourseVO> getCourseVoListBatch(ElectiveQuery electiveQuery) {
        CourseQuery courseQuery = new CourseQuery();
        courseQuery.setSemesterId(electiveQuery.getSemesterId());
        if (electiveQuery.getClassId() != null) {
            courseQuery.setClassIds(Collections.singletonList(electiveQuery.getClassId()));
        }
        if (electiveQuery.getRoomId() != null) {
            courseQuery.setRoomIds(Collections.singletonList(electiveQuery.getRoomId()));
        }
        if (!CollectionUtils.isEmpty(electiveQuery.getTeacherIds())) {
            courseQuery.setTeacherIds(electiveQuery.getTeacherIds());
        }
        if (electiveQuery.getStudentId() != null) {
            courseQuery.setStudentId(Collections.singletonList(electiveQuery.getStudentId()));
        }
        return courseFacade.postCourseInfo(courseQuery);
    }


    /**
     * 获取节次详情map
     *
     * @param sectionType
     * @param enrollmentYear
     * @return
     */
    private Map<String, RestSectionDetailVO> getStringRestSectionDetailVoMap(Long sectionType, Integer enrollmentYear) {
        Map<String, RestSectionDetailVO> restSectionDetailMap = null;
        if (sectionType != null && enrollmentYear != null) {
            restSectionDetailMap = settingFacade.getRestAll(sectionType, enrollmentYear);
        }
        return restSectionDetailMap;
    }


    /**
     * 同步选修课
     *
     * @param holidayList                 节假日列表
     * @param electiveTeacherWeekTableMap 　选修课
     * @param resultWeekTableMap          　结果表
     * @param sourceWeekTableMap          　初始表
     */
    private void syncElectiveTableMap(
            List<WeekEnum> holidayList,
            Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap,
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekTableMap,
            Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> sourceWeekTableMap) {
        for (Long teacherId : electiveTeacherWeekTableMap.keySet()) {
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> electiveWeekMap = electiveTeacherWeekTableMap.get(teacherId);
            for (WeekEnum dayOfWeek : electiveWeekMap.keySet()) {
                if (holidayList.contains(dayOfWeek)) {
                    continue;
                }
                Map<String, TeacherAbbDetailVO> electiveSectionMap = electiveWeekMap.get(dayOfWeek);
                for (String section : electiveSectionMap.keySet()) {
                    TeacherAbbDetailVO courseInfo = electiveSectionMap.get(section);

                    setTeacherWeekTable(resultWeekTableMap, dayOfWeek, section, courseInfo);

                    setTeacherWeekTable(sourceWeekTableMap, dayOfWeek, section, courseInfo);
                }
            }
        }
    }

    /**
     * 同步选修课
     * @param holidayList
     * @param electiveTeacherWeekTableMap
     * @param resultWeekTableMap
     * @param sourceWeekTableMap
     */
    private void syncElectiveTableMapBatch(
            List<WeekEnum> holidayList,
            Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap,
            Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> resultWeekTableMap,
            Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> sourceWeekTableMap) {
        for (Long teacherId : electiveTeacherWeekTableMap.keySet()) {
            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> electiveWeekMap = electiveTeacherWeekTableMap.get(teacherId);
            for (WeekEnum dayOfWeek : electiveWeekMap.keySet()) {
                if (holidayList.contains(dayOfWeek)) {
                    continue;
                }
                Map<String, TeacherAbbDetailVO> electiveSectionMap = electiveWeekMap.get(dayOfWeek);
                for (String section : electiveSectionMap.keySet()) {
                    TeacherAbbDetailVO courseInfo = electiveSectionMap.get(section);

                    setTeacherWeekTableBatch(resultWeekTableMap, dayOfWeek, section, courseInfo,teacherId);

                    setTeacherWeekTableBatch(sourceWeekTableMap, dayOfWeek, section, courseInfo,teacherId);
                }
            }
        }
    }


    /**
     * 设置教师周课表
     *
     * @param weekTableMap
     * @param dayOfWeek
     * @param section
     * @param courseInfo
     */
    private void setTeacherWeekTable(Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekTableMap, WeekEnum dayOfWeek, String section, TeacherAbbDetailVO courseInfo) {
        List<TeacherAbbDetailVO> resultCourseInfoList = new ArrayList<>();
        resultCourseInfoList.add(courseInfo);
        if (weekTableMap.containsKey(dayOfWeek)) {
            Map<String, List<TeacherAbbDetailVO>> sectionCourseInfoMap = weekTableMap.get(dayOfWeek);
            if (sectionCourseInfoMap.containsKey(section)) {
                for (TeacherAbbDetailVO teacherAbbDetailVO : sectionCourseInfoMap.get(section)) {
                    if (RuleUtils.ignoreCourse(teacherAbbDetailVO.getAbbreviation())) {
                        sectionCourseInfoMap.put(section, resultCourseInfoList);
                        break;
                    }
                }
            } else {
                sectionCourseInfoMap.put(section, resultCourseInfoList);
            }
        } else {
            Map<String, List<TeacherAbbDetailVO>> sectionCourseInfoMap = new HashMap<>(5);
            sectionCourseInfoMap.put(section, resultCourseInfoList);
            weekTableMap.put(dayOfWeek, sectionCourseInfoMap);
        }
    }

    /**
     * 设置教师周课表
     *
     * @param weekTableMap
     * @param dayOfWeek
     * @param section
     * @param courseInfo
     */
    private void setTeacherWeekTableBatch(Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> weekTableMap, WeekEnum dayOfWeek,
                                          String section, TeacherAbbDetailVO courseInfo,Long teacherId) {
        List<TeacherAbbDetailVO> resultCourseInfoList = new ArrayList<>();
        resultCourseInfoList.add(courseInfo);
        Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> weekMap = weekTableMap.get(teacherId);
        if (!CollectionUtils.isEmpty(weekMap)) {
            for (Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap : weekMap.values()) {
                if (weekEnumMapMap.containsKey(dayOfWeek)) {
                    Map<String, List<TeacherAbbDetailVO>> sectionCourseInfoMap = weekEnumMapMap.get(dayOfWeek);
                    if (sectionCourseInfoMap.containsKey(section)) {
                        for (TeacherAbbDetailVO teacherAbbDetailVO : sectionCourseInfoMap.get(section)) {
                            if (RuleUtils.ignoreCourse(teacherAbbDetailVO.getAbbreviation())) {
                                sectionCourseInfoMap.put(section, resultCourseInfoList);
                                break;
                            }
                        }
                    } else {
                        sectionCourseInfoMap.put(section, resultCourseInfoList);
                    }
                }else {
                    Map<String, List<TeacherAbbDetailVO>> sectionCourseInfoMap = new HashMap<>(5);
                    sectionCourseInfoMap.put(section, resultCourseInfoList);
                    weekEnumMapMap.put(dayOfWeek, sectionCourseInfoMap);
                }
            }
        }
    }

    /**
     * 节假日同步
     *
     * @param semesterId                   学期id
     * @param weekNumber                   周次
     * @param sourceWeekTableMap           调整前课表
     * @param resultWeekTableMap           调整后课表
     * @param courseAdjustmentWeekTableMap 周课表
     * @param electiveTeacherWeekTableMap  选修课课表
     */
    private Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> syncWeekTableWithHoliday(Long semesterId, Integer weekNumber,
                                          ImmutableMap<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> sourceWeekTableMap,
                                          Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekTableMap,
                                          Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap,
                                          Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap) {
        Map<Integer, WeekConfigVO> weekMap = settingFacade.weekConfigNumberMap(semesterId);
        WeekConfigVO weekConfigVO = weekMap.get(weekNumber);
        Map<WeekEnum, String> currentWeekMap = DateUtils.currentWeekMap(weekConfigVO.getStartDay(), weekConfigVO.getEndDay());
        List<WeekEnum> holidayDayOfWeekList = new ArrayList<>();
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = new HashMap<>(5);
        List<ItemConfigVO> items = settingFacade.items(semesterId);
        List<ItemConfigVO> holidayOrAdjustmentItems = items.stream().filter(itemConfigVO -> itemConfigVO.getType().equals(ItemTypeEnum.HOLIDAY.getType()) || itemConfigVO.getType().equals(ItemTypeEnum.ADJUSTMENT.getType())).collect(Collectors.toList());
        for (ItemConfigVO item : holidayOrAdjustmentItems) {
            if (item.getType().equals(ItemTypeEnum.HOLIDAY.getType())) {
                for (SimpleDayConfigVO day : item.getDayList()) {
                    String detailDate = day.getDetailDate().toString();
                    if (currentWeekMap.containsValue(detailDate)) {
                        holidayDayOfWeekList.add(DateUtils.getDayOfWeekByDate(detailDate, currentWeekMap));
                    }
                }
            }
            for (AdjustmentVO adjustment : item.getAdjustments()) {
                String detailDate = adjustment.getDetailDate().toString();
                if (currentWeekMap.containsValue(detailDate)) {
                    WeekEnum dayOfWeek = WeekEnum.getWeekMap().get(adjustment.getDayOfWeek());
                    List<WeekEnum> dayOfWeekList = compensatoryMap.computeIfAbsent(dayOfWeek, k -> new ArrayList<>());
                    dayOfWeekList.add(DateUtils.getDayOfWeekByDate(detailDate, currentWeekMap));
                }
            }
        }
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultCopyMap = new HashMap<>(5);
        for (WeekEnum dayOfWeek : resultWeekTableMap.keySet()) {
            if (currentWeekMap.containsKey(dayOfWeek)) {
                if (!holidayDayOfWeekList.contains(dayOfWeek)) {
                    resultCopyMap.put(dayOfWeek, resultWeekTableMap.get(dayOfWeek));
                }
            }
            if (currentWeekMap.containsKey(dayOfWeek)) {
                if (compensatoryMap.containsKey(dayOfWeek)) {
                    if (sourceWeekTableMap.containsKey(dayOfWeek)) {
                        List<WeekEnum> weekList = compensatoryMap.get(dayOfWeek);
                        for (WeekEnum actualDayOfWeek : weekList) {
                            resultCopyMap.put(actualDayOfWeek, sourceWeekTableMap.get(dayOfWeek));
                        }
                    }
                }
            }
        }
        resultWeekTableMap = TableCopyConvert.INSTANCE.copy(resultCopyMap);
        syncCourseAdjustment(resultWeekTableMap, courseAdjustmentWeekTableMap);
        syncElectiveTableMap(holidayDayOfWeekList, electiveTeacherWeekTableMap, resultWeekTableMap, new HashMap<>(6));
        return resultWeekTableMap;
    }

    /**
     * 节假日同步
     *
     * @param semesterId                   学期id
     * @param weekNumbers                   周次
     * @param sourceWeekTableMap           调整前课表
     * @param resultWeekTableMap           调整后课表
     * @param courseAdjustmentWeekTableMap 周课表
     * @param electiveTeacherWeekTableMap  选修课课表
     */
    private void syncWeekTableWithHolidayBatch(Long semesterId, List<Integer> weekNumbers,
                                          ImmutableMap<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> sourceWeekTableMap,
                                               Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> resultWeekTableMap,
                                               Map<Long, Map<Integer,Map<WeekEnum, Map<String, TeacherAbbDetailVO>>>> courseAdjustmentWeekTableMap,
                                          Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> electiveTeacherWeekTableMap) {
        Map<Integer, WeekConfigVO> weekMap = settingFacade.weekConfigNumberMap(semesterId);
        List<ItemConfigVO> items = settingFacade.items(semesterId);
        for (Integer weekNumber : weekNumbers) {
            WeekConfigVO weekConfigVO = weekMap.get(weekNumber);
            if (Objects.nonNull(weekConfigVO)) {
                Map<WeekEnum, String> currentWeekMap = DateUtils.currentWeekMap(weekConfigVO.getStartDay(), weekConfigVO.getEndDay());
                List<WeekEnum> holidayDayOfWeekList = new ArrayList<>();
                Map<WeekEnum, List<WeekEnum>> compensatoryMap = new HashMap<>(5);

                List<ItemConfigVO> holidayOrAdjustmentItems = items.stream().filter(itemConfigVO -> itemConfigVO.getType().equals(ItemTypeEnum.HOLIDAY.getType()) || itemConfigVO.getType().equals(ItemTypeEnum.ADJUSTMENT.getType())).collect(Collectors.toList());
                for (ItemConfigVO item : holidayOrAdjustmentItems) {
                    if (item.getType().equals(ItemTypeEnum.HOLIDAY.getType())) {
                        for (SimpleDayConfigVO day : item.getDayList()) {
                            String detailDate = day.getDetailDate().toString();
                            if (currentWeekMap.containsValue(detailDate)) {
                                holidayDayOfWeekList.add(DateUtils.getDayOfWeekByDate(detailDate, currentWeekMap));
                            }
                        }
                    }
                    for (AdjustmentVO adjustment : item.getAdjustments()) {
                        String detailDate = adjustment.getDetailDate().toString();
                        if (currentWeekMap.containsValue(detailDate)) {
                            WeekEnum dayOfWeek = WeekEnum.getWeekMap().get(adjustment.getDayOfWeek());
                            List<WeekEnum> dayOfWeekList = compensatoryMap.computeIfAbsent(dayOfWeek, k -> new ArrayList<>());
                            dayOfWeekList.add(DateUtils.getDayOfWeekByDate(detailDate, currentWeekMap));
                        }
                    }
                }
                Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultCopyMap = new HashMap<>(5);

                resultWeekTableMap.forEach((k,v) ->{
                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMapMap = v.get(weekNumber);
                    if (!CollectionUtils.isEmpty(weekEnumMapMap)) {
                        for (WeekEnum dayOfWeek : weekEnumMapMap.keySet()) {
                            if (currentWeekMap.containsKey(dayOfWeek)) {
                                if (!holidayDayOfWeekList.contains(dayOfWeek)) {
                                    resultCopyMap.put(dayOfWeek, weekEnumMapMap.get(dayOfWeek));
                                }
                            }
                            if (currentWeekMap.containsKey(dayOfWeek)) {
                                if (compensatoryMap.containsKey(dayOfWeek)) {
                                    Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> mapMap = sourceWeekTableMap.get(k);
                                    if (!CollectionUtils.isEmpty(mapMap)) {
                                        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> enumMapMap = mapMap.get(weekNumber);
                                        if (!CollectionUtils.isEmpty(enumMapMap)) {
                                            if (enumMapMap.containsKey(dayOfWeek)) {
                                                List<WeekEnum> weekList = compensatoryMap.get(dayOfWeek);
                                                for (WeekEnum actualDayOfWeek : weekList) {
                                                    Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> integerMapMap = sourceWeekTableMap.get(k);
                                                    assert integerMapMap != null;
                                                    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> weekEnumMap = integerMapMap.get(weekNumber);
                                                    resultCopyMap.put(actualDayOfWeek, weekEnumMap.get(dayOfWeek));
                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        v.remove(weekNumber);
                        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultMap = new HashMap<>(6);
                        resultCopyMap.forEach((i,j) ->{
                            Map<String, List<TeacherAbbDetailVO>> map = new HashMap<>(6);
                            j.forEach((m,n) ->{
                                List<TeacherAbbDetailVO> teacherAbbDetailVos = TableCopyConvert.INSTANCE.listCopy(n);
                                map.put(m,teacherAbbDetailVos);
                            });
                            resultMap.put(i,map);
                        });
                        Map<Integer, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> integerMapMap = new HashMap<>(6);
                        if (!CollectionUtils.isEmpty(courseAdjustmentWeekTableMap)) {
                            integerMapMap = courseAdjustmentWeekTableMap.get(k);
                        }
                        Map<WeekEnum, Map<String, TeacherAbbDetailVO>> abbDetailMap = new HashMap<>(6);
                        if (!CollectionUtils.isEmpty(integerMapMap)) {
                            abbDetailMap = integerMapMap.get(weekNumber);
                        }
                        syncCourseAdjustmentBatch(resultMap, abbDetailMap);
                        syncElectiveTableMap(holidayDayOfWeekList, electiveTeacherWeekTableMap, resultMap, new HashMap<>(6));
                        v.put(weekNumber,resultMap);
                    }
                });
            }
        }

    }

    /**
     * 同步调代课洗洗脑
     *
     * @param resultWeekTableMap
     * @param courseAdjustmentWeekTableMap
     */
    private void syncCourseAdjustment(Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekTableMap, Map<Long, Map<WeekEnum, Map<String, TeacherAbbDetailVO>>> courseAdjustmentWeekTableMap) {
        if (!CollectionUtils.isEmpty(courseAdjustmentWeekTableMap)) {
            for (WeekEnum dayOfWeek : resultWeekTableMap.keySet()) {
                Map<String, List<TeacherAbbDetailVO>> sectionMap = resultWeekTableMap.get(dayOfWeek);
                for (String section : sectionMap.keySet()) {
                    List<TeacherAbbDetailVO> courseInfoList = sectionMap.get(section);
                    for (TeacherAbbDetailVO teacherAbbDetailVO : courseInfoList) {
                        if (courseAdjustmentWeekTableMap.containsKey(teacherAbbDetailVO.getTeacherId())) {
                            Map<WeekEnum, Map<String, TeacherAbbDetailVO>> teacherWeekTable = courseAdjustmentWeekTableMap.get(teacherAbbDetailVO.getTeacherId());
                            if (teacherWeekTable.containsKey(dayOfWeek)) {
                                TeacherAbbDetailVO courseInfo = teacherWeekTable.get(dayOfWeek).get(section);
                                if (courseInfo != null) {
                                    if (teacherAbbDetailVO.getAbbreviation().equals(courseInfo.getAbbreviation())) {
                                        Collections.replaceAll(courseInfoList, teacherAbbDetailVO, courseInfo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            for (Long teacherId : courseAdjustmentWeekTableMap.keySet()) {
                Map<WeekEnum, Map<String, TeacherAbbDetailVO>> teacherWeekTable = courseAdjustmentWeekTableMap.get(teacherId);
                for (WeekEnum dayOfWeek : teacherWeekTable.keySet()) {
                    Map<String, TeacherAbbDetailVO> sectionMap = teacherWeekTable.get(dayOfWeek);
                    for (String section : sectionMap.keySet()) {
                        Map<String, List<TeacherAbbDetailVO>> sectionCourseMap = resultWeekTableMap.get(dayOfWeek);
                        List<TeacherAbbDetailVO> weekData = new ArrayList<>();
                        TeacherAbbDetailVO abbDetail = sectionMap.get(section);
                        if (abbDetail.getType() == AbbTypeEnum.ADJUSTMENT || abbDetail.getType() == AbbTypeEnum.SUBSTITUTE) {
                            if (sectionCourseMap == null) {
                                sectionCourseMap = new HashMap<>(10);
                                resultWeekTableMap.put(dayOfWeek, sectionCourseMap);
                            }
                            weekData.add(abbDetail);
                            sectionCourseMap.put(section, weekData);
                        } else if (abbDetail.getType() == AbbTypeEnum.COVERSUBSTITUTE || abbDetail.getType() == AbbTypeEnum.COVERADJUSTMENT) {
                            if (sectionCourseMap != null) {
                                sectionCourseMap.remove(section);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 同步调代课
     * @param resultWeekTableMap
     * @param courseAdjustmentWeekTableMap
     */
    private void syncCourseAdjustmentBatch(Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> resultWeekTableMap, Map<WeekEnum, Map<String, TeacherAbbDetailVO>> courseAdjustmentWeekTableMap) {
        if (!CollectionUtils.isEmpty(courseAdjustmentWeekTableMap)) {
            for (WeekEnum dayOfWeek : resultWeekTableMap.keySet()) {
                Map<String, List<TeacherAbbDetailVO>> sectionMap = resultWeekTableMap.get(dayOfWeek);
                for (String section : sectionMap.keySet()) {
                    List<TeacherAbbDetailVO> courseInfoList = sectionMap.get(section);
                    for (TeacherAbbDetailVO teacherAbbDetailVO : courseInfoList) {
                        if (courseAdjustmentWeekTableMap.containsKey(dayOfWeek)) {
                            if (courseAdjustmentWeekTableMap.containsKey(dayOfWeek)) {
                                TeacherAbbDetailVO courseInfo = courseAdjustmentWeekTableMap.get(dayOfWeek).get(section);
                                if (courseInfo != null) {
                                    if (teacherAbbDetailVO.getAbbreviation().equals(courseInfo.getAbbreviation())) {
                                        Collections.replaceAll(courseInfoList, teacherAbbDetailVO, courseInfo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            for (WeekEnum dayOfWeek : courseAdjustmentWeekTableMap.keySet()) {
                Map<String, TeacherAbbDetailVO> sectionMap = courseAdjustmentWeekTableMap.get(dayOfWeek);
                for (String section : sectionMap.keySet()) {
                    Map<String, List<TeacherAbbDetailVO>> sectionCourseMap = resultWeekTableMap.get(dayOfWeek);
                    List<TeacherAbbDetailVO> weekData = new ArrayList<>();
                    TeacherAbbDetailVO abbDetail = sectionMap.get(section);
                    if (abbDetail.getType() == AbbTypeEnum.ADJUSTMENT || abbDetail.getType() == AbbTypeEnum.SUBSTITUTE) {
                        if (sectionCourseMap == null) {
                            sectionCourseMap = new HashMap<>(10);
                            resultWeekTableMap.put(dayOfWeek, sectionCourseMap);
                        }
                        weekData.add(abbDetail);
                        sectionCourseMap.put(section, weekData);
                    } else if (abbDetail.getType() == AbbTypeEnum.COVERSUBSTITUTE || abbDetail.getType() == AbbTypeEnum.COVERADJUSTMENT) {
                        if (sectionCourseMap != null) {
                            sectionCourseMap.remove(section);
                        }
                    }
                }
            }
        }
    }


    @Override
    public List<LessonPrepareVO> getLessonPrepare(Long semesterId, Integer orderNo) {
        List<DictionaryVO> dics1 = foundationFacade.getDictionaryValues("subject1");
        Map<String, DictionaryVO> dic1Map = dics1.stream().collect(Collectors.toMap(DictionaryVO::getName, Function.identity()));
        List<DictionaryVO> dics2 = foundationFacade.getDictionaryValues("subject2");
        Map<String, DictionaryVO> dic2Map = dics2.stream().collect(Collectors.toMap(DictionaryVO::getName, Function.identity()));

        List<LessonPrepareVO> result = new ArrayList<>();
        List<CurriculumTableDO> curriculumTableList = curriculumTableService.getTable(semesterId, orderNo);
        for (CurriculumTableDO t : curriculumTableList) {
            Map<String, DictionaryVO> curDicMap = t.getSectionId() == 1 ? dic1Map : dic2Map;
            List<TeacherAbbDetailVO> ttdvList = new ArrayList<>();
            List<GeneralTableVO> classTable = classesTable(t.getId());
            for (GeneralTableVO c : classTable) {
                for (DataTableDTO dto : c.getData()) {
                    for (WeekEnum week : dto.getDetailv().keySet()) {
                        for (String section : dto.getDetailv().get(week).keySet()) {
                            ttdvList.addAll(dto.getDetailv().get(week).get(section));
                        }
                    }
                }
            }
            Map<String, List<TeacherAbbDetailVO>> teacherAbbGroup = ttdvList.stream().collect(Collectors.groupingBy(TeacherAbbDetailVO::getCourseName));
            for (String abb : teacherAbbGroup.keySet()) {
                LessonPrepareVO v = new LessonPrepareVO();
                v.setCurriculumId(t.getId());
                v.setAbb(abb);
                if (curDicMap.containsKey(abb)) {
                    v.setCode(curDicMap.get(abb).getValue());
                }
                v.setSectionId(t.getSectionId());
                v.setEnrollmentYear(t.getEnrollmentYear());
                v.setTeachers(teacherAbbGroup.get(abb).stream().filter(distinctByKey(TeacherDetailVO::getTeacherId)).collect(Collectors.toList()));
                result.add(v);
            }
        }
        return result;
    }

    public <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>(5);
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public Long workload(Long semesterId, Long teacherId, Integer weekNumber) {
        long num = 0L;
        Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> teacherWeekTable = teacherTableWeekDetail(semesterId, teacherId, weekNumber);
        for (WeekEnum week : teacherWeekTable.keySet()) {
            Map<String, List<TeacherAbbDetailVO>> sectionMap = teacherWeekTable.get(week);
            for (String section : sectionMap.keySet()) {
                List<TeacherAbbDetailVO> details = sectionMap.get(section);
                num += details.size();
            }
        }
        return num;
    }

    @Override
    public List<TeacherDataVO> teacherData(Long semesterId) {
        List<CurriculumTableDO> curriculums = curriculumTableService.getTable(semesterId, 0);
        List<Long> ids = curriculums.stream().map(CurriculumTableDO::getId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        Map<Long, Map<String, Set<Long>>> allMap = new HashMap<>(5);
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId", ids);
        List<GeneralTableDO> generalTabls = generalTableDao.queryForList(wrapper);
        for (GeneralTableDO g : generalTabls) {
            for (StudentTableDTO dto : g.getStudentTable()) {
                for (String abb : dto.getDetail().keySet()) {
                    String courseName = g.getCurriculumAlias().get(abb);
                    Map<Long, String> detailMap = dto.getDetail().get(abb);
                    for (Long teacherId : detailMap.keySet()) {
                        Map<String, Set<Long>> techMap = allMap.computeIfAbsent(teacherId, k -> new HashMap<>(5));
                        Set<Long> classOrRoomIds = techMap.computeIfAbsent(courseName, k -> new HashSet<>());
                        classOrRoomIds.add(dto.getClassId());
                    }
                }
            }
        }

        List<TeacherDataVO> teacherData = new ArrayList<>();
        for (Long teacherId : allMap.keySet()) {
            TeacherDataVO data = new TeacherDataVO();
            teacherData.add(data);
            data.setTeacherId(teacherId);
            List<TeacherDataRelationsVO> relations = new ArrayList<>();
            data.setTeacherDataRelations(relations);
            Map<String, Set<Long>> courses = allMap.get(teacherId);
            for (String courseName : courses.keySet()) {
                TeacherDataRelationsVO v = new TeacherDataRelationsVO();
                v.setCourseName(courseName);
                v.setClassIds(new ArrayList<>(courses.get(courseName)));
                relations.add(v);
            }
        }
        return teacherData;
    }

    @Override
    public List<StudentDataVO> studentData(Long semesterId) {
        List<CurriculumTableDO> curriculums = curriculumTableService.getTable(semesterId, 0);
        List<Long> curriculumTableIds = curriculums.stream().map(CurriculumTableDO::getId).distinct().collect(Collectors.toList());
        Map<Long, Map<String, Set<StudentDataRelationsVO>>> allMap = new HashMap<>(5);
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("curriculumTableId", curriculumTableIds);
        List<GeneralTableDO> generalTableDOList = generalTableDao.queryForList(wrapper);
        for (GeneralTableDO g : generalTableDOList) {
            List<GeneralTableVO> classTableList = classesTable(g.getCurriculumTableId());
            for (StudentTableDTO stuDto : g.getStudentTable()) {
                for (String abb : stuDto.getLeaveClass()) {
                    for (GeneralTableVO gt : classTableList) {
                        if (!gt.getType().equals(g.getType())) {
                            continue;
                        }
                        String courseName = g.getCurriculumAlias().get(abb);
                        for (DataTableDTO clsDto : gt.getData()) {
                            for (WeekEnum week : clsDto.getDetailv().keySet()) {
                                Map<String, List<TeacherAbbDetailVO>> detailMap = clsDto.getDetailv().get(week);
                                for (String section : detailMap.keySet()) {
                                    List<TeacherAbbDetailVO> teacherSectionList = detailMap.get(section);
                                    for (TeacherAbbDetailVO v : teacherSectionList) {
                                        if (v.getAbbreviation().equals(abb) &&
                                                stuDto.getDetail().containsKey(abb) &&
                                                stuDto.getDetail().get(abb).containsKey(v.getTeacherId())) {
                                            if (!v.getClassOrRoomId().equals(String.valueOf(stuDto.getClassId()))) {
                                                Map<String, Set<StudentDataRelationsVO>> stuMap = allMap.computeIfAbsent(stuDto.getStuId(), k -> new HashMap<>(5));
                                                Set<StudentDataRelationsVO> teacherAbbSet = stuMap.computeIfAbsent(courseName, k -> new HashSet<>(5));
                                                boolean flag = true;
                                                Long classOrRoomId = ClassTableUtils.getClassOrRoomId(v.getClassOrRoomId());
                                                for (StudentDataRelationsVO r : teacherAbbSet) {
                                                    if (classOrRoomId.equals(r.getClassId())) {
                                                        flag = false;
                                                        break;
                                                    } else if (classOrRoomId.equals(r.getRoomId())) {
                                                        flag = false;
                                                        break;
                                                    }
                                                }
                                                if (flag) {
                                                    StudentDataRelationsVO rel = new StudentDataRelationsVO();
                                                    if (ClassTableUtils.isClass(v.getClassOrRoomId())) {
                                                        rel.setClassId(ClassTableUtils.getClassOrRoomId(v.getClassOrRoomId()));
                                                        rel.setClassName(v.getClassOrRoomName());
                                                    } else {
                                                        rel.setRoomId(ClassTableUtils.getClassOrRoomId(v.getClassOrRoomId()));
                                                        rel.setRoomName(v.getClassOrRoomName());
                                                    }
                                                    rel.setTeacherId(v.getTeacherId());
                                                    rel.setTeacherName(v.getTeacherName());
                                                    teacherAbbSet.add(rel);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        List<StudentDataVO> studentDataVOList = new ArrayList<>();
        for (Long teacherId : allMap.keySet()) {
            StudentDataVO studentDataVO = new StudentDataVO();
            studentDataVOList.add(studentDataVO);
            studentDataVO.setStudentId(teacherId);
            List<StudentDataRelationsItemsVO> relations = new ArrayList<>();
            studentDataVO.setReleations(relations);
            Map<String, Set<StudentDataRelationsVO>> courses = allMap.get(teacherId);
            for (String courseName : courses.keySet()) {
                StudentDataRelationsItemsVO studentDataRelationsItemsVO = new StudentDataRelationsItemsVO();
                studentDataRelationsItemsVO.setCourseName(courseName);
                studentDataRelationsItemsVO.setReleations(new ArrayList<>(courses.get(courseName)));
                relations.add(studentDataRelationsItemsVO);
            }
        }
        return studentDataVOList;
    }

}
