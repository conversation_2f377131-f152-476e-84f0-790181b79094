package com.xiaoshan.edu.openclass.service;

import com.topnetwork.ao.PageAO;
import com.xiaoshan.edu.ao.openclass.CloudRoomAO;
import com.xiaoshan.edu.ao.openclass.RoomNameAO;
import com.xiaoshan.edu.model.ClassBuildingTreeDTO;
import com.xiaoshan.edu.openclass.entity.CloudRoomDO;
import com.xiaoshan.edu.vo.openclass.CloudRoomVO;
import start.framework.commons.result.QueryResult;
import start.framework.service.SqlBaseService;

import java.util.List;

public interface CloudRoomService extends SqlBaseService<CloudRoomDO,Long> {

    /**
     * 获取云课堂教室列表
     * @param ao
     * @return
     */
    QueryResult<List<CloudRoomVO>> getCloudRoom(PageAO ao);

    /**
     * 获取云课堂教室树形结构
     * @return
     */
    List<ClassBuildingTreeDTO> getCloudRoomTree();

    /**
     * 添加云课堂
     * @param ao
     */
    void addCloudRoom(List<CloudRoomAO> ao);

    /**
     * 获取所有房间，除去云课堂房间
     * @param ao
     * @return
     */
    List<CloudRoomVO> all(RoomNameAO ao);
}
