package com.xiaoshan.edu.survey.service.impl;

import com.common.business.CommonBusiness;
import com.common.model.UserContextHolder;
import com.common.utils.ClassConverter;
import com.topnetwork.ao.PageAO;
import com.xiaoshan.basic.vo.SemesterVO;
import com.xiaoshan.basic.vo.StudentVO;
import com.xiaoshan.edu.ao.survey.TaskAddAO;
import com.xiaoshan.edu.ao.survey.TaskPageAO;
import com.xiaoshan.edu.ao.survey.TaskTemplateAO;
import com.xiaoshan.edu.ao.timetable.CurriculumTablePageAO;
import com.xiaoshan.edu.api.facade.FoundationFacade;
import com.xiaoshan.edu.api.facade.SettingFacade;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.enums.survey.SurTaskState;
import com.xiaoshan.edu.enums.survey.SurveyObj;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.service.BasicPushMessage;
import com.xiaoshan.edu.survey.dao.TaskDao;
import com.xiaoshan.edu.survey.entity.TaskDO;
import com.xiaoshan.edu.survey.entity.TaskStudentDO;
import com.xiaoshan.edu.survey.entity.TaskTemplateStudentTeacherQuestionDO;
import com.xiaoshan.edu.survey.service.*;
import com.xiaoshan.edu.timetable.service.CurriculumService;
import com.xiaoshan.edu.timetable.service.CurriculumTableService;
import com.xiaoshan.edu.timetable.service.GeneralTableService;
import com.xiaoshan.edu.vo.survey.*;
import com.xiaoshan.edu.vo.timetable.CurriculumTableVO;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.oa.dto.ClassDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import start.framework.commons.exception.BusinessException;
import start.framework.commons.result.QueryResult;
import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.core.ApplicationException;
import start.magic.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service("taskService")
public class TaskServiceImpl extends SqlBaseServiceImplV2Ext<TaskDO, Long>
        implements TaskService {

    @SuppressWarnings("unused")
    private TaskDao taskDao;
    @Autowired
    private FoundationFacade foundationFacade;
    @Autowired
    private TaskTemplateService taskTemplateService;
    @Autowired
    private SettingFacade settingFacade;
    @Autowired
    private GeneralTableService generalTableService;
    @Autowired
    private CurriculumService curriculumService;
    @Autowired
    private CurriculumTableService curriculumTableService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private TaskTemplateStudentTeacherQuestionService taskTemplateStudentTeacherQuestionService;
    @Autowired
    private TaskStudentService taskStudentService;
    @Autowired
    private BasicPushMessage basicPushMessage;
    @Autowired
    private TaskTemplateStudentService taskTemplateStudentService;

    public TaskServiceImpl(@Qualifier("taskDao") TaskDao taskDao) {
        super(taskDao);
        this.taskDao = taskDao;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(TaskAddAO ao) {
        TaskDO taskDO = ClassConverter.aTob(ao, new TaskDO());
        taskDO.setId(ao.getTaskId());
        taskDO.setTotal(0);
        save(taskDO);
        // 关联模板
        taskTemplateService.add(taskDO.getId(), ao.getTemplateList());
        // 关联 taskId templateId student Teacher Question
        List<Long> studentIds = saveTaskTemplateStudentTeacherQuestion(ao, taskDO);
        if (studentIds.isEmpty()) {
            throw new BusinessException("调查学生为空");
        }
        // 关联 taskId studentId
        saveTaskAndStudent(studentIds, taskDO);
        basicPushMessage.surveyPublish(taskDO, studentIds);

    }


    /**
     * 编辑
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(TaskAddAO ao) {
        TaskDO load = load(ao.getTaskId());
        Date loadStartDate = load.getStartDate();
        if (load.getStartDate().before(new Date())) {
            if (ao.getStartDate().after(new Date())) {
                throw new BusinessException("已经开始的任务不能将时间改成未开始的时间");
            }
        }
        ClassConverter.aTob(ao, load);
        save(load);

        // 如果未开始，更新模板相关数据
        if (loadStartDate.after(new Date())) {
            // 先删除
            taskTemplateService.removeByTaskId(load.getId());
            taskTemplateStudentTeacherQuestionService.removeByTaskId(load.getId());
            taskStudentService.removeByTaskId(load.getId());

            // 再添加
            taskTemplateService.add(ao.getTaskId(), ao.getTemplateList());
            List<Long> studentIds = saveTaskTemplateStudentTeacherQuestion(ao, load);
            saveTaskAndStudent(studentIds, load);
        }
    }

    /**
     * 教师端 -- 调查任务列表
     */
    @Override
    public List<TeacherTaskVO> teacherTaskList() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("teacherId", UserContextHolder.getUser().getId());
        return queryForMapMapper(TeacherTaskVO.class, "getTeacherTask", map);
    }

    /**
     * （公布/取消公布）结果
     */
    @Override
    public void publish(Long taskId) {
        TaskDO load = load(taskId);
        load.setIsPublish(!load.getIsPublish());
        save(load);
        if (load.getIsPublish()) {
            List<Long> userIdList = taskTemplateStudentTeacherQuestionService.getTeacherUserIdList(taskId);
            if (!userIdList.isEmpty()) {
                basicPushMessage.surveyResult(load, userIdList);
            }
        }
    }

    /**
     * 删除调查任务
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeById(Long taskId) {
        taskTemplateStudentTeacherQuestionService.removeByTaskId(taskId);
        taskTemplateService.removeByTaskId(taskId);
        taskStudentService.removeByTaskId(taskId);
        taskTemplateService.removeByTaskId(taskId);
        remove(taskId);
    }

    /**
     * 电子档案--教师调查任务列表
     */
    @Override
    public QueryResult<List<TaskPageVO>> archives(Long teacherId, PageAO ao) {
        Map<String, Object> map = CommonBusiness.httpPageParam(ao);
        map.put("teacherId", teacherId);
        return queryForPage(TaskPageVO.class, "archives", map);
    }

    /**
     * 保存对应的题目
     */
    private List<Long> saveTaskTemplateStudentTeacherQuestion(TaskAddAO ao, TaskDO taskDO) {
        List<ClassDTO> classList = null;
        List<TaskTemplateStudentTeacherQuestionDO> currDoList = null;
        for (TaskTemplateAO tmp : ao.getTemplateList()) {
            if (classList == null && tmp.getSurveyObj().equals(SurveyObj.CLASS)) {
                classList = getClass(ao.getGrade(), ao.getStartYear());
            }
            if (currDoList == null && tmp.getSurveyObj().equals(SurveyObj.CURRICULUM)) {
                currDoList = getCurriculumTeacher(ao.getStartYear(), ao.getGrade());
            }
        }

        List<TaskTemplateStudentTeacherQuestionDO> list = new ArrayList<>();
        for (TaskTemplateAO tmp : ao.getTemplateList()) {
            TemplateAndQuestionVO questionVO = templateService.getById(tmp.getTemplateId());
            // 班主任模板，绑定所有学生和班主任
            if (tmp.getSurveyObj().equals(SurveyObj.CLASS)) {
                if (classList == null || classList.isEmpty()) {
                    continue;
                }
                for (ClassDTO classDto : classList) {
                    if (classDto.getHeadTeacherId() != null && classDto.getStudentIds() != null && !classDto.getStudentIds().isEmpty()) {
                        for (Long studentId : classDto.getStudentIds()) {
                            for (QuestionVO question : questionVO.getQuestionList()) {
                                TaskTemplateStudentTeacherQuestionDO q = new TaskTemplateStudentTeacherQuestionDO();
                                q.setTeacherName(classDto.getHeadTeacherName());
                                q.setTeacherId(classDto.getHeadTeacherId());
                                q.setClassName(classDto.getName());
                                q.setStudentId(studentId);
                                q.setQuestionId(question.getQuestionId());
                                q.setIsVisible(question.getIsVisible());
                                q.setTaskId(taskDO.getId());
                                q.setQuestionType(question.getQuestionType());
                                q.setTemplateId(tmp.getTemplateId());
                                q.setIsFinish(false);
                                q.setQuestionTitle(question.getTitle());
                                q.setAnswer(new ArrayList<>());
                                q.setQuestionSort(question.getQuestionSort());
                                q.setIsSubmit(false);
                                list.add(q);
                            }
                        }
                    }
                }
            }

            // 任课教师模板，绑定任课教师
            if (tmp.getSurveyObj().equals(SurveyObj.CURRICULUM)) {
                if (currDoList == null || currDoList.isEmpty()) {
                    continue;
                }
                for (QuestionVO q : questionVO.getQuestionList()) {
                    for (TaskTemplateStudentTeacherQuestionDO d : currDoList) {
                        if (!q.getCurriculumList().contains(d.getCurriculum())) {
                            continue;
                        }
                        TaskTemplateStudentTeacherQuestionDO dCopy = ClassConverter.aTob(d, new TaskTemplateStudentTeacherQuestionDO());
                        dCopy.setTaskId(taskDO.getId());
                        dCopy.setTemplateId(tmp.getTemplateId());
                        dCopy.setQuestionId(q.getQuestionId());
                        dCopy.setQuestionType(q.getQuestionType());
                        dCopy.setIsVisible(q.getIsVisible());
                        dCopy.setIsFinish(false);
                        dCopy.setQuestionTitle(q.getTitle());
                        dCopy.setAnswer(new ArrayList<>());
                        dCopy.setQuestionSort(q.getQuestionSort());
                        dCopy.setIsSubmit(false);
                        list.add(dCopy);
                    }
                }
            }
        }

        taskTemplateStudentTeacherQuestionService.saveBatch(list);
        return list.stream().map(TaskTemplateStudentTeacherQuestionDO::getStudentId).distinct().collect(Collectors.toList());
    }

    /**
     * 保存调查任务-学生关联表
     */
    private void saveTaskAndStudent(List<Long> studentIds, TaskDO taskDO) {
        List<StudentVO> list = getStudentMap(studentIds);
        ArrayList<TaskStudentDO> res = new ArrayList<>();
        for (StudentVO vo : list) {
            TaskStudentDO taskStudentDO = ClassConverter.aTob(vo, new TaskStudentDO());
            taskStudentDO.setId(null);
            taskStudentDO.setStudentId(vo.getId());
            taskStudentDO.setTaskId(taskDO.getId());
            taskStudentDO.setStudentUserId(vo.getUserId());
            taskStudentDO.setIsFinish(false);
            res.add(taskStudentDO);
        }
        taskDO.setTotal(list.size());
        save(taskDO);
        taskStudentService.saveBatch(res);
    }

    /**
     * 调查任务 -- 分页查询
     */
    @Override
    public QueryResult<List<TaskPageVO>> page(TaskPageAO ao) {
        Map<String, Object> map = CommonBusiness.httpPageParam(ao);
        QueryResult<List<TaskPageVO>> page = queryForPage(TaskPageVO.class, "taskList", map);
        if (page == null) {
            return null;
        }
        for (TaskPageVO vo : page.getResult()) {
            vo.setState(getState(vo.getStartDate(), vo.getEndDate()));
        }
        return page;
    }

    /**
     * 调查任务 -- id查询详细信息
     */
    @Override
    public TaskAddVO getById(Long taskId) {
        TaskDO load = taskDao.load(taskId);
        if (load == null) {
            return null;
        }
        List<TaskTemplateVO> templateList = taskTemplateService.getByTaskId(taskId);
        TaskAddVO vo = ClassConverter.aTob(load, new TaskAddVO());
        vo.setTaskId(taskId);
        vo.setTemplateList(templateList);
        return vo;
    }

    /**
     * 小程序 -- 调查任务
     */
    @Override
    public List<StudentTaskVO> studentTaskList(Long studentId) {
        HashMap<String, Object> map = new HashMap<>(1);
        map.put("studentId", studentId);
        List<StudentTaskVO> list = queryForMapMapper(StudentTaskVO.class, "getTask", map);
        for (StudentTaskVO vo : list) {
            Date now = new Date();
            if (vo.getStartDate().after(now)) {
                vo.setState(SurTaskState.NOT_START);
            } else if (vo.getEndDate().before(now)) {
                vo.setState(SurTaskState.OVER);
            } else {
                vo.setState(SurTaskState.IN_PROGRESS);
            }
        }
        return list;
    }


    /**
     * 获取调查任务状态
     */
    public Integer getState(Date startDate, Date endDate) {
        if (startDate.after(new Date())) {
            return 1;
        } else if (endDate.before(new Date())) {
            return 3;
        } else {
            return 2;
        }
    }


    /**
     * 根据年级获取学生数据
     */
    private List<StudentVO> getStudentMap(List<Long> studentIds) {
        HashMap<String, String> params = new HashMap<>();
        params.put("stuIds", StringUtils.listToString(studentIds));
        return foundationFacade.getStudent(params);
    }


    /**
     * 根据年级获取班级数据
     *
     * @param grade 年级（初一到高三）
     * @return 班级数据
     */
    private List<ClassDTO> getClass(String grade, Integer startYear) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        char chu = '初';
        if (grade.charAt(0) == chu) {
            params.add("sectionType", "2");
        } else {
            char gao = '高';
            if (grade.charAt(0) == gao) {
                params.add("sectionType", "1");
            } else {
                throw new ApplicationException("年级格式错误，只能传初一、初二...高三");
            }
        }

        String gradeNum = "一二三";

        if (gradeNum.indexOf(grade.charAt(1)) == -1) {
            throw new ApplicationException("年级格式错误，只能传初一、初二...高三");
        }

        params.add("enrollmentYear", String.valueOf(startYear - gradeNum.indexOf(grade.charAt(1))));
        params.add("studentFlag", "true");
        params.add("fullClass", "true");
        return foundationFacade.classes(params);
    }

    /**
     * 获取教师科目学生数据
     */
    public List<TaskTemplateStudentTeacherQuestionDO> getCurriculumTeacher(Integer startYear, String grade) {
        SemesterVO currentSemesters = settingFacade.currentSemester();
        Long semesterId = currentSemesters.getId();

        CurriculumTablePageAO ao = new CurriculumTablePageAO();
        ao.setClassTeacher(false);
        ao.setPageIndex(1);
        ao.setPageSize(1);
        ao.setSemesterId(semesterId);
        ao.setSectionId(grade.charAt(0) == '高' ? 1L : 2L);
        ao.setEnrollmentYear(startYear - "一二三".indexOf(grade.charAt(1)));

        QueryResult<List<CurriculumTableVO>> page = curriculumTableService.page(ao, null, null);
        if (page == null) {
            return null;
        }
        List<CurriculumTableVO> result = page.getResult();
        long tableId = result.get(0).getCurriculumTableId();

        List<TaskTemplateStudentTeacherQuestionDO> list = new ArrayList<>();
        Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTableMaps = generalTableService.studentTableDetail(tableId);
        for (Map.Entry<Long, Map<WeekTypeEnum, DataTableDTO>> studentIdEntry : studentTableMaps.entrySet()) {  // key 学生id
            for (DataTableDTO dto : studentIdEntry.getValue().values()) {
                for (Map<String, List<TeacherAbbDetailVO>> sectionMap : dto.getDetailv().values()) {
                    for (List<TeacherAbbDetailVO> detailList : sectionMap.values()) {
                        for (TeacherAbbDetailVO detailVO : detailList) {
                            TaskTemplateStudentTeacherQuestionDO q = new TaskTemplateStudentTeacherQuestionDO();
                            q.setCurriculum(detailVO.getCourseName());
                            q.setClassName(detailVO.getClassOrRoomName());
                            q.setTeacherId(detailVO.getTeacherId());
                            q.setTeacherName(detailVO.getTeacherName());
                            q.setStudentId(studentIdEntry.getKey());
                            list.add(q);
                        }
                    }
                }
            }
        }

        return list.stream().distinct().collect(Collectors.toList());

    }
}
