package com.xiaoshan.edu.patrol.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.xiaoshan.edu.patrol.dao.PatrolObjectDao;
import com.xiaoshan.edu.patrol.entity.PatrolObjectDO;
import com.xiaoshan.edu.patrol.service.PatrolObjectService;

import start.framework.service.impl.SqlBaseServiceImplV2Ext;
import start.magic.persistence.source.jdbc.sqlplus.conditions.query.QueryWrapper;

@Service("patrolObjectService")
public class PatrolObjectServiceImpl extends SqlBaseServiceImplV2Ext<PatrolObjectDO,Long>
implements PatrolObjectService {

	private PatrolObjectDao patrolObjectDao;
	
	public PatrolObjectServiceImpl(@Qualifier("patrolObjectDao")PatrolObjectDao patrolObjectDao) {
		super(patrolObjectDao);
		this.patrolObjectDao=patrolObjectDao;
	}

	@Override
	public List<String> getNamesByPatrolId(Long id) {
		QueryWrapper queryWrapper = new QueryWrapper();
		queryWrapper
				.select("name")
				.eq("patrol_id",id);
		List<PatrolObjectDO> patrolObjectDoList = patrolObjectDao.queryForList(queryWrapper);
		return patrolObjectDoList.stream().map(PatrolObjectDO::getName).collect(Collectors.toList());
	}

	@Override
	public List<PatrolObjectDO> getObjectsByPatrolId(Long id) {
		QueryWrapper queryWrapper = new QueryWrapper();
		queryWrapper.eq("patrol_id",id);
		return patrolObjectDao.queryForList(queryWrapper);
	}

	@Override
	public PatrolObjectDO getObjectsByPatrolIdAndObjectId(Long id, Long objectId) {
		QueryWrapper queryWrapper = new QueryWrapper();
		queryWrapper.eq("patrol_id",id);
		queryWrapper.eq("object_id",objectId);
		return get(patrolObjectDao.queryForList(queryWrapper));
	}
}
