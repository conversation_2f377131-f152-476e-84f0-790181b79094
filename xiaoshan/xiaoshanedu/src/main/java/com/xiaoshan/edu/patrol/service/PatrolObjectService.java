package com.xiaoshan.edu.patrol.service;

import com.xiaoshan.edu.patrol.entity.PatrolObjectDO;
import start.framework.service.SqlBaseService;

import java.util.List;

public interface PatrolObjectService extends SqlBaseService<PatrolObjectDO,Long> {
    /**
     * 根据巡课id获取巡课对象名
     * @param id
     * @return
     */
    List<String> getNamesByPatrolId(Long id);

    /**
     * 根据巡课id获取巡课对象列表
     * @param id
     * @return
     */
    List<PatrolObjectDO> getObjectsByPatrolId(Long id);

    /**
     * 根据巡课id和对象id获取对象信息
     * @param id
     * @param objectId
     * @return
     */
    PatrolObjectDO getObjectsByPatrolIdAndObjectId(Long id, Long objectId);
}
