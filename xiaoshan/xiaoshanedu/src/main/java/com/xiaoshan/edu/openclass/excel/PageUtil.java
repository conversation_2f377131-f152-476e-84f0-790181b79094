package com.xiaoshan.edu.openclass.excel;

import lombok.Data;
import start.framework.commons.result.PageResponse;
import start.framework.commons.result.QueryResult;

import java.util.ArrayList;
import java.util.List;

@Data
public class PageUtil {


	public static <E> List<E> getSubList(List<E> list, int pageIndex, int pageSize){
		if (list == null){
			return new ArrayList<>();
		}

		int size = list.size();

		if (size == 0){
			return list;
		}

		int fromIndex = (pageIndex - 1) * pageSize;

		int toIndex = pageIndex * pageSize;

		if (fromIndex < 0){
			fromIndex = 0;
		}

		if (fromIndex > size){
			return new ArrayList<>();
		}

		if (toIndex < 0 || toIndex > size){
			toIndex = size;
		}

		return list.subList(fromIndex,toIndex);

	}

	public static <E> QueryResult<List<E>> getQueryResult(List<E> list, int pageIndex, int pageSize){
		List<E> subList = getSubList(list, pageIndex, pageSize);

		PageResponse.PageInfo pageInfo = new PageResponse.PageInfo();
		pageInfo.setTotalCount(list.size());

		QueryResult<List<E>> queryResult = new QueryResult<>();
		queryResult.setResult(subList);
		queryResult.setPageInfo(pageInfo);

		return queryResult;
	}


}

