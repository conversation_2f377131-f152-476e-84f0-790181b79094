package com.xiaoshan.edu.openclass.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.IndexesUnion;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.persistence.source.jdbc.script.annotations.indexes.UniqueUnion;

import java.util.Objects;

@Getter@Setter@ToString
@Entity("cloudRoom")
@Table("ope_cloud_room")
@TableDef(comment = "云课堂教室", indexes = @IndexesUnion(
		unique = @UniqueUnion(fields = {"buildingName", "floorName", "roomName", "deleted"})))
public class CloudRoomDO extends BaseV2Ext {

	private static final long serialVersionUID = 1L;
	
	public CloudRoomDO(){}

	public CloudRoomDO(String buildingName, String floorName, String roomName) {
		this.buildingName = buildingName;
		this.floorName = floorName;
		this.roomName = roomName;
	}

	@ColumnDef(comment = "楼宇名称")
	private String buildingName;

	@ColumnDef(comment = "楼层名称")
	private String floorName;

	@ColumnDef(comment = "房间名称")
	private String roomName;


	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		CloudRoomDO that = (CloudRoomDO) o;
		return Objects.equals(buildingName, that.buildingName) && Objects.equals(floorName, that.floorName) && Objects.equals(roomName, that.roomName);
	}

	@Override
	public int hashCode() {
		return Objects.hash(buildingName, floorName, roomName);
	}
}
