package com.xiaoshan.edu.openclass.excel;

import java.util.ArrayList;
import java.util.List;

public class ExcelHeadsUtils {

    // 公开课统计--教师、科目--详情excel表头
    public static List<List<String>> statisticDetailHead(String title) {
        List<List<String>> list = new ArrayList<>();
        String[] head = {"序号","课题","科目","教师","公开课类型","上课日期","节次","上课班级","上课地点"};
        for (String s : head) {
            ArrayList<String> l = new ArrayList<>();
            l.add(title);
            l.add(s);
            list.add(l);
        }
        return list;
    }

    // 公开课统计--教师--excel表头
    public static List<List<String>> statisticTeacherHead(String title) {
        List<List<String>> list = new ArrayList<>();
        String[] head = {"序号","教师","部门","总计（节）","校内公开课（节）","校际公开课（节）","校内讲座（节）", "云课堂（节）"};
        for (String s : head) {
            ArrayList<String> l = new ArrayList<>();
            l.add(title);
            l.add(s);
            list.add(l);
        }
        return list;
    }

    // 公开课统计--科目--excel表头
    public static List<List<String>> statisticSubjectHead(String title) {
        List<List<String>> list = new ArrayList<>();
        String[] head = {"序号","科目","总计（节）","校内公开课（节）","校际公开课（节）","校内讲座（节）", "云课堂（节）"};
        for (String s : head) {
            ArrayList<String> l = new ArrayList<>();
            l.add(title);
            l.add(s);
            list.add(l);
        }
        return list;
    }
}
