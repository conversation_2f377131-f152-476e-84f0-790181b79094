package com.xiaoshan.edu.timetable.utils;

/**
 * <AUTHOR>
 */
public class TableMessage {

    public static String C10000 = "%s,%s,课表不存在";
    public static String C10001 = "教室名称不能重复";
    public static String C10002 = "班级名称不能重复";
    public static String C10003 = "%s教室名称与“建筑空间”中的名称不匹配";
    public static String C10004 = "课程简称与“课程管理”中的简称不匹配";
    public static String C10005 = "%s,班级名称与“班级数据”中的名称不匹配";
    public static String C10007 = "%s,教师名称与“教师数据”中的名称不匹配";
    public static String C10008 = "%s,学生姓名、学号与“学生数据”中的不匹配";
    public static String C10011 = "%s学生所在的班级%s与基础数据不一致";
    public static String C10014 = "课程名称与“课程管理”中的名称不匹配";
    public static String C10015 = "同一张课程表中学生信息不能重复（学号不可重复）";
    public static String C10016 = "%s,课程名称不能重复";
    public static String C10017 = "一个教师只能上一门课程，不可同时出现在两个课程下";
    public static String C10018 = "存在非%s届的班级数据";
    public static String C10020 = "班级名称重复，一个班级只能上传一张课程表";
    public static String C10021 = "Sheet名称与“课程管理”中的课程名称不匹配";
    public static String C10022 = "%s,教师不能进行多科目任课";
    public static String C10025 = "%s,教师名称与“教师课表”中的名称不匹配";
    public static String C10028 = "一个教师只能上一门课程，不可同时出现在两个课程下";
    public static String C10032 = "%s,班级信息不能为空";
    public static String C10033 = "%s,课程下不能有任课老师";
    public static String C10034 = "%s,教师课表下没有任何课程";
    public static String C10035 = "%s,为特殊课程不能出现在班级课表中";
    public static String C10036 = "%s,上课地点:%s没有安排对应老师";
    public static String C10037 = "%s,走班课必须设置对应的上课地点";
    public static String C10038 = "%s,本班上课无须设置对应的上课地点";
    public static String C10039 = "列名称必须定义为走班地点";
    public static String C10040 = "%s,走班地点:%s找不到对应的教室或班级";
    public static String C10041 = "%s科目,%s命名请遵循姓名+教师课表";
    public static String C10042 = "%s学生的%s课程的走班地点不允许是本班班级";
    public static String C10043 = "%s教师对应的上课地点为空";
    public static String C10044 = "%s 存在重复课程";
    public static String C10045 = "%s该地点下需维护至少一名教师";
    public static String C10046 = "%s 存在重复地点";

}
