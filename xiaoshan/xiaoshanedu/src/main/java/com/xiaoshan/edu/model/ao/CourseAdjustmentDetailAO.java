package com.xiaoshan.edu.model.ao;

import com.xiaoshan.basic.vo.ItemConfigVO;
import com.xiaoshan.edu.ao.courseadjustment.StartFlowAO;
import com.xiaoshan.edu.courseadjustment.utils.CourseAdjustmentUtils;
import com.xiaoshan.edu.courseadjustment.utils.CourseTimeUtils;
import com.xiaoshan.edu.enums.timetable.AbbTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.model.vo.WeekConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import start.magic.core.valid.annotation.BooleanValid;
import start.magic.core.valid.annotation.Enum;
import start.magic.core.valid.annotation.NotEmpty;
import start.magic.core.valid.annotation.NotNull;
import start.magic.core.valid.annotation.number.LongValid;
import start.magic.persistence.annotation.Column;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CourseAdjustmentDetailAO {

    @ApiModelProperty("调课课程")
    @NotNull
    @NotEmpty
    private String courseName;

    @ApiModelProperty("调课时间（课程时间：如：第一周周一第一节）")
    @NotNull
    @NotEmpty
    private String courseTime;

    @ApiModelProperty("调课节次开始时间")
    @NotNull
    @NotEmpty
    private String courseStartTime;

    @ApiModelProperty("调课节次开始时间")
    @NotNull
    @NotEmpty
    private String courseEndTime;

    @ApiModelProperty("班级或教室id（教室以ROOM结尾）")
    @NotNull
    @NotEmpty
    private String courseRoomId;

    @ApiModelProperty("班级或教室")
    @NotNull
    @NotEmpty
    private String courseRoom;

    @ApiModelProperty("教师id")
    @NotNull
    @LongValid
    private Long teacherId;

    @ApiModelProperty("被调课程【非自修时必填】")
    private String adjustedCourseName;

    @ApiModelProperty("被调时间（被调课程时间：如：第一周周一第一节）【非自修时必填】")
    private String adjustedCourseTime;

    @ApiModelProperty("被调课班级或教室id【非自修时必填】")
    private String adjustedCourseRoomId;

    @ApiModelProperty("被调课班级或教室【非自修时必填】")
    private String adjustedCourseRoom;

    @ApiModelProperty("被调教师id【非自修时必填】")
    private Long adjustedTeacherId;

    @ApiModelProperty("是否自修")
    @BooleanValid
    @NotNull
    private Boolean isSelfStudy;

    @ApiModelProperty("调课课表id【用于标识学期、年级】")
    private Long curriculumTableId;

    @ApiModelProperty("被调课课表id【用于标识学期、年级】")
    private Long adjustedCurriculumTableId;

    @ApiModelProperty("流程，若流程未开启，则传null或不传")
    @Column("startFlowAO")
    private StartFlowAO startFlowAo;

    @ApiModelProperty("是否由管理端发起")
    @BooleanValid
    @NotNull
    private Boolean isAdminApply;

    @ApiModelProperty("（二）调课父Id,班级课表数据里的adjustmentId,没有则不传")
    private Long fatherId;

    @ApiModelProperty("（二）调课课程类型,班级课表数据里的type（调代类型）")
    @Enum
    @NotNull
    private AbbTypeEnum type;

    @ApiModelProperty("（二）被调课父Id,教师课表数据里的adjustmentId,没有则不传")
    private Long adjustedFatherId;

    @ApiModelProperty("（二）被调课课程类型,教师课表数据里的type（调代类型），如果不是自修必传")
    @Enum
    private AbbTypeEnum adjustedType;


    /**
     * 获取周次中文信息
     *
     * @return
     */
    public String getChineseWeekNumberOfAdjustedCourseTime() {
        return CourseTimeUtils.getChineseWeekNumber(adjustedCourseTime);
    }

    /**
     * 获取周中文信息
     *
     * @return
     */
    public String getChineseWeekNumberOfCourseTime() {
        return CourseTimeUtils.getChineseWeekNumber(courseTime);
    }

    /**
     * 是否有调休，如果没有保存原数据，有保存调休前数据
     *
     * @param items
     * @param weekMap
     * @return
     */
    public String getBeforeOriginCourseTime(List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        Map<WeekEnum, String> currentWeekMap = CourseAdjustmentUtils.getWeekEnumStringMap(weekMap, getWeekNumberOfCourseTime());
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = CourseAdjustmentUtils.getWeekEnumListMap(currentWeekMap, items);
        if (!compensatoryMap.isEmpty()) {
            for (Map.Entry<WeekEnum, List<WeekEnum>> weekEnumListEntry : compensatoryMap.entrySet()) {
                for (WeekEnum weekEnum : weekEnumListEntry.getValue()) {
                    if (weekEnum.equals(getDayOfWeekOfCourseTime())) {
                        WeekEnum key = weekEnumListEntry.getKey();
                        return getCourseTime().substring(0, getIndexOfWeekOfCourseTime() + 1) + WeekEnum.getDayOfWeek(key) + getCourseTime().substring(getIndexOfWeekOfCourseTime() + 3);
                    }
                }
            }
        }
        return getCourseTime();
    }

    /**
     * 是否有调休，如果没有保存原数据，有保存调休前数据
     *
     * @param items
     * @param weekMap
     * @return
     */
    public String getAdjustedBeforeOriginCourseTime(List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        Map<WeekEnum, String> currentWeekMap = CourseAdjustmentUtils.getWeekEnumStringMap(weekMap, getWeekNumberOfAdjustedCourseTime());
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = CourseAdjustmentUtils.getWeekEnumListMap(currentWeekMap, items);
        if (!compensatoryMap.isEmpty()) {
            for (Map.Entry<WeekEnum, List<WeekEnum>> weekEnumListEntry : compensatoryMap.entrySet()) {
                for (WeekEnum weekEnum : weekEnumListEntry.getValue()) {
                    if (weekEnum.equals(getDayOfWeekOfCourseTime())) {
                        WeekEnum key = weekEnumListEntry.getKey();
                        return getAdjustedCourseTime().substring(0, getIndexOfWeekAdjustedCourseTime() + 1) + WeekEnum.getDayOfWeek(key) + getAdjustedCourseTime().substring(getIndexOfWeekAdjustedCourseTime() + 3);
                    }
                }
            }
        }
        return getAdjustedCourseTime();
    }

    /**
     * 是否有调休，如果没有保存原数据，有保存调休前数据
     *
     * @param number
     * @param items
     * @param weekMap
     * @return
     */
    public String getAdjustedBeforeOriginCourseTime(Integer number, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        Map<WeekEnum, String> currentWeekMap = CourseAdjustmentUtils.getWeekEnumStringMap(weekMap, getWeekNumberOfAdjustedCourseTimeByNumber(number));
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = CourseAdjustmentUtils.getWeekEnumListMap(currentWeekMap, items);
        if (!compensatoryMap.isEmpty()) {
            for (Map.Entry<WeekEnum, List<WeekEnum>> weekEnumListEntry : compensatoryMap.entrySet()) {
                for (WeekEnum weekEnum : weekEnumListEntry.getValue()) {
                    if (weekEnum.equals(getDayOfWeekOfAdjustedCourseTime())) {
                        WeekEnum key = weekEnumListEntry.getKey();
                        return getAdjustedCourseTimeByNumber(number).substring(0, getIndexOfWeekOfAdjustedCourseTimeByNumber(number) + 1) + WeekEnum.getDayOfWeek(key) + getAdjustedCourseTimeByNumber(number).substring(getIndexOfWeekOfAdjustedCourseTimeByNumber(number) + 3);
                    }
                }
            }
        }
        return getAdjustedCourseTimeByNumber(number);
    }

    /**
     * 获取循环次数对应index
     *
     * @param number
     * @return
     */
    public Integer getIndexOfWeekOfAdjustedCourseTimeByNumber(Integer number) {
        return getAdjustedCourseTimeByNumber(number).indexOf("周周");
    }

    /**
     * 获取周次信息
     *
     * @param number
     * @return
     */
    public Integer getWeekNumberOfAdjustedCourseTimeByNumber(Integer number) {
        return CourseAdjustmentUtils.chinese2Number(getChineseWeekNumberOfAdjustedCourseTimeByNumber(number));
    }

    /**
     * 获取周中文信息
     *
     * @param number
     * @return
     */
    public String getChineseWeekNumberOfAdjustedCourseTimeByNumber(Integer number) {
        return getAdjustedCourseTimeByNumber(number).substring(1, getIndexOfWeekAdjustedCourseTime());
    }

    /**
     * 根据循环次数获取被调课时间
     *
     * @param number
     * @return
     */
    public String getAdjustedCourseTimeByNumber(Integer number) {
        Integer currentWeekNumber = getWeekNumberOfAdjustedCourseTime() + number;
        String weekNumberStr = CourseAdjustmentUtils.number2Chinese(currentWeekNumber);
        return "第" + weekNumberStr + "周" + getChineseDayOfWeekOfAdjustedCourseTime() + "第" + getSectionOfAdjustedCourseTime() + "节";
    }

    /**
     * 获取周几字符串形式
     *
     * @return
     */
    public String getChineseDayOfWeekOfAdjustedCourseTime() {
        return CourseTimeUtils.getChineseDayOfWeek(adjustedCourseTime);
    }

    /**
     * 是否有调休，如果没有保存原数据，有保存调休前数据
     *
     * @param number
     * @param items
     * @param weekMap
     * @return
     */
    public String getBeforeOriginCourseTimeByNumber(Integer number, List<ItemConfigVO> items, Map<Integer, WeekConfigVO> weekMap) {
        Map<WeekEnum, String> currentWeekMap = CourseAdjustmentUtils.getWeekEnumStringMap(weekMap, getWeekNumberOfCourseTimeByNumber(number));
        Map<WeekEnum, List<WeekEnum>> compensatoryMap = CourseAdjustmentUtils.getWeekEnumListMap(currentWeekMap, items);
        if (!compensatoryMap.isEmpty()) {
            for (Map.Entry<WeekEnum, List<WeekEnum>> weekEnumListEntry : compensatoryMap.entrySet()) {
                for (WeekEnum weekEnum : weekEnumListEntry.getValue()) {
                    if (weekEnum.equals(getDayOfWeekOfCourseTime())) {
                        WeekEnum key = weekEnumListEntry.getKey();
                        return getCourseTimeByNumber(number).substring(0, getIndexOfWeekOfCourseTimeByNumber(number) + 1) + WeekEnum.getDayOfWeek(key) + getCourseTimeByNumber(number).substring(getIndexOfWeekOfCourseTimeByNumber(number) + 3);
                    }
                }
            }
        }
        return getCourseTimeByNumber(number);
    }

    /**
     * 获取调课时间
     *
     * @return
     */
    public String getSectionOfAdjustedCourseTime() {
        return adjustedCourseTime.substring(getIndexOfWeekAdjustedCourseTime() + 4, getIndexOfWeekAdjustedCourseTime() + 5);
    }

    /**
     * 获取周几
     *
     * @return
     */
    public WeekEnum getDayOfWeekOfAdjustedCourseTime() {
        return CourseTimeUtils.getDayOfWeek(adjustedCourseTime);
    }

    /**
     * 获取被调整的课程时间
     *
     * @return
     */
    public Integer getIndexOfWeekAdjustedCourseTime() {
        return CourseTimeUtils.getIndexOfWeek(adjustedCourseTime);
    }

    /**
     * 获取周次
     *
     * @return
     */
    public Integer getWeekNumberOfAdjustedCourseTime() {
        return CourseTimeUtils.getWeekNumber(adjustedCourseTime);
    }

    /**
     * 获取时间
     *
     * @return
     */
    public Integer getIndexOfWeekOfCourseTime() {
        return CourseTimeUtils.getIndexOfWeek(courseTime);
    }

    /**
     * 获取周几
     *
     * @return
     */
    public WeekEnum getDayOfWeekOfCourseTime() {
        return CourseTimeUtils.getDayOfWeek(courseTime);
    }

    /**
     * 获取周次信息
     *
     * @param number
     * @return
     */
    public Integer getWeekNumberOfCourseTimeByNumber(Integer number) {
        return CourseAdjustmentUtils.chinese2Number(getChineseWeekNumberOfCourseTimeByNumber(number));
    }

    /**
     * 获取周中文信息
     *
     * @return
     */
    public String getChineseWeekNumberOfCourseTimeByNumber(Integer number) {
        return getCourseTimeByNumber(number).substring(1, getIndexOfWeekOfCourseTimeByNumber(number));
    }

    /**
     * 根据循环次数获取课程时间
     *
     * @param number
     * @return
     */
    public String getCourseTimeByNumber(Integer number) {
        Integer currentWeekNumber = getWeekNumberOfCourseTime() + number;
        String weekNumberStr = CourseAdjustmentUtils.number2Chinese(currentWeekNumber);
        return "第" + weekNumberStr + "周" + getChineseDayOfWeekOfCourseTime() + "第" + getSectionOfCourseTime() + "节";
    }

    /**
     * 获取节次时间
     *
     * @return
     */
    public String getSectionOfCourseTime() {
        return CourseTimeUtils.getSection(courseTime);
    }

    /**
     * 获取周几的字符串形式
     *
     * @return
     */
    public String getChineseDayOfWeekOfCourseTime() {
        return CourseTimeUtils.getChineseDayOfWeek(courseTime);
    }

    /**
     * 获取循环次数对应index
     *
     * @param number
     * @return
     */
    public Integer getIndexOfWeekOfCourseTimeByNumber(Integer number) {
        return getCourseTimeByNumber(number).indexOf("周周");
    }

    /**
     * 获取周次
     *
     * @return
     */
    public Integer getWeekNumberOfCourseTime() {
        return CourseTimeUtils.getWeekNumber(courseTime);
    }



}
