package com.xiaoshan.edu.courseadjustment.entity;

import com.xiaoshan.edu.enums.courseadjustment.ApprovalState;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import start.framework.entity.BaseV2Ext;
import start.magic.persistence.annotation.Column;
import start.magic.persistence.annotation.Entity;
import start.magic.persistence.annotation.Table;
import start.magic.persistence.source.jdbc.script.annotations.ColumnDef;
import start.magic.persistence.source.jdbc.script.annotations.TableDef;
import start.magic.thirdparty.json.JsonArray;

import java.sql.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity("courseReplace")
@Table("cou_course_replace")
@TableDef(comment = "代课记录表")
public class CourseReplaceDO extends BaseV2Ext {

    private static final long serialVersionUID = 1L;

    public CourseReplaceDO() {
    }

    @ColumnDef(comment = "课程名称")
    @Column("course_name")
    private String courseName;

    @ColumnDef(comment = "代课开始日期")
    @Column("replace_start_date")
    private Date replaceStartDate;

    @ColumnDef(comment = "代课结束日期")
    @Column("replace_end_date")
    private Date replaceEndDate;

    @ColumnDef(comment = "开始日期节次")
    @Column("start_section")
    private String startSection;

    @ColumnDef(comment = "结束日期节次")
    @Column("end_section")
    private String endSection;

    @ColumnDef(comment = "代课节次")
    private Integer section;

    @ColumnDef(comment = "课程教师")
    @Column("teacher_name")
    private String teacherName;

    @ColumnDef(comment = "教师id")
    @Column("teacher_id")
    private Long teacherId;

    @ColumnDef(comment = "部门")
    @Column("department_name")
    private String departmentName;

    @ColumnDef(comment = "代课教师", isNull = true)
    @Column("replace_teacher_name")
    private String replaceTeacherName;

    @ColumnDef(comment = "代课教师id", isNull = true)
    @Column("replace_teacher_id")
    private Long replaceTeacherId;

    @ColumnDef(comment = "代课老师部门", isNull = true)
    @Column("replaced_department_name")
    private String replacedDepartmentName;

    @ColumnDef(comment = "代课事由")
    private String content;

    @ColumnDef(comment = "图片url列表", isNull = true)
    @Column("pic_urls")
    private JsonArray picUrls;

    @ColumnDef(comment = "审批编号", isNull = true)
    @Column("approval_no")
    private String approvalNo;

    @ColumnDef(comment = "审批状态", defaultValue = "'UNDER_APPROVAL'")
    @Column("approval_state")
    private ApprovalState approvalState;

    @ColumnDef(comment = "是否由教务科安排老师", defaultValue = "'false'")
    @Column("is_academic_affairs_section_arrange")
    private Boolean isAcademicAffairsSectionArrange;

    @ColumnDef(comment = "是否异常【因课表调整导致调代课冲突失效】", defaultValue = "'false'")
    @Column("is_unusual")
    private Boolean isUnusual;

    @ColumnDef(comment = "代课老师课表id【用于代课老师教师课表查询代课信息】", isNull = true)
    @Column("replace_curriculum_table_id")
    private Long replaceCurriculumTableId;
}
