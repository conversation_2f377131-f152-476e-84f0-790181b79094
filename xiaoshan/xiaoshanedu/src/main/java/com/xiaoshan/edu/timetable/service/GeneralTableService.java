package com.xiaoshan.edu.timetable.service;

import java.util.List;
import java.util.Map;

import com.xiaoshan.basic.vo.RestSectionVO;
import com.xiaoshan.edu.ao.timetable.*;
import com.xiaoshan.edu.dto.DataTableDTO;
import com.xiaoshan.edu.dto.TeacherTableDTO;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.model.RoomClassVO;
import com.xiaoshan.edu.timetable.entity.CurriculumTableDO;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.vo.timetable.ClassesVO;
import com.xiaoshan.edu.vo.timetable.GeneralTableVO;
import com.xiaoshan.edu.vo.timetable.LessonPrepareVO;
import com.xiaoshan.edu.vo.timetable.StudentDataVO;
import com.xiaoshan.edu.vo.timetable.StudentTableVO;
import com.xiaoshan.edu.vo.timetable.TeachClassesDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherAbbDetailVO;
import com.xiaoshan.edu.vo.timetable.TeacherDataVO;
import com.xiaoshan.edu.vo.timetable.TeacherTableVO;

import start.framework.service.SqlBaseService;

/**
 * <AUTHOR>
 */
public interface GeneralTableService extends SqlBaseService<GeneralTableDO, Long> {

    /**
     * 查询课表
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    List<GeneralTableDO> queryByCurriculumTableId(Long curriculumTableId);

    /**
     * 查询课表
     *
     * @param curriculumTableIds
     * @return
     */
    List<GeneralTableDO> queryByCurriculumTableIdList(List<Long> curriculumTableIds);

    /**
     * 删除课表
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    int removeByCurriculumTableIdDouble(Long curriculumTableId);

    /**
     * 课表数据对象
     *
     * @param curriculumTableId 课表ID
     * @param weekType          周类型
     * @return
     */
    GeneralTableDO getGeneralTableDO(Long curriculumTableId, WeekTypeEnum weekType);

    /**
     * 获取当前学期所有的课表排除 curriculumTableId
     *
     * @param semesterId        学期ID
     * @param orderNo
     * @param curriculumTableId 课表ID
     * @return
     */
    List<GeneralTableDO> getAllSemesterTable(Long semesterId, Integer orderNo, Long curriculumTableId);

    /**
     * 课表列表
     *
     * @param stuId          学生ID
     * @param teacherId      老师ID
     * @param isClassTeacher 是否为班主任
     * @param only
     * @return
     */
    List<Long> getCurriculumTableIds(Long stuId, Long teacherId, Boolean isClassTeacher, Boolean only);

    /**
     * 课表列表
     *
     * @param stuIds
     * @param teacherIds
     * @return
     */
    List<Long> getCurriculumTableList(List<Long> stuIds, List<Long> teacherIds);

    /**
     * 主课表
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    List<GeneralTableVO> mainTable(Long curriculumTableId);

    /**
     * 班级课表
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    List<GeneralTableVO> classesTable(Long curriculumTableId);

    /**
     * 老师课表
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    List<TeacherTableVO> teacherTable(Long curriculumTableId);

    /**
     * 老师课表
     * @param curriculumTableIds
     * @return
     */
    List<TeacherTableVO> teacherTableBatch(Map<Long, CurriculumTableDO> curriculumTableMap,Map<String, RestSectionVO> restSectionMap,List<Long> curriculumTableIds);

    /**
     * 学生课表
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    List<StudentTableVO> studentTable(Long curriculumTableId);

    /**
     * 总课表
     *
     * @param ao
     * @return
     */
    Map<WeekTypeEnum, DataTableDTO> mainTableDetail(ClassesQuery ao);

    /**
     * 班级课表
     *
     * @param ao
     * @return
     */
    Map<WeekTypeEnum, DataTableDTO> classesTableDetail(ClassesQuery ao);

    /**
     * 老师课表
     *
     * @param ao
     * @return
     */
    Map<WeekTypeEnum, TeacherTableDTO> teacherTableDetail(TeacherQuery ao);

    /**
     * 老师课表
     * @param teacherWeekQuery
     * @return
     */
    Map<WeekTypeEnum, List<TeacherTableDTO>> teacherTableDetailBatch(Map<Long, CurriculumTableDO> curriculumTableMap,List<Long> curriculumTableIds, Map<String, RestSectionVO> restSectionMap, TeacherWeekQuery teacherWeekQuery);

    /**
     * 学生课表
     *
     * @param ao
     * @return
     */
    Map<WeekTypeEnum, DataTableDTO> studentTableDetail(StudentQuery ao);

    /**
     * 学生课表按课表ID
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    Map<Long, Map<WeekTypeEnum, DataTableDTO>> studentTableDetail(Long curriculumTableId);

    /**
     * 学生课表按课表ID
     *
     * @param curriculumTableIds
     * @return
     */
    Map<Long, Map<WeekTypeEnum, DataTableDTO>> batchStudentTableDetail(List<Long> curriculumTableIds);

    /**
     * 学生列表按班级
     *
     * @param ao
     * @return
     */
    Map<WeekTypeEnum, List<TeachClassesDetailVO>> studentListByClassId(StudentListQuery ao);

    /**
     * 学生列表按教室
     *
     * @param ao
     * @return
     */
    Map<WeekTypeEnum, List<TeachClassesDetailVO>> studentListByClassOrRoomId(StudentListQuery ao);

    /**
     * 当前学期获取所有班级或教室
     *
     * @param semesterId  学期ID
     * @param orderNo
     * @param isClassRoom 是否为教室
     * @return
     */
    Map<WeekTypeEnum, List<ClassesVO>> allClassList(Long semesterId, Integer orderNo, Boolean isClassRoom);

    /**
     * 根据课表ID获取对应的周->班级信息
     *
     * @param curriculumTableIds 课表ID列表
     * @param isClassRoom        是否为教室
     * @return
     */
    Map<WeekTypeEnum, List<ClassesVO>> allClassList(List<Long> curriculumTableIds, Boolean isClassRoom);

    /**
     * 获取当前课表的所有教室
     *
     * @param curriculumTableId 课表ID
     * @return
     */
    List<RoomClassVO> getAllRoomClass(Long curriculumTableId);

    /**
     * 班级周课表
     *
     * @param classId    班级ID
     * @param weekNumber 周次
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> classTableWeekDetail(Long classId, Integer weekNumber);

    /**
     * 班级周课表
     * @param classId
     * @param weekNumber
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> classTableWeekDetailBatch(Long classId, Integer weekNumber);

    /**
     * 教室周课表
     *
     * @param classRoomId 教室ID
     * @param weekNumber  周次
     * @param cache       是否缓存
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> classRoomTableWeekDetail(Long classRoomId, Integer weekNumber, Boolean cache);

    /**
     * 教师周课表
     *
     * @param teacherId  教师ID
     * @param weekNumber 周次
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> teacherTableWeekDetail(Long teacherId, Integer weekNumber);

    /**
     * 批量教师周课表
     * @param teacherIds
     * @param weekNumbers
     * @return
     */
    Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetailBatch(List<Long> teacherIds, List<Integer> weekNumbers);

    /**
     * 老师周课表
     *
     * @param semesterId 学期ID
     * @param teacherId  老师ID
     * @param weekNumber 周次
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> teacherTableWeekDetail(Long semesterId, Long teacherId, Integer weekNumber);

    /**
     * 老师周课表
     * @param semesterId
     * @param teacherIds
     * @param weekNumbers
     * @return
     */
    Map<Long, Map<Integer, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>>> teacherTableWeekDetailBatch(Long semesterId,List<Long> teacherIds, List<Integer> weekNumbers);

    /**
     * 老师周课表按学期
     *
     * @param semesterId 学期ID
     * @param teacherId  老师ID
     * @return
     */
    String teacherTableSemesterDetailAbbs(Long semesterId, Long teacherId);

    /**
     * 老师周课表
     *
     * @param semesterId 学期ID
     * @param teacherId  老师ID
     * @return
     */
    Map<WeekTypeEnum, Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>>> teacherTableSemesterDetail(Long semesterId, Long teacherId);

    /**
     * 学生周课表
     *
     * @param stuId      学生ID
     * @param weekNumber 周次
     * @return
     */
    Map<WeekEnum, Map<String, List<TeacherAbbDetailVO>>> studentTableWeekDetail(Long stuId, Integer weekNumber);

    /**
     * 课表
     *
     * @param semesterId 学期ID
     * @param orderNo
     * @return
     */
    List<LessonPrepareVO> getLessonPrepare(Long semesterId, Integer orderNo);

    /**
     * 工作量统计
     *
     * @param semesterId
     * @param teacherId
     * @param weekNumber
     * @return
     */
    Long workload(Long semesterId, Long teacherId, Integer weekNumber);

    /**
     * 当前学期的老师所有教学数据
     *
     * @param semesterId
     * @return
     */
    List<TeacherDataVO> teacherData(Long semesterId);

    /**
     * 当前所有学生走班数据
     *
     * @param semesterId
     * @return
     */
    List<StudentDataVO> studentData(Long semesterId);

}
