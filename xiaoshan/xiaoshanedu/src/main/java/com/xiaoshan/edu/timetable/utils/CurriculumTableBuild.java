package com.xiaoshan.edu.timetable.utils;

import com.alibaba.fastjson.JSON;
import com.xiaoshan.basic.vo.ClassVO;
import com.xiaoshan.basic.vo.RoomVO;
import com.xiaoshan.basic.vo.StudentVO;
import com.xiaoshan.basic.vo.TeacherVO;
import com.xiaoshan.common.CodeRes;
import com.xiaoshan.common.Constant;
import com.xiaoshan.edu.ao.timetable.CurriculumTableAO;
import com.xiaoshan.edu.dto.*;
import com.xiaoshan.edu.enums.timetable.CurriculumTableTypeEnum;
import com.xiaoshan.edu.enums.timetable.WeekEnum;
import com.xiaoshan.edu.enums.timetable.WeekTypeEnum;
import com.xiaoshan.edu.timetable.entity.GeneralTableDO;
import com.xiaoshan.edu.timetable.utils.check.CheckData;
import com.xiaoshan.edu.timetable.utils.check.StudentCheck;
import lombok.extern.slf4j.Slf4j;
import start.framework.commons.exception.BusinessException;
import start.magic.utils.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class CurriculumTableBuild {

    public static final Map<Integer, WeekEnum> WEEKMAP = WeekEnum.getWeekMap();

    private List<StudentVO> students;

    private Map<String, TeacherVO> teacherMap;
    private Map<String, ClassVO> classesMap;
    private Map<String, RoomVO> roomMap;

    private Map<Long, ClassVO> classesIdMap;
    private Map<Long, RoomVO> roomIdMap;
    private Set<Long> teacherIds = new HashSet<>();

    private GeneralTableDO generalTable;
    private CheckData cddata;

    public CurriculumTableBuild(CurriculumTableAO ao, Map<String, TempCurriculumTableDTO> cache, WeekTypeEnum weekType,
                                List<StudentVO> students,
                                List<TeacherVO> teachers,
                                List<ClassVO> classes,
                                List<RoomVO> rooms,
                                Map<String, String> curriculumAliasMap, GeneralTableDO generalTable) {
        cddata = new CheckData();
        cddata.setTable(ao);
        cddata.setMainTable(new ArrayList<>());
        cddata.setClassTable(new ArrayList<>());
        cddata.setTeacherTable(new LinkedHashMap<>());
        cddata.setStudentList(new ArrayList<>());
        cddata.setClassTeacher(new LinkedHashMap<>());
        cddata.setClassRoomMap(new LinkedHashMap<>());
        cddata.setClassLeaveMap(new LinkedHashMap<>());
        cddata.setCurriculumAliasMap(curriculumAliasMap);
        String mainTableKey = CurriculumTableTypeEnum.GENERAL + ":" + weekType;
        if (!cache.containsKey(mainTableKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.GENERAL.getDescription(), WeekTypeEnum.getWeekTypeName(weekType));
        }
        cddata.setMainTableTemp(cache.get(mainTableKey));
        String teacherKey = CurriculumTableTypeEnum.TEACHER + ":" + weekType;
        if (!cache.containsKey(teacherKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.TEACHER.getDescription(), WeekTypeEnum.getWeekTypeName(weekType));
        }
        cddata.setTeacherTableTemp(cache.get(teacherKey));
        String classesKey = CurriculumTableTypeEnum.CLASSES + ":" + weekType;
        if (!cache.containsKey(classesKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.CLASSES.getDescription(), WeekTypeEnum.getWeekTypeName(weekType));
        }
        cddata.setClassTableTemp(cache.get(classesKey));
        String studentKey = CurriculumTableTypeEnum.STUDENT + ":" + weekType;
        if (!cache.containsKey(studentKey)) {
            RuleUtils.throwMessage(TableMessage.C10000, CurriculumTableTypeEnum.STUDENT.getDescription(), WeekTypeEnum.getWeekTypeName(weekType));
        }
        cddata.setStudentTableTemp(cache.get(studentKey));

        this.students = students;

        classesMap = classes.stream().collect(Collectors.toMap(ClassVO::getName, Function.identity(), (t, t1) -> t1));
        roomMap = rooms.stream().collect(Collectors.toMap(RoomVO::getName, Function.identity(), (t, t1) -> t1));
        teacherMap = teachers.stream().collect(Collectors.toMap(TeacherVO::getName, Function.identity(), (t, t1) -> t1));
        classesIdMap = classes.stream().collect(Collectors.toMap(ClassVO::getId, Function.identity()));
        roomIdMap = rooms.stream().collect(Collectors.toMap(RoomVO::getId, Function.identity()));

        this.generalTable = generalTable;
    }

    private void analysisMainTable() {
        Map<String, DataTableDTO> listMaps = new LinkedHashMap<>();
        for (List<Map<Integer, String>> items : cddata.getMainTableTemp().getData().values()) {
            Map<Integer, String> headerMap = new LinkedHashMap<>();
            Map<Integer, String> headMap = items.get(0);
            for (int i = 2; i < headMap.size(); i++) {
                String classOrRoomName = headMap.get(i);
                if (classesMap.containsKey(classOrRoomName) ||
                        roomMap.containsKey(classOrRoomName)) {
                    headerMap.put(i, ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName));
                } else {
                    throw new BusinessException(CodeRes.CODE_700007);
                }
            }
            String lastWeekName = null;
            for (int i = 1; i < items.size(); i++) {
                Map<Integer, String> data = items.get(i);
                String weekName = data.get(0);
                if (!StringUtils.isEmpty(weekName)) {
                    lastWeekName = weekName;
                }
                WeekEnum week = WeekEnum.getDayOfWeek(lastWeekName);
                String section = data.get(1);
                for (Integer key : headerMap.keySet()) {
                    if (data.size() > key) {
                        String classOrRoomId = headerMap.get(key);
                        DataTableDTO dt = listMaps.get(classOrRoomId);
                        if (dt == null) {
                            dt = new DataTableDTO();
                            dt.setClassOrRoomId(classOrRoomId);
                            dt.setClassOrRoomName(ClassTableUtils.getClassOrRoomName(classesIdMap, roomIdMap, classOrRoomId));
                            listMaps.put(classOrRoomId, dt);
                        }
                        String abbreviation = data.get(key);
                        if (StringUtils.isEmpty(abbreviation)) {
                            continue;
                        }
                        Map<WeekEnum, Map<String, String>> detail = dt.getDetail();
                        if (detail == null) {
                            detail = new LinkedHashMap<>();
                            dt.setDetail(detail);
                        }
                        Map<String, String> numMaps = detail.computeIfAbsent(week, k -> new LinkedHashMap<>());
                        numMaps.put(section, abbreviation);
                    }
                }
            }
            for (String key : listMaps.keySet()) {
                cddata.getMainTable().add(listMaps.get(key));
            }
        }
        for (DataTableDTO dto : cddata.getMainTable()) {
            if (ClassTableUtils.isClassRoom(dto.getClassOrRoomId())) {
                for (WeekEnum week : dto.getDetail().keySet()) {
                    Map<String, String> detail = dto.getDetail().get(week);
                    for (String section : detail.keySet()) {
                        String abb = detail.get(section);
                        if (RuleUtils.ignoreCourse(abb)) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getMainTableTemp(), "%s教室%s第%s节%s课程不允许走班", dto.getClassOrRoomName(), WeekEnum.getDayOfWeek(week), section, RuleUtils.getCourseName(abb, cddata.getCurriculumAliasMap()));
                        }
                    }
                }
            }
        }
    }

    /**
     * 解析教师课表
     *
     * @param allSemesterTable
     */
    public void analysisTeacherTable(List<GeneralTableDO> allSemesterTable) {
        for (String key : cddata.getTeacherTableTemp().getData().keySet()) {
            List<Map<Integer, String>> items = cddata.getTeacherTableTemp().getData().get(key);
            Map<Integer, String> courseData = items.get(1);
            for (int x = 2; x < items.size(); x++) {
                Map<Integer, String> data = items.get(x);
                String classOrRoomName = data.get(0);
                if (!StringUtils.isEmpty(classOrRoomName)) {
                    String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                    if (classOrRoomId != null) {
                        for (int i = 1; i < data.size(); i++) {
                            String courseName = courseData.get(i);
                            String abbreviation = cddata.getCurriculumAliasMap().get(courseName);
                            List<TeacherTableDTO> teacherDtos = cddata.getTeacherTable().computeIfAbsent(abbreviation, k -> new ArrayList<>());
                            String teacherName = data.get(i);
                            if (!StringUtils.isEmpty(teacherName)) {
                                TeacherVO teacher = teacherMap.get(teacherName);
                                if (Objects.nonNull(teacher)) {
                                    TeacherTableDTO dt = TeacherTableUtils.getTeacherTable(cddata.getTeacherTable(), abbreviation, teacher.getId());
                                    if (dt == null) {
                                        teacherIds.add(teacher.getId());
                                        dt = new TeacherTableDTO();
                                        dt.setTeacherId(teacher.getId());
                                        dt.setTeacherName(teacherName);
                                        dt.setDetail(new LinkedHashMap<>());
                                        if (generalTable != null) {
                                            //同一老师单双周教不同课程
                                            if (TeacherTableUtils.checkTeacherIdDiffAbb(generalTable.getTeacherTable(), dt.getTeacherId(), abbreviation)) {
                                                throw new BusinessException(CodeRes.CODE_700013, dt.getTeacherName());
                                            }
                                        }
                                        teacherDtos.add(dt);
                                    }
                                    DataTableDTO dataTable = MainTableUtils.getDataTableDTO(cddata.getMainTable(), classOrRoomId);
                                    if (dataTable != null) {
                                        boolean flag = false;
                                        for (WeekEnum week : dataTable.getDetail().keySet()) {
                                            Map<String, String> detail = dataTable.getDetail().get(week);
                                            for (String section : detail.keySet()) {
                                                String abb = detail.get(section);
                                                if (abb.equals(abbreviation)) {
                                                    flag = true;
                                                    Map<String, String> sectionDetail = dt.getDetail().get(week);
                                                    if (sectionDetail == null) {
                                                        sectionDetail = new LinkedHashMap<>();
                                                        dt.getDetail().put(week, sectionDetail);
                                                    } else {
                                                        String clsId = sectionDetail.get(section);
                                                        if (clsId != null) {
                                                            if (!classOrRoomId.equals(clsId)) {
                                                                RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s教师 %s第%s节同一时间在不同地点上课", teacherName, WeekEnum.getDayOfWeek(week), section);
                                                            }
                                                        }
                                                    }
                                                    sectionDetail.put(section, classOrRoomId);
                                                    // 每周每节次对应的(班级或教室名称)去总表校验是否全部匹配
                                                    if (!MainTableUtils.checkClassOrRoomIdWeekSectionAbb(cddata.getMainTable(), classOrRoomId, week, section, abbreviation)) {
                                                        RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "科目%s,%s老师的%s上课地点总表不存在", courseName, dt.getTeacherName(), classOrRoomName);
                                                    }
                                                    //同一时间，不同老师，同一个地点上课
                                                    if (TeacherTableUtils.checkEquals(dt.getTeacherId(), cddata.getTeacherTable(), week, section, classOrRoomId)) {
                                                        RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), CodeRes.CODE_700008);
                                                    }
                                                    //跨年级校验
                                                    for (GeneralTableDO t : allSemesterTable) {
                                                        if (TeacherTableUtils.checkTeacherIdDiffAbb(t.getTeacherTable(), dt.getTeacherId(), abbreviation)) {
                                                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "同学期跨年级,同一个老师:" + dt.getTeacherName() + ",只能教同一门课程");
                                                        }
                                                        if (TeacherTableUtils.checkWeekSectionTeacherId(t.getTeacherTable(), week, section, dt.getTeacherId())) {
                                                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "同学期跨年级,同一个老师，不能同一时间上不同年级的课(" + dt.getTeacherName() + " " + WeekEnum.getDayOfWeek(week) + " 第" + section + "节)");
                                                        }
                                                        if (TeacherTableUtils.checkWeekSectionClassOrRoomeId(t.getTeacherTable(), week, section, classOrRoomId)) {
                                                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "同学期跨年级,一个班级或教室不能同一时间被不同老师上课(" + dt.getTeacherName() + " " + WeekEnum.getDayOfWeek(week) + " 第" + section + "节)");
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (!flag) {
                                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s教师在总表%s下未匹配到课程，请至少排一节课", dt.getTeacherName(), classOrRoomName);
                                        }
                                    } else {
                                        RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s教师 %s上课地点总表不存在", dt.getTeacherName(), classOrRoomName);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        // 遍历总表每周节次对应的课程及(班级或教室名称)与教师对应的排课数据是否全部匹配
        for (DataTableDTO dto : cddata.getMainTable()) {
            for (WeekEnum week : dto.getDetail().keySet()) {
                Map<String, String> detail = dto.getDetail().get(week);
                for (String section : detail.keySet()) {
                    String abbreviation = detail.get(section);
                    if (!RuleUtils.ignoreCourse(abbreviation)) {
                        if (!TeacherTableUtils.check(cddata.getTeacherTable(), week, section, abbreviation, dto.getClassOrRoomId())) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getMainTableTemp(), "%s %s第%s节%s课程下无上课老师",
                                    dto.getClassOrRoomName(), WeekEnum.getDayOfWeek(week), section, RuleUtils.getCourseName(abbreviation, cddata.getCurriculumAliasMap()));
                        }
                    }
                }
            }
        }
    }

    @Deprecated
    public void analysisTeacherTable1(List<GeneralTableDO> allSemesterTable) {
        Map<Integer, WeekEnum> weekMap = WeekEnum.getWeekMap();
        for (String courseName : cddata.getTeacherTableTemp().getData().keySet()) {
            String abbreviation = cddata.getCurriculumAliasMap().get(courseName);
            if (!MainTableUtils.checkAbb(cddata.getMainTable(), abbreviation)) {
                RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s课程与总课表不匹配", courseName);
            }
            TeacherTableDTO dt = null;
            List<Map<Integer, String>> items = cddata.getTeacherTableTemp().getData().get(courseName);
            for (int row = 1; row <= items.size(); row++) {
                Map<Integer, String> data = items.get(row - 1);
                if (row % 11 == 1) {
                    dt = new TeacherTableDTO();
                    String teacherName = data.get(0);
                    teacherName = teacherName.substring(0, teacherName.length() - 4);
                    if (!teacherMap.containsKey(teacherName)) {
                        RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), TableMessage.C10025, teacherName);
                    }
                    dt.setTeacherId(teacherMap.get(teacherName).getId());
                    dt.setTeacherName(teacherName);
                    dt.setDetail(new LinkedHashMap<>());
                    if (teacherIds.contains(dt.getTeacherId())) {
                        throw new BusinessException(CodeRes.CODE_700007);
                    }
                    teacherIds.add(dt.getTeacherId());
                } else if (row % 11 == 2) {
                    List<TeacherTableDTO> list = cddata.getTeacherTable().computeIfAbsent(abbreviation, k -> new ArrayList<>());
                    list.add(dt);
                    if (generalTable != null) {
                        //同一老师单双周教不同课程
                        if (TeacherTableUtils.checkTeacherIdDiffAbb(generalTable.getTeacherTable(), dt.getTeacherId(), abbreviation)) {
                            throw new BusinessException(CodeRes.CODE_700013, dt.getTeacherName());
                        }
                    }
                } else {
                    int r = 8;
                    String section = data.get(0);
                    for (int i = 1; i < r; i++) {
                        String classOrRoomName = data.get(i);
                        if (StringUtils.isEmpty(classOrRoomName)) {
                            continue;
                        }
                        WeekEnum week = weekMap.get(i);
                        String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                        // 每周每节次对应的(班级或教室名称)去总表校验是否全部匹配
                        if (!MainTableUtils.checkClassOrRoomIdWeekSectionAbb(cddata.getMainTable(), classOrRoomId, week, section, abbreviation)) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "科目%s,%s老师的%s上课地点总表不存在", courseName, dt.getTeacherName(), classOrRoomName);
                        }
                        //同一时间，不同老师，同一个地点上课
                        if (TeacherTableUtils.checkEquals(dt.getTeacherId(), cddata.getTeacherTable(), week, section, classOrRoomId)) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), CodeRes.CODE_700008);
                        }
                        Map<WeekEnum, Map<String, String>> detail = dt.getDetail();
                        Map<String, String> detailMap = detail.computeIfAbsent(week, k -> new LinkedHashMap<>());
                        detailMap.put(section, classOrRoomId);
                        //跨年级校验
                        for (GeneralTableDO t : allSemesterTable) {
                            if (TeacherTableUtils.checkWeekSectionTeacherId(t.getTeacherTable(), week, section, dt.getTeacherId())) {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "同学期跨年级,同一个老师，不能同一时间上不同年级的课(" + dt.getTeacherName() + " " + WeekEnum.getDayOfWeek(week) + " 第" + section + "节)");
                            }
                            if (TeacherTableUtils.checkWeekSectionClassOrRoomeId(t.getTeacherTable(), week, section, classOrRoomId)) {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "同学期跨年级,一个班级或教室不能同一时间被不同老师上课(" + dt.getTeacherName() + " " + WeekEnum.getDayOfWeek(week) + " 第" + section + "节)");
                            }
                        }
                    }
                }
            }
        }
        for (String abb : cddata.getTeacherTable().keySet()) {
            List<TeacherTableDTO> teachers = cddata.getTeacherTable().get(abb);
            for (TeacherTableDTO t : teachers) {
                if (t.getDetail().isEmpty()) {
                    RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s 未排课,至少排一节课", t.getTeacherName());
                }
            }
        }
        // 遍历总表每周节次对应的课程及(班级或教室名称)与教师对应的排课数据是否全部匹配
        for (DataTableDTO dto : cddata.getMainTable()) {
            for (WeekEnum week : dto.getDetail().keySet()) {
                Map<String, String> detail = dto.getDetail().get(week);
                for (String section : detail.keySet()) {
                    String abbreviation = detail.get(section);
                    if (!RuleUtils.ignoreCourse(abbreviation)) {
                        if (!TeacherTableUtils.check(cddata.getTeacherTable(), week, section, abbreviation, dto.getClassOrRoomId())) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getMainTableTemp(), "%s %s第%s节%s课程下无上课老师",
                                    dto.getClassOrRoomName(), WeekEnum.getDayOfWeek(week), section, RuleUtils.getCourseName(abbreviation, cddata.getCurriculumAliasMap()));
                        }
                    }
                }
            }
        }
    }

    private void analysisClassTable() {
        for (List<Map<Integer, String>> items : cddata.getClassTableTemp().getData().values()) {
            DataTableDTO dt = null;
            String className;
            for (int row = 1; row <= items.size(); row++) {
                Map<Integer, String> data = items.get(row - 1);
                if (row % 11 == 1) {
                    String classNameStr = data.get(0);
                    // 2021班转化为2021
                    className = classNameStr.substring(0, classNameStr.length() - 1);
                    // 去总表校验班级信息是否存在
                    String classId = ClassTableUtils.getClassOrRoomId(classesMap, null, className);
                    if (!MainTableUtils.checkClassOrRoomId(cddata.getMainTable(), classId)) {
                        RuleUtils.getMessage(cddata.getTable(), cddata.getMainTableTemp(), "未存在%s班课表", className);
                    }
                    dt = new DataTableDTO();
                    dt.setClassOrRoomId(classId);
                    dt.setClassOrRoomName(className);
                    dt.setDetail(new LinkedHashMap<>());
                    cddata.getClassTable().add(dt);
                    ClassVO classes = classesMap.get(className);
                    //有班主任则存入
                    if (classes.getHeadTeacherId() != null) {
                        cddata.getClassTeacher().put(classes.getHeadTeacherId(), Long.parseLong(dt.getClassOrRoomId()));
                    }
                    if (classes.getRoomId() != null) {
                        cddata.getClassRoomMap().put(classes.getId(), classes.getRoomId());
                    }
                } else if (row % 11 == 2) {
                } else {
                    int r = 8;
                    String section = data.get(0);
                    for (int i = 1; i < r; i++) {
                        String courseName = data.get(i);
                        WeekEnum week = WEEKMAP.get(i);
                        //当前班级的走班课信息
                        List<String> leaveClassList = cddata.getClassLeaveMap().computeIfAbsent(dt.getClassOrRoomId(), k -> new ArrayList<>());
                        //TODO:后期优化--------------------
                        List<String> abbList = new ArrayList<>();
                        if (StringUtils.isEmpty(courseName)) {
                            //行政班的课程数据（非走班）改成不维护，班级课程数据直接从课程总表获
                            String abb = MainTableUtils.getCourseNameClassOrRoomIdWeekSection(cddata.getMainTable(), dt.getClassOrRoomId(), week, section);
                            if (StringUtils.isEmpty(abb)) {
                                continue;
                            } else {
                                courseName = RuleUtils.getCourseName(abb, cddata.getCurriculumAliasMap());
                            }
                        } else {
                            //只要班级课表维护了课表都默认为走班课
                            String[] courses = courseName.split(Constant.SPLITSTRING);
                            for (String cName : courses) {
                                String abbreviation = cddata.getCurriculumAliasMap().get(cName);
                                leaveClassList.add(week + section + abbreviation);
                            }
                            String mainCourseName = MainTableUtils.getCourseNameClassOrRoomIdWeekSection(cddata.getMainTable(), dt.getClassOrRoomId(), week, section);
                            if (!StringUtils.isEmpty(mainCourseName)) {
                                mainCourseName = RuleUtils.getCourseName(mainCourseName, cddata.getCurriculumAliasMap());
                                courseName = courseName + Constant.SPLITSTRING + mainCourseName;
                            }
                        }
                        String[] courses = courseName.split(Constant.SPLITSTRING);
                        for (String cName : courses) {
                            String abbreviation = cddata.getCurriculumAliasMap().get(cName);
                            abbList.add(abbreviation);
                        }
                        Map<WeekEnum, Map<String, String>> detail = dt.getDetail();
                        if (detail == null) {
                            detail = new LinkedHashMap<>();
                            dt.setDetail(detail);
                        }
                        Map<String, String> detailMap = detail.computeIfAbsent(week, k -> new LinkedHashMap<>());
                        detailMap.put(section, StringUtils.listToString(abbList, Constant.SPLITSTRING));
                    }
                }
            }
        }
        //遍历总表的所有班级校验是否所有班级都存在
        for (DataTableDTO dtd : cddata.getMainTable()) {
            //只校验班级
            if (!ClassTableUtils.isClassRoom(dtd.getClassOrRoomId())) {
                if (!ClassTableUtils.checkClassId(cddata.getClassTable(), dtd.getClassOrRoomId())) {
                    RuleUtils.getMessage(cddata.getTable(), cddata.getClassTableTemp(), "缺少%s班", dtd.getClassOrRoomName());
                }
            }
        }
    }

    private void analysisStudentTable() {
        Map<String, StudentVO> studentMap = students.stream().collect(Collectors.toMap(StudentVO::getStudentNo, Function.identity(), (k1, k2) -> k1));
        for (List<Map<Integer, String>> items : cddata.getStudentTableTemp().getData().values()) {
            Map<Integer, String> headerMap = new LinkedHashMap<>();
            for (int i = 3; i < items.get(1).size(); i++) {
                String courseName = items.get(1).get(i);
                headerMap.put(i, courseName);
            }
            for (int j = 2; j < items.size(); j++) {
                Map<Integer, String> data = items.get(j);
                StudentTableDTO item = new StudentTableDTO();
                Map<String, Map<Long, String>> detail = new LinkedHashMap<>();
                String studentNo = data.get(1);
                StudentVO vo = studentMap.get(studentNo);
                if (vo == null) {
                    throw new BusinessException(CodeRes.CODE_700007);
                }
                item.setStuId(vo.getId());
                item.setStudentNo(vo.getStudentNo());
                item.setName(vo.getName());
                item.setClassId(vo.getClassId());
                item.setClassName(vo.getClassName());
                item.setGradeName(vo.getGradeName());
                item.setDetail(detail);
                List<String> leaveClass = new ArrayList<>();
                item.setLeaveClass(leaveClass);
                int step = 2;
                for (int i = 4; i < data.size(); i = i + step) {
                    String teacherName = data.get(i);
                    if (StringUtils.isEmpty(teacherName)) {
                        continue;
                    }
                    String courseName = headerMap.get(i);
                    String abbreviation = cddata.getCurriculumAliasMap().get(courseName);
                    if (abbreviation == null) {
                        log.info("表头信息如下{}", JSON.toJSONString(headerMap));
                        log.info("缩写信息如下{}", JSON.toJSONString(cddata.getCurriculumAliasMap()));
                        log.info("找不到{}的缩写", courseName);
                    }
                    Map<Long, String> teacherClassMap = new LinkedHashMap<>();
                    Long teacherId;
                    if (RuleUtils.ignoreCourse(courseName)) {
                        teacherId = 0L;
                        teacherClassMap.put(teacherId, vo.getClassId() + "");
                        WeekSectionDTO dto = MainTableUtils.getWeekSectionFirst(cddata.getMainTable(), String.valueOf(item.getClassId()), abbreviation);
                        if (dto == null) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), "%s班%s学生的%s课程在总课表里未匹配到对应课程", vo.getClassName(), vo.getName(), courseName);
                        }
                    } else {
                        if ("1".equals(teacherName)) {
                            //本班
                            //课获取总课表班级该课程第一次出现的周节次
                            teacherName = null;
                            WeekSectionDTO dto = MainTableUtils.getWeekSectionFirst(cddata.getMainTable(), String.valueOf(item.getClassId()), abbreviation);
                            if (dto != null) {
                                TeacherTableDTO teacherTableDTO = TeacherTableUtils.getTeacherTable(cddata.getTeacherTable(), dto.getWeek(), dto.getSection(), abbreviation, item.getClassId() + "");
                                if (teacherTableDTO != null) {
                                    teacherName = teacherTableDTO.getTeacherName();
                                } else {
                                    RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), "总课表与教师课表%s班级下不存在%s学生的%s课程", vo.getClassName(), vo.getName(), courseName);
                                }
                            } else {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), "总课表%s班级下不存在%s学生的%s课程", vo.getClassName(), vo.getName(), courseName);
                            }
                            if (!teacherMap.containsKey(teacherName)) {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), CodeRes.CODE_700011, teacherName);
                            }
                            TeacherVO teacher = teacherMap.get(teacherName);
                            teacherId = teacher.getId();
                            if (!TeacherTableUtils.checkTeacherIdAbbreviation(cddata.getTeacherTable(), teacherId, abbreviation)) {
                                String message = "%s教师科目与教师课表科目不匹配";
                                RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), message, teacherName);
                            }
                            teacherClassMap.put(teacherId, vo.getClassId() + "");
                        } else {
                            String classOrRoomName = data.get(i - 1);
                            String classOrRoomId = ClassTableUtils.getClassOrRoomId(classesMap, roomMap, classOrRoomName);
                            //走班
                            leaveClass.add(abbreviation);
                            if (!teacherMap.containsKey(teacherName)) {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), CodeRes.CODE_700011, teacherName);
                            }
                            TeacherVO teacher = teacherMap.get(teacherName);
                            teacherId = teacher.getId();
                            if (!ClassTableUtils.checkClass(cddata.getClassLeaveMap(), cddata.getClassTable(), item.getClassId(), abbreviation)) {
                                String message = "%s学生的走班课程(%s)未在班级课表中匹配到对应课课程";
                                RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), message, item.getName(), courseName);
                            }
                            StudentCheck.check1(cddata, item, teacher, courseName, abbreviation);
                            teacherClassMap.put(teacherId, classOrRoomId);
                            if (!TeacherTableUtils.checkTeacherIdAbbreviationAtClassOrRoomId(cddata.getTeacherTable(), teacherId, abbreviation, classOrRoomId)) {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), "%s学生科目%s%s老师未安排在上课地点%s上课", item.getName(), courseName, teacherName, classOrRoomName);
                            }
                        }
                    }
                    detail.put(abbreviation, teacherClassMap);
                }
                for (DataTableDTO classDto : cddata.getClassTable()) {
                    if (String.valueOf(item.getClassId()).equals(classDto.getClassOrRoomId())) {
                        for (WeekEnum week : classDto.getDetail().keySet()) {
                            Map<String, String> detaila = classDto.getDetail().get(week);
                            for (String section : detaila.keySet()) {
                                int i = 0;
                                String abbreviation = detaila.get(section);
                                Set<String> courseNames = new HashSet<>();
                                String[] abbs = abbreviation.split(Constant.SPLITSTRING);
                                Set<String> abbset = new HashSet<>(Arrays.asList(abbs));
                                for (String abb : abbset) {
                                    if (item.getDetail().containsKey(abb)) {
                                        for (Long teacherId : item.getDetail().get(abb).keySet()) {
                                            String classOrRoomId = item.getDetail().get(abb).get(teacherId);
                                            if (TeacherTableUtils.check(cddata.getTeacherTable(), week, section, abb, teacherId, classOrRoomId)) {
                                                i++;
                                                courseNames.add(RuleUtils.getCourseName(abb, cddata.getCurriculumAliasMap()));
                                            }
                                        }
                                    }
                                }
                                if (i > 1) {
                                    RuleUtils.getMessage(cddata.getTable(), cddata.getStudentTableTemp(), CodeRes.CODE_700015, vo.getName(), WeekEnum.getDayOfWeek(week), section, StringUtils.listToString(courseNames));
                                }
                            }
                        }
                    }
                }
                cddata.getStudentList().add(item);
            }
        }
    }

    public void analysis(GeneralTableDO data, List<GeneralTableDO> allSemesterTable) {
        analysisMainTable();
        analysisTeacherTable(allSemesterTable);
        analysisClassTable();
        analysisStudentTable();

        //同一班级多个老师教同一门课程
        Map<String, Map<String, Set<String>>> teacherTableMap = new LinkedHashMap<>();
        for (String abbreviation : cddata.getTeacherTable().keySet()) {
            List<TeacherTableDTO> teacherList = cddata.getTeacherTable().get(abbreviation);
            Map<String, Set<String>> tableMap = teacherTableMap.computeIfAbsent(abbreviation, k -> new LinkedHashMap<>());
            for (TeacherTableDTO teacherDto : teacherList) {
                for (WeekEnum week : teacherDto.getDetail().keySet()) {
                    Map<String, String> detail = teacherDto.getDetail().get(week);
                    for (String section : detail.keySet()) {
                        String classOrRoomId = detail.get(section);
                        if (ClassTableUtils.isClass(classOrRoomId)) {
                            Set<String> teacherNames = tableMap.computeIfAbsent(classOrRoomId, k -> new HashSet<>());
                            teacherNames.add(teacherDto.getTeacherName());
                        }
                    }
                }
            }
        }
        for (String abb : teacherTableMap.keySet()) {
            Map<String, Set<String>> teacherMap = teacherTableMap.get(abb);
            for (String classOrRoomId : teacherMap.keySet()) {
                Set<String> teacherNames = teacherMap.get(classOrRoomId);
                if (teacherNames.size() > 1) {
                    String classOrRoomName = ClassTableUtils.getClassOrRoomName(classesIdMap, roomIdMap, classOrRoomId);
                    String courseName = RuleUtils.getCourseName(abb, cddata.getCurriculumAliasMap());
                    RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s多个老师同时教%s班,课程:%s", StringUtils.listToString(teacherNames), classOrRoomName, courseName);
                }
            }
        }
        Map<Long, List<StudentTableDTO>> studentTable = StudentTableUtils.getStudentTable(cddata.getStudentList());
        for (String abb : cddata.getTeacherTable().keySet()) {
            List<TeacherTableDTO> dtos = cddata.getTeacherTable().get(abb);
            for (TeacherTableDTO dto : dtos) {
                for (WeekEnum week : dto.getDetail().keySet()) {
                    Map<String, String> detail = dto.getDetail().get(week);
                    for (String section : detail.keySet()) {
                        String classOrRoomId = detail.get(section);
                        if (!ClassTableUtils.checkClassAbbStudentAtClassOrRoom(cddata.getClassTable(), studentTable, week, section, abb, dto.getTeacherId(), classOrRoomId)) {
                            String classOrRoomName = ClassTableUtils.getClassOrRoomName(classesIdMap, roomIdMap, classOrRoomId);
                            RuleUtils.getMessage(cddata.getTable(), cddata.getTeacherTableTemp(), "%s教师%s第%s节任课的%s地点%s课程缺失上课学生", dto.getTeacherName(), WeekEnum.getDayOfWeek(week), section, classOrRoomName, RuleUtils.getCourseName(abb, cddata.getCurriculumAliasMap()));
                        }
                    }
                }
            }
        }
        for (DataTableDTO dt : cddata.getClassTable()) {
            for (WeekEnum week : dt.getDetail().keySet()) {
                for (String section : dt.getDetail().get(week).keySet()) {
                    String courseName = dt.getDetail().get(week).get(section);
                    String[] courses = courseName.split(Constant.SPLITSTRING);
                    Set<String> abbs = new HashSet<>(Arrays.asList(courses));
                    for (String course : abbs) {
                        Long classId = Long.parseLong(dt.getClassOrRoomId());
                        if (ClassTableUtils.checkLeaveClass(cddata.getClassLeaveMap(), cddata.getClassTable(), classId, week, section, course)) {
                            if (StudentTableUtils.getStudentLeavelClass(cddata.getStudentList(), course, classId).isEmpty()) {
                                RuleUtils.getMessage(cddata.getTable(), cddata.getClassTableTemp(), "%s班%s第%s节%s课程缺失走班课上课学生", dt.getClassOrRoomName(), WeekEnum.getDayOfWeek(week), section, RuleUtils.getCourseName(course, cddata.getCurriculumAliasMap()));
                            }
                        }
                    }
                }
            }
        }
        //总课表的课程每门课必须有对应的学生
        for (DataTableDTO dto : cddata.getMainTable()) {
            if (ClassTableUtils.isClassRoom(dto.getClassOrRoomId())) {
                for (WeekEnum week : dto.getDetail().keySet()) {
                    Map<String, String> detail = dto.getDetail().get(week);
                    for (String section : detail.keySet()) {
                        String abb = detail.get(section);
                        TeacherTableDTO teacherDto = TeacherTableUtils.getTeacherTable(cddata.getTeacherTable(), week, section, abb, dto.getClassOrRoomId());
                        //老师为空前面已做判断
                        List<StudentTableDTO> stus = StudentTableUtils.getStudentLeavelTeacher(cddata.getStudentList(), abb, teacherDto.getTeacherId());
                        boolean flag = false;
                        for (StudentTableDTO s : stus) {
                            if (ClassTableUtils.checkLeaveClass(cddata.getClassLeaveMap(), cddata.getClassTable(), s.getClassId(), week, section, abb)) {
                                flag = true;
                                break;
                            }
                        }
                        if (!flag) {
                            RuleUtils.getMessage(cddata.getTable(), cddata.getMainTableTemp(), "%s教室%s第%s节%s课程下无上课学生", dto.getClassOrRoomName(), WeekEnum.getDayOfWeek(week), section, RuleUtils.getCourseName(abb, cddata.getCurriculumAliasMap()));
                        }
                    }
                }
            }
        }

        data.setMainTable(cddata.getMainTable());
        data.setTeacherTable(cddata.getTeacherTable());
        data.setClassTable(cddata.getClassTable());
        data.setStudentTable(cddata.getStudentList());
        data.setClassTeacher(cddata.getClassTeacher());
        data.setClassRoomMap(cddata.getClassRoomMap());
        data.setClassTeacherIds(new ArrayList<>(cddata.getClassTeacher().keySet()));
        List<Long> classIds = new ArrayList<>();
        for (DataTableDTO d : cddata.getClassTable()) {
            classIds.add(Long.parseLong(d.getClassOrRoomId()));
        }
        data.setClassIds(classIds);
        data.setTeacherIds(new ArrayList<>(teacherIds));
        data.setClassLeaveMap(cddata.getClassLeaveMap());
        List<Long> stuIds = data.getStudentTable().stream().map(StudentTableDTO::getStuId).collect(Collectors.toList());
        data.setStuIds(stuIds);
    }

}
