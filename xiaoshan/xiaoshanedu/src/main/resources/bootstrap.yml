spring:
  cloud:
    consul:
      host: localhost
      port: 8500
      #enabled将此值设置为“false”禁用Consul配置
      config:
        acl-token:  ${CONSUL_HTTP_TOKEN}
        enabled: true   #默认是true --
        format: PROPERTIES    # 表示consul上面文件的格式 有四种 YAML PROPERTIES KEY-VALUE FILES
        data-key: data    #表示consul上面的KEY值(或者说文件的名字) 默认是data
        # watch选项为配置监视功能，主要监视配置的改变
        watch:
          enabled: true
          delay: 10000
          wait-time: 30
  application:
    name: education