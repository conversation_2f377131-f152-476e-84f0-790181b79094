<mapper>
    // 巡课记录、巡课对象联表查询
    <sql id="getPatrolAndObjects">
        SELECT
            pp.id as id,
            pp.type as type,
            pp.class_name as className,
            pp.course_time as courseTime,
            pp.course_name as courseName,
            pp.created_date as recordTime,
            pp.patrol_teacher_name as patrolTeacherName,
            pp.appraise as appraise,
            pp.content as content,
            ppo.name as objectName,
            ppo.object_id as objectId,
            ppo.department_name as departmentName,
            ppo.student_no as studentNo,
            ppo.grade_name as gradeName
        FROM
            pat_patrol pp
        LEFT JOIN
            pat_patrol_object ppo
        ON
            pp.id = ppo.patrol_id
        WHERE pp.deleted = 0
            <if condition="objectName != null and objectName != ''">
                AND ppo.`name` LIKE CONCAT( '%', #{objectName}, '%' )
            </if>
            <if condition="patrolTeacherName != null and patrolTeacherName != ''">
                AND pp.`patrol_teacher_name` LIKE CONCAT( '%', #{patrolTeacherName}, '%' )
            </if>
            <if condition="type != null and type != ''">
                AND pp.type = #{type}
            </if>
            <if condition="startDate != null and endDate != null">
                AND pp.created_date BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
            </if>
        ORDER BY
            pp.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    // 获取巡课统计数据
    <sql id="getPatrolStatistics">
        SELECT
            pp.class_name AS className,
            pp.created_date AS recordTime,
            pp.appraise AS appraise,
            ppo.object_id AS objectId,
            ppo.name AS objectName,
            ppo.department_name AS departmentName,
            ppo.student_no AS studentNo,
            ppo.grade_name AS gradeName
        FROM
            pat_patrol pp
        LEFT JOIN
            pat_patrol_object ppo
        ON
            pp.id = ppo.patrol_id
        WHERE pp.deleted = 0
            <if condition="className != null and className != ''">
                AND pp.class_name = #{className}
            </if>
            <if condition="type != null and type != ''">
                AND pp.type = #{type}
            </if>
        ORDER BY
            pp.created_date DESC
    </sql>

    // 获取单个对象巡课数据
    <sql id="getSingleObjectPatrol">
        SELECT
            *
        FROM
            (SELECT
                pp.id AS id,
                pp.type AS TYPE,
                pp.class_name AS className,
                pp.course_time AS courseTime,
                pp.course_name AS courseName,
                pp.created_date AS recordTime,
                pp.patrol_teacher_name AS patrolTeacherName,
                pp.appraise AS appraise,
                pp.content AS content,
                pp.pic_urls AS picUrls,
                ppo.object_id AS objectId,
                ppo.name AS objectName,
                ppo.department_name AS departmentName,
                ppo.student_no AS studentNo,
                ppo.grade_name AS gradeName
            FROM
                pat_patrol pp
            LEFT JOIN
                pat_patrol_object ppo
            ON
                pp.id = ppo.patrol_id
            WHERE
                pp.deleted = 0
            ) p
        WHERE
            p.type = #{type}
        AND
            p.objectId = #{objectId}
        AND
            p.recordTime BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
        ORDER BY
            p.recordTime DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    // 教师电子档案 巡课记录、巡课对象联表查询
    <sql id="getPatrolElectronicAndObjects">
        SELECT
        pp.id as id,
        pp.class_name as className,
        pp.course_time as courseTime,
        pp.patrol_teacher_name as patrolTeacherName,
        pp.appraise as appraise,
        ppo.name as objectName,
        ppo.object_id as objectId
        FROM
        pat_patrol pp
        LEFT JOIN
        pat_patrol_object ppo
        ON
        pp.id = ppo.patrol_id
        WHERE pp.deleted = 0
        AND pp.type = 'TEACHER'
        AND ppo.object_id = #{teacherId}
    </sql>
</mapper>