<mapper>

    <!-- 调课记录分页查询 -->
    <sql id="adjustmentRecords">
        SELECT
            cca.id AS id,
            cca.course_name AS courseName,
            cca.course_time AS courseTime,
            cca.course_room AS courseRoom,
            cca.teacher_name AS teacherName,
            cca.adjusted_course_name AS adjustedCourseName,
            cca.adjusted_course_time AS adjustedCourseTime,
            cca.adjusted_course_room AS adjustedCourseRoom,
            cca.adjusted_teacher_name AS adjustedTeacherName,
            cca.created_date AS createdDate,
            cca.approval_no AS approvalNo,
            cca.approval_state AS approvalState,
            cca.is_self_study AS isSelfStudy,
            cca.week_cycle_num AS weekCycleNum,
            cca.is_not_statistics AS isNotStatistics,
            cca.cycle_type AS cycleType,
            cca.custom_start_time AS customStartTime,
            cca.custom_end_time AS customEndTime,
            cca.is_attachment AS attachment,
            cca.main_id AS mainId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            (cca.is_attachment = 'false' OR (cca.is_attachment = 'true' AND cca.approval_state = 'PASS'))
        <if condition="courseName != null and courseName != ''">
            AND cca.course_name LIKE CONCAT( '%', #{courseName}, '%' )
        </if>
        <if condition="teacherName != null and teacherName != ''">
            AND cca.teacher_name LIKE CONCAT( '%', #{teacherName}, '%' )
        </if>
        <if condition="courseTime != null and courseTime != ''">
            AND cca.course_time LIKE CONCAT( '%', #{courseTime}, '%' )
        </if>
        <if condition="adjustedCourseName != null and adjustedCourseName != ''">
            AND cca.adjusted_course_name LIKE CONCAT( '%', #{adjustedCourseName}, '%' )
        </if>
        <if condition="adjustedTeacherName != null and adjustedTeacherName != ''">
            AND cca.adjusted_teacher_name LIKE CONCAT( '%', #{adjustedTeacherName}, '%' )
        </if>
        <if condition="adjustedCourseTime != null and adjustedCourseTime != ''">
            AND cca.adjusted_course_time LIKE CONCAT( '%', #{adjustedCourseTime}, '%' )
        </if>
        <if condition="approvalNo != null and approvalNo != ''">
            AND cca.approval_no = #{approvalNo}
        </if>
        <if condition="approvalState != null">
            AND cca.approval_state = #{approvalState}
        </if>
        <if condition="startDate != null and endDate != null">
            AND cca.created_date BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
        </if>
        <if condition="cycleType != null and cycleType != ''">
            AND cca.cycle_type = #{cycleType}
        </if>
        ORDER BY cca.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 代课记录查询 -->
    <sql id="getReplaceRecords">
        SELECT
        ccr.*
        FROM
        cou_course_replace ccr
        LEFT JOIN cou_replace_section crs ON ccr.id = crs.replace_id
        WHERE
        ccr.deleted = 0
        AND crs.replace_is_invalid = 'false'
        AND (ccr.replace_teacher_id in
        <foreach collection="teacherIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
         OR ccr.teacher_id in
        <foreach collection="teacherIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        )
        AND crs.course_time in
        <foreach collection="courseTimeList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        AND ccr.approval_state = #{approvalState}
    </sql>

    <!-- 代课记录查询 -->
    <sql id="getMoreReplaceRecords">
        SELECT
        ccr.teacher_id AS teacherId,
        ccr.replace_teacher_id AS replaceTeacherId,
        crs.course_time AS courseTime
        FROM
        cou_course_replace ccr
        LEFT JOIN cou_replace_section crs ON ccr.id = crs.replace_id
        WHERE
        ccr.deleted = 0
        AND crs.deleted = 0
        AND crs.replace_is_invalid = 'false'
        AND (ccr.replace_teacher_id in
        <foreach collection="teacherIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        OR ccr.teacher_id in
        <foreach collection="teacherIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        )
        AND ccr.approval_state = 'UNDER_APPROVAL'
    </sql>

    <!-- 调课记录查询 -->
    <sql id="getAdjustedRecords">
        SELECT
        *
        FROM
        `cou_course_adjustment`
        WHERE
        deleted = 0
        AND (teacher_id in
        <foreach collection="teacherIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        OR adjusted_teacher_id in
        <foreach collection="teacherIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        )
        AND (course_time in
        <foreach collection="courseTimeList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        OR adjusted_course_time in
        <foreach collection="courseTimeList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        )
        AND approval_state = #{approvalState}
    </sql>

    <!-- 统计单个教师调课记录分页查询 -->
    <sql id="singleAdjustmentRecords">
        SELECT
            cca.id AS id,
            cca.course_name AS courseName,
            cca.course_time AS courseTime,
            cca.course_room AS courseRoom,
            cca.teacher_name AS teacherName,
            cca.adjusted_course_name AS adjustedCourseName,
            cca.adjusted_course_time AS adjustedCourseTime,
            cca.adjusted_course_room AS adjustedCourseRoom,
            cca.adjusted_teacher_name AS adjustedTeacherName,
            cca.created_date AS createdDate,
            cca.approval_no AS approvalNo,
            cca.approval_state AS approvalState,
            cca.is_self_study AS isSelfStudy,
            cca.is_attachment AS attachment,
            cca.main_id AS mainId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.is_not_statistics = 'false'
        AND
            cca.is_invalid = 'false'
        <if condition="teacherName != null and teacherName != ''">
            AND cca.teacher_name = #{teacherName}
        </if>
        <if condition="approvalState != null">
            AND cca.approval_state = #{approvalState}
        </if>
        <if condition="startDate != null and endDate != null">
            AND cca.adjust_date BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
        </if>
        ORDER BY cca.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 个人调课记录分页查询 -->
    <sql id="personalAdjustmentRecords">
        SELECT
            cca.id AS id,
            cca.course_name AS courseName,
            cca.course_time AS courseTime,
            cca.course_room AS courseRoom,
            cca.teacher_name AS teacherName,
            cca.adjusted_course_name AS adjustedCourseName,
            cca.adjusted_course_time AS adjustedCourseTime,
            cca.adjusted_course_room AS adjustedCourseRoom,
            cca.adjusted_teacher_name AS adjustedTeacherName,
            cca.created_date AS createdDate,
            cca.approval_no AS approvalNo,
            cca.approval_state AS approvalState,
            cca.is_self_study AS isSelfStudy,
            cca.week_cycle_num AS weekCycleNum,
            cca.cycle_type AS cycleType,
            cca.custom_start_time AS customStartTime,
            cca.custom_end_time AS customEndTime,
            cca.is_attachment AS attachment,
            cca.main_id AS mainId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            (cca.teacher_name = #{myTeacherName} OR (cca.adjusted_teacher_name = #{myTeacherName} AND cca.approval_state = 'PASS'))
        AND
            (cca.is_attachment = 'false' OR (cca.is_attachment = 'true' AND cca.approval_state = 'PASS'))
        <if condition="courseName != null and courseName != ''">
            AND cca.course_name LIKE CONCAT( '%', #{courseName}, '%' )
        </if>
        <if condition="teacherName != null and teacherName != ''">
            AND cca.teacher_name LIKE CONCAT( '%', #{teacherName}, '%' )
        </if>
        <if condition="courseTime != null and courseTime != ''">
            AND cca.course_time LIKE CONCAT( '%', #{courseTime}, '%' )
        </if>
        <if condition="adjustedCourseName != null and adjustedCourseName != ''">
            AND cca.adjusted_course_name LIKE CONCAT( '%', #{adjustedCourseName}, '%' )
        </if>
        <if condition="adjustedTeacherName != null and adjustedTeacherName != ''">
            AND cca.adjusted_teacher_name LIKE CONCAT( '%', #{adjustedTeacherName}, '%' )
        </if>
        <if condition="adjustedCourseTime != null and adjustedCourseTime != ''">
            AND cca.adjusted_course_time LIKE CONCAT( '%', #{adjustedCourseTime}, '%' )
        </if>
        <if condition="approvalNo != null and approvalNo != ''">
            AND cca.approval_no = #{approvalNo}
        </if>
        <if condition="approvalState != null">
            AND cca.approval_state = #{approvalState}
        </if>
        <if condition="startDate != null and endDate != null">
            AND cca.created_date BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
        </if>
        <if condition="cycleType != null and cycleType != ''">
            AND cca.cycle_type = #{cycleType}
        </if>
        ORDER BY cca.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 代课记录分页查询【代课记录分页：只查询课程老师的代课记录】 -->
    <sql id="replaceRecords">
        SELECT
            ccr.id AS id,
            ccr.course_name AS courseName,
            CONCAT(ccr.replace_start_date, ' ', ccr.start_section) AS replaceStartTime,
            CONCAT(ccr.replace_end_date, ' ', ccr.end_section) AS replaceEndTime,
            ccr.section AS section,
            ccr.teacher_name AS teacherName,
            ccr.replace_teacher_name AS replaceTeacherName,
            ccr.created_date AS createdDate,
            ccr.approval_no AS approvalNo,
            ccr.approval_state AS approvalState
        FROM
            cou_course_replace ccr
        WHERE ccr.deleted = 0
        <if condition="courseName != null and courseName != ''">
            AND ccr.course_name LIKE CONCAT( '%', #{courseName}, '%' )
        </if>
        <if condition="teacherName != null and teacherName != ''">
            AND ccr.teacher_name LIKE CONCAT( '%', #{teacherName}, '%' )
        </if>
        <if condition="replaceTeacherName != null and replaceTeacherName != ''">
            AND ccr.replace_teacher_name LIKE CONCAT( '%', #{replaceTeacherName}, '%' )
        </if>
        <if condition="replaceStartDate != null and replaceEndDate != null">
            AND NOT (ccr.replace_start_date > #{replaceEndDate} OR ccr.replace_end_date &lt; #{replaceStartDate})
        </if>
        <if condition="approvalNo != null and approvalNo != ''">
            AND ccr.approval_no = #{approvalNo}
        </if>
        <if condition="approvalState != null">
            AND ccr.approval_state = #{approvalState}
        </if>
        <if condition="startDate != null and endDate != null">
            AND ccr.created_date BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
        </if>
        ORDER BY ccr.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 个人代课统计查询【个人代课统计：查询课程老师和代课老师（自己）的代课记录】 -->
    <sql id="replaceAllRecords">

        SELECT
            ccr.id AS id,
            ccr.course_name AS courseName,
            CONCAT(ccr.replace_start_date, ' ', ccr.start_section) AS replaceStartTime,
            CONCAT(ccr.replace_end_date, ' ', ccr.end_section) AS replaceEndTime,
            ccr.section AS section,
            ccr.teacher_name AS teacherName,
            ccr.replace_teacher_name AS replaceTeacherName,
            ccr.created_date AS createdDate,
            ccr.approval_no AS approvalNo,
            ccr.approval_state AS approvalState
        FROM
            cou_course_replace ccr
        WHERE ccr.deleted = 0
        <foreach collection="ids" item="id" open="AND ccr.id in (" separator="," close=")">
            #{id}
        </foreach>
        <if condition="teacherName != null and teacherName != ''">
            AND (ccr.teacher_name = #{teacherName} OR ccr.replace_teacher_name = #{teacherName})
        </if>
        <if condition="approvalState != null">
            AND ccr.approval_state = #{approvalState}
        </if>
        ORDER BY ccr.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 根据课程日期范围获取代课id -->
    <sql id="getReplaceIdsByReplaceDateScope">
        SELECT
            crs.replace_id
        FROM
            cou_replace_section crs
        WHERE
            crs.deleted = 0
        AND
            crs.replace_date BETWEEN #{startDate} AND #{endDate}

    </sql>

    <!-- 个人端代课记录分页查询 -->
    <sql id="personalReplaceRecords">
        SELECT
            ccr.id AS id,
            ccr.course_name AS courseName,
            CONCAT(ccr.replace_start_date, ' ', ccr.start_section) AS replaceStartTime,
            CONCAT(ccr.replace_end_date, ' ', ccr.end_section) AS replaceEndTime,
            ccr.start_section AS startSection,
            ccr.end_section AS endSection,
            ccr.section AS section,
            ccr.teacher_name AS teacherName,
            ccr.replace_teacher_name AS replaceTeacherName,
            ccr.created_date AS createdDate,
            ccr.approval_no AS approvalNo,
            ccr.approval_state AS approvalState
        FROM
            cou_course_replace ccr
        WHERE
            ccr.deleted = 0
        AND
            (ccr.teacher_name = #{myTeacherName} OR (ccr.replace_teacher_name = #{myTeacherName} AND ccr.approval_state = 'PASS'))
        <if condition="courseName != null and courseName != ''">
            AND ccr.course_name LIKE CONCAT( '%', #{courseName}, '%' )
        </if>
        <if condition="teacherName != null and teacherName != ''">
            AND ccr.teacher_name LIKE CONCAT( '%', #{teacherName}, '%' )
        </if>
        <if condition="replaceTeacherName != null and replaceTeacherName != ''">
            AND ccr.replace_teacher_name LIKE CONCAT( '%', #{replaceTeacherName}, '%' )
        </if>
        <if condition="replaceStartDate != null and replaceEndDate != null">
            AND NOT (ccr.replace_start_date > #{replaceEndDate} OR ccr.replace_end_date &lt; #{replaceStartDate})
        </if>
        <if condition="approvalNo != null and approvalNo != ''">
            AND ccr.approval_no = #{approvalNo}
        </if>
        <if condition="approvalState != null">
            AND ccr.approval_state = #{approvalState}
        </if>
        <if condition="startDate != null and endDate != null">
            AND ccr.created_date BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)
        </if>
        ORDER BY ccr.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 调代课统计 -->
    <sql id="adjustmentStatistics">
        SELECT
            allccar.teacherName as teacherName,
            allccar.teacherId as teacherId,
            allccar.departmentName as departmentName,
            allccar.adjustmentCount as adjustmentCount,
            allccar.toReplaceSection as toReplaceSection,
            allccar.beReplacedSection as beReplacedSection
        FROM
        (
            SELECT
                ccar.teacherName as teacherName,
                ccar.teacherId as teacherId,
                ccar.departmentName as departmentName,
                count(case when ccar.type='1' then 1 end) as adjustmentCount,
                ifnull(sum(case when ccar.type='2' and isToReplace='1' then ccar.section end),0) as toReplaceSection,
                ifnull(sum(case when ccar.type='2' and isToReplace='0' then ccar.section end),0) as beReplacedSection
            FROM
                (
                    SELECT
                        cca.teacher_name AS teacherName,
                        cca.teacher_id AS teacherId,
                        cca.department_name AS departmentName,
                        cca.adjust_date AS courseDate,
                        '0' AS section,
                        '1' AS type,
                        '0' AS isToReplace
                    FROM
                        cou_course_adjustment cca
                    WHERE
                        cca.deleted = 0
                    AND
                        cca.approval_state = 'PASS'
                    AND
                        cca.is_not_statistics = 'false'
                    <!-- 失效非异常的数据也要参与统计 -->
                    AND
                        (cca.is_invalid = 'false' OR (cca.is_invalid = 'true' AND cca.is_unusual = 'false'))
                    UNION ALL
                    SELECT
                        ccr.teacher_name AS teacherName,
                        ccr.teacher_id AS teacherId,
                        ccr.department_name AS departmentName,
                        crs.replace_date AS courseDate,
                        '1' AS section,
                        '2' AS type,
                        '0' AS isToReplace
                    FROM
                        cou_course_replace ccr
                    LEFT JOIN
                        cou_replace_section crs
                    ON
                        ccr.id = crs.replace_id
                    WHERE
                        ccr.deleted = 0
                    AND
                        ccr.approval_state = 'PASS'
                    <!-- 多次代课中间课程老师不纳入统计 -->
                    AND
                        (crs.replace_is_invalid = 'true' OR (crs.replace_is_invalid = 'false' AND (crs.replace_father_type IS NULL OR crs.replace_father_type = 'ADJUSTMENT')))
                    UNION ALL
                    SELECT
                        ccr.replace_teacher_name AS teacherName,
                        ccr.replace_teacher_Id AS teacherId,
                        ccr.replaced_department_name AS departmentName,
                        crs.replace_date AS courseDate,
                        '1' AS section,
                        '2' AS type,
                        '1' AS isToReplace
                    FROM
                        cou_course_replace ccr
                    LEFT JOIN
                        cou_replace_section crs
                    ON
                        ccr.id = crs.replace_id
                    WHERE
                        ccr.deleted = 0
                    AND
                        ccr.approval_state = 'PASS'
                    <!-- 多次代课中间代课老师不纳入统计/先代课后调课，代课老师纳入统计 -->
                    AND
                        (crs.replace_is_invalid = 'false' OR (crs.replace_is_invalid = 'true' AND (crs.is_consequent IS NULL OR crs.is_consequent = 'false')))
                ) AS ccar
            WHERE 1=1
            <if condition="startDate != null and endDate != null">
<!--                AND ccar.courseDate BETWEEN #{startDate} AND date_add(#{endDate},interval 1 day)-->
                AND ccar.courseDate BETWEEN #{startDate} AND #{endDate}
            </if>
            <if condition="departmentName != null and departmentName != ''">
                AND ccar.departmentName like concat('%',#{departmentName},'%')
            </if>
            <if condition="teacherName != null and teacherName != ''">
                AND ccar.teacherName like concat('%',#{teacherName},'%')
            </if>
            GROUP BY ccar.teacherId
        ) AS allccar
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 被代课节次 -->
    <sql id="getBeReplaceCount">
        SELECT
            ccr.teacher_id AS teacherId,
            crs.replace_id AS replaceId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <!-- 多次代课中间课程老师不纳入统计 -->
        AND
            (crs.replace_is_invalid = 'true' OR (crs.replace_is_invalid = 'false' AND (crs.replace_father_type IS NULL OR crs.replace_father_type = 'ADJUSTMENT')))
        <if condition="startDate != null and endDate != null">
            AND crs.replace_date BETWEEN #{startDate} AND #{endDate}
        </if>
        <if condition="departmentName != null and departmentName != ''">
            AND ccr.department_name like concat('%',#{departmentName},'%')
        </if>
        <if condition="teacherName != null and teacherName != ''">
            AND ccr.teacher_name = #{teacherName}
        </if>
    </sql>

    <!-- 代课节次 -->
    <sql id="getToReplaceCount">
        SELECT
            ccr.replace_teacher_Id AS teacherId,
            crs.replace_id AS replaceId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <!-- 多次代课中间代课老师不纳入统计/先代课后调课，代课老师纳入统计 -->
        AND
            (crs.replace_is_invalid = 'false' OR (crs.replace_is_invalid = 'true' AND (crs.is_consequent IS NULL OR crs.is_consequent = 'false')))
        <if condition="startDate != null and endDate != null">
            AND crs.replace_date BETWEEN #{startDate} AND #{endDate}
        </if>
        <if condition="departmentName != null and departmentName != ''">
            AND ccr.replaced_department_name like concat('%',#{departmentName},'%')
        </if>
        <if condition="teacherName != null and teacherName != ''">
            AND ccr.replace_teacher_name = #{teacherName}
        </if>
    </sql>

    <!-- 根据ids统计代课次数与被代次数 -->
    <sql id="getReplaceCount">
        SELECT
            allccar.teacherId as teacherId,
            allccar.toReplaceCount as toReplaceCount,
            allccar.beReplacedCount as beReplacedCount
        FROM
        (
            SELECT
                ccar.teacherId as teacherId,
                count(case when isToReplace='1' then 1 end) as toReplaceCount,
                count(case when isToReplace='0' then 1 end) as beReplacedCount
            FROM
            (
                SELECT
                    ccr.teacher_name AS teacherName,
                    ccr.teacher_id AS teacherId,
                    ccr.department_name AS departmentName,
                    '0' AS isToReplace
                FROM
                    cou_course_replace ccr
                WHERE
                    ccr.deleted = 0
                AND
                    ccr.approval_state = 'PASS'
                <foreach collection="ids" item="id" open="AND ccr.id in (" separator="," close=")">
                    #{id}
                </foreach>
                UNION ALL
                SELECT
                    ccr.replace_teacher_name AS teacherName,
                    ccr.replace_teacher_Id AS teacherId,
                    ccr.replaced_department_name AS departmentName,
                    '1' AS isToReplace
                FROM
                    cou_course_replace ccr
                WHERE
                    ccr.deleted = 0
                AND
                    ccr.approval_state = 'PASS'
                <foreach collection="ids" item="id" open="AND ccr.id in (" separator="," close=")">
                    #{id}
                </foreach>
            ) AS ccar
            WHERE 1=1
            <if condition="departmentName != null and departmentName != ''">
                AND ccar.departmentName like concat('%',#{departmentName},'%')
            </if>
            <if condition="teacherName != null and teacherName != ''">
                AND ccar.teacherName like concat('%',#{teacherName},'%')
            </if>
                GROUP BY ccar.teacherId
            ) AS allccar
    </sql>

    <!-- 小程序调代课申请记录分页查询 -->
    <sql id="personalApplyRecords">
        SELECT
            ccar.id AS id,
            ccar.teacherName AS teacherName,
            ccar.courseTime AS courseTime,
            ccar.adjustedCourseTime AS adjustedCourseTime,
            ccar.createdDate AS createdDate,
            ccar.approvalState AS approvalState,
            ccar.isUnusual AS isUnusual,
            ccar.type AS type,
            CONCAT(ccar.replaceStartDate, ' ', ccar.startSection) AS replaceStartTime,
            CONCAT(ccar.replaceEndDate, ' ', ccar.endSection) AS replaceEndTime,
            ccar.isSelfStudy AS isSelfStudy,
            ccar.weekCycleNum AS weekCycleNum,
            ccar.cycleType AS cycleType,
            ccar.customStartTime AS customStartTime,
            ccar.customEndTime AS customEndTime
        FROM
        (
            SELECT
                cca.id AS id,
                cca.teacher_name AS teacherName,
                cca.course_time AS courseTime,
                cca.adjusted_course_time AS adjustedCourseTime,
                cca.created_date AS createdDate,
                cca.approval_state AS approvalState,
                cca.is_unusual AS isUnusual,
                cca.is_self_study AS isSelfStudy,
                1 AS type,
                NULL AS replaceStartDate,
                NULL AS replaceEndDate,
                NULL AS StartSection,
                NULL AS EndSection,
                cca.week_cycle_num AS weekCycleNum,
                cca.cycle_type AS cycleType,
                cca.custom_start_time AS customStartTime,
                cca.custom_end_time AS customEndTime
            FROM
                cou_course_adjustment cca
            WHERE
                cca.deleted = 0
            AND
                (cca.teacher_name = #{teacherName} OR (cca.adjusted_teacher_name = #{teacherName} AND cca.approval_state = 'PASS'))
            AND
                (cca.is_attachment = 'false' OR (cca.is_attachment = 'true' AND cca.approval_state = 'PASS'))
        <if condition="cycleType != null and cycleType != ''">
            AND cca.cycle_type = #{cycleType}
        </if>
            UNION
            SELECT
                ccr.id AS id,
                ccr.teacher_name AS teacherName,
                NULL AS courseTime,
                NULL AS adjustedCourseTime,
                ccr.created_date AS createdDate,
                ccr.approval_state AS approvalState,
                ccr.is_unusual AS isUnusual,
                NULL AS isSelfStudy,
                2 AS type,
                ccr.replace_start_date AS replaceStartDate,
                ccr.replace_end_date AS replaceEndDate,
                ccr.start_section AS startSection,
                ccr.end_section AS endSection,
                NULL AS weekCycleNum,
                NULL AS cycleType,
                NULL AS customStartTime,
                NULL AS customEndTime
            FROM
                cou_course_replace ccr
            WHERE
                ccr.deleted = 0
            AND
                (ccr.teacher_name = #{teacherName} OR (ccr.replace_teacher_name = #{teacherName} AND ccr.approval_state = 'PASS'))
        ) AS ccar
        ORDER BY ccar.createdDate DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!-- 根据第几周和教师id获取审批通过的调代课记录 -->
    <sql id="getCourseAdjustmentAndReplace">
        SELECT
            (case when cca.is_self_study='true' then '自修' else cca.course_name end) AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) LIKE CONCAT( '%', #{week}, '%' )
        <!-- 先代课后调课可能curriculumTableId不同 -->
<!--        AND-->
<!--            cca.curriculum_table_id = #{curriculumTableId}-->
        UNION ALL
        SELECT
            cca.course_name AS courseName,
            'COVERADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            cca.origin_course_time AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        <!-- 先调课后代课此数据失效查不出来 -->
        AND
            cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.course_time LIKE CONCAT( '%', #{week}, '%' )
        <!-- 先代课后调课可能curriculumTableId不同 -->
<!--        AND-->
<!--            cca.curriculum_table_id = #{curriculumTableId}-->
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'COVERADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.adjusted_origin_course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.adjusted_origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
        UNION ALL
        SELECT
            ccr.course_name AS courseName,
            'SUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            ccr.replace_teacher_id AS teacherId,
            ccr.replace_teacher_name AS teacherName,
            crs.course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND ccr.replace_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            crs.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            crs.replace_is_invalid != 'true'
        AND
            ccr.replace_curriculum_table_id = #{curriculumTableId}
        UNION ALL
        SELECT
            ccr.course_name AS courseName,
            'COVERSUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            crs.origin_teacher_id AS teacherId,
            ccr.teacher_name AS teacherName,
            crs.origin_course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND crs.origin_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            crs.curriculum_table_id = #{curriculumTableId}
        AND
            crs.origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        <!-- 多次代课过滤 -->
        AND
            crs.replace_is_invalid = 'false'
<!--        AND-->
<!--            crs.course_time = crs.origin_course_time-->
    </sql>

    <!-- 根据代课节次id获取代课记录 -->
    <sql id="getReplaceByIdAndCourseTime">
        SELECT
            ccr.course_name AS courseName,
            ccr.teacher_name AS teacherName,
            ccr.replace_teacher_name AS replaceTeacherName,
            crs.course_room AS courseRoom,
            crs.course_room_id AS courseRoomId,
            crs.course_time AS courseTime,
            crs.replace_date AS replaceDate
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        AND
            crs.id = #{id}
    </sql>

    <sql id="getCourseAdjustmentAndReplaceByClassIds">
        SELECT
            (case when cca.is_self_study='true' then '自修' else cca.course_name end) AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.is_invalid = 'false'
        AND
            cca.course_room_id = #{classId}
        AND
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
<!--        UNION ALL-->
<!--        SELECT-->
<!--            cca.course_name AS courseName,-->
<!--            'COVERADJUSTMENT' AS type,-->
<!--            cca.id AS adjustmentId,-->
<!--            cca.course_room_id AS classOrRoomId,-->
<!--            cca.course_room AS classOrRoomName,-->
<!--            cca.teacher_id AS teacherId,-->
<!--            cca.teacher_name AS teacherName,-->
<!--            cca.origin_course_time AS courseTime,-->
<!--            cca.curriculum_table_id AS curriculumTableId-->
<!--        FROM-->
<!--            cou_course_adjustment cca-->
<!--        WHERE-->
<!--            cca.deleted = 0-->
<!--        AND-->
<!--            cca.approval_state = 'PASS'-->
<!--        AND-->
<!--            cca.is_invalid = 'false'-->
<!--        AND-->
<!--            cca.course_room_id = #{classId}-->
<!--        AND-->
<!--            cca.origin_course_time LIKE CONCAT( '%', #{week}, '%' )-->
<!--        AND-->
<!--            cca.curriculum_table_id = #{curriculumTableId}-->
<!--        &lt;!&ndash; 针对班级课表，跨周调课的情况，原始课程周次与被调周次不一样时返回 &ndash;&gt;-->
<!--        AND-->
<!--            left(cca.origin_course_time, 4) != left(cca.adjusted_course_time, 4)-->
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        AND
            cca.adjusted_course_room_id = #{classId}
        AND
            cca.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
<!--        UNION ALL-->
<!--        SELECT-->
<!--            cca.adjusted_course_name AS courseName,-->
<!--            'COVERADJUSTMENT' AS type,-->
<!--            cca.id AS adjustmentId,-->
<!--            cca.adjusted_course_room_id AS classOrRoomId,-->
<!--            cca.adjusted_course_room AS classOrRoomName,-->
<!--            cca.adjusted_teacher_id AS teacherId,-->
<!--            cca.adjusted_teacher_name AS teacherName,-->
<!--            cca.adjusted_origin_course_time AS courseTime,-->
<!--            cca.adjusted_curriculum_table_id AS curriculumTableId-->
<!--        FROM-->
<!--            cou_course_adjustment cca-->
<!--        WHERE-->
<!--            cca.deleted = 0-->
<!--        AND-->
<!--            cca.approval_state = 'PASS'-->
<!--        AND-->
<!--            cca.adjusted_is_invalid = 'false'-->
<!--        AND-->
<!--            cca.course_room_id = #{classId}-->
<!--        AND-->
<!--            cca.adjusted_origin_course_time LIKE CONCAT( '%', #{week}, '%' )-->
<!--        AND-->
<!--            cca.curriculum_table_id = #{curriculumTableId}-->
<!--        &lt;!&ndash; 针对班级课表，跨周调课的情况，被调原始课程周次与调课周次不一样时返回 &ndash;&gt;-->
<!--        AND-->
<!--            left(cca.adjusted_origin_course_time, 4) != left(cca.course_time, 4)-->
        UNION ALL
        SELECT
            ccr.course_name AS courseName,
            'SUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            ccr.replace_teacher_id AS teacherId,
            ccr.replace_teacher_name AS teacherName,
            crs.course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        AND
            crs.course_room_id = #{classId}
        AND
            crs.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            crs.replace_is_invalid != 'true'
    </sql>

    <sql id="getCourseAdjustmentAndReplaceOfStudentTable">
        SELECT
            (case when cca.is_self_study='true' then '自修' else cca.course_name end) AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.is_invalid = 'false'
        AND (
            <foreach collection="teacherIds" item="teacherId" open="cca.teacher_id in (" separator="," close=")">
                #{teacherId}
            </foreach>
            OR
            <foreach collection="teacherIds" item="teacherId" open="cca.origin_teacher_id in (" separator="," close=")">
                #{teacherId}
            </foreach>
        )

        AND
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
        AND
            cca.course_room_id = #{classId}
        UNION ALL
        SELECT
            cca.course_name AS courseName,
            'COVERADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            cca.origin_course_time AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
<!--        &lt;!&ndash; 针对学生课表，跨周调课的情况，原始课程周次与被调周次不一样时返回 &ndash;&gt;-->
<!--        AND-->
<!--            left(cca.origin_course_time, 4) != left(cca.adjusted_course_time, 4)-->
        AND
            cca.course_room_id = #{classId}
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        AND (
            <foreach collection="teacherIds" item="teacherId" open="cca.adjusted_teacher_id in (" separator="," close=")">
                #{teacherId}
            </foreach>
        OR
            <foreach collection="teacherIds" item="teacherId" open="cca.adjusted_origin_teacher_id in (" separator="," close=")">
                #{teacherId}
            </foreach>
        )
        AND
            cca.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
        AND
            cca.adjusted_course_room_id = #{classId}
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'COVERADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.adjusted_origin_course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.adjusted_origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            cca.curriculum_table_id = #{curriculumTableId}
<!--        &lt;!&ndash; 针对班级课表，跨周调课的情况，被调原始课程周次与调课周次不一样时返回 &ndash;&gt;-->
<!--        AND-->
<!--            left(cca.adjusted_origin_course_time, 4) != left(cca.course_time, 4)-->
        AND
            cca.adjusted_course_room_id = #{classId}
        UNION ALL
        SELECT
            ccr.course_name AS courseName,
            'SUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            ccr.replace_teacher_id AS teacherId,
            ccr.replace_teacher_name AS teacherName,
            crs.course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND crs.origin_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            crs.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            crs.replace_is_invalid != 'true'
        AND
            crs.curriculum_table_id = #{curriculumTableId}
        AND
            crs.course_room_id = #{classId}
    </sql>

    <sql id="getCourseAdjustmentAndReplaceOfRoomTable">
        SELECT
            ccr.course_name AS courseName,
            'SUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            ccr.replace_teacher_id AS teacherId,
            ccr.replace_teacher_name AS teacherName,
            crs.course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        AND
            crs.course_room_id = #{roomId}
        AND
            crs.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            crs.replace_is_invalid != 'true'
    </sql>

    <sql id="getCourseAdjustmentAndReplaceByRoomIdAndCourseName">
        SELECT
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            cca.course_name AS courseName
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_course_room_id = #{roomId}
        AND
            cca.adjusted_course_time = #{courseTime}
        UNION ALL
        SELECT
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.adjusted_course_name AS courseName
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.course_room_id = #{roomId}
        AND
            cca.course_time = #{courseTime}
        UNION ALL
        SELECT
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            ccr.replace_teacher_id AS teacherId,
            ccr.replace_teacher_name AS teacherName,
            ccr.course_name AS courseName
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        AND
            crs.course_room_id = #{roomId}
        AND
            crs.course_time = #{courseTime}
    </sql>

    <!-- 课程时间列表和教师id获取审批中或审批通过的 调课、被调课、代课记录-->
    <sql id="getAdjustmentOrReplaceRecords">
<!--        SELECT-->
<!--            cca.id AS id-->
<!--        FROM-->
<!--            cou_course_adjustment cca-->
<!--        WHERE-->
<!--            cca.deleted = 0-->
<!--        AND-->
<!--            cca.teacher_id = #{teacherId}-->
<!--        AND-->
<!--            cca.approval_state = 'UNDER_APPROVAL'-->
<!--        <foreach collection="courseTimeList" item="courseTime" open="AND cca.course_time in (" separator="," close=")">-->
<!--            #{courseTime}-->
<!--        </foreach>-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--            cca.id AS id-->
<!--        FROM-->
<!--            cou_course_adjustment cca-->
<!--        WHERE-->
<!--            cca.deleted = 0-->
<!--        AND-->
<!--            cca.adjusted_teacher_id = #{teacherId}-->
<!--        AND-->
<!--        cca.approval_state = 'UNDER_APPROVAL'-->
<!--            <foreach collection="courseTimeList" item="courseTime" open="AND cca.adjusted_course_time in (" separator="," close=")">-->
<!--                #{courseTime}-->
<!--            </foreach>-->
<!--        UNION ALL-->
        SELECT
            ccr.replace_teacher_id AS teacherId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        <foreach collection="teacherIds" item="teacherId" open="AND ccr.replace_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            ccr.approval_state = 'UNDER_APPROVAL'
        <foreach collection="courseTimeList" item="courseTime" open="AND crs.course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
    </sql>

    <!-- 课程时间列表和教师id获取审批中的调课/被调课/被代课记录-->
    <sql id="getAdjustmentOrReplaceRecords2">
<!--        SELECT-->
<!--            cca.id AS id-->
<!--        FROM-->
<!--            cou_course_adjustment cca-->
<!--        WHERE-->
<!--            cca.deleted = 0-->
<!--        AND-->
<!--            cca.teacher_id = #{teacherId}-->
<!--        AND-->
<!--            cca.approval_state = 'UNDER_APPROVAL'-->
<!--        <foreach collection="courseTimeList" item="courseTime" open="AND cca.course_time in (" separator="," close=")">-->
<!--            #{courseTime}-->
<!--        </foreach>-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--            cca.id AS id-->
<!--        FROM-->
<!--            cou_course_adjustment cca-->
<!--        WHERE-->
<!--            cca.deleted = 0-->
<!--        AND-->
<!--            cca.adjusted_teacher_id = #{teacherId}-->
<!--        AND-->
<!--            cca.approval_state = 'UNDER_APPROVAL'-->
<!--        <foreach collection="courseTimeList" item="courseTime" open="AND cca.adjusted_course_time in (" separator="," close=")">-->
<!--            #{courseTime}-->
<!--        </foreach>-->
<!--        UNION ALL-->
        SELECT
            ccr.teacher_id AS teacherId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.is_unusual = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND ccr.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            ccr.approval_state = 'UNDER_APPROVAL'
        <foreach collection="courseTimeList" item="courseTime" open="AND crs.course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
    </sql>

    <sql id="getCourseAdjustmentAndReplaceByCurriculumTableIds">
        SELECT
            (case when cca.is_self_study='true' then '自修' else cca.course_name end) AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) LIKE CONCAT( '%', #{week}, '%' )
        <!-- 先代课后调课可能curriculumTableId不同 -->
        <!--        AND-->
        <!--            cca.curriculum_table_id = #{curriculumTableId}-->
        UNION ALL
        SELECT
            cca.course_name AS courseName,
            'COVERADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.course_room_id AS classOrRoomId,
            cca.course_room AS classOrRoomName,
            cca.teacher_id AS teacherId,
            cca.teacher_name AS teacherName,
            cca.origin_course_time AS courseTime,
            cca.curriculum_table_id AS curriculumTableId
            FROM
            cou_course_adjustment cca
            WHERE
            cca.deleted = 0
            AND
            cca.approval_state = 'PASS'
        <!-- 先调课后代课此数据失效查不出来 -->
            AND
                cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND cca.curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'ADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.course_time LIKE CONCAT( '%', #{week}, '%' )
        <!-- 先代课后调课可能curriculumTableId不同 -->
        <!--        AND-->
        <!--            cca.curriculum_table_id = #{curriculumTableId}-->
        UNION ALL
        SELECT
            cca.adjusted_course_name AS courseName,
            'COVERADJUSTMENT' AS type,
            cca.id AS adjustmentId,
            cca.adjusted_course_room_id AS classOrRoomId,
            cca.adjusted_course_room AS classOrRoomName,
            cca.adjusted_teacher_id AS teacherId,
            cca.adjusted_teacher_name AS teacherName,
            cca.adjusted_origin_course_time AS courseTime,
            cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.approval_state = 'PASS'
        AND
            cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            cca.adjusted_origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND cca.adjusted_curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
        UNION ALL
        SELECT
            ccr.course_name AS courseName,
            'SUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            ccr.replace_teacher_id AS teacherId,
            ccr.replace_teacher_name AS teacherName,
            crs.course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND ccr.replace_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            crs.course_time LIKE CONCAT( '%', #{week}, '%' )
        AND
            crs.replace_is_invalid = 'false'
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND ccr.replace_curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
        UNION ALL
        SELECT
            ccr.course_name AS courseName,
            'COVERSUBSTITUTE' AS type,
            crs.id AS adjustmentId,
            crs.course_room_id AS classOrRoomId,
            crs.course_room AS classOrRoomName,
            crs.origin_teacher_id AS teacherId,
            ccr.teacher_name AS teacherName,
            crs.origin_course_time AS courseTime,
            crs.curriculum_table_id AS curriculumTableId
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND crs.origin_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
            crs.origin_course_time LIKE CONCAT( '%', #{week}, '%' )
        <!-- 多次代课过滤 -->
        AND
            crs.replace_is_invalid = 'false'
<!--        AND-->
<!--            crs.course_time = crs.origin_course_time-->
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND crs.curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
    </sql>


    <sql id="getCourseAdjustmentAndReplaceByCurriculumTableIdsBatch">
        SELECT
        (case when cca.is_self_study='true' then '自修' else cca.course_name end) AS courseName,
        'ADJUSTMENT' AS type,
        cca.id AS adjustmentId,
        cca.course_room_id AS classOrRoomId,
        cca.course_room AS classOrRoomName,
        cca.teacher_id AS teacherId,
        cca.teacher_name AS teacherName,
        (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) AS courseTime,
        cca.curriculum_table_id AS curriculumTableId
        FROM
        cou_course_adjustment cca
        WHERE
        cca.deleted = 0
        AND
        cca.approval_state = 'PASS'
        AND
        cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
        (case when cca.is_self_study='true' then cca.course_time else cca.adjusted_course_time end) REGEXP #{week}
        <!-- 先代课后调课可能curriculumTableId不同 -->
        <!--        AND-->
        <!--            cca.curriculum_table_id = #{curriculumTableId}-->
        UNION ALL
        SELECT
        cca.course_name AS courseName,
        'COVERADJUSTMENT' AS type,
        cca.id AS adjustmentId,
        cca.course_room_id AS classOrRoomId,
        cca.course_room AS classOrRoomName,
        cca.teacher_id AS teacherId,
        cca.teacher_name AS teacherName,
        cca.origin_course_time AS courseTime,
        cca.curriculum_table_id AS curriculumTableId
        FROM
        cou_course_adjustment cca
        WHERE
        cca.deleted = 0
        AND
        cca.approval_state = 'PASS'
        <!-- 先调课后代课此数据失效查不出来 -->
        AND
        cca.is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
        cca.origin_course_time REGEXP #{week}
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND cca.curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
        UNION ALL
        SELECT
        cca.adjusted_course_name AS courseName,
        'ADJUSTMENT' AS type,
        cca.id AS adjustmentId,
        cca.adjusted_course_room_id AS classOrRoomId,
        cca.adjusted_course_room AS classOrRoomName,
        cca.adjusted_teacher_id AS teacherId,
        cca.adjusted_teacher_name AS teacherName,
        cca.course_time AS courseTime,
        cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
        cou_course_adjustment cca
        WHERE
        cca.deleted = 0
        AND
        cca.approval_state = 'PASS'
        AND
        cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
        cca.course_time REGEXP #{week}
        <!-- 先代课后调课可能curriculumTableId不同 -->
        <!--        AND-->
        <!--            cca.curriculum_table_id = #{curriculumTableId}-->
        UNION ALL
        SELECT
        cca.adjusted_course_name AS courseName,
        'COVERADJUSTMENT' AS type,
        cca.id AS adjustmentId,
        cca.adjusted_course_room_id AS classOrRoomId,
        cca.adjusted_course_room AS classOrRoomName,
        cca.adjusted_teacher_id AS teacherId,
        cca.adjusted_teacher_name AS teacherName,
        cca.adjusted_origin_course_time AS courseTime,
        cca.adjusted_curriculum_table_id AS curriculumTableId
        FROM
        cou_course_adjustment cca
        WHERE
        cca.deleted = 0
        AND
        cca.approval_state = 'PASS'
        AND
        cca.adjusted_is_invalid = 'false'
        <foreach collection="teacherIds" item="teacherId" open="AND cca.adjusted_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
        cca.adjusted_origin_course_time REGEXP #{week}
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND cca.adjusted_curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
        UNION ALL
        SELECT
        ccr.course_name AS courseName,
        'SUBSTITUTE' AS type,
        crs.id AS adjustmentId,
        crs.course_room_id AS classOrRoomId,
        crs.course_room AS classOrRoomName,
        ccr.replace_teacher_id AS teacherId,
        ccr.replace_teacher_name AS teacherName,
        crs.course_time AS courseTime,
        crs.curriculum_table_id AS curriculumTableId
        FROM
        cou_course_replace ccr
        LEFT JOIN
        cou_replace_section crs
        ON
        ccr.id = crs.replace_id
        WHERE
        ccr.deleted = 0
        AND
        ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND ccr.replace_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
        crs.course_time REGEXP #{week}
        AND
        crs.replace_is_invalid = 'false'
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND ccr.replace_curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
        UNION ALL
        SELECT
        ccr.course_name AS courseName,
        'COVERSUBSTITUTE' AS type,
        crs.id AS adjustmentId,
        crs.course_room_id AS classOrRoomId,
        crs.course_room AS classOrRoomName,
        crs.origin_teacher_id AS teacherId,
        ccr.teacher_name AS teacherName,
        crs.origin_course_time AS courseTime,
        crs.curriculum_table_id AS curriculumTableId
        FROM
        cou_course_replace ccr
        LEFT JOIN
        cou_replace_section crs
        ON
        ccr.id = crs.replace_id
        WHERE
        ccr.deleted = 0
        AND
        ccr.approval_state = 'PASS'
        <foreach collection="teacherIds" item="teacherId" open="AND crs.origin_teacher_id in (" separator="," close=")">
            #{teacherId}
        </foreach>
        AND
        crs.origin_course_time REGEXP #{week}
        <!-- 多次代课过滤 -->
        AND
        crs.replace_is_invalid = 'false'
        <!--        AND-->
        <!--            crs.course_time = crs.origin_course_time-->
        <foreach collection="curriculumTableIds" item="curriculumTableId" open="AND crs.curriculum_table_id in (" separator="," close=")">
            #{curriculumTableId}
        </foreach>
    </sql>


    <!-- 查询老师1在时间集合1中 老师2在时间集合2中有无调代课记录（审批通过未删除）-->
    <sql id="getRepeatDate">
        SELECT
            cca.teacher_name AS teacherName,
            cca.course_time AS courseTime
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.teacher_id = #{teacherId1}
        AND
            cca.approval_state = 'PASS'
        <foreach collection="courseTimes1" item="courseTime" open="AND cca.course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
        UNION ALL
        SELECT
            cca.adjusted_teacher_name AS teacherName,
            cca.adjusted_course_time AS courseTime
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.adjusted_teacher_id = #{teacherId1}
        AND
            cca.approval_state = 'PASS'
        <foreach collection="courseTimes1" item="courseTime" open="AND cca.adjusted_course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
        UNION ALL
        SELECT
            cca.teacher_name AS teacherName,
            cca.course_time AS courseTime
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.teacher_id = #{teacherId2}
        AND
            cca.approval_state = 'PASS'
        <foreach collection="courseTimes2" item="courseTime" open="AND cca.course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
        UNION ALL
        SELECT
            cca.adjusted_teacher_name AS teacherName,
            cca.adjusted_course_time AS courseTime
        FROM
            cou_course_adjustment cca
        WHERE
            cca.deleted = 0
        AND
            cca.adjusted_teacher_id = #{teacherId2}
        AND
            cca.approval_state = 'PASS'
        <foreach collection="courseTimes2" item="courseTime" open="AND cca.adjusted_course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
        UNION ALL
        SELECT
            ccr.teacher_name AS teacherName,
            crs.course_time AS courseTime
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.teacher_id = #{teacherId1}
        AND
        ccr.approval_state = 'PASS'
        <foreach collection="courseTime1" item="courseTime" open="AND crs.course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
        UNION ALL
        SELECT
            ccr.teacher_name AS teacherName,
            crs.course_time AS courseTime
        FROM
            cou_course_replace ccr
        LEFT JOIN
            cou_replace_section crs
        ON
            ccr.id = crs.replace_id
        WHERE
            ccr.deleted = 0
        AND
            ccr.teacher_id = #{teacherId2}
        AND
            ccr.approval_state = 'PASS'
        <foreach collection="courseTime2" item="courseTime" open="AND crs.course_time in (" separator="," close=")">
            #{courseTime}
        </foreach>
    </sql>
</mapper>