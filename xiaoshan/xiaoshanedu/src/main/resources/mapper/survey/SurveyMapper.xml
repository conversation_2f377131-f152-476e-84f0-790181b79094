<mapper>

    <sql id="curriculumList">
        SELECT
            distinct
            a.curriculum,
            a.teacherName,
            a.teacherId,
            IF( b.teacherId IS NOT NULL, 'true', 'false' ) AS isFinish
        FROM
            sur_task_template_student_teacher_question a
        LEFT JOIN (
            SELECT DISTINCT teacherId
            FROM sur_task_template_student_teacher_question
            WHERE deleted = 0
                AND taskId = #{taskId}
                AND templateId = #{templateId}
                AND studentId = #{studentId}
                AND isFinish = 'true'
        ) b ON a.teacherId = b.teacherId
        WHERE
            a.deleted = 0
            AND a.taskId = #{taskId}
            AND a.templateId = #{templateId}
            AND a.studentId = #{studentId};
    </sql>

    <sql id="questionList">
        SELECT
            a.id AS questionId,
            a.isFinish,
            a.answer,
            a.questionTitle AS title,
            a.questionSort,
            a.questionType
        FROM
            sur_task_template_student_teacher_question a
        WHERE a.deleted = 0
            AND a.taskId = #{taskId}
            AND a.templateId = #{templateId}
            AND a.studentId = #{studentId}
            AND a.teacherId = #{teacherId}
    </sql>

    <sql id="taskList">
        SELECT
            a.id AS taskId,
            a.startYear,
            a.endYear,
            a.term,
            a.grade,
            a.`name`,
            a.startDate,
            a.endDate,
            a.total,
            a.isPublish,
            b.finishNum
        FROM
            sur_task a
            LEFT JOIN (
                SELECT
                    taskId,
                    count( 1 ) AS finishNum
                FROM sur_task_student
                WHERE
                    deleted = 0
                    AND isFinish = 'true'
                GROUP BY taskId
            ) b ON a.id = b.taskId
        WHERE a.deleted = 0
        <if condition="startYear!=null">
            AND a.startYear = #{startYear}
        </if>
        <if condition="term!=null">
            AND a.term = #{term}
        </if>
        <if condition="grade!=null">
            AND a.grade = #{grade}
        </if>
        <if condition="name!=null">
            AND a.name like concat('%',#{name},'%')
        </if>
        <if condition="startDate!=null and endDate!=null">
            AND a.endDate between #{startDate} and #{endDate}
        </if>
        ORDER BY created_date DESC LIMIT #{pageIndex},#{pageSize}
    </sql>

    <sql id="getFinishNum">
        SELECT
            count( 1 ) AS total,
            b.`undo` AS `undo`
        FROM
            sur_task_student a
            JOIN (
                SELECT
                    count( 1 ) AS `undo`
                FROM
                    sur_task_student
                WHERE
                    deleted = 0
                    AND taskId = #{taskId}
                    AND isFinish = 'false'
            ) b
        WHERE
            a.deleted = 0
            AND a.taskId = #{taskId}

    </sql>

    <sql id="getTask">
        SELECT
            DISTINCT a.taskId,
            b.`name`,
            b.startDate,
            b.endDate,
            a.isFinish AS isSubmit,
            b.explanation
        FROM
            sur_task_student a
            LEFT JOIN sur_task b ON b.deleted = 0 AND a.taskId = b.id
        WHERE
            a.deleted = 0
            AND a.studentId = #{studentId}
        ORDER BY b.modified_date desc
    </sql>

    <sql id="getTeacherTask">
        SELECT DISTINCT
            a.taskId,
            b.`name`,
            b.modified_date AS modifiedDate
        FROM
            sur_task_template_student_teacher_question a
            left join sur_task b on b.deleted=0 and a.taskId = b.id
        WHERE
            a.deleted = 0
            AND b.isPublish = 'true'
            AND a.teacherId = #{teacherId}
        ORDER BY b.modified_date desc
    </sql>

    <sql id="getTemplate">
        SELECT DISTINCT
            a.templateId,
            b.`name` AS templateName,
            b.surveyObj,
            if(c.id is null, 'false', 'true') AS isFinish
        FROM
            sur_task_template_student_teacher_question a
            left join sur_template b on b.deleted=0 and a.templateId = b.id
            left join sur_task_template_student c on c.deleted = 0 and  c.taskId = #{taskId} and c.studentId = #{studentId} and c.templateId = a.templateId
        WHERE
            a.deleted = 0
            AND a.taskId = #{taskId}
            AND a.studentId = #{studentId}
    </sql>

    <sql id="getTeacherTemplate">
        SELECT
            DISTINCT a.templateId,
            b.`name` AS templateName,
            b.surveyObj
        FROM
            sur_task_template_student_teacher_question a
            LEFT JOIN sur_template b ON b.deleted = 0 AND a.templateId = b.id
        WHERE
            a.deleted = 0
            AND a.taskId = #{taskId}
            AND a.teacherId = #{teacherId}
    </sql>

    <sql id="archives">
        SELECT
            *
        FROM
            sur_task
        WHERE
            deleted = 0
            AND sur_task.id IN ( SELECT DISTINCT taskId FROM sur_task_template_student_teacher_question WHERE deleted = 0 AND teacherId = #{teacherId} )
        ORDER BY startDate
        LIMIT #{pageIndex},#{pageSize}
    </sql>

</mapper>
