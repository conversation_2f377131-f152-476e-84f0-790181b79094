<mapper>
    <!--公开课记录分页查询-->
    <sql id="openClassRecords">
        SELECT
            ooc.id AS openClassId,
            ooc.`subject` AS SUBJECT,
            ooc.course_name AS courseName,
            ooc.teacher_name AS teacherName,
            ooc.type AS type,
            concat( ooc.date, ' ', '第', ooc.week_num, '周', ' ', week_name ) AS date,
            ooc.section AS section,
            replace(ooc.attend_class, ' ', '') AS attendClass,
            replace(ooc.place, ' ', '') AS place,
            ooc.score AS score,
            ooc.confirm_state AS confirmState,
            ooc.approval_state AS approvalState,
            cou.listenTeacherNum AS listenTeacherNum,
            ooc.is_new AS isNew,
            ooc.approval_code AS approvalCode,
            IF( ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}), 'true', 'false') AS courseState,
            CASE
            WHEN week_name = '星期一' THEN
            '1'
            WHEN week_name = '星期二' THEN
            '2'
            WHEN week_name = '星期三' THEN
            '3'
            WHEN week_name = '星期四' THEN
            '4'
            WHEN week_name = '星期五' THEN
            '5'
            WHEN week_name = '星期六' THEN
            '6'
            WHEN week_name = '星期日' THEN
            '7' ELSE 0
            END AS weekInt
        FROM
            ope_open_class ooc
        LEFT JOIN (
                SELECT
                    oocp.open_class_id,
                    count( 1 ) AS listenTeacherNum
                FROM
                    ope_open_class_person oocp
                GROUP BY oocp.open_class_id
            ) cou ON ooc.id = cou.open_class_id
        WHERE ooc.deleted = 0
            <if condition="approvalCode != null and !''.equals(approvalCode)">
                AND ooc.`approval_code` = #{approvalCode}
            </if>
            <if condition="subject != null and subject != ''">
                AND ooc.`subject` = #{subject}
            </if>
            <if condition="type != null">
                AND ooc.type = #{type}
            </if>
            <if condition="courseName != null and courseName != ''">
                AND ooc.course_name LIKE CONCAT( '%', #{courseName}, '%' )
            </if>
            <if condition="courseState != null and courseState.equals(true)">
                AND (ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}))
            </if>
            <if condition="courseState != null and courseState.equals(false)">
                AND (ooc.date &gt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &gt; #{nowTime}))
            </if>
            <if condition="approvalState != null">
                AND ooc.approval_state = #{approvalState}
            </if>
            <if condition="confirmState != null">
                AND ooc.confirm_state = #{confirmState}
            </if>
            <if condition="teacherName != null and teacherName != ''">
                AND ooc.teacher_name LIKE concat( '%', #{teacherName}, '%' )
            </if>
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if condition="confirmState != null and confirmState.equals('CONFIRMED')">
                <if condition="dataSort != null and dataSort == 1">
                    ORDER BY
                    date ASC,
                    week_num ASC,
                    weekInt ASC,
                    ooc.start_time ASC
                </if>
                <if condition="dataSort != null and dataSort == 2">
                    ORDER BY
                    date DESC,
                    week_num DESC,
                    weekInt DESC,
                    ooc.start_time DESC
                </if>
                <if condition="dataSort == null">
                    ORDER BY ooc.confirm_date DESC
                </if>
            </if>
            <if condition="confirmState != null and confirmState.equals('CONFIRMING')">
                <if condition="dataSort != null and dataSort == 1">
                    ORDER BY
                    date ASC,
                    week_num ASC,
                    weekInt ASC,
                    ooc.start_time ASC
                </if>
                <if condition="dataSort != null and dataSort == 2">
                    ORDER BY
                    date DESC,
                    week_num DESC,
                    weekInt DESC,
                    ooc.start_time DESC
                </if>
                <if condition="dataSort == null">
                    ORDER BY
                    CASE WHEN IF( ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}), 'true', 'false') = 'true' THEN ooc.date END DESC,
                    CASE WHEN IF( ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}), 'true', 'false') = 'true' THEN ooc.start_time END DESC,
                    CASE WHEN IF( ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}), 'true', 'false') = 'true' THEN ooc.publish_date END DESC,
                    CASE WHEN IF( ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}), 'true', 'false') = 'false' THEN ooc.publish_date END DESC
                </if>
            </if>
            <if condition="confirmState != null and confirmState.equals('TO_BE_RELEASED')">
                <if condition="dataSort != null and dataSort == 1">
                    ORDER BY
                    date ASC,
                    week_num ASC,
                    weekInt ASC,
                    ooc.start_time ASC
                </if>
                <if condition="dataSort != null and dataSort == 2">
                    ORDER BY
                    date DESC,
                    week_num DESC,
                    weekInt DESC,
                    ooc.start_time DESC
                </if>
                <if condition="dataSort == null">
                    ORDER BY
                    CASE WHEN ooc.approval_state = 'PASS' THEN ooc.approval_state END DESC,
                    CASE WHEN ooc.approval_state = 'PASS' THEN ooc.approval_pass_date END DESC,
                    CASE WHEN ooc.approval_state &lt;&gt; 'PASS' THEN ooc.created_date END DESC
                </if>
            </if>
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!--公开课广场、听课记录分页查询-->
    <sql id="square">
        SELECT
            ooc.id AS openClassId,
            ooc.`subject` AS `subject`,
            ooc.course_name AS courseName,
            ooc.teacher_name AS teacherName,
            ooc.type AS type,
            concat( ooc.date, ' 第', ooc.week_num, '周 ', week_name ) AS date,
            ooc.section AS section,
            replace(ooc.attend_class, ' ', '') AS attendClass,
            replace(ooc.place, ' ', '') AS place,
            IF ( oocp.id IS NULL, 'false', 'true' ) AS listen,
            IF( ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}), 'true', 'false') AS courseState,
            oocp.evaluated AS evaluated,
            CASE
            WHEN week_name = '星期一' THEN
            '1'
            WHEN week_name = '星期二' THEN
            '2'
            WHEN week_name = '星期三' THEN
            '3'
            WHEN week_name = '星期四' THEN
            '4'
            WHEN week_name = '星期五' THEN
            '5'
            WHEN week_name = '星期六' THEN
            '6'
            WHEN week_name = '星期日' THEN
            '7' ELSE 0
            END AS weekInt
        FROM
            ope_open_class ooc
        LEFT JOIN ope_open_class_person oocp ON ooc.id = oocp.open_class_id AND oocp.teacher_user_id = #{teacherUserId}
        WHERE
            ooc.deleted = 0
            AND ooc.confirm_state &lt;&gt; 'TO_BE_RELEASED'
            AND ooc.teacher_user_id &lt;&gt; #{teacherUserId}
            <if condition="typeMult != null and !typeMult.isEmpty()">
                <foreach collection="typeMult" item="ty" open="AND ooc.type in (" separator="," close=")">
                    #{ty}
                </foreach>
            </if>
            <if condition="subjectMult != null and !subjectMult.isEmpty()">
                <foreach collection="subjectMult" item="sub" open="AND ooc.subject in (" separator="," close=")">
                    #{sub}
                </foreach>
            </if>
            <if condition="search != null and search != ''">
                AND ((ooc.`course_name` LIKE CONCAT( '%', #{search}, '%' )) or (ooc.`teacher_name` LIKE CONCAT( '%', #{search}, '%' )))
            </if>
            <if condition="subject != null and subject != ''">
                AND ooc.`subject` = #{subject}
            </if>
            <if condition="type != null">
                AND ooc.type = #{type}
            </if>
            <if condition="courseName != null and courseName != ''">
                AND ooc.course_name LIKE CONCAT( '%', #{courseName}, '%' )
            </if>
            <if condition="courseState != null and courseState.equals(true)">
                AND (ooc.date &lt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}))
            </if>
            <if condition="courseState != null and courseState.equals(false)">
                AND (ooc.date &gt; #{nowDate} OR (ooc.date = #{nowDate} AND ooc.start_time &gt; #{nowTime}))
            </if>
            <if condition="listenRecord.equals(false)">
                AND oocp.id IS NULL
            </if>
            <if condition="listenRecord != null and listenRecord.equals(true)">
                AND oocp.id IS NOT NULL
            </if>
            <if condition="teacherName != null and teacherName != ''">
                AND ooc.teacher_name LIKE concat( '%', #{teacherName}, '%' )
            </if>
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if condition="listenRecord != null and listenRecord.equals(true)">
                <if condition="dataSort != null and dataSort == 1">
                    ORDER BY
                    date ASC,
                    ooc.week_num ASC,
                    weekInt ASC,
                    ooc.start_time ASC
                </if>
                <if condition="dataSort != null and dataSort == 2">
                    ORDER BY
                    date DESC,
                    ooc.week_num DESC,
                    weekInt DESC,
                    ooc.start_time DESC
                </if>
                <if condition="dataSort == null">
                    ORDER BY oocp.created_date DESC
                </if>
            </if>
            <if condition="listenRecord != null and listenRecord.equals(false)">
                <if condition="dataSort != null and dataSort == 1">
                    ORDER BY
                    date ASC,
                    ooc.week_num ASC,
                    weekInt ASC,
                    ooc.start_time ASC
                </if>
                <if condition="dataSort != null and dataSort == 2">
                    ORDER BY
                    date DESC,
                    ooc.week_num DESC,
                    weekInt DESC,
                    ooc.start_time DESC
                </if>
                <if condition="dataSort == null">
                    ORDER BY (ooc.date &lt; DATE_FORMAT(NOW(),'%Y-%m-%d') || (ooc.date = DATE_FORMAT(NOW(),'%Y-%m-%d') and ooc.start_time &lt; DATE_FORMAT(NOW(),'%H-%i-%s'))),  ooc.publish_date DESC
                </if>
            </if>
        LIMIT #{pageIndex},#{pageSize}
    </sql>

    <!--上课记录分页查询-->
    <sql id="launchRecord">
        SELECT
            ooc.id AS openClassId,
            ooc.`subject` AS SUBJECT,
            ooc.course_name AS courseName,
            ooc.teacher_name AS teacherName,
            ooc.type AS type,
            concat( ooc.date, ' ', '第', ooc.week_num, '周', ' ', week_name ) AS date,
            ooc.section AS section,
            replace(ooc.attend_class, ' ', '') AS attendClass,
            replace(ooc.place, ' ', '') AS place,
            ooc.score AS score,
            ooc.confirm_state AS confirmState,
            ooc.approval_state AS approvalState,
            cou.listenTeacherNum AS listenTeacherNum,
            ooc.is_new AS isNew,
            ooc.approval_code AS approvalCode,
            CASE
            WHEN week_name = '星期一' THEN
            '1'
            WHEN week_name = '星期二' THEN
            '2'
            WHEN week_name = '星期三' THEN
            '3'
            WHEN week_name = '星期四' THEN
            '4'
            WHEN week_name = '星期五' THEN
            '5'
            WHEN week_name = '星期六' THEN
            '6'
            WHEN week_name = '星期日' THEN
            '7' ELSE 0
            END AS weekInt
        FROM
            ope_open_class ooc
        LEFT JOIN (
            SELECT
                oocp.open_class_id,
                count( 1 ) AS listenTeacherNum
            FROM
                ope_open_class_person oocp
            GROUP BY oocp.open_class_id ) cou ON ooc.id = cou.open_class_id
        WHERE ooc.deleted = 0
            <if condition="approvalCode != null and !''.equals(approvalCode)">
                AND ooc.`approval_code` = #{approvalCode}
            </if>
            <if condition="typeMult != null and !typeMult.isEmpty()">
                <foreach collection="typeMult" item="ty" open="AND ooc.type in (" separator="," close=")">
                    #{ty}
                </foreach>
            </if>
            <if condition="subjectMult != null and !subjectMult.isEmpty()">
                <foreach collection="subjectMult" item="sub" open="AND ooc.subject in (" separator="," close=")">
                    #{sub}
                </foreach>
            </if>
            <if condition="subject != null and subject != ''">
                AND ooc.`subject` = #{subject}
            </if>
            <if condition="type != null">
                AND ooc.type = #{type}
            </if>
            <if condition="courseName != null and courseName != ''">
                AND ooc.course_name LIKE CONCAT( '%', #{courseName}, '%' )
            </if>
            <if condition="teacherUserId != null">
                AND ooc.teacher_user_id = #{teacherUserId}
            </if>
            <if condition="approvalState != null">
                AND ooc.approval_state = #{approvalState}
            </if>
            <if condition="confirmState != null">
                AND ooc.confirm_state = #{confirmState}
            </if>
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>

        <if condition="dataSort != null and dataSort == 1">
            ORDER BY
            date ASC,
            ooc.week_num ASC,
            weekInt ASC,
            ooc.start_time ASC
        </if>
        <if condition="dataSort != null and dataSort == 2">
            ORDER BY
            date DESC,
            ooc.week_num DESC,
            weekInt DESC,
            ooc.start_time DESC
        </if>
        <if condition="dataSort == null">
            ORDER BY created_date DESC
        </if>
        LIMIT #{pageIndex},#{pageSize}
    </sql>

    <!--小程序公开课记录-->
    <sql id="wxRecord">
        SELECT
        id AS openClassId,
        subject,
        course_name AS courseName,
        teacher_name AS teacherName,
        type,
        concat(date, ' ', '第', week_num, '周', ' ', week_name ) AS date,
        section,
        attend_class AS attendClass,
        place,
        IF(date &lt; #{nowDate} OR (date = #{nowDate} AND start_time &lt;= #{nowTime}), 'true', 'false') AS courseState,
        CASE
        WHEN week_name = '星期一' THEN
        '1'
        WHEN week_name = '星期二' THEN
        '2'
        WHEN week_name = '星期三' THEN
        '3'
        WHEN week_name = '星期四' THEN
        '4'
        WHEN week_name = '星期五' THEN
        '5'
        WHEN week_name = '星期六' THEN
        '6'
        WHEN week_name = '星期日' THEN
        '7' ELSE 0
        END AS weekInt
        FROM
        ope_open_class
        WHERE deleted = 0
        AND `approval_state` = 'PASS'
        <if condition="typeMult != null and !typeMult.isEmpty()">
            <foreach collection="typeMult" item="ty" open="AND type in (" separator="," close=")">
                #{ty}
            </foreach>
        </if>
        <if condition="subjectMult != null and !subjectMult.isEmpty()">
            <foreach collection="subjectMult" item="sub" open="AND subject in (" separator="," close=")">
                #{sub}
            </foreach>
        </if>
        <if condition="isPublish != null and isPublish.equals(true)">
            AND confirm_state != 'TO_BE_RELEASED'
        </if>
        <if condition="isPublish != null and isPublish.equals(false)">
            AND confirm_state = 'TO_BE_RELEASED'
        </if>
        <if condition="isFinish != null and isFinish.equals(true)">
            AND (date &lt; #{nowDate} OR (date = #{nowDate} AND ooc.start_time &lt;= #{nowTime}))
        </if>
        <if condition="isFinish != null and isFinish.equals(false)">
            AND (date &gt; #{nowDate} OR (date = #{nowDate} AND start_time &gt; #{nowTime}))
        </if>
        <if condition="keywords != null and keywords != ''">
            AND (course_name like CONCAT( '%', #{keywords}, '%' ) or teacher_name like CONCAT( '%', #{keywords}, '%' ))
        </if>
        <if condition="dataSort != null and dataSort == 1">
            ORDER BY
            date ASC,
            week_num ASC,
            weekInt ASC,
            start_time ASC
        </if>
        <if condition="dataSort != null and dataSort == 2">
            ORDER BY
            date DESC,
            week_num DESC,
            weekInt DESC,
            start_time DESC
        </if>
        <if condition="dataSort == null">
            ORDER BY date DESC,start_time DESC
        </if>
        LIMIT #{pageIndex},#{pageSize}
    </sql>

    <!--三种公开课数量统计-->
    <sql id="statistics">
        SELECT
            ooc.type,
            count( 1 ) AS count
        FROM
            ope_open_class ooc
        WHERE ooc.deleted = 0
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            AND confirm_state = 'CONFIRMED'
        GROUP BY
            ooc.type
    </sql>

    <!--公开课统计 教师-->
    <sql id="statisticsTeachers">
        SELECT
            ooc.teacher_user_id AS teacherUserId,
            ooc.teacher_name AS teacherName,
            count( 1 ) AS total,
            ooc.teacher_department AS teacherDepartment,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.teacher_name = ooc.teacher_name and o.type = 'INNER_LECTURE' and o.deleted=0) innerLectureNum,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.teacher_name = ooc.teacher_name and o.type = 'INNER_OPEN_CLASS' and o.deleted=0) innerOpenClassNum,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.teacher_name = ooc.teacher_name and o.type = 'SCHOOL_OPEN_CLASS' and o.deleted=0) schoolOpenClassNum,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.teacher_name = ooc.teacher_name and o.type = 'CLOUD_CLASS' and o.deleted=0) cloudClassNum
        FROM
            ope_open_class ooc
        WHERE ooc.deleted = 0
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            AND ooc.confirm_state = 'CONFIRMED'
            <if condition="teacherName != null and teacherName != ''">
                AND ooc.teacher_name like concat('%',#{teacherName},'%')
            </if>
        GROUP BY
            ooc.teacher_name
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!--公开课统计 教师、科目 总页数-->
    <sql id="statisticsTeachersCount">
        SELECT
            count(DISTINCT ooc.teacher_name)
        FROM
            ope_open_class ooc
        WHERE ooc.deleted = 0
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            AND ooc.confirm_state = 'CONFIRMED'
            <if condition="teacherName != null and teacherName != ''">
                AND ooc.teacher_name like concat('%',#{teacherName},'%')
            </if>

    </sql>

    <!--公开课教师、科目详情-->
    <sql id="statisticsTeachersDetails">
        SELECT
            ooc.`subject` AS subject,
            ooc.course_name AS courseName,
            ooc.teacher_name AS teacherName,
            ooc.type AS type,
            concat( ooc.date, ' ', '第', ooc.week_num, '周', ' ', week_name ) AS date,
            ooc.section AS section,
            replace(ooc.attend_class, ' ', '') AS attendClass,
            replace(ooc.place, ' ', '') AS place
        FROM
            ope_open_class ooc
        WHERE ooc.deleted = 0
            <if condition="type != null">
                AND ooc.type = #{type}
            </if>
            <if condition="teacherUserId != null">
                AND ooc.teacher_user_id = #{teacherUserId}
            </if>
            <if condition="subject != null and subject != ''">
                AND ooc.subject = #{subject}
            </if>
            AND ooc.confirm_state = 'CONFIRMED'
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
        ORDER BY
            ooc.created_date DESC
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!--公开课统计 科目-->
    <sql id="statisticsSubjects">
        SELECT
            ooc.subject AS subject,
            count( 1 ) AS total,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.subject = ooc.subject and o.type = 'INNER_LECTURE') innerLectureNum,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.subject = ooc.subject and o.type = 'INNER_OPEN_CLASS') innerOpenClassNum,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.subject = ooc.subject and o.type = 'SCHOOL_OPEN_CLASS') schoolOpenClassNum,
            (select count(1) from ope_open_class o WHERE o.date BETWEEN #{startDate} AND #{endDate} and o.confirm_state = 'CONFIRMED' and o.subject = ooc.subject and o.type = 'CLOUD_CLASS') cloudClassNum
        FROM
            ope_open_class ooc
        WHERE ooc.deleted = 0
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            AND ooc.confirm_state = 'CONFIRMED'
        GROUP BY
            ooc.subject
        <if condition="pageSize != -1">
            LIMIT #{pageIndex},#{pageSize}
        </if>
    </sql>

    <!--公开课统计 总页数-->
    <sql id="statisticsSubjectsCount">
        SELECT
            count(DISTINCT ooc.subject)
        FROM
            ope_open_class ooc
        WHERE ooc.deleted = 0
            <if condition="startDate != null and endDate != null">
                AND ooc.date BETWEEN #{startDate} AND #{endDate}
            </if>
            AND ooc.confirm_state = 'CONFIRMED'
    </sql>

    <!--公开课电子档案听课教师-->
    <sql id="archivesAll">
        SELECT
            ooc.id AS openClassId,
            ooc.course_name AS courseName,
            ooc.subject,
            ooc.type,
            concat(date, ' ', '第', ooc.week_num, '周', ' ', ooc.week_name) AS date,
            ooc.attend_class AS attendClass,
            oocp.total_score AS score,
            ooc.confirm_state AS confirmState,
            'true' AS listen
        FROM
            ope_open_class_person oocp
            inner JOIN ope_open_class ooc ON oocp.open_class_id = ooc.id
        WHERE
            ooc.deleted = 0
            and oocp.teacher_id = #{teacherId}
            and ooc.confirm_state = 'CONFIRMED'
            <if condition="startDate!=null">
                AND ooc.date &gt;= #{startDate}
            </if>
            <if condition="endDate!=null">
                AND ooc.date &lt;= #{endDate}
            </if>
    </sql>
</mapper>
