###########-------------------------System Configuration
spring.profiles.active=dev
spring.application.name=education
magic.system.version=@project.version@
common.coderes.classpath=@<EMAIL>
maven.package.time=@maven.package.time@
###########-------------------------\u8BF7\u52FF\u4FEE\u6539\u4EE5\u4E0A\u914D\u7F6E----------------
###########-------------------------\u4EE5\u4E0B\u53EF\u914D\u7F6E\u516C\u5171\u4FE1\u606F---------------
start.framework.sendconfig=true
server.port=8089
root.path=/home/<USER>
#\u4E0A\u4F20\u76EE\u5F55
upload.file.path=${root.path}/upload
local.file.path=${root.path}/local
#\u4E0A\u4F20\u6587\u4EF6\u603B\u7684\u6700\u5927\u503C
spring.servlet.multipart.max-request-size=10MB
#\u5355\u4E2A\u6587\u4EF6\u7684\u6700\u5927\u503C
spring.servlet.multipart.max-file-size=10MB
server.max-http-header-size=512KB
###########-------------------------\u5FAE\u4FE1
xiaoshan.weixin.appid=wx86aa340a09e076a5
swagger.custom.check.admin=1
swagger.custom.name.restful=\u516C\u5171\u65E0\u6388\u6743\u63A5\u53E3
swagger.custom.name.client=\u5BA2\u6237\u7AEF(APP\u3001H5\u3001\u5C0F\u7A0B\u5E8F\u3001\u684C\u9762)\u7B49\u5E94\u7528API-(\u7528\u6237\u767B\u5F55\u540E)
swagger.custom.name.admin=\u540E\u53F0\u7BA1\u7406\u6587\u6863
swagger.custom.name.secretKey=\u5185\u90E8\u63A5\u53E3
# consul \u914D\u7F6E
spring.cloud.consul.host=localhost
spring.cloud.consul.port=8500
spring.cloud.consul.discovery.prefer-ip-address=true
spring.cloud.loadbalancer.ribbon.enabled=false
management.endpoints.web.exposure.include=prometheus,health,metrics
management.endpoint.health.show-details=always
management.metrics.tags.application=${spring.application.name}