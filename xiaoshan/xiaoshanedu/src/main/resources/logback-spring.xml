<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <springProperty name="LOG_HOME" source="root.path" defaultValue="/home/<USER>"/>
    <springProperty name="LOG_REDIS_HOST" source="logging.redis.host" defaultValue=""/>
    <springProperty scope="context" name="LOG_REDIS_PASSWORD" source="logging.redis.password" defaultValue=""/>
    <springProperty name="LOG_REDIS_DATABASE" source="logging.redis.database" defaultValue="0"/>
    <springProperty name="LOG_REDIS_TYPE" source="logging.redis.type" defaultValue="systemlog"/>
    <contextName>logback</contextName>
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springboot.sample" level="TRACE"/>

    <!-- 控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/systeminfo-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="LOGSTASH" class="com.cwbase.logback.RedisAppender">
        <source>education</source>
        <type>${LOG_REDIS_TYPE}</type>
        <host>${LOG_REDIS_HOST}</host>
        <if condition='!property("LOG_REDIS_PASSWORD").equals("")'>
            <then>
                <password>${LOG_REDIS_PASSWORD}</password>
                <database>${LOG_REDIS_DATABASE}</database>
            </then>
        </if>
        <key>systemlog</key>
        <mdc>true</mdc>
        <location>true</location>
        <callerStackIndex>0</callerStackIndex>
    </appender>

    <appender name="LOGSTASH_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="LOGSTASH"/>
    </appender>

    <appender name="FILE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="FILE"/>
        <appender-ref ref="LOGSTASH"/>
    </root>

</configuration>